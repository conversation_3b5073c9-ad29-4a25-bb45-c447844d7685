version: "3.8"

services:
  postgres:
    image: postgres:16
    ports:
      - "0.0.0.0:${DATABASE_PORT}:5432"
    volumes:
      - "$HOME/addie_data/data:/var/lib/postgresql/data"
      - "$APP_DIR/docker/postgresql.conf:/var/lib/postgresql/data/postgresql.conf"
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}

  redis:
    image:
      redis
    volumes:
      - "./redis.conf:/usr/local/etc/redis/redis.conf"
    ports:
      - 0.0.0.0:${REDIS_PORT}:6379
    command: redis-server /usr/local/etc/redis/redis.conf
