"""
Tests for the engagement tracking system (ADDY-499, ADDY-495, ADDY-501)
"""

import unittest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from tests.utils.base_test_cases import BaseWorkflowTestCase

from addie.services.engagement_tracker import EngagementTracker
from addie.services.engagement_tiers import EngagementTierService
from addie.services.admin_dashboard import AdminDashboardService
from addie.services.reminder_template_service import ReminderTemplateService
from prisma.enums import (
    EngagementChannel, 
    EngagementEventType, 
    EngagementTier
)


class TestEngagementTracker(BaseWorkflowTestCase):
    """Test the EngagementTracker service"""
    
    def setUp(self):
        """Set up test environment"""
        super().setUp()
        self.tracker = EngagementTracker(self.prisma)
    
    def test_track_message_event(self):
        """Test tracking a message event"""
        # Track a message event
        self.tracker.track_message_event(
            user_id=self.user_id,
            student_id=self.student_id,
            session_id="test-session",
            channel=EngagementChannel.web_student,
            event_type=EngagementEventType.message_sent,
            workflow_id=self.workflow_id,
            metadata={"test": "data"}
        )
        
        # Verify the event was tracked
        events = self.prisma.engagementevent.find_many(
        where={"student_id": self.student_id}
        )
        
        self.assertEqual(len(events), 1)
        self.assertEqual(events[0].channel, EngagementChannel.web_student)
        self.assertEqual(events[0].event_type, EngagementEventType.message_sent)
        self.assertEqual(events[0].workflow_id, self.workflow_id)
        self.assertEqual(events[0].metadata, {"test": "data"})
    
    def test_track_email_event(self):
        """Test tracking an email event"""
        # Track an email event
        self.tracker.track_email_event(
        user_id=self.user_id,
        email_id="test-email-123",
        event_type=EngagementEventType.email_opened,
        email_template_name="welcome_email",
        student_id=self.student_id,
        metadata={"tracking_pixel": True}
        )
        
        # Verify the event was tracked
        events = self.prisma.engagementevent.find_many(
        where={"student_id": self.student_id}
        )
        
        self.assertEqual(len(events), 1)
        self.assertEqual(events[0].channel, EngagementChannel.email)
        self.assertEqual(events[0].event_type, EngagementEventType.email_opened)
        self.assertEqual(events[0].email_id, "test-email-123")
        self.assertEqual(events[0].email_template_name, "welcome_email")
    
    def test_track_reminder_sent_and_engagement(self):
        """Test tracking reminder sent and subsequent engagement"""
        # Create a reminder template first
        template = self.prisma.remindertemplate.create(
            data={
                "name": "Test Template",
                "content": "Test reminder message",
                "tier": "Active"
            }
        )
        
        # Create a reminder record
        reminder = self.prisma.reminder.create(
            data={
                "student_id": self.student_id,
                "template_id": template.id,
                "status": "SENT",
                "final_message": "Test reminder message"
            }
        )
        
        # Track reminder sent
        self.tracker.track_reminder_sent(
            user_id=self.user_id,
            student_id=self.student_id,
            reminder_id=reminder.id,
            template_id=template.id,
            channel=EngagementChannel.sms
        )
        
        # Track reminder engagement within 24h
        within_24h = self.tracker.track_reminder_engagement(
            reminder_id=reminder.id,
            channel=EngagementChannel.sms,
            metadata={"response": "Yes, interested"}
        )
        
        self.assertTrue(within_24h)
        
        # Verify both events were tracked
        events = self.prisma.engagementevent.find_many(
            where={"reminder_id": reminder.id}
        )
        
        self.assertEqual(len(events), 2)
        # Sort events by creation time to check order
        events_sorted = sorted(events, key=lambda x: x.engaged_at)
        self.assertEqual(events_sorted[0].event_type, EngagementEventType.reminder_sent)
        self.assertEqual(events_sorted[1].event_type, EngagementEventType.reminder_engaged)
    
    def test_reminder_engagement_outside_24h(self):
        """Test reminder engagement tracking outside 24h window"""
        # Create a reminder template first
        template = self.prisma.remindertemplate.create(
            data={
                "name": "Old Test Template",
                "content": "Old test reminder message",
                "tier": "Active"
            }
        )
        
        # Create a reminder record
        reminder = self.prisma.reminder.create(
            data={
                "student_id": self.student_id,
                "template_id": template.id,
                "status": "SENT",
                "final_message": "Old test reminder message"
            }
        )
        
        # Create a reminder sent event from 25 hours ago
        old_time = datetime.utcnow() - timedelta(hours=25)
        self.prisma.engagementevent.create(
            data={
                "user_id": self.user_id,
                "student_id": self.student_id,
                "channel": EngagementChannel.sms,
                "event_type": EngagementEventType.reminder_sent,
                "reminder_id": reminder.id,
                "engaged_at": old_time
            }
        )
        
        # Track engagement now (outside 24h window)
        within_24h = self.tracker.track_reminder_engagement(
            reminder_id=reminder.id,
            channel=EngagementChannel.sms
        )
        
        self.assertFalse(within_24h)
    
    def test_get_student_engagement_stats(self):
        """Test getting student engagement statistics"""
        # Create multiple engagement events
        events_to_create = [
            {
                "channel": EngagementChannel.web_student,
                "event_type": EngagementEventType.message_sent,
                "engaged_at": datetime.utcnow() - timedelta(days=1)
            },
            {
                "channel": EngagementChannel.sms,
                "event_type": EngagementEventType.message_received,
                "engaged_at": datetime.utcnow() - timedelta(days=2)
            },
            {
                "channel": EngagementChannel.email,
                "event_type": EngagementEventType.email_opened,
                "engaged_at": datetime.utcnow() - timedelta(days=3)
            }
        ]
        
        for event_data in events_to_create:
            self.prisma.engagementevent.create(
                data={
                    "user_id": self.user_id,
                    "student_id": self.student_id,
                    **event_data
                }
            )
        
        # Get stats
        stats = self.tracker.get_student_engagement_stats(
            self.student_id, days=30
        )
        
        self.assertEqual(stats["total_events"], 3)
        self.assertEqual(stats["channels_used"], 3)
        self.assertEqual(stats["engagement_events"], 2)  # message_sent and email_opened


class TestEngagementTierService(BaseWorkflowTestCase):
    """Test the EngagementTierService"""
    
    def setUp(self):
        """Set up test environment"""
        super().setUp()
        self.tier_service = EngagementTierService(prisma=self.prisma)
    
    def test_calculate_tier_active(self):
        """Test calculating Active tier for recent activity"""
        # Create recent engagement event
        self.prisma.engagementevent.create(
            data={
                "user_id": self.user_id,
                "student_id": self.student_id,
                "channel": EngagementChannel.web_student,
                "event_type": EngagementEventType.message_sent,
                "engaged_at": datetime.utcnow() - timedelta(hours=2)
            }
        )
        
        tier = self.tier_service.calculate_tier(self.student_id)
        self.assertEqual(tier, EngagementTier.Active)
    
    def test_calculate_tier_at_risk(self):
        """Test calculating At-Risk tier for moderate inactivity"""
        # Create engagement event from 1.5 days ago
        self.prisma.engagementevent.create(
            data={
                "user_id": self.user_id,
                "student_id": self.student_id,
                "channel": EngagementChannel.sms,
                "event_type": EngagementEventType.message_received,
                "engaged_at": datetime.utcnow() - timedelta(days=1.5)
            }
        )
        
        tier = self.tier_service.calculate_tier(self.student_id)
        self.assertEqual(tier, EngagementTier.At_Risk)
    
    def test_calculate_tier_dormant(self):
        """Test calculating Dormant tier for long inactivity"""
        # Create old engagement event
        self.prisma.engagementevent.create(
            data={
                "user_id": self.user_id,
                "student_id": self.student_id,
                "channel": EngagementChannel.web_student,
                "event_type": EngagementEventType.message_sent,
                "engaged_at": datetime.utcnow() - timedelta(days=7)
            }
        )
        
        tier = self.tier_service.calculate_tier(self.student_id)
        self.assertEqual(tier, EngagementTier.Dormant)
    
    def test_update_student_tier(self):
        """Test updating student's engagement tier"""
        # Create recent engagement
        self.prisma.engagementevent.create(
            data={
                "user_id": self.user_id,
                "student_id": self.student_id,
                "channel": EngagementChannel.sms,
                "event_type": EngagementEventType.message_received,
                "engaged_at": datetime.utcnow() - timedelta(hours=12)
            }
        )
        
        # Update tier
        new_tier = self.tier_service.update_student_tier(self.student_id)
        self.assertEqual(new_tier, "Active")
        
        # Verify student record was updated
        student = self.prisma.student.find_unique(
            where={"id": self.student_id}
        )
        self.assertEqual(student.engagement_tier, EngagementTier.Active)
        self.assertIsNotNone(student.tier_updated_at)
        self.assertIsNotNone(student.last_activity_at)
    
    def test_get_tier_distribution(self):
        """Test getting tier distribution"""
        # Set engagement tier for our test student
        self.prisma.student.update(
            where={"id": self.student_id},
            data={"engagement_tier": EngagementTier.Active}
        )
        
        distribution = self.tier_service.get_tier_distribution()
        
        # Should have at least our test student
        self.assertGreaterEqual(distribution.get("Active", 0), 1)
        self.assertIn("At_Risk", distribution)
        self.assertIn("Dormant", distribution)


class TestAdminDashboardService(BaseWorkflowTestCase):
    """Test the AdminDashboardService"""
    
    def setUp(self):
        """Set up test environment"""
        super().setUp()
        self.dashboard_service = AdminDashboardService(prisma=self.prisma)
    
    def test_get_students_engagement_data(self):
        """Test getting paginated student engagement data"""
        # Set engagement tier and activity for our test student
        self.prisma.student.update(
            where={"id": self.student_id},
            data={
                "engagement_tier": EngagementTier.Active,
                "last_activity_at": datetime.utcnow() - timedelta(hours=2),
                "last_activity_channel": "web_student"
            }
        )
        
        # Create some messages
        from addie.lib import dict_to_json
        self.prisma.messages.create(
            data={
                "session_id": f"test-{self.student_id}",
                "message": dict_to_json({
                    "type": "human",
                    "content": "Hello, I need help with college applications"
                })
            }
        )
        
        result = self.dashboard_service.get_students_engagement_data(
            limit=10,
            offset=0
        )
        
        self.assertIn("students", result)
        self.assertIn("total", result)
        self.assertIn("has_more", result)
        
        # Find our test student in results
        test_student = None
        for student in result["students"]:
            if student["id"] == self.student_id:
                test_student = student
                break
        
        self.assertIsNotNone(test_student)
        self.assertEqual(test_student["engagement_tier"], "Active")
        self.assertEqual(test_student["last_activity_channel"], "web_student")
    
    def test_send_message_to_student(self):
        """Test sending message to student"""
        result = self.dashboard_service.send_message_to_student(
            student_id=self.student_id,
            message="Test message from admin",
            sent_by_user_id="admin-123"
        )
        
        self.assertTrue(result["success"])
        self.assertIn("message_id", result)
    
    def test_get_engagement_summary(self):
        """Test getting engagement summary"""
        # Set up test data
        self.prisma.student.update(
            where={"id": self.student_id},
            data={"engagement_tier": EngagementTier.Active}
        )
        
        # Create recent engagement event
        self.prisma.engagementevent.create(
            data={
                "user_id": self.user_id,
                "student_id": self.student_id,
                "channel": EngagementChannel.web_student,
                "event_type": EngagementEventType.message_sent,
                "engaged_at": datetime.utcnow() - timedelta(hours=2)
            }
        )
        
        summary = self.dashboard_service.get_engagement_summary()
        
        self.assertIn("tier_distribution", summary)
        self.assertIn("total_students", summary)
        self.assertIn("recent_activity_24h", summary)
        self.assertGreaterEqual(summary["recent_activity_24h"], 1)


class TestReminderTemplateService(BaseWorkflowTestCase):
    """Test the ReminderTemplateService"""
    
    def setUp(self):
        """Set up test environment"""
        super().setUp()
        self.template_service = ReminderTemplateService(self.prisma)
    
    def test_create_template(self):
        """Test creating a reminder template"""
        template_id = self.template_service.create_template(
        name="Test Template",
        content="Hello {name}, this is a test message!",
        tier="Active"
        )
        
        self.assertIsNotNone(template_id)
        
        # Verify template was created
        template = self.prisma.remindertemplate.find_unique(
        where={"id": template_id}
        )
        
        self.assertEqual(template.name, "Test Template")
        self.assertEqual(template.tier, "Active")
    
    def test_get_templates_by_tier(self):
        """Test getting templates by tier"""
        # Create templates for different tiers
        self.template_service.create_template(
        name="Active Template",
        content="Active student message",
        tier="Active"
        )
        
        self.template_service.create_template(
        name="At-Risk Template",
        content="At-risk student message",
        tier="At-Risk"
        )
        
        self.template_service.create_template(
        name="All Tiers Template",
        content="Message for all tiers",
        tier="All"
        )
        
        # Get templates for Active tier
        active_templates = self.template_service.get_templates_by_tier("Active")
        
        # Should include Active and All tier templates
        self.assertGreaterEqual(len(active_templates), 2)
        
        template_names = [t["name"] for t in active_templates]
        self.assertIn("Active Template", template_names)
        self.assertIn("All Tiers Template", template_names)
        self.assertNotIn("At-Risk Template", template_names)
    
    def test_create_default_templates(self):
        """Test creating default templates"""
        # Delete any existing default templates first to test creation
        default_names = [
            "Active Student Follow-up",
            "At-Risk Check-in", 
            "Dormant Re-engagement",
            "General Reminder"
        ]
        
        for name in default_names:
            try:
                existing = self.prisma.remindertemplate.find_first(
                    where={'name': name}
                )
                if existing:
                    self.prisma.remindertemplate.delete(where={'id': existing.id})
            except:
                pass
        
        created_ids = self.template_service.create_default_templates()
        
        # Should have created 4 default templates
        self.assertEqual(len(created_ids), 4)
        
        # Verify templates were created
        templates = self.prisma.remindertemplate.find_many()
        
        self.assertGreaterEqual(len(templates), len(created_ids))
        
        # Check that we have templates for each tier
        tiers = set(t.tier for t in templates if t.tier)
        self.assertIn("Active", tiers)
        self.assertIn("At-Risk", tiers)
        self.assertIn("Dormant", tiers)
        self.assertIn("All", tiers)
    
    def test_update_template(self):
        """Test updating a template"""
        # Create a template
        template_id = self.template_service.create_template(
        name="Original Template",
        content="Original content",
        tier="Active"
        )
        
        # Update it
        success = self.template_service.update_template(
        template_id=template_id,
        name="Updated Template",
        content="Updated content",
        tier="At-Risk"
        )
        
        self.assertTrue(success)
        
        # Verify updates
        template = self.prisma.remindertemplate.find_unique(
        where={"id": template_id}
        )
        
        self.assertEqual(template.name, "Updated Template")
        self.assertEqual(template.content, "Updated content")
        self.assertEqual(template.tier, "At-Risk")
    
    def test_delete_template(self):
        """Test deleting a template"""
        # Create a template
        template_id = self.template_service.create_template(
        name="Template to Delete",
        content="This will be deleted",
        tier="Active"
        )
        
        # Delete it
        success = self.template_service.delete_template(template_id)
        self.assertTrue(success)
        
        # Verify it's been deleted
        template = self.prisma.remindertemplate.find_unique(
        where={"id": template_id}
        )
        
        self.assertIsNone(template)
    
    def test_get_template_usage_stats(self):
        """Test getting template usage statistics"""
        # Create templates of different tiers
        self.template_service.create_template(
        name="Active Followup",
        content="Content",
        tier="Active"
        )
        
        self.template_service.create_template(
        name="At-Risk Engagement",
        content="Content",
        tier="At-Risk"
        )
        
        stats = self.template_service.get_template_usage_stats()
        
        self.assertIn("total_templates", stats)
        self.assertIn("by_tier", stats)
        
        self.assertGreaterEqual(stats["total_templates"], 2)
        self.assertGreaterEqual(stats["by_tier"]["Active"], 1)
        self.assertGreaterEqual(stats["by_tier"]["At-Risk"], 1)


class TestEngagementIntegration(BaseWorkflowTestCase):
    """Integration tests for the complete engagement tracking system"""
    
    def setUp(self):
        """Set up test environment"""
        super().setUp()
        self.tracker = EngagementTracker(self.prisma)
        self.tier_service = EngagementTierService(prisma=self.prisma)
        self.dashboard_service = AdminDashboardService(prisma=self.prisma)
        self.template_service = ReminderTemplateService(self.prisma)
    
    def test_full_engagement_flow(self):
        """Test complete engagement tracking and tier calculation flow"""
        # 1. Track initial student message
        self.tracker.track_message_event(
            user_id=self.user_id,
            student_id=self.student_id,
            session_id="integration-test",
            channel=EngagementChannel.web_student,
            event_type=EngagementEventType.message_sent,
            workflow_id=self.workflow_id,
            metadata={"test": "data"}
        )
        
        # 2. Update student tier based on activity
        new_tier = self.tier_service.update_student_tier(self.student_id)
        self.assertEqual(new_tier, "Active")
        
        # 3. Get student data through dashboard service
        dashboard_data = self.dashboard_service.get_students_engagement_data(
            limit=10
        )
        
        # Find our test student
        test_student = None
        for student in dashboard_data["students"]:
            if student["id"] == self.student_id:
                test_student = student
                break
        
        self.assertIsNotNone(test_student)
        self.assertEqual(test_student["engagement_tier"], "Active")
        
        # 4. Get appropriate templates for the student's tier  
        templates = self.dashboard_service.get_message_templates(self.student_id)
        
        # Should have some templates (after creating defaults)
        self.template_service.create_default_templates()
        templates = self.dashboard_service.get_message_templates(self.student_id)
        self.assertGreater(len(templates), 0)
        
        # 5. Simulate student becoming dormant
        # Update engagement event to be old
        self.prisma.engagementevent.update_many(
            where={"student_id": self.student_id},
            data={"engaged_at": datetime.utcnow() - timedelta(days=7)}
        )
        
        # Recalculate tier
        new_tier = self.tier_service.update_student_tier(self.student_id, force_recalculate=True)
        self.assertEqual(new_tier, "Dormant")
        
        # 6. Verify tier distribution includes our student
        distribution = self.tier_service.get_tier_distribution()
        self.assertGreaterEqual(distribution.get("Dormant", 0), 1)
    
    def test_reminder_effectiveness_tracking(self):
        """Test end-to-end reminder effectiveness tracking"""
        # These will be set after creating the template and reminder
        
        # 1. Create and send a reminder
        template = self.prisma.remindertemplate.create(
            data={
                "name": "Integration Reminder",
                "content": "Don't forget about your college application deadline!",
                "tier": "At-Risk"
            }
        )
        template_id = template.id
        
        # Create a reminder record
        reminder = self.prisma.reminder.create(
            data={
                "student_id": self.student_id,
                "template_id": template_id,
                "status": "SENT",
                "final_message": "Don't forget about your college application deadline!"
            }
        )
        reminder_id = reminder.id
        
        # 2. Track reminder sent
        self.tracker.track_reminder_sent(
            user_id=self.user_id,
            student_id=self.student_id,
            reminder_id=reminder_id,
            template_id=template_id,
            channel=EngagementChannel.sms
        )
        
        # 3. Simulate student engaging with reminder
        within_24h = self.tracker.track_reminder_engagement(
            reminder_id=reminder_id,
            channel=EngagementChannel.web_student,
            metadata={"source": "reminder_link"}
        )
        
        self.assertTrue(within_24h)
        
        # 4. Verify student tier is updated to Active
        new_tier = self.tier_service.update_student_tier(self.student_id)
        self.assertEqual(new_tier, "Active")
        
        # 5. Check engagement summary reflects the activity
        summary = self.dashboard_service.get_engagement_summary()
        self.assertGreaterEqual(summary["recent_activity_24h"], 2)  # reminder sent + engagement


if __name__ == '__main__':
    unittest.main()