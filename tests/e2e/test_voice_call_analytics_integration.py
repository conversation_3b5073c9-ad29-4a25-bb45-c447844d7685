"""
E2E Integration Tests for Voice Call Analytics Pipeline

Tests the complete flow: Voice Call → CallRecord → PhoneNumberUsage → Analytics Display
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any, List

from addie.lib import prisma_client
from addie.services.phone_number_service import PhoneNumberService
from addie.voice_agent.models import CallRecord, CallStatus
from addie.voice_agent.twilio_client import TwilioVoiceClient
from addie.tools.voice_call_tool import VoiceCallTool
from prisma.enums import WorkflowType, StudentWorkflowMode, StudentWorkflowStatus


class TestVoiceCallAnalyticsIntegration:
    """Test suite for voice call analytics integration."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        self.prisma = prisma_client()
        if not self.prisma.is_connected():
            self.prisma.connect()
        
        # Clean up test data
        self.cleanup_test_data()
        
        yield
        
        # Clean up after test
        self.cleanup_test_data()

    def cleanup_test_data(self):
        """Clean up test data."""
        try:
            # Delete test call records
            self.prisma.callrecord.delete_many(
                where={'twilio_call_sid': {'startswith': 'test_'}}
            )
            
            # Delete test phone number usage records
            test_phone_records = self.prisma.twiliophonenumber.find_many(
                where={'phone_number': {'startswith': '+1555TEST'}}
            )
            for record in test_phone_records:
                self.prisma.phonenumberusage.delete_many(
                    where={'phone_number_id': record.id}
                )
            
            # Delete test phone numbers
            self.prisma.twiliophonenumber.delete_many(
                where={'phone_number': {'startswith': '+1555TEST'}}
            )
            
            # Delete test student workflows and students
            self.prisma.studentworkflow.delete_many(
                where={'id': {'startswith': 'test_'}}
            )
            self.prisma.student.delete_many(
                where={'id': {'startswith': 'test_'}}
            )
            self.prisma.user.delete_many(
                where={'phone_number': {'startswith': '+1555TEST'}}
            )
            self.prisma.workflow.delete_many(
                where={'id': {'startswith': 'test_'}}
            )
        except Exception as e:
            print(f"Cleanup error: {e}")

    def create_test_phone_number(self, phone_number: str = "+1555TEST001") -> str:
        """Create a test phone number in the database."""
        phone_record = self.prisma.twiliophonenumber.create(
            data={
                'phone_number': phone_number,
                'twilio_sid': f'PN{phone_number[-10:]}',
                'country_code': '+1',
                'country_name': 'United States',
                'is_active': True,
                'capabilities': '{"sms": true, "voice": true, "mms": false}',
                'twilio_verified': True,
                'priority': 10,
                'environment': 'test'
            }
        )
        return phone_record.id

    def create_test_student_and_workflow(self) -> tuple[str, str, str]:
        """Create test student, user, and workflow."""
        # Create test user
        user = self.prisma.user.create(
            data={
                'first_name': 'Test',
                'last_name': 'Student',
                'email': '<EMAIL>',
                'phone_number': '+1555TEST123',
                'role': 'STUDENT',
                'enabled': True
            }
        )
        
        # Create test workflow
        workflow = self.prisma.workflow.create(
            data={
                'id': 'test_workflow_001',
                'name': 'Test Voice Workflow',
                'workflow_type': WorkflowType.UNSTRUCTURED,
                'owner_id': user.id
            }
        )
        
        # Create test student
        student = self.prisma.student.create(
            data={
                'id': 'test_student_001',
                'school_id': 'test_school'
            }
        )
        
        # Link student to user
        self.prisma.student.update(
            where={'id': student.id},
            data={'users': {'connect': [{'id': user.id}]}}
        )
        
        # Create student workflow
        student_workflow = self.prisma.studentworkflow.create(
            data={
                'id': 'test_sw_001',
                'student_id': student.id,
                'workflow_id': workflow.id,
                'mode': StudentWorkflowMode.text,
                'status': StudentWorkflowStatus.NOT_STARTED
            }
        )
        
        return student.id, student_workflow.id, user.id

    def create_test_call_record(
        self, 
        student_id: str, 
        student_workflow_id: str,
        call_sid: str = "test_CA123456789",
        status: CallStatus = CallStatus.COMPLETED,
        duration: int = 120
    ) -> str:
        """Create a test call record."""
        call_record = self.prisma.callrecord.create(
            data={
                'student_id': student_id,
                'student_workflow_id': student_workflow_id,
                'twilio_call_sid': call_sid,
                'status': status.value,
                'start_time': datetime.now() - timedelta(minutes=5),
                'end_time': datetime.now() - timedelta(minutes=3),
                'duration': duration,
                'transcript': 'Test call transcript',
                'retry_count': 0
            }
        )
        return call_record.id

    def create_test_phone_usage(self, phone_number_id: str, voice_count: int = 1) -> str:
        """Create test phone number usage record."""
        usage_record = self.prisma.phonenumberusage.create(
            data={
                'phone_number_id': phone_number_id,
                'date': datetime.now().replace(hour=0, minute=0, second=0, microsecond=0),
                'sms_count': 0,
                'voice_count': voice_count,
                'success_count': voice_count,
                'failure_count': 0
            }
        )
        return usage_record.id

    @pytest.mark.asyncio
    async def test_call_record_shows_in_analytics(self):
        """Test that call records appear in analytics with correct data."""
        # Setup test data
        phone_number_id = self.create_test_phone_number("+1555TEST001")
        student_id, student_workflow_id, user_id = self.create_test_student_and_workflow()
        
        # Create call record
        call_record_id = self.create_test_call_record(
            student_id=student_id,
            student_workflow_id=student_workflow_id,
            call_sid="test_CA_analytics_001",
            status=CallStatus.COMPLETED,
            duration=180  # 3 minutes
        )
        
        # Create phone usage record
        usage_record_id = self.create_test_phone_usage(phone_number_id, voice_count=1)
        
        # Test analytics service
        phone_service = PhoneNumberService()
        usage_stats = phone_service.get_usage_statistics(days=1)
        
        # Verify analytics include call data
        assert len(usage_stats) >= 1
        
        # Find our test phone number in stats
        test_stats = None
        for stat in usage_stats:
            if stat['phone_number'] == '+1555TEST001':
                test_stats = stat
                break
        
        assert test_stats is not None, "Test phone number not found in analytics"
        
        # Verify basic stats
        assert test_stats['total_voice'] >= 1
        assert test_stats['total_success'] >= 1
        
        # Verify enhanced call analytics
        assert 'total_call_duration' in test_stats
        assert 'avg_call_duration' in test_stats
        assert 'total_answered_calls' in test_stats
        assert 'call_success_rate' in test_stats
        assert 'recent_calls' in test_stats
        
        # Verify call duration (should be 3 minutes = 180 seconds)
        assert test_stats['total_call_duration'] == 3.0  # 180 seconds / 60 = 3 minutes
        assert test_stats['avg_call_duration'] == 3.0
        
        # Verify call counts
        assert test_stats['total_answered_calls'] >= 1
        assert test_stats['call_success_rate'] > 0
        
        # Verify recent calls
        assert len(test_stats['recent_calls']) >= 1
        recent_call = test_stats['recent_calls'][0]
        assert recent_call['call_sid'] == "test_CA_analytics_001"
        assert recent_call['status'] == "COMPLETED"
        assert recent_call['duration'] == 180
        assert recent_call['student_id'] == student_id

    @pytest.mark.asyncio
    async def test_failed_call_analytics(self):
        """Test that failed calls are properly tracked in analytics."""
        # Setup test data
        phone_number_id = self.create_test_phone_number("+1555TEST002")
        student_id, student_workflow_id, user_id = self.create_test_student_and_workflow()
        
        # Create failed call record
        call_record_id = self.create_test_call_record(
            student_id=student_id,
            student_workflow_id=student_workflow_id,
            call_sid="test_CA_failed_001",
            status=CallStatus.FAILED,
            duration=0
        )
        
        # Create phone usage record (with failure)
        usage_record = self.prisma.phonenumberusage.create(
            data={
                'phone_number_id': phone_number_id,
                'date': datetime.now().replace(hour=0, minute=0, second=0, microsecond=0),
                'sms_count': 0,
                'voice_count': 1,
                'success_count': 0,
                'failure_count': 1
            }
        )
        
        # Test analytics
        phone_service = PhoneNumberService()
        usage_stats = phone_service.get_usage_statistics(days=1)
        
        # Find our test phone number
        test_stats = None
        for stat in usage_stats:
            if stat['phone_number'] == '+1555TEST002':
                test_stats = stat
                break
        
        assert test_stats is not None
        
        # Verify failure tracking
        assert test_stats['total_failure'] >= 1
        assert test_stats['total_failed_calls'] >= 1
        assert test_stats['call_success_rate'] == 0.0  # No successful calls
        
        # Verify failed call in recent calls
        assert len(test_stats['recent_calls']) >= 1
        recent_call = test_stats['recent_calls'][0]
        assert recent_call['status'] == "FAILED"
        assert recent_call['duration'] == 0

    @pytest.mark.asyncio
    async def test_multiple_calls_load_balancing_analytics(self):
        """Test analytics for multiple calls across different phone numbers."""
        # Setup multiple test phone numbers
        phone_id_1 = self.create_test_phone_number("+1555TEST101")
        phone_id_2 = self.create_test_phone_number("+1555TEST102")
        
        student_id, student_workflow_id, user_id = self.create_test_student_and_workflow()
        
        # Create multiple call records
        for i in range(3):
            self.create_test_call_record(
                student_id=student_id,
                student_workflow_id=student_workflow_id,
                call_sid=f"test_CA_multi_{i:03d}",
                status=CallStatus.COMPLETED,
                duration=60 + (i * 30)  # Varying durations
            )
        
        # Create usage records for both phone numbers
        self.create_test_phone_usage(phone_id_1, voice_count=2)
        self.create_test_phone_usage(phone_id_2, voice_count=1)
        
        # Test analytics
        phone_service = PhoneNumberService()
        usage_stats = phone_service.get_usage_statistics(days=1)
        
        # Verify both phone numbers appear in analytics
        test_phones = [stat for stat in usage_stats if stat['phone_number'].startswith('+1555TEST1')]
        assert len(test_phones) >= 2
        
        # Verify total voice calls are tracked
        total_voice_calls = sum(stat['total_voice'] for stat in test_phones)
        assert total_voice_calls >= 3

    @pytest.mark.asyncio
    async def test_analytics_api_endpoint_integration(self):
        """Test the full API endpoint integration with enhanced analytics."""
        from addie.api.admin.phone_numbers import get_usage_statistics
        from unittest.mock import Mock
        
        # Setup test data
        phone_number_id = self.create_test_phone_number("+1555TEST003")
        student_id, student_workflow_id, user_id = self.create_test_student_and_workflow()
        
        # Create comprehensive test data
        call_record_id = self.create_test_call_record(
            student_id=student_id,
            student_workflow_id=student_workflow_id,
            call_sid="test_CA_api_001",
            status=CallStatus.COMPLETED,
            duration=240  # 4 minutes
        )
        
        usage_record_id = self.create_test_phone_usage(phone_number_id, voice_count=1)
        
        # Test API endpoint
        response = await get_usage_statistics(days=1)
        
        # Verify response structure
        assert isinstance(response, list)
        assert len(response) >= 1
        
        # Find our test phone number in response
        test_response = None
        for item in response:
            if item.phone_number == '+1555TEST003':
                test_response = item
                break
        
        assert test_response is not None
        
        # Verify enhanced fields are present
        assert hasattr(test_response, 'total_call_duration')
        assert hasattr(test_response, 'avg_call_duration')
        assert hasattr(test_response, 'call_success_rate')
        assert hasattr(test_response, 'recent_calls')
        
        # Verify values
        assert test_response.total_call_duration == 4.0  # 4 minutes
        assert test_response.avg_call_duration == 4.0
        assert test_response.call_success_rate > 0
        assert len(test_response.recent_calls) >= 1
        
        # Verify recent call structure
        recent_call = test_response.recent_calls[0]
        assert hasattr(recent_call, 'call_sid')
        assert hasattr(recent_call, 'status')
        assert hasattr(recent_call, 'duration')
        assert hasattr(recent_call, 'start_time')
        assert hasattr(recent_call, 'student_id')

    @pytest.mark.asyncio
    async def test_voice_call_tool_integration(self):
        """Test voice call tool creates records that appear in analytics."""
        # Setup test data
        phone_number_id = self.create_test_phone_number("+1555TEST004")
        student_id, student_workflow_id, user_id = self.create_test_student_and_workflow()
        
        # Mock Twilio client to avoid actual calls
        with patch('addie.voice_agent.twilio_client.TwilioVoiceClient') as mock_client:
            mock_instance = Mock()
            mock_instance.initiate_call.return_value = {
                'success': True,
                'call_sid': 'test_CA_tool_001'
            }
            mock_client.return_value = mock_instance
            
            # Mock voice handler
            with patch('addie.voice_agent.websocket_handler.get_voice_handler') as mock_handler:
                mock_handler_instance = Mock()
                mock_handler_instance.initiate_call = AsyncMock(return_value={
                    'success': True,
                    'call_sid': 'test_CA_tool_001'
                })
                mock_handler.return_value = mock_handler_instance
                
                # Test voice call tool
                voice_tool = VoiceCallTool()
                
                # This would normally create a CallRecord
                # For this test, we'll create it manually to simulate the flow
                call_record_id = self.create_test_call_record(
                    student_id=student_id,
                    student_workflow_id=student_workflow_id,
                    call_sid="test_CA_tool_001",
                    status=CallStatus.COMPLETED,
                    duration=150
                )
                
                # Track usage (this would normally happen in TwilioVoiceClient)
                phone_service = PhoneNumberService()
                phone_service.track_phone_number_usage("+1555TEST004", 'voice', success=True)
                
                # Verify analytics include the call
                usage_stats = phone_service.get_usage_statistics(days=1)
                
                test_stats = None
                for stat in usage_stats:
                    if stat['phone_number'] == '+1555TEST004':
                        test_stats = stat
                        break
                
                assert test_stats is not None
                assert test_stats['total_voice'] >= 1
                assert test_stats['total_call_duration'] == 2.5  # 150 seconds = 2.5 minutes

    @pytest.mark.asyncio
    async def test_analytics_date_range_filtering(self):
        """Test analytics properly filter by date range."""
        # Setup test data
        phone_number_id = self.create_test_phone_number("+1555TEST005")
        student_id, student_workflow_id, user_id = self.create_test_student_and_workflow()
        
        # Create old call record (beyond date range)
        old_call = self.prisma.callrecord.create(
            data={
                'student_id': student_id,
                'student_workflow_id': student_workflow_id,
                'twilio_call_sid': 'test_CA_old_001',
                'status': CallStatus.COMPLETED.value,
                'start_time': datetime.now() - timedelta(days=10),  # 10 days ago
                'end_time': datetime.now() - timedelta(days=10),
                'duration': 120
            }
        )
        
        # Create recent call record
        recent_call_id = self.create_test_call_record(
            student_id=student_id,
            student_workflow_id=student_workflow_id,
            call_sid="test_CA_recent_001",
            status=CallStatus.COMPLETED,
            duration=180
        )
        
        # Create usage record for today
        usage_record_id = self.create_test_phone_usage(phone_number_id, voice_count=1)
        
        # Test analytics with 7-day range (should only include recent call)
        phone_service = PhoneNumberService()
        usage_stats = phone_service.get_usage_statistics(days=7)
        
        test_stats = None
        for stat in usage_stats:
            if stat['phone_number'] == '+1555TEST005':
                test_stats = stat
                break
        
        assert test_stats is not None
        
        # Should only include recent call, not old one
        assert len(test_stats['recent_calls']) == 1
        assert test_stats['recent_calls'][0]['call_sid'] == 'test_CA_recent_001'
        
        # Test with 14-day range (should include both calls)
        usage_stats_14 = phone_service.get_usage_statistics(days=14)
        
        test_stats_14 = None
        for stat in usage_stats_14:
            if stat['phone_number'] == '+1555TEST005':
                test_stats_14 = stat
                break
        
        # Should now include both calls in duration calculation
        # But recent_calls still limited to 5 most recent
        assert test_stats_14 is not None
        assert len(test_stats_14['recent_calls']) <= 5


if __name__ == "__main__":
    pytest.main([__file__, "-v"])