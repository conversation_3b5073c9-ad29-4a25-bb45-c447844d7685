# 🧪 Testing Guide: Understanding TestCase with Existing Test Utils

## 📚 **What is TestCase?**

`unittest.TestCase` is Python's standard library testing framework that provides structured testing methods. In our project, we use **existing test utilities** from `tests/utils/` to make testing more efficient.

### **Why use TestCase with our existing test utils?**

| Simple Functions | TestCase + Our Utils | Advantages |
|-----------------|---------------------|------------|
| `def test_something():` | `class TestSomething(BaseWorkflowTestCase):` | Structured organization + shared setup |
| Manual print results | Automatic assertions and reporting | Standardized validation |
| Manual cleanup | Automatic setup/teardown + proper DB cleanup | Reliable environment management |
| Hard to run in batches | Automatic test discovery + parallel execution | Easy CI/CD integration |

## 🏗️ **Our Test Infrastructure**

### **Base Classes Available:**
- `BaseWorkflowTestCase` - General workflow testing with user/student setup
- `UnstructuredWorkflowTestCase` - Specialized for unstructured workflows
- `StructuredWorkflowTestCase` - Specialized for structured workflows

## 🏗️ **TestCase Structure with Our Utils**

### **1. Using BaseWorkflowTestCase**
```python
from tests.utils.base_test_cases import BaseWorkflowTestCase

class TestDisableSystemPrompt(BaseWorkflowTestCase):
    """Test class - inherits workflow setup and cleanup"""
```

### **2. Automatic Setup and Teardown**
```python
def setUp(self):
    """Run before each test method"""
    # Call parent setup - creates workflow, user, student automatically
    super().setUp()

    # Your specific test setup
    self.student_context = {
        "workflow_id": self.workflow_id,  # Available from parent
        "mode": "web",
        "current_question": {"step_id": "test_step_123"}
    }

# tearDown() is handled automatically by BaseWorkflowTestCase
# - Cleans up messages, question responses, student workflows
# - Deletes students, users, workflow steps, workflows
# - Handles foreign key constraints properly
```

### **3. What You Get Automatically**
```python
# Available after super().setUp():
self.prisma          # Connected Prisma client
self.workflow_id     # Created test workflow ID
self.workflow        # Workflow object
self.user_id         # Created test user ID
self.student_id      # Created test student ID
self.test_phone      # Unique test phone number
```

### **4. Test Methods**
```python
def test_something_specific(self):
    """Test method - must start with test_"""
    # Arrange - test data already prepared by BaseWorkflowTestCase
    # Act - execute the functionality being tested
    result = gen_system_prompt(self.student_context)

    # Assert - verify results using unittest assertions
    self.assertGreater(len(result), 0)
```

### **4. Assertion Methods**
```python
self.assertEqual(a, b)          # a == b
self.assertNotEqual(a, b)       # a != b
self.assertTrue(x)              # x is True
self.assertFalse(x)             # x is False
self.assertIsNone(x)            # x is None
self.assertIsNotNone(x)         # x is not None
self.assertIn(a, b)             # a in b
self.assertNotIn(a, b)          # a not in b
self.assertGreater(a, b)        # a > b
self.assertLess(a, b)           # a < b
```

## 🚀 **How to Run Tests**

### **Method 1: Using pytest (Recommended)**
```bash
# Run single test file
python -m pytest tests/test_disable_system_prompt.py -v

# Run all tests
python -m pytest tests/ -v

# Run specific test method
python -m pytest tests/test_disable_system_prompt.py::TestDisableSystemPrompt::test_system_prompt_enabled_generates_longer_prompt -v
```

### **Method 2: Using unittest**
```bash
# Run single test file
python tests/test_disable_system_prompt.py

# Run specific test class
python -m unittest tests.test_disable_system_prompt.TestDisableSystemPrompt -v

# Run specific test method
python -m unittest tests.test_disable_system_prompt.TestDisableSystemPrompt.test_system_prompt_enabled_generates_longer_prompt -v
```

### **Method 3: Test Discovery**
```bash
# Automatically discover and run all tests
python -m unittest discover tests/ -v
```

## 📁 **Our Test Files**

### **1. `test_disable_system_prompt.py`**
- **Purpose**: Test core disable_system_prompt functionality
- **Base Class**: `BaseWorkflowTestCase`
- **Test Coverage**:
  - System prompt enable/disable
  - Prompt length changes
  - Content difference verification
  - Data persistence

### **2. `test_counsellor_web_disable_system_prompt.py`**
- **Purpose**: Test counsellor_web specific functionality
- **Base Class**: `BaseWorkflowTestCase`
- **Test Coverage**:
  - Backend API compatibility
  - Data structure support
  - Multi-mode support (web/sms/voice)

### **3. `setup_system_prompt.py`**
- **Purpose**: Test system prompt configuration
- **Base Class**: `BaseWorkflowTestCase`
- **Test Coverage**:
  - AddieConfig existence
  - System prompt creation
  - Configuration verification

## 🎯 **Testing Best Practices**

### **1. AAA Pattern**
```python
def test_something(self):
    # Arrange - prepare
    workflow_id = "test_123"

    # Act - execute
    result = gen_system_prompt(context)

    # Assert - verify
    self.assertGreater(len(result), 0)
```

### **2. Descriptive Test Names**
```python
def test_system_prompt_disabled_generates_shorter_prompt(self):
    """Test that disabling system prompt generates shorter prompts"""
```

### **3. Independent Tests**
- Each test should run independently
- Don't depend on other test results
- Use setUp/tearDown to ensure clean environment

### **4. Meaningful Assertions**
```python
# Good assertion
self.assertLess(len(prompt_disabled), len(prompt_enabled))

# Poor assertion
self.assertTrue(len(prompt_disabled) < len(prompt_enabled))
```

## 🔧 **Debugging Tests**

### **View Detailed Output**
```bash
python -m pytest tests/test_disable_system_prompt.py -v -s
```

### **Run Specific Test for Debugging**
```bash
python -m pytest tests/test_disable_system_prompt.py::TestDisableSystemPrompt::test_system_prompt_enabled_generates_longer_prompt -v -s
```

### **Add Debug Information in Tests**
```python
def test_something(self):
    result = some_function()
    print(f"Debug: result = {result}")  # Use -s flag to see this
    self.assertEqual(result, expected)
```

## 📊 **Test Reports**

After running tests, you'll see output like this:
```
test_system_prompt_disabled_generates_shorter_prompt ... ok
test_system_prompt_enabled_generates_longer_prompt ... ok
test_prompts_are_different_when_disabled ... ok

----------------------------------------------------------------------
Ran 3 tests in 2.345s

OK
```

## 🎉 **Summary**

TestCase provides:
- **Structure** - Clear test organization
- **Automation** - Automatic execution and reporting
- **Reliability** - Standardized assertions and environment management
- **Maintainability** - Easy to modify and extend
- **CI/CD Friendly** - Easy integration into automation pipelines

This is why your senior recommended using TestCase instead of simple print functions!
