import alog
from addie.data_model.gen_questionnaire_workflow import GeneratedQuestionnaireWorkflow
from addie.lib import prisma_client
from typing import List, Dict, Any, Optional


def generate_questionnaire_workflow(
    user_id: str,
    title: str,
    prompt_str: str,
    questions: List[Dict[str, Any]],
    clean_existing: bool = True
) -> GeneratedQuestionnaireWorkflow:
    """Generate a questionnaire workflow with the provided parameters.
    
    Args:
        user_id: The ID of the user creating the questionnaire
        title: The title of the questionnaire
        prompt_str: The prompt string for generating the questionnaire
        questions: A list of question dictionaries with fields like index, question, type, etc.
        clean_existing: Whether to delete existing questionnaires with the same name
        
    Returns:
        The generated questionnaire workflow object
    """
    prisma = prisma_client()
    
    # Clean up existing questionnaires and workflows with the same name if requested
    if clean_existing:
        prisma.questionnaire.delete_many(where=dict(name=title))
        prisma.workflow.delete_many(where=dict(name=title))
    
    # Generate the questionnaire workflow
    qnaire = GeneratedQuestionnaireWorkflow.generate_from_prompt_str(
        title=title, 
        prompt_str=prompt_str, 
        questions=questions, 
        user_id=user_id
    )
    
    return qnaire


def reload_questionnaire(qnaire_id: str, user_id: str) -> GeneratedQuestionnaireWorkflow:
    """Reload a questionnaire from its ID.
    
    Args:
        qnaire_id: The ID of the questionnaire to reload
        user_id: The ID of the user who owns the questionnaire
        
    Returns:
        The reloaded questionnaire workflow object
    """
    return GeneratedQuestionnaireWorkflow(id=qnaire_id, user_id=user_id)


def validate_question_content(qnaire: GeneratedQuestionnaireWorkflow, questions: List[Dict[str, Any]]):
    """Validate that the questions in the questionnaire match the expected content.
    
    Args:
        qnaire: The questionnaire workflow object to validate
        questions: The list of expected question dictionaries
    """
    for q in qnaire.question_records:
        assert q.question == questions[q.index]["question"], \
            f"Question content mismatch at index {q.index}"


def validate_workflow_steps(qnaire: GeneratedQuestionnaireWorkflow, reloaded_qnaire: GeneratedQuestionnaireWorkflow):
    """Validate that the workflow steps are correctly created and linked.
    
    Args:
        qnaire: The original questionnaire workflow object
        reloaded_qnaire: The reloaded questionnaire workflow object
    """
    workflow_id = reloaded_qnaire.workflow_with_steps.id
    assert workflow_id, "Workflow ID should exist"
    
    # Validate question IDs and workflow steps
    for q in reloaded_qnaire.question_records:
        # Verify question IDs match between original and reloaded questionnaire
        assert q.id == qnaire.question_records[q.index].id, \
            f"Question ID mismatch at index {q.index}"

        # Verify workflow step exists and is linked to the workflow
        # Find step by checking if the data contains the question ID
        wf_step = prisma_client().workflowstep.find_first(
            where=dict(
                parent_workflow_id=workflow_id,
                index=q.index
            )
        )
        
        assert wf_step, f"Workflow step not found for question index {q.index} with hash {q.question_hash}"
        assert wf_step.parent_workflow_id == workflow_id, \
            f"Workflow step has incorrect parent workflow ID"
        
        # Verify that the workflow step data contains the question ID
        import json
        step_data = json.loads(wf_step.data) if isinstance(wf_step.data, str) else wf_step.data
        assert step_data.get("questionId") == q.id, \
            f"Workflow step data doesn't contain correct question ID"


def validate_deletion(qnaire: GeneratedQuestionnaireWorkflow, questions: List[Dict[str, Any]]):
    """Validate that the questionnaire is properly deleted.
    
    Args:
        qnaire: The questionnaire workflow object that was deleted
        questions: The list of question dictionaries to verify count
    """
    # Verify question count matches
    assert len(qnaire.question_records) == len(questions), \
        f"Expected {len(questions)} questions, found {len(qnaire.question_records)}"
    
    # Verify questions no longer exist in the database
    for q in qnaire.question_records:
        ques = prisma_client().question.find_first(where=dict(id=q.id))
        assert not ques, f"Question with ID {q.id} still exists after deletion"


def get_college_application_questions() -> List[Dict[str, Any]]:
    """Get a predefined list of college application questions.
    
    Returns:
        A list of question dictionaries for college applications
    """
    return [
        {
            "index": 0,
            "question": "What factors are most important to you when choosing a college "
            "to apply to in 2025?",
            "type": "Question",
            "can_skip": False,
        },
        {
            "index": 1,
            "question": "Are you considering applying to colleges in different states or "
            "regions?",
            "type": "BinaryQuestion",
            "can_skip": False,
        },
        {
            "index": 2,
            "options": [
                "Science and Technology",
                "Arts and Humanities",
                "Business and Economics",
                "Social Sciences",
            ],
            "question": "Which field of study do you plan to pursue in college?",
            "type": "MultipleChoiceQuestion",
            "can_skip": False,
        },
        {
            "index": 3,
            "question": "How do you plan to research and gather information about "
            "potential colleges to apply to?",
            "type": "Question",
            "can_skip": False,
        },
    ]


def test_generate_qnaire():
    """Test the generation, validation, and deletion of a questionnaire workflow."""
    # Get <NAME_EMAIL>
    user = prisma_client().user.find_unique_or_raise(
        where=dict(email="<EMAIL>")
    )

    prompt = """Generate an open ended questionnaire about which colleges 
    the student intends to apply to in 2025."""
    title = "Student College Application Plans"
    questions = get_college_application_questions()

    # Generate the questionnaire workflow
    qnaire = generate_questionnaire_workflow(
        user_id=user.id,
        title=title,
        prompt_str=prompt,
        questions=questions
    )

    # Validate question content
    validate_question_content(qnaire, questions)

    alog.info('### getting ready to reload the qnaire ###')

    # Reload the questionnaire from ID
    reloaded_qnaire = reload_questionnaire(qnaire.id, user.id)

    # Validate workflow steps
    validate_workflow_steps(qnaire, reloaded_qnaire)

    # Delete the questionnaire
    qnaire.delete()

    # Validate deletion
    validate_deletion(reloaded_qnaire, questions)
