#!/usr/bin/env python
"""
Tests for OCEAN Scores Calculation System

This module tests the complete OCEAN scores functionality including:
- Service layer calculations
- Database operations
- API endpoints
- Workflow integration
"""

import pytest
import asyncio
from typing import Dict, List
from addie.services.ocean_scores import OceanScoresService
from addie.lib import prisma_client
import alog


class TestOceanScoresService:
    """Test the OCEAN scores service functionality."""
    
    def __init__(self):
        self.service = OceanScoresService()
        self.prisma = prisma_client()
        
        # Test data - using provided IDs
        self.test_student_id = "cmdpua1oj00037006fbztn051"
        self.test_workflow_id = "cmdptstly0000oc8fvqxfhdwl"
    
    def test_student_and_workflow_exist(self):
        """Test that the provided student and workflow exist in the database."""
        print("\n=== Testing Student and Workflow Existence ===")
        
        # Check student exists
        student = self.prisma.student.find_unique(where={"id": self.test_student_id})
        assert student is not None, f"Student {self.test_student_id} not found"
        print(f"✅ Student found: {student.id}")
        
        # Check workflow exists
        workflow = self.prisma.workflow.find_unique(
            where={"id": self.test_workflow_id},
            include={"steps": True}
        )
        assert workflow is not None, f"Workflow {self.test_workflow_id} not found"
        print(f"✅ Workflow found: {workflow.name} with {len(workflow.steps)} steps")
        
        # Check if it's tagged as BFI workflow
        workflow_tags = workflow.tags or []
        is_bfi_workflow = any(tag == "type:The Big Five Personality Test (BFPT)" for tag in workflow_tags)
        print(f"📋 Workflow tags: {workflow_tags}")
        print(f"🧠 Is BFI workflow: {is_bfi_workflow}")
        
        return student, workflow
    
    def test_find_latest_bfi_workflow(self):
        """Test finding the latest BFI workflow for the student."""
        print("\n=== Testing Latest BFI Workflow Detection ===")

        workflow_id = self.service.find_latest_bfi_workflow(self.test_student_id)
        print(f"🔍 Found BFI workflow: {workflow_id}")

        if workflow_id:
            # Get workflow details
            workflow = self.prisma.workflow.find_unique(
                where={"id": workflow_id},
                include={"steps": True}
            )
            if workflow:
                print(f"📋 Workflow name: {workflow.name}")
                print(f"📋 Workflow tags: {workflow.tags}")
                print(f"📋 Steps count: {len(workflow.steps)}")

        return workflow_id

    def test_get_bfi_responses(self):
        """Test retrieving BFI responses for the student."""
        print("\n=== Testing BFI Response Retrieval ===")

        # Test with automatic workflow detection
        responses = self.service.get_bfi_responses(self.test_student_id)
        print(f"📊 Retrieved {len(responses)} responses (auto-detected workflow)")

        if responses:
            print("Sample responses:")
            for i, (q_num, value) in enumerate(list(responses.items())[:5]):
                print(f"  Question {q_num}: {value}")
            if len(responses) > 5:
                print(f"  ... and {len(responses) - 5} more")

        return responses
    
    def test_response_conversion(self):
        """Test the response text to numeric conversion."""
        print("\n=== Testing Response Conversion ===")
        
        test_cases = [
            ("1", 1),
            ("5", 5),
            ("Strongly Disagree", 1),
            ("strongly disagree", 1),
            ("Disagree", 2),
            ("Neutral", 3),
            ("Agree", 4),
            ("Strongly Agree", 5),
            ("strongly agreed", 5),
            ("invalid", None)
        ]
        
        for input_text, expected in test_cases:
            result = self.service._convert_response_to_numeric(input_text)
            print(f"  '{input_text}' -> {result} (expected: {expected})")
            assert result == expected, f"Conversion failed for '{input_text}'"
        
        print("✅ All response conversions working correctly")
    
    def test_calculate_ocean_scores(self):
        """Test OCEAN scores calculation."""
        print("\n=== Testing OCEAN Scores Calculation ===")

        # Test with automatic workflow detection
        scores = self.service.calculate_ocean_scores(self.test_student_id)
        
        if scores:
            print("🎯 OCEAN Scores calculated:")
            for trait, score in scores.items():
                print(f"  {trait.replace('_', ' ').title()}: {score}")
            
            # Validate score ranges (typically 0-40 for each trait)
            for trait, score in scores.items():
                assert 0 <= score <= 50, f"{trait} score {score} is out of expected range"
            
            print("✅ All scores within valid ranges")
        else:
            print("❌ No scores calculated - checking why...")
            responses = self.service.get_bfi_responses(self.test_student_id, self.test_workflow_id)
            print(f"Available responses: {len(responses)}")
            if len(responses) < 50:
                print("⚠️  Insufficient responses for calculation (need 50)")
        
        return scores
    
    def test_save_ocean_scores(self):
        """Test saving OCEAN scores to database."""
        print("\n=== Testing OCEAN Scores Saving ===")
        
        # First calculate scores (auto-detect workflow)
        scores = self.service.calculate_ocean_scores(self.test_student_id)
        
        if not scores:
            print("⚠️  Cannot test saving - no scores calculated")
            return False
        
        # Save scores
        success = self.service.save_ocean_scores(self.test_student_id, scores)
        print(f"💾 Save operation: {'✅ Success' if success else '❌ Failed'}")
        
        if success:
            # Verify scores were saved
            saved_scores = self.service.get_ocean_scores(self.test_student_id)
            if saved_scores:
                print("📖 Retrieved saved scores:")
                for trait in ['extroversion', 'agreeableness', 'conscientiousness', 'neuroticism', 'openness_to_experience']:
                    print(f"  {trait.replace('_', ' ').title()}: {saved_scores[trait]}")
                print(f"  Created: {saved_scores['created_at']}")
                print(f"  Updated: {saved_scores['updated_at']}")
            else:
                print("❌ Could not retrieve saved scores")
                success = False
        
        return success
    
    def test_complete_process(self):
        """Test the complete OCEAN processing workflow."""
        print("\n=== Testing Complete OCEAN Processing ===")
        
        success = self.service.process_student_ocean_scores(self.test_student_id)
        print(f"🔄 Complete process: {'✅ Success' if success else '❌ Failed'}")
        
        return success
    
    def test_workflow_step_ids(self):
        """Test getting workflow step IDs."""
        print("\n=== Testing Workflow Step IDs ===")
        
        step_ids = self.service._get_workflow_step_ids(self.test_workflow_id)
        print(f"🔗 Found {len(step_ids)} workflow steps")
        
        if step_ids:
            print("Sample step IDs:")
            for i, step_id in enumerate(step_ids[:3]):
                print(f"  {i+1}: {step_id}")
            if len(step_ids) > 3:
                print(f"  ... and {len(step_ids) - 3} more")
        
        return step_ids
    
    def run_all_tests(self):
        """Run all tests in sequence."""
        print("🧪 Starting OCEAN Scores Service Tests")
        print("=" * 50)
        
        try:
            # Test 1: Basic existence checks
            student, workflow = self.test_student_and_workflow_exist()
            
            # Test 2: Response conversion
            self.test_response_conversion()
            
            # Test 3: Find latest BFI workflow
            latest_workflow_id = self.test_find_latest_bfi_workflow()

            # Test 4: Workflow step IDs
            step_ids = self.test_workflow_step_ids()

            # Test 5: BFI response retrieval
            responses = self.test_get_bfi_responses()
            
            # Test 6: OCEAN calculation
            scores = self.test_calculate_ocean_scores()

            # Test 7: Save scores
            if scores:
                save_success = self.test_save_ocean_scores()
            else:
                print("⚠️  Skipping save test - no scores to save")
                save_success = False

            # Test 8: Complete process
            process_success = self.test_complete_process()

            print("\n" + "=" * 50)
            print("🏁 Test Summary:")
            print(f"  Student/Workflow exist: ✅")
            print(f"  Response conversion: ✅")
            print(f"  Latest BFI workflow: {'✅' if latest_workflow_id else '❌'}")
            print(f"  Workflow steps: ✅ ({len(step_ids)} found)")
            print(f"  BFI responses: {'✅' if responses else '❌'} ({len(responses)} found)")
            print(f"  OCEAN calculation: {'✅' if scores else '❌'}")
            print(f"  Save scores: {'✅' if save_success else '❌'}")
            print(f"  Complete process: {'✅' if process_success else '❌'}")

            return {
                'student_exists': True,
                'workflow_exists': True,
                'latest_bfi_workflow': latest_workflow_id,
                'responses_found': len(responses),
                'scores_calculated': scores is not None,
                'scores_saved': save_success,
                'process_success': process_success,
                'scores': scores
            }
            
        except Exception as e:
            print(f"\n❌ Test failed with error: {str(e)}")
            import traceback
            traceback.print_exc()
            return {'error': str(e)}


def run_ocean_tests():
    """Main function to run OCEAN tests."""
    tester = TestOceanScoresService()
    return tester.run_all_tests()


if __name__ == "__main__":
    results = run_ocean_tests()
    print(f"\n🎯 Final Results: {results}")
