"""
Tests for SMS mode priority handling when users set workflows to SMS mode in UI.

These tests verify that when a student sets a workflow to SMS mode in the UI,
the SMS system correctly prioritizes and routes to those workflows.
"""

import json
import alog
from unittest.mock import patch
from tests.utils.base_test_cases import BaseWorkflowTestCase
from tests.utils.test_helpers import (
    setup_structured_sms_workflow,
    setup_unstructured_sms_workflow,
    cleanup_structured_workflow_test_data,
    cleanup_workflow_test_data,
)
from prisma.enums import WorkflowType
from addie.sms_agent.agent import find_student_sms_workflow, get_priority_sms_workflow, find_student_sms_ready_workflows
from addie.api.sms_routes import route_sms_to_agent
import asyncio

# Configure logger
logger = alog.getLogger()


class SMSModePriorityTestCase(BaseWorkflowTestCase):
    """Test SMS mode priority handling"""
    
    def setUp(self):
        """Set up both structured and unstructured workflows"""
        super().setUp()
        
        # Set up structured workflow
        (
            self.structured_workflow_id,
            self.structured_workflow_step_id,
            self.structured_student_workflow_id,
            self.structured_workflow,
            self.structured_step,
            self.structured_student_workflow,
        ) = setup_structured_sms_workflow(
            self.prisma, self.user_id, self.student_id, "SMS Priority Structured"
        )
        
        # Set up unstructured workflow
        (
            self.unstructured_workflow_id,
            self.unstructured_workflow_step_id,
            self.unstructured_student_workflow_id,
            self.unstructured_workflow,
            self.unstructured_step,
            self.unstructured_student_workflow,
        ) = setup_unstructured_sms_workflow(
            self.prisma, self.user_id, self.student_id, "SMS Priority Unstructured"
        )
        
        # Initially set both to web mode to simulate UI state
        self.prisma.studentworkflow.update(
            where={"id": self.structured_student_workflow_id},
            data={"mode": "web", "status": "IN_PROGRESS"}
        )
        self.prisma.studentworkflow.update(
            where={"id": self.unstructured_student_workflow_id},
            data={"mode": "web", "status": "IN_PROGRESS"}
        )
    
    def tearDown(self):
        """Clean up both workflows"""
        try:
            cleanup_structured_workflow_test_data(
                self.prisma, self.structured_workflow_id, 
                self.structured_workflow_step_id, self.structured_student_workflow_id
            )
        except Exception as e:
            logger.warning(f"Error cleaning up structured workflow: {e}")
        
        try:
            cleanup_workflow_test_data(
                self.prisma, self.unstructured_workflow_id,
                self.unstructured_workflow_step_id, self.unstructured_student_workflow_id
            )
        except Exception as e:
            logger.warning(f"Error cleaning up unstructured workflow: {e}")
        
        super().tearDown()


class TestSMSModePriority(SMSModePriorityTestCase):
    """Test SMS mode priority scenarios"""
    
    def test_current_sms_workflow_discovery_limitation(self):
        """Test that current system only finds unstructured workflows"""
        # Set ONLY structured workflow to SMS mode (user sets this in UI)
        self.prisma.studentworkflow.update(
            where={"id": self.structured_student_workflow_id},
            data={"mode": "sms", "status": "IN_PROGRESS"}
        )
        
        # Set unstructured workflow to web mode AND not started
        self.prisma.studentworkflow.update(
            where={"id": self.unstructured_student_workflow_id},
            data={"mode": "web", "status": "NOT_STARTED"}
        )
        
        # Current system should NOT find the structured workflow because it only looks for UNSTRUCTURED
        sms_workflow, user_id = find_student_sms_workflow(self.student_id)
        
        # This demonstrates the current limitation - it finds the unstructured workflow even though
        # the structured one is actually set to SMS mode by the user
        if sms_workflow:
            # If it finds something, it should be the unstructured one (not the user's preferred SMS mode one)
            self.assertEqual(sms_workflow.workflow.workflow_type, WorkflowType.UNSTRUCTURED,
                           "Current system only finds unstructured workflows")
            self.assertNotEqual(sms_workflow.id, self.structured_student_workflow_id,
                               "Current system ignores structured workflows even when set to SMS mode")
        else:
            # If it finds nothing, that's also a limitation
            logger.info("Current system found no SMS workflow despite structured being set to SMS mode")
        
        # Now set unstructured to SMS mode - system should find it
        self.prisma.studentworkflow.update(
            where={"id": self.unstructured_student_workflow_id},
            data={"mode": "sms", "status": "IN_PROGRESS"}
        )
        
        sms_workflow, user_id = find_student_sms_workflow(self.student_id)
        self.assertIsNotNone(sms_workflow, "Current system should find unstructured workflows")
        self.assertEqual(sms_workflow.workflow.workflow_type, WorkflowType.UNSTRUCTURED)
    
    def test_structured_workflow_sms_mode_priority(self):
        """Test that structured workflows set to SMS mode should be prioritized"""
        # User sets structured workflow to SMS mode in UI
        self.prisma.studentworkflow.update(
            where={"id": self.structured_student_workflow_id},
            data={"mode": "sms", "status": "IN_PROGRESS"}
        )
        
        # Keep unstructured in web mode
        self.prisma.studentworkflow.update(
            where={"id": self.unstructured_student_workflow_id},
            data={"mode": "web", "status": "IN_PROGRESS"}
        )
        
        # This test will currently fail but shows what should happen
        # TODO: Implement find_student_sms_ready_workflows() to make this pass
        
        # We need a new function that can find ANY workflow type in SMS mode
        sms_ready_workflows = self.prisma.studentworkflow.find_many(
            where={
                "student_id": self.student_id,
                "mode": "sms",
                "status": "IN_PROGRESS"
            },
            include={"workflow": True}
        )
        
        # Should find at least one SMS-ready workflow (our test workflow)
        self.assertGreaterEqual(len(sms_ready_workflows), 1, "Should find at least one SMS-ready workflow")
        
        # Filter to our specific test workflow
        our_workflows = [sw for sw in sms_ready_workflows if sw.id == self.structured_student_workflow_id]
        self.assertEqual(len(our_workflows), 1, "Should find our test workflow")
        
        # Verify our test workflow properties
        our_workflow = our_workflows[0]
        self.assertEqual(our_workflow.id, self.structured_student_workflow_id)
        self.assertEqual(our_workflow.workflow.workflow_type, WorkflowType.STRUCTURED)
    
    def test_mixed_sms_mode_priority_by_timestamp(self):
        """Test priority when both workflows are set to SMS mode"""
        # Set both workflows to SMS mode (user sets both in UI)
        self.prisma.studentworkflow.update(
            where={"id": self.structured_student_workflow_id},
            data={"mode": "sms", "status": "IN_PROGRESS"}
        )
        
        self.prisma.studentworkflow.update(
            where={"id": self.unstructured_student_workflow_id},
            data={"mode": "sms", "status": "IN_PROGRESS"}
        )
        
        # Should prioritize by most recently updated
        sms_ready_workflows = self.prisma.studentworkflow.find_many(
            where={
                "student_id": self.student_id,
                "mode": "sms",
                "status": "IN_PROGRESS"
            },
            include={"workflow": True},
            order={"updated_at": "desc"}
        )
        
        # Should find at least both test workflows (may include others from previous tests)
        self.assertGreaterEqual(len(sms_ready_workflows), 2, "Should find at least both SMS-ready workflows")
        
        # Most recently updated should be first
        most_recent = sms_ready_workflows[0]
        self.assertEqual(most_recent.id, self.unstructured_student_workflow_id, 
                        "Most recently updated should be prioritized")
    
    def test_routing_to_structured_workflow_in_sms_mode(self):
        """Test that routing works correctly for structured workflows in SMS mode"""
        # Set structured workflow to SMS mode
        structured_sw = self.prisma.studentworkflow.update(
            where={"id": self.structured_student_workflow_id},
            data={"mode": "sms", "status": "IN_PROGRESS"}
        )
        
        # Mock Twilio SMS sending
        with patch("addie.sms_agent.agent.send_sms_via_twilio") as mock_send_sms:
            mock_send_sms.return_value = True
            
            # Test routing to structured workflow
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                response = loop.run_until_complete(
                    route_sms_to_agent(
                        prisma=self.prisma,
                        student_id=self.student_id,
                        message_text="start",
                        from_number="+***********",
                        workflow_id=self.structured_workflow_id,
                        sms_workflow=structured_sw
                    )
                )
                
                # Should get a structured workflow response (Big Five question)
                self.assertIsNotNone(response)
                
                # Structured responses typically contain Likert scale options
                self.assertIn("Strongly Disagree", response, "Should get structured Big Five response")
                
                logger.info(f"Structured workflow response: {response}")
                
            finally:
                loop.close()
    
    def test_routing_to_unstructured_workflow_in_sms_mode(self):
        """Test that routing works correctly for unstructured workflows in SMS mode"""
        # Set unstructured workflow to SMS mode
        unstructured_sw = self.prisma.studentworkflow.update(
            where={"id": self.unstructured_student_workflow_id},
            data={"mode": "sms", "status": "IN_PROGRESS"}
        )
        
        # Mock Twilio SMS sending
        with patch("addie.sms_agent.agent.send_sms_via_twilio") as mock_send_sms:
            mock_send_sms.return_value = True
            
            # Test routing to unstructured workflow
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                response = loop.run_until_complete(
                    route_sms_to_agent(
                        prisma=self.prisma,
                        student_id=self.student_id,
                        message_text="Hello, I want to talk about college",
                        from_number="+***********",
                        workflow_id=self.unstructured_workflow_id,
                        sms_workflow=unstructured_sw
                    )
                )
                
                # Should get an unstructured workflow response
                self.assertIsNotNone(response)
                
                # Unstructured responses are more conversational
                self.assertNotIn("Strongly Disagree", response, "Should not get structured response")
                
                logger.info(f"Unstructured workflow response: {response}")
                
            finally:
                loop.close()


class TestNewSMSPriorityFunctionality(SMSModePriorityTestCase):
    """Test the new SMS mode priority functionality"""
    
    def test_find_student_sms_ready_workflows_both_types(self):
        """Test that find_student_sms_ready_workflows finds both workflow types"""
        # Set both workflows to SMS mode
        self.prisma.studentworkflow.update(
            where={"id": self.structured_student_workflow_id},
            data={"mode": "sms", "status": "IN_PROGRESS"}
        )
        self.prisma.studentworkflow.update(
            where={"id": self.unstructured_student_workflow_id},
            data={"mode": "sms", "status": "IN_PROGRESS"}
        )
        
        # Find SMS-ready workflows
        sms_ready_workflows = find_student_sms_ready_workflows(self.student_id)
        
        # Should find at least both test workflows (may include others from previous tests)
        self.assertGreaterEqual(len(sms_ready_workflows), 2, "Should find at least both SMS-ready workflows")
        
        # Filter to our specific test workflows
        test_workflow_ids = {self.structured_student_workflow_id, self.unstructured_student_workflow_id}
        our_workflows = [sw for sw in sms_ready_workflows if sw.id in test_workflow_ids]
        self.assertEqual(len(our_workflows), 2, "Should find both of our test workflows")
        
        # Should contain both workflow types
        workflow_types = [sw.workflow.workflow_type for sw in sms_ready_workflows]
        self.assertIn(WorkflowType.STRUCTURED, workflow_types, "Should include structured workflow")
        self.assertIn(WorkflowType.UNSTRUCTURED, workflow_types, "Should include unstructured workflow")
        
    def test_get_priority_sms_workflow_prioritizes_sms_mode(self):
        """Test that get_priority_sms_workflow prioritizes workflows already in SMS mode"""
        # Set only structured workflow to SMS mode
        self.prisma.studentworkflow.update(
            where={"id": self.structured_student_workflow_id},
            data={"mode": "sms", "status": "IN_PROGRESS"}
        )
        
        # Leave unstructured workflow in web mode but make it more recently updated
        self.prisma.studentworkflow.update(
            where={"id": self.unstructured_student_workflow_id},
            data={"mode": "web", "status": "IN_PROGRESS"}
        )
        
        # Get priority workflow
        priority_workflow, user_id = get_priority_sms_workflow(self.student_id)
        
        # Should prioritize the structured workflow because it's already in SMS mode
        self.assertIsNotNone(priority_workflow, "Should find a priority workflow")
        self.assertEqual(priority_workflow.id, self.structured_student_workflow_id,
                        "Should prioritize structured workflow in SMS mode")
        self.assertEqual(priority_workflow.workflow.workflow_type, WorkflowType.STRUCTURED)
        self.assertEqual(priority_workflow.mode, "sms", "Should be in SMS mode")
        
    def test_get_priority_sms_workflow_supports_structured_workflows(self):
        """Test that get_priority_sms_workflow supports structured workflows"""
        # Set only structured workflow to SMS mode
        self.prisma.studentworkflow.update(
            where={"id": self.structured_student_workflow_id},
            data={"mode": "sms", "status": "IN_PROGRESS"}
        )
        
        # Set unstructured workflow to web mode
        self.prisma.studentworkflow.update(
            where={"id": self.unstructured_student_workflow_id},
            data={"mode": "web", "status": "IN_PROGRESS"}
        )
        
        # Get priority workflow
        priority_workflow, user_id = get_priority_sms_workflow(self.student_id)
        
        # Should find the structured workflow
        self.assertIsNotNone(priority_workflow, "Should find structured workflow")
        self.assertEqual(priority_workflow.workflow.workflow_type, WorkflowType.STRUCTURED)
        self.assertEqual(priority_workflow.mode, "sms")
        
    def test_get_priority_sms_workflow_switches_to_sms_mode(self):
        """Test that get_priority_sms_workflow switches workflows to SMS mode when needed"""
        # Set unstructured workflow to web mode
        self.prisma.studentworkflow.update(
            where={"id": self.unstructured_student_workflow_id},
            data={"mode": "web", "status": "IN_PROGRESS"}
        )
        
        # Set structured workflow to web mode
        self.prisma.studentworkflow.update(
            where={"id": self.structured_student_workflow_id},
            data={"mode": "web", "status": "IN_PROGRESS"}
        )
        
        # Get priority workflow
        priority_workflow, user_id = get_priority_sms_workflow(self.student_id)
        
        # Should find and switch a workflow to SMS mode
        self.assertIsNotNone(priority_workflow, "Should find a workflow")
        self.assertEqual(priority_workflow.mode, "sms", "Should be switched to SMS mode")
        
        # Verify the workflow was actually updated in the database
        updated_workflow = self.prisma.studentworkflow.find_unique(
            where={"id": priority_workflow.id},
            include={"workflow": True}
        )
        self.assertEqual(updated_workflow.mode, "sms", "Should be persisted in SMS mode")
        
    def test_structured_workflow_routing_with_priority_function(self):
        """Test that structured workflow routing works with new priority function"""
        # Set structured workflow to SMS mode
        structured_sw = self.prisma.studentworkflow.update(
            where={"id": self.structured_student_workflow_id},
            data={"mode": "sms", "status": "IN_PROGRESS"}
        )
        
        # Mock Twilio SMS sending
        with patch("addie.sms_agent.agent.send_sms_via_twilio") as mock_send_sms:
            mock_send_sms.return_value = True
            
            # Test routing should now find the structured workflow
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                response = loop.run_until_complete(
                    route_sms_to_agent(
                        prisma=self.prisma,
                        student_id=self.student_id,
                        message_text="start",
                        from_number="+***********",
                        workflow_id=self.structured_workflow_id,
                        sms_workflow=structured_sw
                    )
                )
                
                # Should get a structured workflow response
                self.assertIsNotNone(response, "Should get a response")
                self.assertIn("Strongly Disagree", response, "Should get structured Big Five response")
                
            finally:
                loop.close()
    
    def test_mixed_workflow_priority_by_sms_mode(self):
        """Test priority when both workflows exist but only one is in SMS mode"""
        # Set structured workflow to SMS mode (user-initiated)
        self.prisma.studentworkflow.update(
            where={"id": self.structured_student_workflow_id},
            data={"mode": "sms", "status": "IN_PROGRESS"}
        )
        
        # Set unstructured workflow to web mode but more recently updated
        self.prisma.studentworkflow.update(
            where={"id": self.unstructured_student_workflow_id},
            data={"mode": "web", "status": "IN_PROGRESS"}
        )
        
        # Get priority workflow
        priority_workflow, user_id = get_priority_sms_workflow(self.student_id)
        
        # Should prioritize the SMS mode workflow over the more recent web mode one
        self.assertIsNotNone(priority_workflow, "Should find a priority workflow")
        self.assertEqual(priority_workflow.id, self.structured_student_workflow_id,
                        "Should prioritize workflow already in SMS mode")
        self.assertEqual(priority_workflow.mode, "sms", "Should be in SMS mode")
        
    def test_fallback_to_switchable_workflows(self):
        """Test fallback to switchable workflows when no SMS mode workflows exist"""
        # Set both workflows to web mode
        self.prisma.studentworkflow.update(
            where={"id": self.structured_student_workflow_id},
            data={"mode": "web", "status": "IN_PROGRESS"}
        )
        self.prisma.studentworkflow.update(
            where={"id": self.unstructured_student_workflow_id},
            data={"mode": "web", "status": "IN_PROGRESS"}
        )
        
        # Get priority workflow
        priority_workflow, user_id = get_priority_sms_workflow(self.student_id)
        
        # Should find and switch a workflow to SMS mode
        self.assertIsNotNone(priority_workflow, "Should find a workflow")
        self.assertEqual(priority_workflow.mode, "sms", "Should be switched to SMS mode")
        
        # Verify it was actually switched in the database
        updated_workflow = self.prisma.studentworkflow.find_unique(
            where={"id": priority_workflow.id}
        )
        self.assertEqual(updated_workflow.mode, "sms", "Should be persisted in SMS mode")
        
    def test_no_workflows_available(self):
        """Test behavior when no workflows are available"""
        # Set both workflows to completed
        self.prisma.studentworkflow.update(
            where={"id": self.structured_student_workflow_id},
            data={"mode": "web", "status": "COMPLETED"}
        )
        self.prisma.studentworkflow.update(
            where={"id": self.unstructured_student_workflow_id},
            data={"mode": "web", "status": "COMPLETED"}
        )
        
        # Get priority workflow
        priority_workflow, user_id = get_priority_sms_workflow(self.student_id)
        
        # Should find no workflow among our test workflows (may find others from previous tests)
        # If a workflow is found, it should not be one of our test workflows
        if priority_workflow:
            test_workflow_ids = {self.structured_student_workflow_id, self.unstructured_student_workflow_id}
            self.assertNotIn(priority_workflow.id, test_workflow_ids, 
                           "Should not find our test workflows since they are completed")
        self.assertIsNotNone(user_id, "Should still return user_id")
        
    def test_specific_workflow_id_priority(self):
        """Test that specific workflow_id parameter works correctly"""
        # Set both workflows to web mode
        self.prisma.studentworkflow.update(
            where={"id": self.structured_student_workflow_id},
            data={"mode": "web", "status": "IN_PROGRESS"}
        )
        self.prisma.studentworkflow.update(
            where={"id": self.unstructured_student_workflow_id},
            data={"mode": "web", "status": "IN_PROGRESS"}
        )
        
        # Get priority workflow for specific structured workflow
        priority_workflow, user_id = get_priority_sms_workflow(
            self.student_id, self.structured_workflow_id
        )
        
        # Should find the specific structured workflow
        self.assertIsNotNone(priority_workflow, "Should find specific workflow")
        self.assertEqual(priority_workflow.workflow_id, self.structured_workflow_id,
                        "Should find the specific workflow requested")
        self.assertEqual(priority_workflow.mode, "sms", "Should be switched to SMS mode")
        
    def test_old_function_still_works_for_unstructured(self):
        """Test that old find_student_sms_workflow still works for unstructured workflows"""
        # Set unstructured workflow to web mode (will be switched to SMS)
        self.prisma.studentworkflow.update(
            where={"id": self.unstructured_student_workflow_id},
            data={"mode": "web", "status": "IN_PROGRESS"}
        )
        
        # Use old function
        sms_workflow, user_id = find_student_sms_workflow(self.student_id)
        
        # Should find the unstructured workflow
        self.assertIsNotNone(sms_workflow, "Should find unstructured workflow")
        self.assertEqual(sms_workflow.workflow.workflow_type, WorkflowType.UNSTRUCTURED)
        self.assertEqual(sms_workflow.mode, "sms", "Should be in SMS mode")
        
    def test_old_function_ignores_structured_workflows(self):
        """Test that old find_student_sms_workflow ignores structured workflows"""
        # Set structured workflow to SMS mode
        self.prisma.studentworkflow.update(
            where={"id": self.structured_student_workflow_id},
            data={"mode": "sms", "status": "IN_PROGRESS"}
        )
        
        # Set unstructured workflow to web mode
        self.prisma.studentworkflow.update(
            where={"id": self.unstructured_student_workflow_id},
            data={"mode": "web", "status": "IN_PROGRESS"}
        )
        
        # Use old function
        sms_workflow, user_id = find_student_sms_workflow(self.student_id)
        
        # Should find the unstructured workflow, not the structured one
        self.assertIsNotNone(sms_workflow, "Should find unstructured workflow")
        self.assertEqual(sms_workflow.workflow.workflow_type, WorkflowType.UNSTRUCTURED)
        self.assertNotEqual(sms_workflow.id, self.structured_student_workflow_id,
                           "Should not find structured workflow")