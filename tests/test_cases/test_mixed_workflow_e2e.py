"""
End-to-end test for mixed structured and unstructured SMS workflows.

This test creates both structured (Big Five) and unstructured workflows for the same student
and tests that they can be completed in either order, verifying the workflow switching logic.
"""

import json
import alog
from unittest.mock import patch
from unittest import TestCase
from tests.utils.base_test_cases import BaseWorkflowTestCase
from tests.utils.structured_sms_runner import StructuredSMSConversationRunner, STUDENT_PERSONAS
from tests.utils.test_helpers import (
    setup_structured_sms_workflow,
    setup_unstructured_sms_workflow,
    cleanup_structured_workflow_test_data,
    cleanup_workflow_test_data,
)
from prisma.enums import WorkflowType
from addie.lib import prisma_client

# Configure logger
logger = alog.getLogger()


class MixedWorkflowTestCase(BaseWorkflowTestCase):
    """
    Test case for mixed structured and unstructured SMS workflows.
    Sets up both workflow types for the same student.
    """
    
    def setUp(self):
        """Set up both structured and unstructured workflows for the same student"""
        super().setUp()
        
        # Set up structured workflow
        logger.debug("Setting up structured workflow...")
        (
            self.structured_workflow_id,
            self.structured_workflow_step_id,
            self.structured_student_workflow_id,
            self.structured_workflow,
            self.structured_step,
            self.structured_student_workflow,
        ) = setup_structured_sms_workflow(
            self.prisma, self.user_id, self.student_id, "Mixed Test Structured"
        )
        
        # Set up unstructured workflow
        logger.debug("Setting up unstructured workflow...")
        (
            self.unstructured_workflow_id,
            self.unstructured_workflow_step_id,
            self.unstructured_student_workflow_id,
            self.unstructured_workflow,
            self.unstructured_step,
            self.unstructured_student_workflow,
        ) = setup_unstructured_sms_workflow(
            self.prisma, self.user_id, self.student_id, "Mixed Test Unstructured"
        )
        
        logger.debug(f"Set up structured workflow: {self.structured_workflow_id}")
        logger.debug(f"Set up unstructured workflow: {self.unstructured_workflow_id}")
    
    def tearDown(self):
        """Clean up both workflows"""
        logger.debug("Cleaning up mixed workflow test data...")
        
        try:
            # Clean up structured workflow
            cleanup_structured_workflow_test_data(
                self.prisma, 
                self.structured_workflow_id, 
                self.structured_workflow_step_id, 
                self.structured_student_workflow_id
            )
        except Exception as e:
            logger.warning(f"Error cleaning up structured workflow: {e}")
        
        try:
            # Clean up unstructured workflow
            cleanup_workflow_test_data(
                self.prisma,
                self.unstructured_workflow_id,
                self.unstructured_workflow_step_id,
                self.unstructured_student_workflow_id
            )
        except Exception as e:
            logger.warning(f"Error cleaning up unstructured workflow: {e}")
        
        # Call parent teardown
        super().tearDown()


class TestMixedWorkflowEndToEnd(MixedWorkflowTestCase):
    """Test mixed structured and unstructured workflows end-to-end"""
    
    def test_both_workflows_created_correctly(self):
        """Test that both workflows are created with correct types and settings"""
        # Verify structured workflow
        self.assertIsNotNone(self.structured_workflow_id)
        self.assertEqual(self.structured_workflow.workflow_type, WorkflowType.STRUCTURED)
        self.assertEqual(self.structured_student_workflow.mode, "sms")
        self.assertEqual(self.structured_student_workflow.status, "IN_PROGRESS")
        
        # Verify unstructured workflow
        self.assertIsNotNone(self.unstructured_workflow_id)
        self.assertEqual(self.unstructured_workflow.workflow_type, WorkflowType.UNSTRUCTURED)
        self.assertEqual(self.unstructured_student_workflow.mode, "sms")
        self.assertEqual(self.unstructured_student_workflow.status, "IN_PROGRESS")
        
        # Verify they're different workflows
        self.assertNotEqual(self.structured_workflow_id, self.unstructured_workflow_id)
        
        # Verify student has both workflows
        student_workflows = self.prisma.studentworkflow.find_many(
            where={"student_id": self.student_id},
            include={"workflow": True}
        )
        
        workflow_types = [sw.workflow.workflow_type for sw in student_workflows if sw.workflow]
        self.assertIn(WorkflowType.STRUCTURED, workflow_types)
        self.assertIn(WorkflowType.UNSTRUCTURED, workflow_types)
        
    def test_structured_first_then_unstructured(self):
        """Test completing structured workflow first, then unstructured workflow"""
        # Mock Twilio SMS sending
        with patch("addie.sms_agent.agent.send_sms_via_twilio") as mock_send_sms:
            mock_send_sms.return_value = True
            
            # Step 1: Complete structured workflow (Big Five)
            logger.info("=== Starting structured workflow completion ===")
            
            structured_runner = StructuredSMSConversationRunner(
                student_id=self.student_id,
                user_id=self.user_id,
                workflow_id=self.structured_workflow_id,
                phone_number="+***********"
            )
            
            try:
                # Setup student agent with balanced persona
                structured_runner.setup_student_agent(STUDENT_PERSONAS["balanced"])
                
                # Complete part of structured workflow (first 10 questions)
                structured_results = structured_runner.run_conversation(max_turns=25)
                
                # Verify structured workflow progress
                self.assertTrue(structured_results["questions_answered"] > 0)
                logger.info(f"Structured workflow: {structured_results['questions_answered']} questions answered")
                
                # Mark structured workflow as completed for testing
                self.prisma.studentworkflow.update(
                    where={"id": self.structured_student_workflow_id},
                    data={"status": "COMPLETED"}
                )
                
            finally:
                structured_runner.cleanup()
            
            # Step 2: Now test unstructured workflow
            logger.info("=== Starting unstructured workflow after structured completion ===")
            
            # Import the unstructured runner
            from tests.test_cases.test_sms_realworld_e2e import StudentCounselorConversationRunner
            
            unstructured_runner = StudentCounselorConversationRunner(
                student_id=self.student_id,
                user_id=self.user_id,
                workflow_id=self.unstructured_workflow_id,
                phone_number="+***********"
            )
            
            try:
                # Setup student agent for unstructured conversation
                unstructured_runner.setup_student_agent(
                    """
                    You are a high school senior who just completed a personality inventory and now wants to 
                    talk about college planning. You're thoughtful, engaged, and ready to discuss your future.
                    You can reference that you just took a personality test if it comes up.
                    """
                )
                
                # Run unstructured conversation
                def check_unstructured_completion():
                    # For this test, we'll just run a few turns
                    return unstructured_runner.conversation_log and len(unstructured_runner.conversation_log) > 10
                
                unstructured_results = unstructured_runner.run_conversation(
                    max_turns=12,
                    progress_callback=check_unstructured_completion
                )
                
                # Verify unstructured workflow ran
                self.assertTrue(unstructured_results["total_turns"] > 0)
                logger.info(f"Unstructured workflow: {unstructured_results['total_turns']} turns completed")
                
                # Verify both workflows have activity
                self.assertTrue(structured_results["questions_answered"] > 0)
                self.assertTrue(unstructured_results["total_turns"] > 0)
                
            finally:
                unstructured_runner.cleanup()
    
    def test_unstructured_first_then_structured(self):
        """Test completing unstructured workflow first, then structured workflow"""
        # Mock Twilio SMS sending
        with patch("addie.sms_agent.agent.send_sms_via_twilio") as mock_send_sms:
            mock_send_sms.return_value = True
            
            # Step 1: Start with unstructured workflow
            logger.info("=== Starting unstructured workflow first ===")
            
            from tests.test_cases.test_sms_realworld_e2e import StudentCounselorConversationRunner
            
            unstructured_runner = StudentCounselorConversationRunner(
                student_id=self.student_id,
                user_id=self.user_id,
                workflow_id=self.unstructured_workflow_id,
                phone_number="+***********"
            )
            
            try:
                # Setup student agent for unstructured conversation
                unstructured_runner.setup_student_agent(
                    """
                    You are a high school senior starting college planning conversations. You're curious about 
                    colleges, majors, and the application process. You're ready to engage in meaningful dialogue.
                    """
                )
                
                # Run unstructured conversation
                def check_unstructured_completion():
                    return unstructured_runner.conversation_log and len(unstructured_runner.conversation_log) > 8
                
                unstructured_results = unstructured_runner.run_conversation(
                    max_turns=10,
                    progress_callback=check_unstructured_completion
                )
                
                # Verify unstructured workflow ran
                self.assertTrue(unstructured_results["total_turns"] > 0)
                logger.info(f"Unstructured workflow: {unstructured_results['total_turns']} turns completed")
                
                # Mark unstructured workflow as completed
                self.prisma.studentworkflow.update(
                    where={"id": self.unstructured_student_workflow_id},
                    data={"status": "COMPLETED"}
                )
                
            finally:
                unstructured_runner.cleanup()
            
            # Step 2: Now test structured workflow
            logger.info("=== Starting structured workflow after unstructured completion ===")
            
            structured_runner = StructuredSMSConversationRunner(
                student_id=self.student_id,
                user_id=self.user_id,
                workflow_id=self.structured_workflow_id,
                phone_number="+***********"
            )
            
            try:
                # Setup student agent with extrovert persona
                structured_runner.setup_student_agent(STUDENT_PERSONAS["extrovert"])
                
                # Complete part of structured workflow
                structured_results = structured_runner.run_conversation(max_turns=20)
                
                # Verify structured workflow progress
                self.assertTrue(structured_results["questions_answered"] > 0)
                logger.info(f"Structured workflow: {structured_results['questions_answered']} questions answered")
                
                # Verify both workflows have activity
                self.assertTrue(unstructured_results["total_turns"] > 0)
                self.assertTrue(structured_results["questions_answered"] > 0)
                
            finally:
                structured_runner.cleanup()
    
    def test_workflow_switching_logic(self):
        """Test that the SMS routing correctly switches between workflow types"""
        # Mock Twilio SMS sending
        with patch("addie.sms_agent.agent.send_sms_via_twilio") as mock_send_sms:
            mock_send_sms.return_value = True
            
            # Test message routing to structured workflow
            from addie.api.sms_routes import route_sms_to_agent
            import asyncio
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Test structured workflow routing
                structured_response = loop.run_until_complete(
                    route_sms_to_agent(
                        prisma=self.prisma,
                        student_id=self.student_id,
                        message_text="start structured test",
                        from_number="+***********",
                        workflow_id=self.structured_workflow_id,
                        sms_workflow=self.structured_student_workflow
                    )
                )
                
                self.assertIsNotNone(structured_response)
                logger.info(f"Structured routing response: {structured_response}")
                
                # Test unstructured workflow routing
                unstructured_response = loop.run_until_complete(
                    route_sms_to_agent(
                        prisma=self.prisma,
                        student_id=self.student_id,
                        message_text="start unstructured test",
                        from_number="+***********",
                        workflow_id=self.unstructured_workflow_id,
                        sms_workflow=self.unstructured_student_workflow
                    )
                )
                
                self.assertIsNotNone(unstructured_response)
                logger.info(f"Unstructured routing response: {unstructured_response}")
                
                # Verify responses are different (different agent types)
                self.assertNotEqual(structured_response, unstructured_response)
                
            finally:
                loop.close()
    
    def test_workflow_data_isolation(self):
        """Test that data from different workflow types doesn't interfere"""
        # Mock Twilio SMS sending
        with patch("addie.sms_agent.agent.send_sms_via_twilio") as mock_send_sms:
            mock_send_sms.return_value = True
            
            # Create some activity in both workflows
            structured_runner = StructuredSMSConversationRunner(
                student_id=self.student_id,
                user_id=self.user_id,
                workflow_id=self.structured_workflow_id,
                phone_number="+***********"
            )
            
            try:
                # Setup and run structured workflow briefly
                structured_runner.setup_student_agent(STUDENT_PERSONAS["balanced"])
                structured_results = structured_runner.run_conversation(max_turns=10)
                
                # Verify structured workflow data
                structured_responses = self.prisma.questionresponse.find_many(
                    where={"student_id": self.student_id}
                )
                
                structured_steps = self.prisma.studentworkflowstep.find_many(
                    where={
                        "student_id": self.student_id,
                        "student_workflow_id": self.structured_student_workflow_id
                    }
                )
                
                unstructured_steps = self.prisma.studentworkflowstep.find_many(
                    where={
                        "student_id": self.student_id,
                        "student_workflow_id": self.unstructured_student_workflow_id
                    }
                )
                
                # Verify data isolation
                self.assertTrue(len(structured_responses) > 0, "Should have structured responses")
                self.assertTrue(len(structured_steps) > 0, "Should have structured steps")
                self.assertTrue(len(unstructured_steps) > 0, "Should have unstructured steps")
                
                # Verify structured steps are linked to structured workflow
                for step in structured_steps:
                    self.assertEqual(step.student_workflow_id, self.structured_student_workflow_id)
                
                # Verify unstructured steps are linked to unstructured workflow
                for step in unstructured_steps:
                    self.assertEqual(step.student_workflow_id, self.unstructured_student_workflow_id)
                
                logger.info(f"Data isolation verified: {len(structured_responses)} responses, "
                           f"{len(structured_steps)} structured steps, {len(unstructured_steps)} unstructured steps")
                
            finally:
                structured_runner.cleanup()