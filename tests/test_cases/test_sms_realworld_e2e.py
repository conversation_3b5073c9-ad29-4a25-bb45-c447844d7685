"""
End-to-end real-world SMS test case using existing user and active workflows.

This test uses an existing user (<EMAIL>) and their active 
student workflow to test the SMS unstructured agent end-to-end without mocks.
The test continues until all workflow steps are completed.
"""

import json
import alog
import asyncio
from unittest import TestCase
from unittest.mock import patch
from addie.lib import prisma_client
from addie.sms_agent.agent import sms_unstructured_agent, get_session_id_for_sms


def get_or_create_event_loop():
    """Get the current event loop or create a new one if it's closed."""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_closed():
            raise RuntimeError("Event loop is closed")
        return loop
    except RuntimeError:
        # Create a new event loop if the current one is closed
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        return loop
from addie.student_agent.invoke_student import invoke_student
from langchain_core.messages import HumanMessage
from tests.utils.test_helpers import (
    create_student_agent_with_state,
    cleanup_student_agent_config,
)
from prisma.enums import StudentWorkflowStatus

# Configure logger
logger = alog.getLogger()


class StudentCounselorConversationRunner:
    """
    Runs realistic SMS conversations between a student agent and SMS counselor agent.
    
    This class encapsulates the logic for conducting turn-based conversations where:
    - Student agent generates authentic student responses
    - SMS counselor agent provides guidance and asks questions
    - Conversation continues until workflow completion or max turns reached
    """
    
    def __init__(self, student_id: str, user_id: str, workflow_id: str = None, phone_number: str = "+15551234567"):
        self.student_id = student_id
        self.user_id = user_id
        self.workflow_id = workflow_id  # None means use dynamic workflow selection
        self.phone_number = phone_number
        self.conversation_log = []
        self.student_agent = None
        self.student_state = None
        self.config_id = None
        
        # Initialize post-completion tracking attributes
        self.post_completion_messages = 0
        self.messages_after_completion = 0
        self.workflow_completed = False
        
    def setup_student_agent(self, prompt_content: str, role: str = "student", post_completion_messages: int = 0):
        """Initialize the student agent with specified persona
        
        Args:
            prompt_content: The persona/prompt for the student agent
            role: The role for the agent (default: "student")
            post_completion_messages: Number of messages to allow after workflow completion (default: 0)
        """
        self.student_agent, self.student_state, self.config_id = create_student_agent_with_state(
            student_id=self.student_id,
            user_id=self.user_id,
            prompt_content=prompt_content,
            role=role
        )
        
        # Store the post-completion message count
        self.post_completion_messages = post_completion_messages
        self.messages_after_completion = 0
        self.workflow_completed = False
        
        # Start with an initial prompt to the student agent
        initial_prompt = HumanMessage(content="Hi! I'm ready to start working on my college planning. Can you help me get started?")
        student_msgs = [initial_prompt]
        self.student_state["messages"] = student_msgs
    
    def run_conversation(self, max_turns: int = 100, progress_callback=None):
        """
        Run the conversation between student and counselor agents
        
        Args:
            max_turns: Maximum number of conversation turns
            progress_callback: Optional function to check workflow completion
            
        Returns:
            Dict with conversation results and statistics
        """
        turn = 0
        
        logger.info("Starting student-counselor conversation")
        
        while turn < max_turns:
            logger.info(f"\n--- Turn {turn + 1} ---")
            
            # Fail-safe: Check if we've exceeded post-completion message limit
            if self.workflow_completed and hasattr(self, 'post_completion_messages'):
                if self.messages_after_completion >= self.post_completion_messages:
                    logger.info(f"FAIL-SAFE: Exceeded post-completion message limit ({self.messages_after_completion}/{self.post_completion_messages}). Breaking conversation.")
                    break
            
            # Check workflow completion if callback provided
            if progress_callback and progress_callback():
                if not self.workflow_completed:
                    logger.info("Workflow completed! Starting post-completion message tracking.")
                    self.workflow_completed = True
                    
                # Check if we should continue after completion
                if self.workflow_completed and hasattr(self, 'post_completion_messages'):
                    if self.messages_after_completion >= self.post_completion_messages:
                        logger.info(f"Reached post-completion message limit ({self.post_completion_messages}). Breaking out of conversation loop.")
                        break
                    else:
                        logger.info(f"Post-completion message {self.messages_after_completion + 1}/{self.post_completion_messages}")
                else:
                    logger.info("Workflow completed! Breaking out of conversation loop.")
                    break
                
            if turn % 2 == 0:
                # Student turn - generate response using student agent
                logger.info("Student agent generating response...")
                self.student_state = invoke_student(self.student_state)
                student_msgs = self.student_state["messages"]

                # Get the last message from student
                last_student_msg = student_msgs[-1]
                student_message = last_student_msg.content

                logger.info(f"Student: {student_message}")

                # Store in conversation log
                self.conversation_log.append({
                    "speaker": "student",
                    "message": student_message,
                    "turn": turn + 1
                })

            else:
                # SMS Agent turn - get response from SMS unstructured agent
                logger.info("SMS agent responding...")
                
                # Get the most recent student message
                last_student_message = self.conversation_log[-1]["message"]
                
                # Run the async function in the event loop
                loop = get_or_create_event_loop()
                
                # Call SMS agent with or without workflow_id based on test setup
                if self.workflow_id:
                    # For backward compatibility when workflow_id is explicitly provided
                    sms_result = loop.run_until_complete(
                        sms_unstructured_agent(
                            student_id=self.student_id,
                            message_text=last_student_message,
                            phone_number=self.phone_number,
                            workflow_id=self.workflow_id
                        )
                    )
                else:
                    # Use dynamic workflow selection (default behavior)
                    sms_result = loop.run_until_complete(
                        sms_unstructured_agent(
                            student_id=self.student_id,
                            message_text=last_student_message,
                            phone_number=self.phone_number
                        )
                    )

                # Extract response text from result dict
                sms_response = sms_result["response"]
                workflow_metadata = sms_result["workflow_steps"]

                logger.info(f"SMS Agent: {sms_response}")
                logger.info(f"Workflow metadata: {workflow_metadata}")

                # Store in conversation log
                self.conversation_log.append({
                    "speaker": "sms_agent",
                    "message": sms_response,
                    "turn": turn + 1,
                    "workflow_progress": workflow_metadata
                })

                # Feed SMS agent response back to student agent for next turn
                agent_msg = HumanMessage(content=sms_response)
                student_msgs = self.student_state["messages"]
                student_msgs.append(agent_msg)
                self.student_state["messages"] = student_msgs
            
            # Increment turn counter
            turn += 1
            
            # Increment post-completion message counter if workflow is completed
            if self.workflow_completed and hasattr(self, 'post_completion_messages'):
                self.messages_after_completion += 1
                logger.info(f"Post-completion message count: {self.messages_after_completion}/{self.post_completion_messages}")
            
            # Check completion after each exchange
            if progress_callback and progress_callback():
                if not self.workflow_completed:
                    logger.info("✅ All workflow steps completed through conversation!")
                    self.workflow_completed = True
                    
                # Check if we should continue after completion
                if self.workflow_completed and hasattr(self, 'post_completion_messages'):
                    if self.messages_after_completion >= self.post_completion_messages:
                        logger.info(f"Reached post-completion message limit ({self.post_completion_messages}). Ending conversation.")
                        break
                    else:
                        logger.info(f"Continuing conversation post-completion: {self.messages_after_completion}/{self.post_completion_messages}")
                else:
                    break
        
        return {
            "total_turns": turn,
            "conversation_log": self.conversation_log,
            "student_turns": [entry for entry in self.conversation_log if entry["speaker"] == "student"],
            "sms_turns": [entry for entry in self.conversation_log if entry["speaker"] == "sms_agent"]
        }
    
    def log_conversation_summary(self, results: dict):
        """Log a summary of the conversation"""
        logger.info(f"\n=== Conversation Summary ===")
        logger.info(f"Total turns: {results['total_turns']}")
        logger.info(f"Student messages: {len(results['student_turns'])}")
        logger.info(f"SMS agent messages: {len(results['sms_turns'])}")
        
        logger.info("\n=== Complete Conversation Log ===")
        for entry in self.conversation_log:
            speaker_label = "Student" if entry["speaker"] == "student" else "SMS Agent"
            logger.info(f"{speaker_label} (Turn {entry['turn']}): {entry['message']}")
            if entry["speaker"] == "sms_agent" and "workflow_progress" in entry:
                progress = entry["workflow_progress"]
                logger.info(f"  → Progress: {progress.get('completed_steps', 0)}/{progress.get('total_steps', 0)} steps")
            logger.info("---")
    
    def cleanup(self):
        """Clean up resources"""
        if self.config_id:
            cleanup_student_agent_config(self.config_id)
            logger.info("Student agent configuration cleaned up")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup"""
        self.cleanup()


class RealWorldSMSE2ETestCase(TestCase):
    """
    Real-world end-to-end test case for SMS unstructured agent conversations.
    Uses an existing user (<EMAIL>) and their active workflow.
    No fixtures or mocks - tests the actual production workflow.
    
    Before each test, automatically resets all student workflows to clean state by:
    - Clearing all SMS conversation messages for each workflow
    - Marking all workflow steps as incomplete
    - Setting workflow status to NOT_STARTED
    """
    
    def setUp(self):
        """Set up test environment using existing user and workflow"""
        logger.debug(f"===== Setting up real-world SMS E2E test: {self._testMethodName} =====")
        
        # Get prisma client
        self.prisma = prisma_client()
        logger.debug("Prisma client initialized")
        
        # Create a generated student for this test to avoid parallel execution conflicts
        from addie.data_model.generated_student import GeneratedStudent
        from tests.utils.test_helpers import create_student_agent_config
        
        # Create a student agent config for the test
        config = create_student_agent_config("Test student for SMS real-world E2E test", role="student")
        
        # Generate a test student dynamically
        gen_student = GeneratedStudent(prompt_id=config.prompt_id)
        self.user_id = gen_student.id
        self.student_id = gen_student.student_id
        self.user = self.prisma.user.find_unique(
            where={"id": self.user_id},
            include={"students": True}
        )
        self.student = self.user.students[0] if self.user.students else None
        
        if not self.user:
            self.fail(f"Could not find generated user with ID: {self.user_id}")
        
        if not self.student:
            self.fail(f"Generated user {self.user_id} has no associated students")
        
        logger.info(f"Using existing user: {self.user.email} (ID: {self.user_id})")
        logger.info(f"Using student: {self.student.student_id} (ID: {self.student_id})")
        
        # Create test workflows for the generated student
        self._create_test_workflows()
        
        # Reset all student workflows to clean state for testing
        self._reset_all_student_workflows()
        
        # Verify that unstructured workflows exist for this student
        unstructured_workflows = self.prisma.studentworkflow.find_many(
            where={
                "student_id": self.student_id,
                "workflow": {
                    "workflow_type": "UNSTRUCTURED",
                    "status": "PUBLISHED"
                }
            },
            include={"workflow": True}
        )
        
        if not unstructured_workflows:
            self.fail(f"No unstructured workflows found for student {self.student_id}")
            
        logger.info(f"Found {len(unstructured_workflows)} unstructured workflows for dynamic selection:")
        for wf in unstructured_workflows:
            logger.info(f"  - {wf.workflow.name} (Status: {wf.status}, Mode: {wf.mode})")
            
        # Set placeholder values (will be determined dynamically by the agent)
        self.workflow_id = None  # Will be set by agent
        self.student_workflow_id = None  # Will be set by agent
        self.student_workflow = None  # Will be set by agent
    
    def tearDown(self):
        """Clean up after test - restore original workflow state if needed"""
        logger.debug(f"===== Cleaning up after real-world SMS E2E test: {self._testMethodName} =====")
        
        # Clean up test workflows and data created for this test
        try:
            if hasattr(self, 'created_workflow_ids'):
                for workflow_id in self.created_workflow_ids:
                    # Delete student workflows first
                    self.prisma.studentworkflow.delete_many(
                        where={"workflow_id": workflow_id}
                    )
                    # Delete workflow steps
                    self.prisma.workflowstep.delete_many(
                        where={"parent_workflow_id": workflow_id}
                    )
                    # Delete workflow
                    self.prisma.workflow.delete(
                        where={"id": workflow_id}
                    )
                logger.debug("Cleaned up test workflows")
        except Exception as e:
            logger.warning(f"Could not clean up test workflows: {e}")
            
        # Clean up generated user data
        try:
            if hasattr(self, 'user_id') and self.user_id:
                # Delete student records
                self.prisma.student.delete_many(
                    where={"users": {"some": {"id": self.user_id}}}
                )
                # Delete user
                self.prisma.user.delete(
                    where={"id": self.user_id}
                )
                logger.debug("Cleaned up generated user data")
        except Exception as e:
            logger.warning(f"Could not clean up generated user: {e}")
    
    def _create_test_workflows(self):
        """Create test unstructured workflows for the generated student"""
        from tests.utils.test_helpers import setup_unstructured_sms_workflow
        
        logger.info("Creating test unstructured workflows for generated student...")
        
        self.created_workflow_ids = []
        
        # Create a simple unstructured workflow for testing
        workflow_id, workflow_step_id, student_workflow_id, workflow, step, student_workflow = setup_unstructured_sms_workflow(
            self.prisma, self.user_id, self.student_id, "Test SMS E2E Workflow"
        )
        
        self.created_workflow_ids.append(workflow_id)
        
        logger.info(f"Created test workflow: {workflow.name} (ID: {workflow_id})")
        logger.info(f"Created student workflow: {student_workflow_id}")
    
    def _reset_all_student_workflows(self):
        """Reset all student workflows to clean state for testing"""
        logger.info("Resetting all student workflows to clean state for testing...")
        
        # Find all student workflows for this student
        all_student_workflows = self.prisma.studentworkflow.find_many(
            where={"student_id": self.student_id},
            include={
                "workflow": True,
                "steps": True
            }
        )
        
        logger.info(f"Found {len(all_student_workflows)} student workflows to reset")
        
        for student_workflow in all_student_workflows:
            workflow_id = student_workflow.workflow_id
            student_workflow_id = student_workflow.id
            
            logger.debug(f"Resetting workflow: {student_workflow.workflow.name} (ID: {workflow_id})")
            
            # Generate session ID for this workflow to clear messages
            session_id = get_session_id_for_sms(self.student_id, workflow_id)
            
            # Clear all messages for this workflow session
            deleted_messages = self.prisma.messages.delete_many(
                where={"session_id": session_id}
            )
            logger.debug(f"Deleted {deleted_messages.count if hasattr(deleted_messages, 'count') else 'unknown'} messages for session {session_id}")
            
            # Reset all student workflow steps to incomplete
            updated_steps = self.prisma.studentworkflowstep.update_many(
                where={
                    "student_id": self.student_id,
                    "student_workflow_id": student_workflow_id
                },
                data={"completed": False}
            )
            logger.debug(f"Reset {updated_steps.count if hasattr(updated_steps, 'count') else 'unknown'} steps to incomplete for workflow {workflow_id}")
            
            # Reset the student workflow status to NOT_STARTED (we'll set the active one to SMS mode later)
            self.prisma.studentworkflow.update(
                where={"id": student_workflow_id},
                data={
                    "status": "NOT_STARTED",
                    "mode": "web"  # Reset to web mode initially, will be changed to SMS for the active one
                }
            )
            logger.debug(f"Reset workflow {workflow_id} status to NOT_STARTED and mode to web")
        
        logger.info("Successfully reset all student workflows to clean state")
    
    def get_workflow_progress(self):
        """Get current workflow progress information"""
        try:
            # Dynamically find the current SMS unstructured workflow (including completed ones)
            current_workflow = self.prisma.studentworkflow.find_first(
                where={
                    "student_id": self.student_id,
                    "mode": "sms",
                    "status": {"in": ["IN_PROGRESS", "NOT_STARTED", "COMPLETED"]},
                    "workflow": {
                        "workflow_type": "UNSTRUCTURED",
                        "status": "PUBLISHED"
                    }
                },
                include={
                    "steps": {"include": {"step": True}},
                    "workflow": True
                },
                order={"updated_at": "desc"}
            )
            
            if not current_workflow:
                return {"error": "No active unstructured SMS workflow found"}
            
            total_steps = len(current_workflow.steps)
            completed_steps = len([step for step in current_workflow.steps if step.completed])
            
            return {
                "workflow_id": current_workflow.workflow_id,
                "workflow_name": current_workflow.workflow.name,
                "student_workflow_id": current_workflow.id,
                "status": current_workflow.status,
                "mode": current_workflow.mode,
                "total_steps": total_steps,
                "completed_steps": completed_steps,
                "remaining_steps": total_steps - completed_steps,
                "completion_percentage": (completed_steps / total_steps * 100) if total_steps > 0 else 0,
                "all_steps_completed": completed_steps == total_steps or current_workflow.status == "COMPLETED"
            }
        except Exception as e:
            logger.error(f"Error getting workflow progress: {e}")
            return {"error": str(e)}


class TestRealWorldSMSE2E(RealWorldSMSE2ETestCase):
    """Real-world end-to-end SMS tests using existing user and workflow"""
    
    def test_complete_workflow_conversation(self):
        """
        Test a complete conversation that continues until all workflow steps are completed.
        Uses StudentCounselorConversationRunner to manage realistic agent-to-agent conversations.
        
        The workflow starts in a clean state (all steps incomplete, no previous messages)
        due to the automatic reset performed in setUp().
        """
        
        logger.info("Starting complete workflow conversation test using StudentCounselorConversationRunner")
        
        def is_workflow_completed():
            """Check completion status and determine if conversation should end"""
            try:
                # Check if there are any unstructured workflows still in progress or not started
                active_workflows = self.prisma.studentworkflow.find_many(
                    where={
                        "student_id": self.student_id,
                        "status": {"in": ["IN_PROGRESS", "NOT_STARTED"]},
                        "workflow": {
                            "workflow_type": "UNSTRUCTURED",
                            "status": "PUBLISHED"
                        }
                    },
                    include={"workflow": True}
                )
                
                # If no active workflows remain, all are completed - allow 3 general chat messages
                if not active_workflows:
                    logger.info("All unstructured workflows are completed! Allowing 3 general chat messages.")
                    return True
                    
                # If there are multiple workflows and we've completed some, limit new workflow exploration
                # Allow only 3 messages into a new workflow before stopping the test
                progress = self.get_workflow_progress()
                if progress.get("error"):
                    # No current workflow found, might be in general chat mode
                    logger.info("No current workflow found - may be transitioning or in general chat")
                    return len(active_workflows) == 0  # Complete if no active workflows
                
                current_completed = progress.get("all_steps_completed", False)
                current_status = progress.get("status", "")
                
                # If current workflow is completed, stop the test (don't continue to new workflows)
                if current_completed or current_status == "COMPLETED":
                    logger.info(f"Current workflow completed (status: {current_status}), stopping test")
                    return True
                
                return False  # Still working on current workflow
                
            except Exception as e:
                logger.error(f"Error checking workflow completion: {e}")
                return False

        # Use the conversation runner with context manager for automatic cleanup
        # Note: Not passing workflow_id to use dynamic workflow selection
        with StudentCounselorConversationRunner(
            student_id=self.student_id,
            user_id=self.user_id
        ) as conversation_runner:
            
            # Set up student agent with realistic persona
            conversation_runner.setup_student_agent(
                prompt_content="""
                You are a real high school student who is engaged in a college planning conversation.
                You are motivated and interested in getting help with your college journey.
                Respond naturally and ask follow-up questions when appropriate.
                Keep your responses conversational and authentic, like a real student would text.
                Answer questions thoughtfully and provide details when asked.
                Feel free to ask the counselor if the conversation is complete or do we have pending items to cover.
                Periodically, especially of the back and forth is excessive. you should
                ask the counselor what we need to do to complete the conversation.
                You can ask the counselor to focus on completing the conversation and you
                must do what you can to assist so that they can mark the conversation
                as complete.
                """,
                role="student",
                post_completion_messages=3  # Allow 3 messages after workflow completion
            )
            
            # Run the conversation with workflow completion check
            # Use fewer max turns for efficiency in testing
            results = conversation_runner.run_conversation(
                max_turns=20,
                progress_callback=is_workflow_completed
            )
            
            # Log conversation details
            conversation_runner.log_conversation_summary(results)
            
            # Log post-completion message information
            logger.info(f"\n=== Post-Completion Message Analysis ===")
            logger.info(f"Workflow completed: {conversation_runner.workflow_completed}")
            logger.info(f"Messages after completion: {conversation_runner.messages_after_completion}")
            logger.info(f"Max post-completion messages allowed: {conversation_runner.post_completion_messages}")
            
            # Get final workflow progress
            final_progress = self.get_workflow_progress()
            
            logger.info(f"\n=== Final Workflow Status ===")
            logger.info(f"Student Workflow ID: {final_progress.get('student_workflow_id', 'Unknown')}")
            logger.info(f"Workflow ID: {final_progress.get('workflow_id', 'Unknown')}")
            logger.info(f"Workflow Name: {final_progress.get('workflow_name', 'Unknown')}")
            logger.info(f"Status: {final_progress.get('status', 'Unknown')}")
            logger.info(f"Steps completed: {final_progress.get('completed_steps', 0)}/{final_progress.get('total_steps', 0)}")
            logger.info(f"Completion percentage: {final_progress.get('completion_percentage', 0):.1f}%")
            
            # Verify conversation structure and quality
            self.assertGreaterEqual(len(results["conversation_log"]), 4, "Should have at least 2 exchanges")
            self.assertGreater(len(results["student_turns"]), 0, "Should have student messages")
            self.assertGreater(len(results["sms_turns"]), 0, "Should have SMS agent messages")
            
            # Validate SMS agent responses
            for entry in results["sms_turns"]:
                sms_response = entry["message"]
                self.assertIsNotNone(sms_response)
                self.assertIsInstance(sms_response, str)
                self.assertGreaterEqual(len(sms_response.strip()), 10)
                self.assertNotIn("ToolMessage", sms_response, "Response should not contain tool messages")
                self.assertNotIn("SystemMessage", sms_response, "Response should not contain system messages")
            
            # Verify workflow completion or reasonable progress
            if final_progress.get("error"):
                logger.warning(f"⚠️  Could not get final workflow progress: {final_progress['error']}")
                # Test still passes as long as we had a conversation
            elif final_progress.get("all_steps_completed", False):
                logger.info("✅ Workflow successfully completed through conversation!")
                self.assertEqual(final_progress.get("status"), "COMPLETED")
                self.assertEqual(final_progress.get("completed_steps"), final_progress.get("total_steps"))
            elif final_progress.get("completion_percentage", 0) >= 50:
                logger.info(f"⚠️  Workflow not fully completed but made significant progress ({final_progress.get('completion_percentage', 0):.1f}%)")
                self.assertIn(final_progress.get("status"), ["IN_PROGRESS", "COMPLETED"])
            else:
                logger.info(f"⚠️  Workflow progress: {final_progress.get('completion_percentage', 0):.1f}% - conversation may need more turns")
                self.assertIn(final_progress.get("status"), ["IN_PROGRESS", "COMPLETED", "NOT_STARTED"])
            
            # Ensure we had a meaningful conversation (at least one exchange)
            self.assertGreaterEqual(results["total_turns"], 1, "Should have at least one turn")

            logger.info("Multi-turn SMS conversation with StudentCounselorConversationRunner completed successfully")
    
    def test_workflow_metadata_accuracy(self):
        """Test that the workflow metadata returned by the agent is accurate"""
        
        logger.info("Testing workflow metadata accuracy")
        
        # First, set up at least one workflow in SMS mode for the agent to find
        unstructured_workflows = self.prisma.studentworkflow.find_many(
            where={
                "student_id": self.student_id,
                "workflow": {
                    "workflow_type": "UNSTRUCTURED",
                    "status": "PUBLISHED"
                }
            },
            include={"workflow": True}
        )
        
        if not unstructured_workflows:
            self.fail("No unstructured workflows available for SMS mode setup")
        
        # Set the first unstructured workflow to SMS mode so the agent can find it
        test_workflow = unstructured_workflows[0]
        self.prisma.studentworkflow.update(
            where={"id": test_workflow.id},
            data={
                "mode": "sms",
                "status": "IN_PROGRESS"
            }
        )
        
        logger.info(f"Set workflow '{test_workflow.workflow.name}' to SMS mode for dynamic selection")
        
        # Get initial progress through direct database query
        initial_progress = self.get_workflow_progress()
        
        # Send a message and get metadata
        loop = get_or_create_event_loop()
        result = loop.run_until_complete(
            sms_unstructured_agent(
                student_id=self.student_id,
                message_text="Hello, what's my current progress?",
                phone_number="+15551234567"
            )
        )
        
        # Verify metadata structure
        self.assertIsInstance(result, dict)
        self.assertIn("response", result)
        self.assertIn("student_workflow", result)
        self.assertIn("workflow_id", result)
        self.assertIn("session_id", result)
        self.assertIn("message_count", result)
        self.assertIn("workflow_steps", result)
        self.assertIn("user_id", result)
        self.assertIn("student_id", result)
        
        # Verify metadata values
        # With dynamic workflow selection, the agent should find a valid workflow
        # If no workflow is found, the agent returns None and uses general chat mode
        if result["workflow_id"] is None:
            # This is expected behavior when no active workflow exists - agent uses general chat
            alog.info("No active workflow found - agent is using general chat mode")
            self.assertIn("general_chat", result.get("workflow_steps", {}))
        else:
            # Workflow was found - verify it's valid
            self.assertIsNotNone(result["workflow_id"], "Agent should dynamically find a workflow")
        self.assertEqual(result["student_id"], self.student_id)
        self.assertEqual(result["user_id"], self.user_id)
        self.assertIsNotNone(result["session_id"])
        self.assertGreater(result["message_count"], 0)
        
        # Verify workflow steps metadata
        workflow_steps = result["workflow_steps"]
        self.assertIn("total_steps", workflow_steps)
        self.assertIn("completed_steps", workflow_steps)
        self.assertIn("remaining_steps", workflow_steps)
        self.assertIn("completion_percentage", workflow_steps)
        self.assertIn("all_steps_completed", workflow_steps)
        
        # Verify consistency with database - get current progress after agent ran
        current_progress = self.get_workflow_progress()
        if not current_progress.get("error"):
            self.assertEqual(workflow_steps["total_steps"], current_progress["total_steps"])
            # Note: completed_steps might differ by 1 due to timing of workflow step completion
        
        logger.info(f"Metadata verification passed:")
        logger.info(f"  Total steps: {workflow_steps['total_steps']}")
        logger.info(f"  Completed steps: {workflow_steps['completed_steps']}")
        logger.info(f"  Completion percentage: {workflow_steps['completion_percentage']:.1f}%")
        logger.info(f"  All completed: {workflow_steps['all_steps_completed']}")
    
    def test_api_format(self):
        """Test that the agent returns the expected dictionary format with metadata"""
        
        logger.info("Testing API format")
        
        # Test current API (should return dict with metadata)
        loop = get_or_create_event_loop()
        result = loop.run_until_complete(
            sms_unstructured_agent(
                student_id=self.student_id,
                message_text="Hello, this is an API test",
                phone_number="+15551234567"
            )
        )
        
        # Should return a dict with metadata
        self.assertIsInstance(result, dict)
        self.assertIn("response", result)
        self.assertGreater(len(result["response"].strip()), 0)
        self.assertNotIn("ToolMessage", result["response"])
        
        logger.info(f"API test passed - received dict response with response: {result['response'][:50]}...")
        
        # Test response text extraction
        response_text = result["response"]
        self.assertIsInstance(response_text, str)
        self.assertGreater(len(response_text.strip()), 0)
        
        logger.info(f"Response text extraction test passed: {response_text[:50]}...")

    def test_sms_system_prompt_context_verification(self):
        """Test that SMS agent responses have SystemPromptContext records"""
        
        logger.info("Testing SMS agent SystemPromptContext verification")
        
        from tests.utils.message_system_prompt_helpers import (
            find_ai_messages_by_content,
            assert_message_system_prompt_linkage,
            verify_message_has_system_prompt_context
        )
        
        # Send a message to the SMS agent
        loop = get_or_create_event_loop()
        result = loop.run_until_complete(
            sms_unstructured_agent(
                student_id=self.student_id,
                message_text="Hi, I'm ready to work on my college planning. Can you help me get started?",
                phone_number="+15551234567"
            )
        )
        
        # Verify the response structure
        self.assertIsInstance(result, dict)
        self.assertIn("response", result)
        self.assertIn("session_id", result)
        
        response_text = result["response"]
        session_id = result["session_id"]
        
        logger.info(f"SMS agent response: {response_text[:100]}...")
        logger.info(f"Session ID: {session_id}")
        
        # Find the AI message in the database
        message_ids = find_ai_messages_by_content(response_text[:50], session_id)
        
        self.assertGreater(len(message_ids), 0, 
                         f"Could not find AI message in database for response: {response_text[:50]}")
        
        # Use the most recent message ID
        message_id = message_ids[-1]
        logger.info(f"Found AI message with ID: {message_id}")
        
        # CORE TEST: Verify message → SystemPromptContext linkage
        assert_message_system_prompt_linkage(message_id)
        
        # Verify SystemPromptContext details
        context = verify_message_has_system_prompt_context(message_id, "sms")
        
        # Additional assertions for SMS agent
        self.assertIsNotNone(context["systemPrompt"])
        self.assertGreater(len(context["systemPrompt"]), 50)
        self.assertEqual(context["agentType"], "sms")
        self.assertIsNotNone(context["studentContext"])
        
        logger.info("✅ SMS SystemPromptContext verification completed successfully!")