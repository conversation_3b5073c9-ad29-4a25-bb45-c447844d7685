"""
Test case for unstructured SMS workflow testing.
"""

import json
import alog
import unittest.mock as mock
import pytest
import asyncio
from unittest.mock import patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import Request
from tests.utils.base_test_cases import UnstructuredWorkflowTestCase
from prisma.enums import WorkflowType
from addie.api.sms_routes import route_sms_to_agent
from addie.lib import prisma_client
from prisma.errors import RecordNotFoundError

from tests.utils.mock_workflows import (
    REAL_WORKFLOW_NAME,
    REAL_STEP_GOAL,
    REAL_STEP_NAME,
    create_mock_unstructured_workflow,
)
from addie.sms_agent.agent import sms_unstructured_agent
from addie.api.app import app

# Configure logger
logger = alog.getLogger()


class UnstructuredSMSTestCase(UnstructuredWorkflowTestCase):
    """
    Test case specifically for unstructured SMS workflow tests.
    Uses the UnstructuredWorkflowTestCase base which provides shared fixture functionality.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Configure workflow name suffix for this specific test type
        self._workflow_name_suffix = "Unstructured SMS Test"


# Example test class that uses UnstructuredSMSTestCase
class TestUnstructuredSMSWorkflow(UnstructuredSMSTestCase):
    """Test for unstructured SMS workflows"""

    def test_unstructured_workflow_created(self):
        """Test that the unstructured workflow was created correctly"""
        self.assertIsNotNone(self.workflow_id)
        self.assertEqual(self.workflow.workflow_type, WorkflowType.UNSTRUCTURED)
        self.assertEqual(self.student_workflow.mode, "sms")

    def test_unstructured_workflow_is_sms_mode(self):
        """Test that the unstructured workflow is set to SMS mode"""
        # Verify the workflow type and mode are set correctly
        self.assertIsNotNone(self.workflow_id)
        self.assertEqual(self.workflow.workflow_type, WorkflowType.UNSTRUCTURED)
        self.assertEqual(self.student_workflow.mode, "sms")

        # Verify the student workflow exists and is linked correctly
        student_workflow = self.prisma.studentworkflow.find_first(
            where={
                "student_id": self.student_id,
                "workflow_id": self.workflow_id,
            }
        )
        self.assertIsNotNone(student_workflow)
        self.assertEqual(student_workflow.mode, "sms")
        self.assertEqual(student_workflow.status, "IN_PROGRESS")

    def test_unstructured_agent_is_imported(self):
        """Test that the unstructured SMS agent can be imported"""
        # Verify that the unstructured agent function exists and is callable
        self.assertTrue(callable(sms_unstructured_agent))

        # Verify the agent has the expected signature
        import inspect

        sig = inspect.signature(sms_unstructured_agent)
        params = sig.parameters

        # Check that the function has the expected parameters
        self.assertIn("student_id", params)
        self.assertIn("message_text", params)
        self.assertIn("phone_number", params)
        self.assertIn("workflow_id", params)

    def test_route_sms_to_agent_for_unstructured_workflow(self):
        """Test that the route_sms_to_agent function correctly routes to the unstructured agent"""

        # Get a prisma client
        prisma = prisma_client()

        # Set up test data
        message_text = "Hello from the route test"
        from_number = "+15551234567"

        # Find the workflow to ensure it exists
        workflow = prisma.workflow.find_unique(where={"id": self.workflow_id})
        self.assertIsNotNone(workflow)
        self.assertEqual(workflow.workflow_type, WorkflowType.UNSTRUCTURED)

        # Find the student workflow
        student_workflow = prisma.studentworkflow.find_first(
            where={
                "student_id": self.student_id,
                "workflow_id": self.workflow_id,
            }
        )
        self.assertIsNotNone(student_workflow)

        # Test with mocks for both agent functions
        with patch(
            "addie.api.sms_routes.sms_unstructured_agent"
        ) as mock_unstructured_agent:
            with patch(
                "addie.api.sms_routes.process_sms_message"
            ) as mock_structured_agent:
                # Set up mock responses
                mock_unstructured_response = "Mock unstructured response"
                mock_structured_response = "Mock structured response"
                mock_unstructured_agent.return_value = mock_unstructured_response
                mock_structured_agent.return_value = mock_structured_response

                # Run the async function in a new event loop
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    # Call route_sms_to_agent for unstructured workflow
                    response = loop.run_until_complete(
                        route_sms_to_agent(
                            prisma=prisma,
                            student_id=self.student_id,
                            message_text=message_text,
                            from_number=from_number,
                            workflow_id=self.workflow_id,
                            sms_workflow=student_workflow,
                        )
                    )

                    # For unstructured workflow, verify unstructured agent was called and structured was not
                    mock_unstructured_agent.assert_called_once_with(
                        self.student_id, message_text, from_number, self.workflow_id
                    )
                    mock_structured_agent.assert_not_called()

                    # Verify we got the unstructured response
                    self.assertEqual(response, mock_unstructured_response)

                finally:
                    loop.close()
                    
    def test_route_sms_to_agent_with_real_agent(self):
        """Test that the route_sms_to_agent function correctly routes SMS messages to the unstructured agent"""
        # Get a prisma client
        prisma = prisma_client()

        # Set up test data
        message_text = "Hello from the real agent test"
        from_number = "+15551234567"

        # Make sure the student workflow is set to sms mode and IN_PROGRESS
        student_workflow = prisma.studentworkflow.update(
            where={"id": self.student_workflow_id},
            data={"mode": "sms", "status": "IN_PROGRESS"}
        )
        self.assertIsNotNone(student_workflow)

        alog.info(f'\n## student_workflow_id: {student_workflow.id}')
        
        # We need to also create a studentworkflowstep to avoid the database lookup error
        # First check if one already exists
        existing_step = prisma.studentworkflowstep.find_first(
            where={
                "student_id": self.student_id,
                "student_workflow_id": self.student_workflow_id
            }
        )
        
        if not existing_step:
            # Create a student workflow step linked to our test step
            student_step = prisma.studentworkflowstep.create(
                data={
                    "student": {"connect": {"id": self.student_id}},
                    "step": {"connect": {"id": self.workflow_step_id}},
                    "student_workflow": {"connect": {"id": self.student_workflow_id}},
                    "completed": False,
                    "data": json.dumps({"test": "data"})
                }
            )
            logger.info(f"Created student workflow step with ID: {student_step.id}")
        
        # Run the test in an async event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            response = loop.run_until_complete(
                route_sms_to_agent(
                    prisma=prisma,
                    student_id=self.student_id,
                    message_text=message_text,
                    from_number=from_number,
                    workflow_id=self.workflow_id,
                    sms_workflow=student_workflow,
                )
            )
            
            # If we get here, we didn't get the expected exception
            logger.info(f"Agent returned response: {response}")

        finally:
            loop.close()
            
    def test_completed_workflow_reactivation(self):
        """Test that completed workflows are reactivated when new SMS messages come in"""
        # Get a prisma client
        prisma = prisma_client()

        # Set up test data
        message_text = "Hello after workflow completion"
        from_number = "+15551234567"
        
        # First make sure we have a student workflow step
        existing_step = prisma.studentworkflowstep.find_first(
            where={
                "student_id": self.student_id,
                "student_workflow_id": self.student_workflow_id
            }
        )
        
        if not existing_step:
            # Create a student workflow step linked to our test step
            student_step = prisma.studentworkflowstep.create(
                data={
                    "student": {"connect": {"id": self.student_id}},
                    "step": {"connect": {"id": self.workflow_step_id}},
                    "student_workflow": {"connect": {"id": self.student_workflow_id}},
                    "completed": True,  # Mark as completed
                    "data": json.dumps({"test": "data"})
                }
            )
            logger.info(f"Created completed student workflow step with ID: {student_step.id}")
        else:
            # Update existing step to mark it as completed
            student_step = prisma.studentworkflowstep.update(
                where={"id": existing_step.id},
                data={"completed": True}
            )
            logger.info(f"Updated student workflow step {student_step.id} to completed status")
        
        # Mark the student workflow as COMPLETED
        student_workflow = prisma.studentworkflow.update(
            where={"id": self.student_workflow_id},
            data={"mode": "sms", "status": "COMPLETED"}
        )
        self.assertIsNotNone(student_workflow)
        self.assertEqual(student_workflow.status, "COMPLETED")
        
        alog.info(f"Marked student workflow {student_workflow.id} as COMPLETED")

        # Now test that the agent can handle this completed workflow
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # First verify the student workflow is marked as COMPLETED
            workflow_before = prisma.studentworkflow.find_unique(
                where={"id": self.student_workflow_id}
            )
            self.assertEqual(workflow_before.status, "COMPLETED")
            
            # Run the agent with the completed workflow
            response = loop.run_until_complete(
                route_sms_to_agent(
                    prisma=prisma,
                    student_id=self.student_id,
                    message_text=message_text,
                    from_number=from_number,
                    workflow_id=self.workflow_id,
                    sms_workflow=None,  # Simulate the agent having to find the workflow
                )
            )
            
            # Check if we got a valid response
            self.assertIsNotNone(response)
            logger.info(f"Agent returned response for completed workflow: {response}")
            
            # Verify the workflow was reactivated (status changed back to IN_PROGRESS)
            workflow_after = prisma.studentworkflow.find_unique(
                where={"id": self.student_workflow_id}
            )
            self.assertEqual(workflow_after.status, "IN_PROGRESS")
            logger.info(f"Workflow status after message: {workflow_after.status}")
        except Exception as err:
            logger.error(err)
        finally:
            loop.close()
            
    def test_current_question_hierarchical_relationship(self):
        """Test that current_question in unstructured SMS agent corresponds to correct studentworkflowstep hierarchy"""
        # Get a prisma client
        prisma = prisma_client()
        
        # Import the student context to get current_question
        from addie.data_model.student_context import StudentContext
        
        # Create a StudentContext to get current_question
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Create StudentContext with our test workflow
            student_context = loop.run_until_complete(
                StudentContext(
                    user_id=self.user_id,
                    id=self.student_id,
                    messages=[],
                    mode="sms",
                    _workflow_id=self.workflow_id,
                    grade=11
                ).async_init()
            )
            
            # Trigger step setup to ensure current_question is populated
            loop.run_until_complete(student_context.get_pending_workflow_steps())
            
            # Verify current_question is set
            self.assertIsNotNone(student_context.current_question, "current_question should be set")
            
            current_question = student_context.current_question
            
            # Verify current_question has step_id
            self.assertIn("step_id", current_question, "current_question should have step_id")
            step_id = current_question["step_id"]
            
            # Find the StudentWorkflowStep that current_question references
            student_workflow_step = prisma.studentworkflowstep.find_unique(
                where={"id": step_id},
                include={"step": True, "student_workflow": True}
            )
            
            self.assertIsNotNone(student_workflow_step, f"StudentWorkflowStep with id {step_id} should exist")
            
            # Verify the StudentWorkflowStep belongs to our expected student
            self.assertEqual(
                student_workflow_step.student_id, 
                self.student_id,
                "StudentWorkflowStep should belong to the correct student"
            )
            
            # Verify the StudentWorkflowStep is linked to our expected StudentWorkflow
            self.assertEqual(
                student_workflow_step.student_workflow_id,
                self.student_workflow_id,
                "StudentWorkflowStep should be linked to the correct StudentWorkflow"
            )
            
            # Verify the StudentWorkflow belongs to our expected workflow
            self.assertEqual(
                student_workflow_step.student_workflow.workflow_id,
                self.workflow_id,
                "StudentWorkflow should belong to the correct Workflow"
            )
            
            # Verify the underlying WorkflowStep belongs to our expected workflow
            self.assertEqual(
                student_workflow_step.step.parent_workflow_id,
                self.workflow_id,
                "WorkflowStep should belong to the correct parent Workflow"
            )
            
            # Verify workflow type is UNSTRUCTURED (since this is unstructured SMS test)
            self.assertEqual(
                self.workflow.workflow_type,
                WorkflowType.UNSTRUCTURED,
                "Workflow should be UNSTRUCTURED type"
            )
            
            # Verify the step index is valid (should be >= 0)
            self.assertGreaterEqual(
                student_workflow_step.step.index,
                0,
                "WorkflowStep index should be >= 0"
            )
            
            # Verify the question data consistency
            # The current_question should contain data that matches the underlying step
            self.assertIn("data", current_question, "current_question should have data field")
            
            # Parse the step data to compare
            step_data = json.loads(student_workflow_step.step.data) if isinstance(student_workflow_step.step.data, str) else student_workflow_step.step.data
            current_question_data = current_question.get("data", {})
            
            # Verify the prompt matches (if it exists)
            if step_data and "prompt" in step_data:
                self.assertEqual(
                    current_question_data.get("prompt"),
                    step_data.get("prompt"),
                    "current_question prompt should match step prompt"
                )
            
            logger.info("✓ Test passed: current_question hierarchical relationship validated")
            
        finally:
            loop.close()
            
    def test_workflow_id_consistency_webhook_to_student_context(self):
        """Test that workflow_id from webhook selection matches the one used in StudentContext"""
        # Get a prisma client
        prisma = prisma_client()
        
        # Import needed functions
        from addie.api.sms_routes import get_priority_sms_workflow
        from addie.data_model.student_context import StudentContext
        
        # Set up test data
        message_text = "Test message for workflow ID consistency"
        from_number = "+15551234567"
        
        # Step 1: Get the workflow_id that would be selected by the SMS webhook
        sms_workflow, user_id = get_priority_sms_workflow(self.student_id)
        
        self.assertIsNotNone(sms_workflow, "SMS workflow should be found")
        self.assertEqual(sms_workflow.id, self.student_workflow_id, "SMS workflow should match our test setup")
        
        webhook_selected_workflow_id = sms_workflow.workflow_id
        self.assertEqual(webhook_selected_workflow_id, self.workflow_id, "Webhook should select our test workflow")
        
        # Step 2: Create StudentContext the same way the unstructured SMS agent does
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Create StudentContext with student_workflow_id (as the agent does)
            student_context = loop.run_until_complete(
                StudentContext(
                    user_id=user_id,
                    id=self.student_id,
                    messages=[],
                    mode="sms",
                    _student_workflow_id=self.student_workflow_id
                ).async_init()
            )
            
            # Step 3: Verify StudentContext resolved to the same workflow_id
            student_context_workflow_id = student_context.workflow_id
            
            self.assertEqual(
                webhook_selected_workflow_id,
                student_context_workflow_id,
                f"Workflow ID from webhook ({webhook_selected_workflow_id}) should match StudentContext ({student_context_workflow_id})"
            )
            
            # Step 4: Verify the student_workflow_id consistency
            self.assertEqual(
                student_context.student_workflow_id,
                self.student_workflow_id,
                "StudentContext should use the same student_workflow_id"
            )
            
            # Step 5: Verify workflow type consistency by checking the actual workflow record
            workflow_record = prisma.workflow.find_unique(where={"id": student_context_workflow_id})
            self.assertIsNotNone(workflow_record, "Workflow record should exist")
            self.assertEqual(
                workflow_record.workflow_type,
                WorkflowType.UNSTRUCTURED,
                "Workflow should have UNSTRUCTURED type"
            )
            
            # Step 6: Verify session ID format consistency  
            from addie.sms_agent.agent import get_session_id_for_sms
            expected_session_id = get_session_id_for_sms(self.student_id, webhook_selected_workflow_id)
            expected_session_id_format = f"{webhook_selected_workflow_id}-{user_id}"
            
            self.assertEqual(
                expected_session_id,
                expected_session_id_format,
                "Session ID should follow the expected format"
            )
            
            # Step 7: Verify workflow record consistency (already checked above, but verify it's the same record)
            self.assertEqual(
                workflow_record.id,
                webhook_selected_workflow_id,
                "Workflow record ID should match webhook selection"
            )
            
            logger.info("✓ Test passed: Workflow ID consistency verified from webhook to StudentContext")
            
        finally:
            loop.close()
