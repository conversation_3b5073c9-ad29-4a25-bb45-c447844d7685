"""
Test case for unstructured conversation summary generation.
"""

import json
import alog
import pytest
from unittest.mock import patch, MagicMock
from tests.utils.base_test_cases import UnstructuredWorkflowTestCase
from addie.data_model.gen_unstructured_conversation_summary import GeneratedUnstructuredConversationSummary
from datetime import datetime, timezone

# Configure logger
logger = alog.getLogger()


class TestUnstructuredConversationSummary(UnstructuredWorkflowTestCase):
    """Test for unstructured conversation summary generation with chat messages only"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Configure workflow name suffix for this specific test type
        self._workflow_name_suffix = "Conversation Summary Test"

    def setUp(self):
        """Set up test environment including mock prompt and conversation messages"""
        super().setUp()
        
        # Store test data for cleanup
        self.test_prompt_id = None
        self.test_summary_id = None
        self.test_insight_ids = []
        
        # Create test prompt in database
        self._create_test_prompt()
        
        # Create mock conversation messages
        self._create_mock_conversation_messages()

    def tearDown(self):
        """Clean up test data"""
        # Clean up summary insights first (foreign key constraint)
        for insight_id in self.test_insight_ids:
            try:
                self.prisma.summaryinsights.delete(where={"id": insight_id})
            except Exception as e:
                logger.warning(f"Error deleting insight {insight_id}: {e}")
        
        # Clean up summary
        if self.test_summary_id:
            try:
                self.prisma.generatedunstructuredconversationsummary.delete(
                    where={"id": self.test_summary_id}
                )
            except Exception as e:
                logger.warning(f"Error deleting summary {self.test_summary_id}: {e}")
        
        # Clean up test prompt
        if self.test_prompt_id:
            try:
                self.prisma.prompt.delete(where={"id": self.test_prompt_id})
            except Exception as e:
                logger.warning(f"Error deleting prompt {self.test_prompt_id}: {e}")
        
        # Call parent tearDown
        super().tearDown()

    def _create_test_prompt(self):
        """Create a test prompt with the required tag for summary generation"""
        prompt_content = """
You are an AI assistant that analyzes student conversations and generates summaries.

Please analyze the conversation below and provide insights about the student's progress toward their goals.

For each goal, provide your analysis in the following format:
<goal>Goal description here</goal>
<insight>Your insight about the student's progress toward this goal</insight>

At the end, provide an overall summary:
<summary>Overall summary of the conversation and student progress</summary>
"""
        
        test_prompt = self.prisma.prompt.create(
            data={
                "content": prompt_content,
                "tags": ["default-unstructured-conversation-summary-prompt"],
                "created_at": datetime.now(timezone.utc)
            }
        )
        
        self.test_prompt_id = test_prompt.id
        logger.debug(f"Created test prompt with ID: {self.test_prompt_id}")

    def _create_mock_conversation_messages(self):
        """Create mock conversation messages in the database using the SMS agent's logging function"""
        from addie.sms_agent.agent import log_sms_message_to_history
        
        # Use the composite session ID format: workflowId-userId 
        session_id = f"{self.workflow_id}-{self.user_id}"
        
        # Create conversation messages using the same method as the SMS agent
        conversation_pairs = [
            ("ai", "Hi! I'm Addie, your AI counselor. How are you feeling today?"),
            ("human", "I'm feeling a bit overwhelmed with my college applications."),
            ("ai", "I understand that college applications can feel overwhelming. Can you tell me more about what specifically is causing you stress?"),
            ("human", "I'm not sure which schools to apply to and I'm worried about my essays."),
            ("ai", "Those are very common concerns. Let's work through this together. What are your academic interests and career goals?"),
            ("human", "I'm interested in computer science and want to work in tech eventually.")
        ]
        
        # Log each message using the SMS agent's method
        for message_type, content in conversation_pairs:
            log_sms_message_to_history(
                session_id=session_id,
                message_text=content,
                message_type=message_type,
                source="test"
            )
        
        logger.debug(f"Created {len(conversation_pairs)} mock conversation messages for session: {session_id}")

    def test_summary_generation_with_chat_messages_only(self):
        """Test that summary generation works with only chat message history"""
        
        # Mock the OpenAI response to return predictable output
        mock_response = """
<goal>Help student identify suitable colleges for computer science</goal>
<insight>Student has clear interest in computer science and tech career but needs guidance on school selection</insight>

<goal>Assist with college application essay writing</goal>
<insight>Student expressed anxiety about essay writing but hasn't started the process yet</insight>

<summary>
Student is beginning their college application journey with a clear interest in computer science. 
They are experiencing typical application anxiety, particularly around school selection and essay writing. 
The conversation established their career goals and identified specific areas where they need support.
</summary>
"""
        
        # Patch the generate method directly to avoid complex chain mocking
        def mock_generate(self):
            self.result = mock_response
        
        with patch.object(GeneratedUnstructuredConversationSummary, 'generate', mock_generate):
            
            # Create and run the summary generator
            summary_generator = GeneratedUnstructuredConversationSummary(
                student_id=self.student_id,
                workflow_id=self.workflow_id
            )
            
            # Verify the summary was generated
            self.assertIsNotNone(summary_generator.result)
            self.assertEqual(summary_generator.result, mock_response)
            
            # Store summary ID for cleanup
            summary_record = self.prisma.generatedunstructuredconversationsummary.find_first(
                where={
                    "student_id": self.student_id,
                    "workflow_id": self.workflow_id
                }
            )
            self.test_summary_id = summary_record.id

    def test_summary_and_insights_saved_to_database(self):
        """Test that both summary content and insights are properly saved to database"""
        
        # Mock response with clear goal/insight pairs
        mock_response = """
<goal>College selection guidance</goal>
<insight>Student needs help identifying schools that match their interests</insight>

<goal>Essay writing support</goal>
<insight>Student has essay anxiety but clear academic direction</insight>

<summary>
Productive initial conversation establishing student's goals and concerns.
</summary>
"""
        
        # Patch the generate method directly to avoid complex chain mocking
        def mock_generate(self):
            self.result = mock_response
        
        with patch.object(GeneratedUnstructuredConversationSummary, 'generate', mock_generate):
            
            # Generate summary
            summary_generator = GeneratedUnstructuredConversationSummary(
                student_id=self.student_id,
                workflow_id=self.workflow_id
            )
            
            # Verify summary was saved to database
            summary_record = self.prisma.generatedunstructuredconversationsummary.find_first(
                where={
                    "student_id": self.student_id,
                    "workflow_id": self.workflow_id
                }
            )
            
            self.assertIsNotNone(summary_record)
            self.assertEqual(summary_record.student_id, self.student_id)
            self.assertEqual(summary_record.workflow_id, self.workflow_id)
            self.assertIn("Productive initial conversation", summary_record.content)
            
            self.test_summary_id = summary_record.id
            
            # Verify insights were extracted and saved
            insights = self.prisma.summaryinsights.find_many(
                where={"summary_id": summary_record.id}
            )
            
            self.assertEqual(len(insights), 2)
            
            # Store insight IDs for cleanup
            self.test_insight_ids = [insight.id for insight in insights]
            
            # Verify the content of the insights
            goals = [insight.goal for insight in insights]
            insight_texts = [insight.insight for insight in insights]
            
            self.assertIn("College selection guidance", goals)
            self.assertIn("Essay writing support", goals)
            self.assertIn("Student needs help identifying schools", " ".join(insight_texts))
            self.assertIn("essay anxiety", " ".join(insight_texts))

    def test_context_data_collection_from_messages_only(self):
        """Test that context data is properly collected from chat messages and workflow data"""
        
        # Create a summary generator instance (without calling generate)
        generator = GeneratedUnstructuredConversationSummary.__new__(GeneratedUnstructuredConversationSummary)
        generator.student_id = self.student_id
        generator.workflow_id = self.workflow_id
        generator.prisma = self.prisma
        
        # Get the context data
        context_json = generator.context
        context_data = json.loads(context_json)
        
        # Verify all required data is present
        self.assertIn('conversation', context_data)
        self.assertIn('workflow_name', context_data)
        self.assertIn('workflow_status', context_data)
        self.assertIn('workflow_goals', context_data)
        self.assertIn('student_steps', context_data)
        self.assertIn('first_name', context_data)
        self.assertIn('last_name', context_data)
        
        # Verify conversation contains our mock messages
        conversation = context_data['conversation']
        self.assertGreater(len(conversation), 0)
        
        # Check that our test messages are in the conversation
        conversation_text = json.dumps(conversation)
        self.assertIn('college applications', conversation_text)
        self.assertIn('computer science', conversation_text)
        
        # Verify workflow data
        self.assertIn('Test', context_data['workflow_name'])
        self.assertEqual(context_data['workflow_status'], 'IN_PROGRESS')

    def test_error_handling_for_missing_prompt(self):
        """Test that appropriate error is raised when prompt is missing"""
        
        # Store existing prompts to restore later (including the one created in setUp)
        existing_prompts = self.prisma.prompt.find_many(
            where={
                "tags": {"has": "default-unstructured-conversation-summary-prompt"}
            }
        )
        
        # Delete ALL prompts with this tag to ensure none are found
        deleted_count = self.prisma.prompt.delete_many(
            where={
                "tags": {"has": "default-unstructured-conversation-summary-prompt"}
            }
        )
        
        logger.debug(f"Deleted {deleted_count} prompts with the required tag")
        
        # Verify no prompts exist with this tag
        remaining_prompts = self.prisma.prompt.find_many(
            where={
                "tags": {"has": "default-unstructured-conversation-summary-prompt"}
            }
        )
        self.assertEqual(len(remaining_prompts), 0, "Should have deleted all prompts with the tag")
        
        try:
            # Attempt to generate summary should raise ValueError
            # Do NOT mock the generate method here - we want the real error to be raised
            with self.assertRaises(ValueError) as context:
                GeneratedUnstructuredConversationSummary(
                    student_id=self.student_id,
                    workflow_id=self.workflow_id
                )
            
            self.assertIn("No prompt found with tag", str(context.exception))
            self.assertIn("default-unstructured-conversation-summary-prompt", str(context.exception))
        
        finally:
            # Restore the existing prompts to avoid breaking other tests
            for prompt in existing_prompts:
                restored_prompt = self.prisma.prompt.create(
                    data={
                        "content": prompt.content,
                        "tags": prompt.tags,
                        "created_at": prompt.created_at
                    }
                )
                # If this was our test prompt, update the ID for cleanup
                if prompt.id == self.test_prompt_id:
                    self.test_prompt_id = restored_prompt.id
        
        logger.debug(f"Restored {len(existing_prompts)} prompts after error test")