"""
Test for disabled user handling in SMS webhook.

This test verifies that when a user is disabled, the SMS webhook returns a helpful message
directing them to contact their counselor, admin, or support.
"""

import uuid
import alog
from unittest import TestCase
from unittest.mock import patch, AsyncMock
from addie.api.sms_routes import sms_webhook
from addie.data_model.generated_student import GeneratedStudent
from addie.lib import prisma_client
from fastapi import Request
from fastapi.datastructures import FormData
from tests.utils.test_helpers import create_student_agent_config, setup_unstructured_sms_workflow, cleanup_workflow_test_data

# Configure logger
logger = alog.getLogger()


class TestDisabledUserSMS(TestCase):
    """Test SMS webhook behavior for disabled users"""
    
    def setUp(self):
        """Set up test user and disable them"""
        # Create a student agent config for the test
        config = create_student_agent_config("Test disabled user SMS handling", role="student")
        
        # Generate a test student dynamically
        self.gen_student = GeneratedStudent(prompt_id=config.prompt_id)
        self.user_id = self.gen_student.id
        self.student_id = self.gen_student.student_id
        
        # Create a unique phone number for this test instance to avoid conflicts in parallel execution
        test_suffix = str(uuid.uuid4())[:8]
        self.test_phone = f"+1555{test_suffix[:7]}"  # Create unique test phone number
        
        # Get prisma client
        self.prisma = prisma_client()
        
        # Update the user's phone number to the unique one
        updated_user = self.prisma.user.update(
            where={"id": self.user_id},
            data={"phone_number": self.test_phone}
        )
        
        # Verify the phone number was updated
        self.assertEqual(updated_user.phone_number, self.test_phone, 
                        "Phone number should be updated successfully")
        
        # Clear any existing message history to ensure clean state
        try:
            from addie.history import get_history
            disabled_session_id = self.user_id
            history = get_history(disabled_session_id)
            history.clear()
            logger.debug(f"Cleared message history in setUp for session: {disabled_session_id}")
        except Exception as e:
            logger.warning(f"Error clearing message history in setUp: {e}")
        
        # Disable the test user
        self.prisma.user.update(
            where={"id": self.user_id},
            data={"enabled": False}
        )
        
        # Verify user is disabled
        disabled_user = self.prisma.user.find_unique(where={"id": self.user_id})
        self.assertFalse(disabled_user.enabled, "User should be disabled for this test")
        
        logger.debug(f"Test setup complete: user_id={self.user_id}, phone={self.test_phone}, enabled={disabled_user.enabled}")
        
    def tearDown(self):
        """Re-enable user and clean up"""
        # Clean up message history for disabled user sessions
        try:
            from addie.history import get_history
            disabled_session_id = self.user_id
            history = get_history(disabled_session_id)
            history.clear()  # Clear message history
            logger.debug(f"Cleared message history for session: {disabled_session_id}")
        except Exception as e:
            logger.warning(f"Error clearing disabled user message history: {e}")
        
        # Note: GeneratedStudent cleanup is handled automatically
    
    def _verify_disabled_user_messages_logged(self):
        """Helper method to verify that disabled user messages are logged to the messages table"""
        from addie.history import get_history
        
        # Check that messages were logged for the disabled user session
        disabled_session_id = self.user_id
        history = get_history(disabled_session_id)
        
        # Get messages from the session
        messages = history.messages
        
        # Should have at least 2 messages: incoming (human) and outgoing (ai)
        self.assertGreaterEqual(len(messages), 2, "Should have logged both incoming and outgoing messages")
        
        # Verify incoming message (human)
        incoming_messages = [msg for msg in messages if msg.type == "human"]
        self.assertGreater(len(incoming_messages), 0, "Should have logged incoming human message")
        
        # Verify outgoing message (ai)
        outgoing_messages = [msg for msg in messages if msg.type == "ai"]
        self.assertGreater(len(outgoing_messages), 0, "Should have logged outgoing AI message")
        
        # Verify content of outgoing message
        latest_ai_message = outgoing_messages[-1]
        self.assertIn("Your account is currently disabled", latest_ai_message.content)
        self.assertIn("<EMAIL>", latest_ai_message.content)
        
        logger.info(f"Verified {len(messages)} messages logged for disabled user session {disabled_session_id}")
    
    def test_disabled_user_receives_helpful_message(self):
        """Test that disabled users get a helpful message instead of being ignored"""
        
        # Clear any existing message history before the test to avoid pollution
        from addie.history import get_history
        disabled_session_id = self.user_id
        history = get_history(disabled_session_id)
        history.clear()
        
        # Mock the FastAPI Request object
        mock_request = AsyncMock(spec=Request)
        mock_request.form = AsyncMock(return_value=FormData([
            ("From", self.test_phone),
            ("Body", "Hello, I want to start a conversation")
        ]))
        mock_request.headers = {
            "x-skip-twilio-validation": "true"  # Skip Twilio validation for testing
        }
        
        # Mock Twilio SMS sending (though it shouldn't be called for disabled users)
        with patch("addie.sms_agent.agent.send_sms_via_twilio") as mock_send_sms:
            mock_send_sms.return_value = True
            
            # Call the SMS webhook
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                response = loop.run_until_complete(sms_webhook(mock_request))
                
                # Verify response contains helpful message for disabled user
                self.assertEqual(response.media_type, "application/xml")
                self.assertIn("Your account is currently disabled", response.body.decode())
                self.assertIn("contact your counselor, admin, or <EMAIL>", response.body.decode())
                
                # Verify the response is properly formatted TwiML
                self.assertIn("<?xml version='1.0' encoding='UTF-8'?>", response.body.decode())
                self.assertIn("<Response>", response.body.decode())
                self.assertIn("<Message>", response.body.decode())
                self.assertIn("</Message>", response.body.decode())
                self.assertIn("</Response>", response.body.decode())
                
                logger.info(f"Disabled user SMS response: {response.body.decode()}")
                
                # Verify SMS sending was not called (no workflow processing)
                mock_send_sms.assert_not_called()
                
                # Verify that both incoming and outgoing messages were logged
                self._verify_disabled_user_messages_logged()
                
            finally:
                loop.close()
    
    def test_disabled_user_response_content(self):
        """Test the exact content of the disabled user response"""
        
        # Clear any existing message history before the test to avoid pollution
        from addie.history import get_history
        disabled_session_id = self.user_id
        history = get_history(disabled_session_id)
        history.clear()
        
        # Mock the FastAPI Request object
        mock_request = AsyncMock(spec=Request)
        mock_request.form = AsyncMock(return_value=FormData([
            ("From", self.test_phone),
            ("Body", "test message")
        ]))
        mock_request.headers = {
            "x-skip-twilio-validation": "true"
        }
        
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            response = loop.run_until_complete(sms_webhook(mock_request))
            
            expected_message = "Your account is currently disabled. Please contact your counselor, admin, or <EMAIL> to enable your account."
            
            # Verify exact message content
            response_body = response.body.decode()
            self.assertIn(expected_message, response_body)
            
            # Verify TwiML structure
            expected_twiml = f"<?xml version='1.0' encoding='UTF-8'?><Response><Message>{expected_message}</Message></Response>"
            self.assertEqual(response_body, expected_twiml)
            
        finally:
            loop.close()
    
    def test_disabled_user_messages_are_logged(self):
        """Test that disabled user SMS messages are properly logged to messages table"""
        
        # Clear any existing message history before the test to avoid pollution
        from addie.history import get_history
        disabled_session_id = self.user_id
        history = get_history(disabled_session_id)
        history.clear()
        
        test_message = "Help me with college planning"
        
        # Mock the FastAPI Request object
        mock_request = AsyncMock(spec=Request)
        mock_request.form = AsyncMock(return_value=FormData([
            ("From", self.test_phone),
            ("Body", test_message)
        ]))
        mock_request.headers = {
            "x-skip-twilio-validation": "true"
        }
        
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Call the SMS webhook
            response = loop.run_until_complete(sms_webhook(mock_request))
            
            # Verify response is correct
            self.assertIn("Your account is currently disabled", response.body.decode())
            
            # Verify messages were logged
            messages = history.messages
            
            # Should have at least 2 messages: incoming and outgoing
            self.assertGreaterEqual(len(messages), 2, "Should have logged at least 2 messages")
            
            # Find the latest human and AI messages from this test
            human_messages = [msg for msg in messages if msg.type == "human"]
            ai_messages = [msg for msg in messages if msg.type == "ai"]
            
            # Verify we have the expected messages
            self.assertGreater(len(human_messages), 0, "Should have human messages")
            self.assertGreater(len(ai_messages), 0, "Should have AI messages")
            
            # Verify the latest human message content matches what we sent
            latest_human = human_messages[-1]
            self.assertEqual(latest_human.content, test_message, 
                           f"Expected '{test_message}', but got '{latest_human.content}'")
            
            # Verify the latest AI message content
            latest_ai = ai_messages[-1]
            self.assertIn("Your account is currently disabled", latest_ai.content)
            self.assertIn("<EMAIL>", latest_ai.content)
            
            logger.info(f"Successfully verified message logging for disabled user session: {disabled_session_id}")
            logger.info(f"Human message: '{latest_human.content}'")
            logger.info(f"AI message: '{latest_ai.content}'")
            
        finally:
            loop.close()
    
    def test_enabled_user_still_works_normally(self):
        """Test that enabled users still work normally (regression test)"""
        
        # Clear any existing message history before the test to avoid pollution
        from addie.history import get_history
        disabled_session_id = self.user_id
        history = get_history(disabled_session_id)
        history.clear()
        
        # Re-enable the user for this test
        self.prisma.user.update(
            where={"id": self.user_id},
            data={"enabled": True}
        )
        
        # Create a workflow for the enabled user
        from tests.utils.test_helpers import setup_unstructured_sms_workflow
        
        try:
            workflow_id, workflow_step_id, student_workflow_id, workflow, step, student_workflow = setup_unstructured_sms_workflow(
                self.prisma, self.user_id, self.student_id, "Enabled User Test"
            )
            
            # Mock the FastAPI Request object
            mock_request = AsyncMock(spec=Request)
            mock_request.form = AsyncMock(return_value=FormData([
                ("From", self.test_phone),
                ("Body", "Hello")
            ]))
            mock_request.headers = {
                "x-skip-twilio-validation": "true"
            }
            
            # Mock Twilio SMS sending
            with patch("addie.sms_agent.agent.send_sms_via_twilio") as mock_send_sms:
                mock_send_sms.return_value = True
                
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    response = loop.run_until_complete(sms_webhook(mock_request))
                    
                    # Should get a normal agent response, not a disabled user message
                    response_body = response.body.decode()
                    self.assertNotIn("Your account is currently disabled", response_body)
                    
                    # Should be TwiML with an actual agent response
                    self.assertIn("<?xml version='1.0' encoding='UTF-8'?>", response_body)
                    self.assertIn("<Response>", response_body)
                    self.assertIn("<Message>", response_body)
                    
                    logger.info(f"Enabled user SMS response: {response_body}")
                    
                finally:
                    loop.close()
        
        finally:
            # Clean up the workflow
            try:
                from tests.utils.test_helpers import cleanup_workflow_test_data
                cleanup_workflow_test_data(self.prisma, workflow_id, workflow_step_id, student_workflow_id)
            except Exception as e:
                logger.warning(f"Error cleaning up enabled user test workflow: {e}")
            
            # Disable the user again for tearDown
            self.prisma.user.update(
                where={"id": self.user_id},
                data={"enabled": False}
            )