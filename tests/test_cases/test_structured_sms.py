"""
Test case for structured SMS workflow testing.
"""

import json
import alog
import asyncio
from unittest.mock import patch, AsyncMock
from tests.utils.base_test_cases import StructuredWorkflowTestCase
from prisma.enums import WorkflowType
from tests.utils.mock_workflows import (
    mock_structured_workflow
)
from addie.sms_agent.agent import process_sms_message, sms_unstructured_agent
from addie.api.sms_routes import route_sms_to_agent
from addie.lib import prisma_client


# Configure logger
logger = alog.getLogger()

class StructuredSMSTestCase(StructuredWorkflowTestCase):
    """
    Test case specifically for structured SMS workflow tests.
    Uses the StructuredWorkflowTestCase base which provides shared fixture functionality.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Configure workflow name suffix for this specific test type
        self._workflow_name_suffix = "Structured SMS Test"

# Example test class that uses StructuredSMSTestCase
class TestStructuredSMSWorkflow(StructuredSMSTestCase):
    """Test for structured SMS workflows"""
    
    def test_structured_workflow_created(self):
        """Test that the structured workflow was created correctly"""
        self.assertIsNotNone(self.workflow_id)
        self.assertEqual(self.workflow.workflow_type, WorkflowType.STRUCTURED)
        self.assertEqual(self.student_workflow.mode, "sms")
        
    def test_big5_inventory_workflow(self):
        """Test that the Big 5 Inventory workflow is used by default"""
        self.assertIn("Big 5 Inventory", self.workflow.name)
        self.assertIn("survey_type", self.workflow.data)
        
        # Parse the workflow data
        workflow_data = json.loads(self.workflow.data) if isinstance(self.workflow.data, str) else self.workflow.data
        self.assertEqual("likert", workflow_data["survey_type"])
    
    def test_process_sms_message_is_imported(self):
        """Test that the structured SMS agent can be imported"""
        # Verify that the structured agent function exists and is callable
        self.assertTrue(callable(process_sms_message))
        
        # Verify the agent has the expected signature
        import inspect
        sig = inspect.signature(process_sms_message)
        params = sig.parameters
        
        # Check that the function has the expected parameters
        self.assertIn("student_id", params)
        self.assertIn("message_text", params)
        self.assertIn("phone_number", params)
        self.assertIn("workflow_id", params)
    
    def test_route_sms_to_agent_for_structured_workflow(self):
        """Test that the route_sms_to_agent function correctly routes to the structured agent"""
        # Get a prisma client
        # Use the prisma client from the base class
        
        # Set up test data
        message_text = "Hello from the structured route test"
        from_number = "+15551234567"
        
        # Find the workflow to ensure it exists
        workflow = self.prisma.workflow.find_unique(
            where={"id": self.workflow_id}
        )
        self.assertIsNotNone(workflow)
        self.assertEqual(workflow.workflow_type, WorkflowType.STRUCTURED)
        
        # Find the student workflow
        student_workflow = self.prisma.studentworkflow.find_first(
            where={
                "student_id": self.student_id,
                "workflow_id": self.workflow_id,
            }
        )
        self.assertIsNotNone(student_workflow)
        
        # Test with mocks for both agent functions
        with patch("addie.api.sms_routes.sms_unstructured_agent") as mock_unstructured_agent:
            with patch("addie.api.sms_routes.process_sms_message") as mock_structured_agent:
                # Set up mock responses
                mock_unstructured_response = "Mock unstructured response"
                mock_structured_response = "Mock structured response"
                mock_unstructured_agent.return_value = mock_unstructured_response
                mock_structured_agent.return_value = mock_structured_response
                
                # Run the async function in a new event loop
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    # Call route_sms_to_agent for structured workflow
                    response = loop.run_until_complete(
                        route_sms_to_agent(
                            prisma=self.prisma,
                            student_id=self.student_id, 
                            message_text=message_text, 
                            from_number=from_number, 
                            workflow_id=self.workflow_id,
                            sms_workflow=student_workflow
                        )
                    )
                    
                    # For structured workflow, verify structured agent was called and unstructured was not
                    mock_structured_agent.assert_called_once_with(
                        self.student_id, 
                        message_text, 
                        from_number, 
                        self.workflow_id
                    )
                    mock_unstructured_agent.assert_not_called()
                    
                    # Verify we got the structured response
                    self.assertEqual(response, mock_structured_response)
                    
                finally:
                    loop.close()
                    
    def test_route_sms_to_agent_with_real_agent(self):
        """Test that the route_sms_to_agent function correctly routes SMS messages to the structured agent"""
        # Set up test data
        message_text = "Hello from the real structured agent test"
        from_number = "+15551234567"

        # Mock only Twilio SMS sending to avoid actual SMS costs
        with patch("addie.sms_agent.agent.send_sms_via_twilio") as mock_send_sms:
            mock_send_sms.return_value = True
            
            # Run the test in an async event loop
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # Call route_sms_to_agent - let it use real agent logic
                response = loop.run_until_complete(
                    route_sms_to_agent(
                        prisma=self.prisma,
                        student_id=self.student_id,
                        message_text=message_text,
                        from_number=from_number,
                        workflow_id=self.workflow_id,
                        sms_workflow=self.student_workflow,
                    )
                )
                
                # Verify we got a response from the real agent
                logger.info(f"Agent returned response: {response}")
                self.assertIsNotNone(response)
                self.assertTrue(len(response) > 0)

            finally:
                loop.close()
            
    def test_multiple_steps_workflow_navigation(self):
        """Test navigating through multiple steps in a structured workflow with the real agent"""
        # Set up test data
        from_number = "+15551234567"

        # Mock only Twilio SMS sending
        with patch("addie.sms_agent.agent.send_sms_via_twilio") as mock_send_sms:
            mock_send_sms.return_value = True
            
            # Create an event loop for async operations
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Send a first message to the workflow - should trigger first question
                first_message = "start"
                first_response = loop.run_until_complete(
                    route_sms_to_agent(
                        prisma=self.prisma,
                        student_id=self.student_id,
                        message_text=first_message,
                        from_number=from_number,
                        workflow_id=self.workflow_id,
                        sms_workflow=self.student_workflow,
                    )
                )
                logger.info(f"First response: {first_response}")
                self.assertIsNotNone(first_response)
                
                # Send a response to the first question - typically a Likert scale answer like 3 or 4
                second_message = "4"
                second_response = loop.run_until_complete(
                    route_sms_to_agent(
                        prisma=self.prisma,
                        student_id=self.student_id,
                        message_text=second_message,
                        from_number=from_number,
                        workflow_id=self.workflow_id,
                        sms_workflow=self.student_workflow,
                    )
                )
                logger.info(f"Second response: {second_response}")
                self.assertIsNotNone(second_response)
                
                # Verify that we now have at least one question response in the database
                question_responses = self.prisma.questionresponse.find_many(
                    where={
                        "student_id": self.student_id
                    }
                )
                self.assertTrue(len(question_responses) > 0, "Expected at least one question response in database")
                
                # Verify the response data contains the answer we provided
                found_value = False
                for response in question_responses:
                    if response.response_data:
                        # Parse the response data - it might be a JSON string or already a dict
                        response_data = response.response_data
                        if isinstance(response_data, str):
                            try:
                                response_data = json.loads(response_data)
                            except json.JSONDecodeError:
                                # Not valid JSON, skip
                                continue
                        
                        # Check if it contains our answer value
                        if isinstance(response_data, dict) and "value" in response_data:
                            value = response_data['value']
                            logger.info(f"Found response value: {value}")
                            # Value could be "4", 4, or "Strongly Agree" depending on the question format
                            # Check if the value matches our expected answer (convert to string for comparison)
                            if str(value) == second_message or value == "Strongly Agree":
                                found_value = True
                                break
                
                # Log if we found a matching value
                logger.info(f"Found matching response value: {found_value}")
                
            finally:
                loop.close()
    
    def test_completed_workflow_handling(self):
        """Test that completed structured workflows are correctly handled"""
        # Set up test data
        message_text = "Hello after completing the structured workflow"
        from_number = "+15551234567"
        
        # Mark all student workflow steps as completed
        student_steps = self.prisma.studentworkflowstep.find_many(
            where={
                "student_id": self.student_id,
                "student_workflow_id": self.student_workflow_id
            }
        )
        
        for step in student_steps:
            self.prisma.studentworkflowstep.update(
                where={"id": step.id},
                data={"completed": True}
            )
        
        # Mark the student workflow as COMPLETED
        student_workflow = self.prisma.studentworkflow.update(
            where={"id": self.student_workflow_id},
            data={"status": "COMPLETED"}
        )
        self.assertIsNotNone(student_workflow)
        self.assertEqual(student_workflow.status, "COMPLETED")
        
        logger.info(f"Marked student workflow {student_workflow.id} as COMPLETED")

        # Mock only Twilio SMS sending
        with patch("addie.sms_agent.agent.send_sms_via_twilio") as mock_send_sms:
            mock_send_sms.return_value = True
            
            # Now test sending a message to the completed workflow
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # Run the agent with the completed workflow
                response = loop.run_until_complete(
                    route_sms_to_agent(
                        prisma=self.prisma,
                        student_id=self.student_id,
                        message_text=message_text,
                        from_number=from_number,
                        workflow_id=self.workflow_id,
                        sms_workflow=None,  # Simulate the agent having to find the workflow
                    )
                )
                
                # Check if we got a valid response
                self.assertIsNotNone(response)
                logger.info(f"Agent returned response for completed workflow: {response}")
                
                # Check the workflow status after message
                workflow_after = self.prisma.studentworkflow.find_unique(
                    where={"id": self.student_workflow_id}
                )
                logger.info(f"Workflow status after message to completed workflow: {workflow_after.status}")
                
            finally:
                loop.close()