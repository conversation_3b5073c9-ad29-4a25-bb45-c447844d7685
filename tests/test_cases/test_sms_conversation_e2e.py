"""
End-to-end conversation test for SMS unstructured agent.

This test simulates a complete back-and-forth conversation between a student 
and the SMS unstructured agent, similar to the questionnaire workflow test.
"""

import json
import alog
import asyncio
import unittest.mock as mock
import uuid
from unittest.mock import patch, AsyncMock
from tests.utils.base_test_cases import UnstructuredWorkflowTestCase
from prisma.enums import WorkflowType
from addie.sms_agent.agent import sms_unstructured_agent
from addie.lib import prisma_client
from addie.student_agent.invoke_student import invoke_student
from langchain_core.messages import HumanMessage
from tests.utils.test_helpers import (
    create_student_agent_with_state,
    cleanup_student_agent_config,
)
from tests.utils.mock_workflows import (
    REAL_WORKFLOW_NAME,
    REAL_STEP_GOAL,
    REAL_STEP_NAME,
    create_mock_unstructured_workflow,
)

# Configure logger
logger = alog.getLogger()


class SMSConversationE2ETestCase(UnstructuredWorkflowTestCase):
    """
    End-to-end test case for SMS unstructured agent conversations.
    Uses the UnstructuredWorkflowTestCase base which provides shared fixture functionality.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Configure workflow name suffix for this specific test type
        self._workflow_name_suffix = "Conversation Test"
    
    def setUp(self):
        """Set up test environment and get student step reference for compatibility"""
        super().setUp()
        
        # Get student step reference for compatibility with existing test methods
        self.student_step = self.prisma.studentworkflowstep.find_first(
            where={
                "student_id": self.student_id,
                "student_workflow_id": self.student_workflow_id
            }
        )


class TestSMSConversationE2E(SMSConversationE2ETestCase):
    """Test complete SMS conversations with the unstructured agent"""

    def test_multi_turn_sms_conversation_with_student_agent(self):
        """Test a complete back-and-forth SMS conversation using student agent to generate responses"""
        
        # Ensure we have a fresh event loop for this test
        try:
            # Close any existing loop to ensure clean state
            try:
                existing_loop = asyncio.get_event_loop()
                if not existing_loop.is_closed():
                    existing_loop.close()
            except RuntimeError:
                pass  # No existing loop to close
            
            # Create a new event loop for this test
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
        except Exception as e:
            logger.warning(f"Error setting up event loop: {e}")
            # Fallback: create new loop anyway
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        try:
            # Create student agent with state using helper function
            student_agent, student_state, config_id = create_student_agent_with_state(
                student_id=self.student_id,
                user_id=self.user_id,
                prompt_content="""
                You are a curious 17-year-old high school junior who needs help with college planning.
                You're interested in STEM fields, particularly engineering and computer science.
                Ask thoughtful questions about colleges, programs, and requirements.
                Keep your responses natural and conversational, like a real student would text.
                """,
                role="student"
            )

            phone_number = "+15551234567"
            conversation_log = []
            max_turns = 50  # Maximum number of back-and-forth exchanges (safety limit)
            turn = 0

            logger.info("Starting multi-turn SMS conversation with student agent")

            def is_workflow_completed():
                """Check if the student workflow is completed"""
                try:
                    current_workflow = self.prisma.studentworkflow.find_unique(
                        where={"id": self.student_workflow_id}
                    )
                    return current_workflow and current_workflow.status == "COMPLETED"
                except Exception as e:
                    logger.warning(f"Error checking workflow completion: {e}")
                    return False

            def get_workflow_progress():
                """Get detailed workflow progress information"""
                try:
                    current_workflow = self.prisma.studentworkflow.find_unique(
                        where={"id": self.student_workflow_id},
                        include={"steps": True}
                    )
                    if not current_workflow:
                        return {"total_steps": 0, "completed_steps": 0, "status": "NOT_FOUND"}
                    
                    total_steps = len(current_workflow.steps)
                    completed_steps = len([step for step in current_workflow.steps if step.completed])
                    
                    return {
                        "total_steps": total_steps,
                        "completed_steps": completed_steps,
                        "status": current_workflow.status,
                        "completion_percentage": (completed_steps / total_steps * 100) if total_steps > 0 else 0
                    }
                except Exception as e:
                    logger.warning(f"Error getting workflow progress: {e}")
                    return {"total_steps": 0, "completed_steps": 0, "status": "ERROR"}

            try:
                # Start with an initial prompt to the student agent
                initial_prompt = HumanMessage(content="Hi! I heard you can help with college planning. I'm a junior in high school and feeling overwhelmed about the whole process. Can you help me?")
                
                student_msgs = [initial_prompt]
                student_state["messages"] = student_msgs

                while turn < max_turns and not is_workflow_completed():
                    logger.info(f"\n--- Turn {turn + 1} ---")
                    
                    # Check workflow completion before each turn
                    if is_workflow_completed():
                        logger.info("Workflow completed! Breaking out of conversation loop.")
                        break

                    if turn % 2 == 0:
                        # Student turn - generate response using student agent
                        logger.info("Student agent generating response...")
                        student_state = invoke_student(student_state)
                        student_msgs = student_state["messages"]

                        # Get the last message from student
                        last_student_msg = student_msgs[-1]
                        student_message = last_student_msg.content

                        logger.info(f"Student: {student_message}")

                        # Store in conversation log
                        conversation_log.append({
                            "speaker": "student",
                            "message": student_message,
                            "turn": turn + 1
                        })

                    else:
                        # SMS Agent turn - get response from SMS unstructured agent
                        logger.info("SMS agent responding...")
                        
                        # Get the most recent student message
                        last_student_message = conversation_log[-1]["message"]
                        
                        # Run the async function using the event loop we created
                        sms_result = loop.run_until_complete(
                            sms_unstructured_agent(
                                student_id=self.student_id,
                                message_text=last_student_message,
                                phone_number=phone_number,
                                workflow_id=self.workflow_id
                            )
                        )

                        # Extract response text from result dict
                        self.assertIsInstance(sms_result, dict)
                        self.assertIn("response", sms_result)
                        sms_response = sms_result["response"]

                        # Verify we got a valid response
                        self.assertIsNotNone(sms_response)
                        self.assertIsInstance(sms_response, str)
                        self.assertGreater(len(sms_response.strip()), 0)

                        logger.info(f"SMS Agent: {sms_response}")

                        # Store in conversation log
                        conversation_log.append({
                            "speaker": "sms_agent",
                            "message": sms_response,
                            "turn": turn + 1
                        })

                        # Basic response validation
                        self.assertNotIn("ToolMessage", sms_response, "Response should not contain tool messages")
                        self.assertNotIn("SystemMessage", sms_response, "Response should not contain system messages")

                        # Feed SMS agent response back to student agent for next turn
                        agent_msg = HumanMessage(content=sms_response)
                        student_msgs.append(agent_msg)
                        student_state["messages"] = student_msgs
                    
                    # Increment turn counter
                    turn += 1
                    
                    # Log progress every few turns
                    if turn % 5 == 0:
                        progress = get_workflow_progress()
                        logger.info(f"Progress check at turn {turn}: {progress['completed_steps']}/{progress['total_steps']} steps completed ({progress['completion_percentage']:.1f}%)")

                # Check final workflow status and progress
                final_progress = get_workflow_progress()
                
                logger.info(f"\n=== Final Workflow Status ===")
                logger.info(f"Workflow ID: {self.student_workflow_id}")
                logger.info(f"Status: {final_progress['status']}")
                logger.info(f"Steps completed: {final_progress['completed_steps']}/{final_progress['total_steps']}")
                logger.info(f"Completion percentage: {final_progress['completion_percentage']:.1f}%")
                logger.info(f"Total conversation turns: {turn}")
                
                # Log the complete conversation
                logger.info("\n=== Complete Conversation Log ===")
                for entry in conversation_log:
                    speaker_label = "Student" if entry["speaker"] == "student" else "SMS Agent"
                    logger.info(f"{speaker_label} (Turn {entry['turn']}): {entry['message']}")
                    logger.info("---")

                # Verify conversation structure
                self.assertGreaterEqual(len(conversation_log), 4)  # At least 2 exchanges
                
                # Check alternating pattern
                student_turns = [entry for entry in conversation_log if entry["speaker"] == "student"]
                sms_turns = [entry for entry in conversation_log if entry["speaker"] == "sms_agent"]
                
                self.assertGreater(len(student_turns), 0)
                self.assertGreater(len(sms_turns), 0)

                # All SMS agent responses should be non-empty and substantial
                for entry in sms_turns:
                    self.assertGreater(len(entry["message"].strip()), 10)

                # Verify workflow completion or reasonable progress
                if final_progress["status"] == "COMPLETED":
                    logger.info("✅ Workflow successfully completed through conversation!")
                    self.assertEqual(final_progress["status"], "COMPLETED")
                    self.assertEqual(final_progress["completed_steps"], final_progress["total_steps"])
                elif final_progress["completion_percentage"] >= 50:
                    logger.info(f"⚠️  Workflow not fully completed but made significant progress ({final_progress['completion_percentage']:.1f}%)")
                    # Accept significant progress even if not fully completed
                    self.assertIn(final_progress["status"], ["IN_PROGRESS", "COMPLETED"])
                else:
                    logger.info(f"⚠️  Workflow progress: {final_progress['completion_percentage']:.1f}% - conversation may need more turns")
                    # For unstructured workflows, completion depends on specific agent behavior
                    # We'll accept any valid status if we had meaningful conversation
                    self.assertIn(final_progress["status"], ["IN_PROGRESS", "COMPLETED", "NOT_STARTED"])
                
                # Ensure we had a meaningful conversation regardless of completion status
                self.assertGreaterEqual(turn, 2)  # At least one complete exchange

                logger.info("Multi-turn SMS conversation with student agent completed successfully")

            except Exception as e:
                logger.error(f"Error during conversation test: {str(e)}")
                raise

        finally:
            # Clean up using helper function
            cleanup_student_agent_config(config_id)
            
            # Clean up event loop
            try:
                if loop and not loop.is_closed():
                    loop.close()
            except Exception as e:
                logger.warning(f"Error closing event loop: {e}")

    def test_sms_tool_message_filtering(self):
        """Test that tool messages are properly filtered from SMS responses"""
        
        # Set up event loop
        try:
            # Close any existing loop to ensure clean state
            try:
                existing_loop = asyncio.get_event_loop()
                if not existing_loop.is_closed():
                    existing_loop.close()
            except RuntimeError:
                pass  # No existing loop to close
            
            # Create a new event loop for this test
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
        except Exception as e:
            logger.warning(f"Error setting up event loop: {e}")
            # Fallback: create new loop anyway
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        try:
            # Mock the scenario where agent might return tool messages
            with patch('addie.sms_agent.agent.agent') as mock_agent_func:
                # Create a mock agent that returns messages including ToolMessage
                mock_agent = mock.Mock()
                mock_agent_func.return_value = mock_agent
                
                # Create mock messages including a ToolMessage that should be filtered
                from langchain_core.messages import AIMessage, ToolMessage
                from langchain_core.messages.tool import ToolCall
                
                mock_messages = [
                    AIMessage(content="I can help you with college planning.", 
                              tool_calls=[ToolCall(name="college_planning_tool", args={}, id="123")]),
                    ToolMessage(content='{"result": "tool_execution_result"}', tool_call_id="123"),
                    AIMessage(content="Based on your interests, I'd recommend looking into engineering programs.")
                ]
                
                # Mock the agent invoke to return these messages
                mock_state = {"messages": mock_messages}
                mock_agent.invoke.return_value = mock_state

                # Send a message to the agent  
                result = loop.run_until_complete(
                    sms_unstructured_agent(
                        student_id=self.student_id,
                        message_text="Help me find engineering schools",
                        phone_number="+15551234567",
                        workflow_id=self.workflow_id
                    )
                )

            # Extract response from result dict
            self.assertIsInstance(result, dict)
            self.assertIn("response", result)
            response = result["response"]

            # Verify the response is the last AIMessage, not the ToolMessage
            self.assertEqual(response, "Based on your interests, I'd recommend looking into engineering programs.")
            self.assertNotIn("tool_execution_result", response)
            
            logger.info(f"Filtered response: {response}")
            
        finally:
            # Clean up event loop
            try:
                if loop and not loop.is_closed():
                    loop.close()
            except Exception as e:
                logger.warning(f"Error closing event loop: {e}")

    def test_sms_conversation_with_no_ai_messages(self):
        """Test handling when no AIMessage is found in response"""
        
        # Set up event loop
        try:
            # Close any existing loop to ensure clean state
            try:
                existing_loop = asyncio.get_event_loop()
                if not existing_loop.is_closed():
                    existing_loop.close()
            except RuntimeError:
                pass  # No existing loop to close
            
            # Create a new event loop for this test
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
        except Exception as e:
            logger.warning(f"Error setting up event loop: {e}")
            # Fallback: create new loop anyway
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        try:
            with patch('addie.sms_agent.agent.agent') as mock_agent_func:
                mock_agent = mock.Mock()
                mock_agent_func.return_value = mock_agent
                
                # Create mock messages with no AIMessage to test fallback behavior
                from langchain_core.messages import SystemMessage
                
                mock_messages = [
                    SystemMessage(content="System prompt"),
                ]
                
                mock_state = {"messages": mock_messages}
                mock_agent.invoke.return_value = mock_state

                # Send a message to the agent
                result = loop.run_until_complete(
                    sms_unstructured_agent(
                        student_id=self.student_id,
                        message_text="Test message",
                        phone_number="+15551234567",
                        workflow_id=self.workflow_id
                    )
                )

            # Extract response from result dict
            self.assertIsInstance(result, dict)
            self.assertIn("response", result)
            response = result["response"]

            # Should return the fallback message
            self.assertEqual(response, "I'm sorry, I couldn't process your message.")
            
            logger.info(f"Fallback response: {response}")
            
        finally:
            # Clean up event loop
            try:
                if loop and not loop.is_closed():
                    loop.close()
            except Exception as e:
                logger.warning(f"Error closing event loop: {e}")

    def test_sms_conversation_persistence(self):
        """Test that SMS conversations persist across multiple messages"""
        
        # Set up event loop
        try:
            # Close any existing loop to ensure clean state
            try:
                existing_loop = asyncio.get_event_loop()
                if not existing_loop.is_closed():
                    existing_loop.close()
            except RuntimeError:
                pass  # No existing loop to close
            
            # Create a new event loop for this test
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
        except Exception as e:
            logger.warning(f"Error setting up event loop: {e}")
            # Fallback: create new loop anyway
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        try:
            messages = [
                "Hello, I'm a high school student",
                "I want to study computer science",
                "What colleges should I consider?"
            ]
            
            phone_number = "+15551234567"
            responses = []

            for i, message in enumerate(messages):
                logger.info(f"Sending message {i+1}: {message}")
                
                result = loop.run_until_complete(
                    sms_unstructured_agent(
                        student_id=self.student_id,
                        message_text=message,
                        phone_number=phone_number,
                        workflow_id=self.workflow_id
                    )
                )
                
                # Extract response from result dict
                self.assertIsInstance(result, dict)
                self.assertIn("response", result)
                response = result["response"]
                
                responses.append(response)
                logger.info(f"Received response {i+1}: {response}")

            # Verify all responses are valid
            for i, response in enumerate(responses):
                self.assertIsNotNone(response)
                self.assertGreater(len(response.strip()), 0)
                self.assertNotIn("ToolMessage", response)

            # Check that conversation context is maintained
            # Later responses should potentially reference earlier context
            # This is a basic check - the actual behavior depends on the agent implementation
            self.assertEqual(len(responses), len(messages))
            
            logger.info("SMS conversation persistence test completed")
            
        finally:
            # Clean up event loop
            try:
                if loop and not loop.is_closed():
                    loop.close()
            except Exception as e:
                logger.warning(f"Error closing event loop: {e}")

    def test_sms_error_handling(self):
        """Test SMS agent error handling with invalid inputs"""
        
        # Set up event loop
        try:
            # Close any existing loop to ensure clean state
            try:
                existing_loop = asyncio.get_event_loop()
                if not existing_loop.is_closed():
                    existing_loop.close()
            except RuntimeError:
                pass  # No existing loop to close
            
            # Create a new event loop for this test
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
        except Exception as e:
            logger.warning(f"Error setting up event loop: {e}")
            # Fallback: create new loop anyway
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        try:
            # Test with empty message
            try:
                result = loop.run_until_complete(
                    sms_unstructured_agent(
                        student_id=self.student_id,
                        message_text="",
                        phone_number="+15551234567",
                        workflow_id=self.workflow_id
                    )
                )
                # Should handle gracefully - extract response from dict
                self.assertIsInstance(result, dict)
                self.assertIn("response", result)
                response = result["response"]
                self.assertIsNotNone(response)
                logger.info(f"Empty message response: {response}")
            except Exception as e:
                logger.info(f"Empty message raised exception (expected): {e}")

            # Test with None workflow_id
            try:
                result = loop.run_until_complete(
                    sms_unstructured_agent(
                        student_id=self.student_id,
                        message_text="Test message",
                        phone_number="+15551234567",
                        workflow_id=None
                    )
                )
                # Should handle gracefully or raise specific exception - extract response from dict
                if isinstance(result, dict) and "response" in result:
                    response = result["response"]
                    logger.info(f"None workflow_id response: {response}")
                else:
                    logger.info(f"None workflow_id result: {result}")
            except Exception as e:
                logger.info(f"None workflow_id raised exception (expected): {e}")

            # Test with invalid student_id
            try:
                result = loop.run_until_complete(
                    sms_unstructured_agent(
                        student_id="invalid_student_id",
                        message_text="Test message",
                        phone_number="+15551234567",
                        workflow_id=self.workflow_id
                    )
                )
                # Extract response from result dict if available
                if isinstance(result, dict) and "response" in result:
                    response = result["response"]
                    logger.info(f"Invalid student_id response: {response}")
                else:
                    logger.info(f"Invalid student_id result: {result}")
            except Exception as e:
                logger.info(f"Invalid student_id raised exception (expected): {e}")

            logger.info("SMS error handling test completed")
            
        finally:
            # Clean up event loop
            try:
                if loop and not loop.is_closed():
                    loop.close()
            except Exception as e:
                logger.warning(f"Error closing event loop: {e}")