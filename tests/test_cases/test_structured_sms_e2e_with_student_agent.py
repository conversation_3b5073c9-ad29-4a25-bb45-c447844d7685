"""
End-to-end test for structured SMS workflow using student agent.

This test creates a complete Big Five Inventory workflow and uses a student agent
to provide realistic responses to all 50 questions, testing the full SMS flow.
"""

import json
import alog
from unittest.mock import patch
from tests.utils.base_test_cases import StructuredWorkflowTestCase
from tests.utils.structured_sms_runner import StructuredSMSConversationRunner, STUDENT_PERSONAS
from prisma.enums import WorkflowType

# Configure logger
logger = alog.getLogger()


class StructuredSMSEndToEndTestCase(StructuredWorkflowTestCase):
    """
    Test case for end-to-end structured SMS workflow testing with student agent.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._workflow_name_suffix = "E2E Student Agent Test"


class TestStructuredSMSEndToEnd(StructuredSMSEndToEndTestCase):
    """Test structured SMS workflow end-to-end with student agent"""
    
    def test_structured_workflow_setup_with_all_steps(self):
        """Test that the structured workflow is set up with all 50 steps"""
        self.assertIsNotNone(self.workflow_id)
        self.assertEqual(self.workflow.workflow_type, WorkflowType.STRUCTURED)
        self.assertEqual(self.student_workflow.mode, "sms")
        self.assertEqual(self.student_workflow.status, "IN_PROGRESS")
        
        # Check that we have all 50 steps
        workflow_steps = self.prisma.workflowstep.find_many(
            where={"parent_workflow_id": self.workflow_id},
            order={"index": "asc"}
        )
        
        self.assertEqual(len(workflow_steps), 50, f"Expected 50 workflow steps, got {len(workflow_steps)}")
        
        # Check that we have all 50 student workflow steps
        student_steps = self.prisma.studentworkflowstep.find_many(
            where={
                "student_id": self.student_id,
                "student_workflow_id": self.student_workflow_id
            }
        )
        
        self.assertEqual(len(student_steps), 50, f"Expected 50 student workflow steps, got {len(student_steps)}")
        
        # Verify all steps are initially incomplete
        completed_steps = [step for step in student_steps if step.completed]
        self.assertEqual(len(completed_steps), 0, f"Expected 0 completed steps initially, got {len(completed_steps)}")
    
    def test_structured_sms_conversation_with_extrovert_persona(self):
        """Test complete structured SMS conversation with extrovert student persona"""
        # Mock Twilio SMS sending
        with patch("addie.sms_agent.agent.send_sms_via_twilio") as mock_send_sms:
            mock_send_sms.return_value = True
            
            # Create and setup the conversation runner
            runner = StructuredSMSConversationRunner(
                student_id=self.student_id,
                user_id=self.user_id,
                workflow_id=self.workflow_id,
                phone_number="+15551234567"
            )
            
            try:
                # Setup student agent with extrovert persona
                runner.setup_student_agent(STUDENT_PERSONAS["extrovert"])
                
                # Run the conversation (limit to first 10 questions to avoid long test)
                results = runner.run_conversation(max_turns=25)  # ~12-13 questions
                
                # Verify conversation results
                self.assertTrue(results["total_turns"] > 0, "Expected some conversation turns")
                self.assertTrue(results["student_turns"] > 0, "Expected student to respond")
                self.assertTrue(results["agent_turns"] > 0, "Expected agent to respond")
                self.assertTrue(results["questions_answered"] > 0, "Expected some questions to be answered")
                
                logger.info(f"Conversation completed: {results['questions_answered']} questions answered")
                logger.info(f"Completion percentage: {results['completion_percentage']:.1f}%")
                
                # Verify question responses were saved to database
                question_responses = self.prisma.questionresponse.find_many(
                    where={"student_id": self.student_id}
                )
                
                self.assertEqual(len(question_responses), results["questions_answered"], 
                               "Database responses should match conversation results")
                
                # Verify at least one response has expected structure
                if question_responses:
                    response = question_responses[0]
                    self.assertIsNotNone(response.response_data)
                    
                    # Parse response data
                    if isinstance(response.response_data, str):
                        response_data = json.loads(response.response_data)
                    else:
                        response_data = response.response_data
                    
                    self.assertIn("value", response_data, "Response should have 'value' field")
                    
                    # Value should be a valid Likert scale response
                    value = response_data["value"]
                    valid_values = ["1", "2", "3", "4", "5", "Strongly Disagree", "Disagree", "Neutral", "Agree", "Strongly Agree"]
                    self.assertIn(str(value), valid_values, f"Response value '{value}' should be valid Likert scale response")
                
            finally:
                runner.cleanup()
    
    def test_structured_sms_conversation_with_introvert_persona(self):
        """Test structured SMS conversation with introvert student persona"""
        # Mock Twilio SMS sending
        with patch("addie.sms_agent.agent.send_sms_via_twilio") as mock_send_sms:
            mock_send_sms.return_value = True
            
            # Create and setup the conversation runner
            runner = StructuredSMSConversationRunner(
                student_id=self.student_id,
                user_id=self.user_id,
                workflow_id=self.workflow_id,
                phone_number="+15551234567"
            )
            
            try:
                # Setup student agent with introvert persona
                runner.setup_student_agent(STUDENT_PERSONAS["introvert"])
                
                # Run the conversation (limit to first few questions)
                results = runner.run_conversation(max_turns=15)  # ~7-8 questions
                
                # Verify conversation results
                self.assertTrue(results["total_turns"] > 0, "Expected some conversation turns")
                self.assertTrue(results["questions_answered"] > 0, "Expected some questions to be answered")
                
                logger.info(f"Introvert conversation: {results['questions_answered']} questions answered")
                
                # Verify responses are appropriate for introvert persona
                question_responses = self.prisma.questionresponse.find_many(
                    where={"student_id": self.student_id}
                )
                
                self.assertTrue(len(question_responses) > 0, "Expected some question responses")
                
                # Get conversation summary
                summary = runner.get_conversation_summary()
                self.assertIn("total_messages", summary)
                self.assertIn("questions_answered", summary)
                
            finally:
                runner.cleanup()
    
    def test_workflow_completion_detection(self):
        """Test that workflow completion is correctly detected"""
        # Mock Twilio SMS sending
        with patch("addie.sms_agent.agent.send_sms_via_twilio") as mock_send_sms:
            mock_send_sms.return_value = True
            
            # Create and setup the conversation runner
            runner = StructuredSMSConversationRunner(
                student_id=self.student_id,
                user_id=self.user_id,
                workflow_id=self.workflow_id,
                phone_number="+15551234567"
            )
            
            try:
                # Setup student agent
                runner.setup_student_agent(STUDENT_PERSONAS["balanced"])
                
                # Initial check - should not be completed
                self.assertFalse(runner.check_workflow_completion())
                
                # Run a few turns
                results = runner.run_conversation(max_turns=10)
                
                # Should have made some progress
                self.assertTrue(results["questions_answered"] > 0, "Expected some progress")
                
                # If less than 50 questions answered, should not be completed
                if results["questions_answered"] < 50:
                    self.assertFalse(results["workflow_completed"], "Workflow should not be completed yet")
                
            finally:
                runner.cleanup()
    
    def test_student_agent_response_format(self):
        """Test that student agent responses are in the correct format for Big Five"""
        # Mock Twilio SMS sending
        with patch("addie.sms_agent.agent.send_sms_via_twilio") as mock_send_sms:
            mock_send_sms.return_value = True
            
            # Create and setup the conversation runner
            runner = StructuredSMSConversationRunner(
                student_id=self.student_id,
                user_id=self.user_id,
                workflow_id=self.workflow_id,
                phone_number="+15551234567"
            )
            
            try:
                # Setup student agent
                runner.setup_student_agent(STUDENT_PERSONAS["balanced"])
                
                # Run just a few turns to check format
                results = runner.run_conversation(max_turns=6)  # 3 questions
                
                # Check that student responses are reasonable
                student_messages = [msg for msg in results["conversation_log"] if msg["speaker"] == "student"]
                
                self.assertTrue(len(student_messages) > 0, "Expected student messages")
                
                # Check that at least one student message looks like a Likert response
                found_likert_response = False
                for msg in student_messages:
                    message_content = msg["message"].lower()
                    if any(term in message_content for term in ["agree", "disagree", "neutral", "1", "2", "3", "4", "5"]):
                        found_likert_response = True
                        break
                
                self.assertTrue(found_likert_response, "Expected at least one Likert-style response from student")
                
            finally:
                runner.cleanup()