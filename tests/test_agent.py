import uuid

import alog
from langchain_core.messages import HumanMessage, AIMessage

from addie.agent import agent
from addie.agent.state.state import AgentState
from addie.data_model.generated_student import GeneratedStudent
from tests.utils.test_helpers import create_student_agent_config


async def test_init():
    # Create a student agent config for the test
    config = create_student_agent_config("Test student for agent initialization", role="student")
    
    # Generate a test student dynamically
    gen_student = GeneratedStudent(prompt_id=config.prompt_id)
    user_id = gen_student.id
    student_id = gen_student.student_id
    
    # Generate a unique session ID for this test
    session_id = uuid.uuid4().hex
    
    # Verify the generated student was properly created before proceeding
    from addie.lib import prisma_client
    from tests.utils.test_helpers import setup_unstructured_sms_workflow
    prisma = prisma_client()
    
    # Wait for database consistency - verify user and student exist
    import time
    max_retries = 5
    retry_count = 0
    user = None
    
    while retry_count < max_retries:
        try:
            user = prisma.user.find_unique(
                where={"id": user_id},
                include={
                    "students": {
                        "include": {
                            "growth_opportunities": True,
                            "academic_achievements": True,
                        }
                    },
                }
            )
            if user and user.students and len(user.students) > 0:
                alog.debug(f"User and student verified after {retry_count} retries")
                break
            else:
                alog.debug(f"User or student not ready, retry {retry_count + 1}/{max_retries}")
                retry_count += 1
                time.sleep(0.1)  # Small delay for database consistency
        except Exception as e:
            alog.warning(f"Error verifying user/student, retry {retry_count + 1}/{max_retries}: {e}")
            retry_count += 1
            time.sleep(0.1)
    
    if not user or not user.students or len(user.students) == 0:
        raise RuntimeError(f"Failed to create or verify user {user_id} with student {student_id} after {max_retries} retries")
    
    # Create an unstructured workflow for this test
    workflow_id, workflow_step_id, student_workflow_id, workflow, step, student_workflow = setup_unstructured_sms_workflow(
        prisma, user_id, student_id, f"agent-test-{session_id[:8]}"
    )

    try:
        state = await AgentState.init(session_id, user_id, student_id=student_id)
        msgs = [
            AIMessage(content='''Hi there! I'm Addie, here to support your college journey.\n
            Did you know that students with professional guidance have 40% better admission rates?\n
            Let's get started!'''),
            HumanMessage(content="""What am I good at and what majors would you
             suggest I should pursue in order to make a good salary.""")
        ]

        state['messages'] += msgs

        # alog.debug(alog.pformat(state))

        res = agent().invoke(state)

        alog.debug(f"Initial messages count: {len(msgs)}")
        alog.debug(f"State messages after invoke: {len(state.get('messages', []))}")
        alog.debug(f"Result type: {type(res)}")
        
        # The agent returns the updated state, not mutating the original
        if isinstance(res, dict) and 'messages' in res:
            final_messages = res['messages']
        else:
            final_messages = state.get('messages', [])
            
        alog.debug(f"Final messages count: {len(final_messages)}")
        
        # Verify the agent responded appropriately
        assert len(final_messages) > len(msgs), f"Agent should have added a response. Initial: {len(msgs)}, Final: {len(final_messages)}"
        
    finally:
        # Clean up the test data to avoid conflicts in parallel execution
        from tests.utils.test_helpers import cleanup_workflow_test_data
        try:
            cleanup_workflow_test_data(prisma, workflow_id, workflow_step_id, student_workflow_id)
        except Exception as e:
            alog.warning(f"Error cleaning up test data: {e}")
        
        # Clean up the generated student data
        try:
            # Delete student first (references user)
            prisma.student.delete(where={"id": student_id})
            # Delete user
            prisma.user.delete(where={"id": user_id})
            # Delete prompt
            prisma.prompt.delete(where={"id": config.prompt_id})
            # Delete config
            prisma.studentagentconfig.delete(where={"id": config.id})
        except Exception as e:
            alog.warning(f"Error cleaning up generated student data: {e}")
            