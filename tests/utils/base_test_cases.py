from unittest import TestCase
import alog
import json
from unittest.mock import patch
from datetime import datetime, timezone

from addie.lib import prisma_client
from addie import settings
from prisma.enums import WorkflowStatus, WorkflowType
from tests.utils.mock_workflows import (
    REAL_WORKFLOW_NAME,
    REAL_WORKFLOW_OWNER_ID,
    REAL_WORKFLOW_TYPE,
    REAL_STEP_GOAL,
    REAL_STEP_NAME,
    create_mock_unstructured_workflow,
    mock_structured_workflow,
    create_mock_unstructured_workflow_object,
    create_mock_structured_workflow_object
)

# Configure logger
logger = alog.getLogger()
logger.set_level(settings.LOG_LEVEL)


class BaseWorkflowTestCase(TestCase):
    """Base test case with common workflow setup and teardown functionality"""
    
    def setUp(self):
        """Set up common test environment for workflow tests"""
        logger.debug(f"===== Setting up workflow test: {self._testMethodName} =====")
        # Get a real prisma client
        self.prisma = prisma_client()
        logger.debug("Prisma client initialized")
        
        # Set up test phone
        self.test_phone = "+15555555555"
        logger.debug(f"Using test phone number: {self.test_phone}")
        
        # Set up user and workflow
        self._setup_user_and_student()
        self._setup_test_workflow()
        
    def tearDown(self):
        """Clean up after each test method completes with proper order for foreign key constraints"""
        logger.debug(f"===== Cleaning up after workflow test: {self._testMethodName} =====")
        
        # Clean up database records in proper order to respect foreign key constraints
        logger.debug("Starting database cleanup...")
        
        # Clean up any remaining messages for this test
        try:
            if hasattr(self, 'student_id') and hasattr(self, 'workflow_id'):
                from addie.sms_agent.agent import get_session_id_for_sms
                session_id = get_session_id_for_sms(self.student_id, self.workflow_id)
                self.prisma.messages.delete_many(
                    where={"session_id": session_id}
                )
                logger.debug(f"Cleaned up messages for session: {session_id}")
        except Exception as e:
            logger.warning(f"Error cleaning up messages: {e}")
        
        try:
            # Delete question responses first (references student workflow steps)
            if hasattr(self, 'student_id'):
                self.prisma.questionresponse.delete_many(
                    where={"student_id": self.student_id}
                )
                logger.debug("Cleaned up question responses")
        except Exception as e:
            logger.warning(f"Error deleting question responses: {e}")
        
        try:
            # Delete student workflow steps (references student workflows)
            if hasattr(self, 'student_workflow_id'):
                self.prisma.studentworkflowstep.delete_many(
                    where={"student_workflow_id": self.student_workflow_id}
                )
                logger.debug("Cleaned up student workflow steps")
        except Exception as e:
            logger.warning(f"Error deleting student workflow steps: {e}")
            
        try:
            # Delete the student workflow
            if hasattr(self, 'student_workflow_id'):
                logger.debug(f"Deleting student workflow with ID: {self.student_workflow_id}")
                self.prisma.studentworkflow.delete(
                    where={"id": self.student_workflow_id}
                )
                logger.debug("Student workflow deleted successfully")
        except Exception as e:
            logger.warning(f"Error deleting student workflow: {e}")
            
        try:
            # Delete engagement events first (references student)
            if hasattr(self, 'student_id'):
                self.prisma.engagementevent.delete_many(
                    where={"student_id": self.student_id}
                )
                logger.debug("Cleaned up engagement events")
        except Exception as e:
            logger.warning(f"Error deleting engagement events: {e}")
        
        try:
            # Delete reminder templates (no user association needed)
            self.prisma.remindertemplate.delete_many()
            logger.debug("Cleaned up reminder templates")
        except Exception as e:
            logger.warning(f"Error deleting reminder templates: {e}")
            
        try:
            # Delete the student (references user)
            if hasattr(self, 'student_id'):
                self.prisma.student.delete(
                    where={"id": self.student_id}
                )
                logger.debug("Student deleted successfully")
        except Exception as e:
            logger.warning(f"Error deleting student: {e}")
            
        try:
            # Delete the user
            if hasattr(self, 'user_id'):
                self.prisma.user.delete(
                    where={"id": self.user_id}
                )
                logger.debug("User deleted successfully")
        except Exception as e:
            logger.warning(f"Error deleting user: {e}")
            
        try:
            # Delete the workflow step
            if hasattr(self, 'workflow_step_id'):
                self.prisma.workflowstep.delete(
                    where={"id": self.workflow_step_id}
                )
                logger.debug("Workflow step deleted successfully")
        except Exception as e:
            logger.warning(f"Error deleting workflow step: {e}")
            
        try:
            # Delete the workflow
            if hasattr(self, 'workflow_id'):
                self.prisma.workflow.delete(
                    where={"id": self.workflow_id}
                )
                logger.debug("Workflow deleted successfully")
        except Exception as e:
            logger.warning(f"Error deleting workflow: {e}")
    
    def _setup_user_and_student(self):
        """Set up user and student records for testing with unique IDs for parallel execution"""
        import uuid
        import time
        
        # Create unique identifiers for this test run to avoid parallel execution conflicts
        test_run_id = f"{int(time.time() * 1000000)}_{uuid.uuid4().hex[:8]}"
        unique_phone = f"+1555{test_run_id[-10:]}"  # Use last 10 digits for phone
        unique_email = f"test_sms_{test_run_id}@example.com"
        unique_student_id = f"test_sms_student_{test_run_id}"
        
        logger.debug(f"Creating unique test user for parallel execution: {unique_email}")
        
        # Always create new user to ensure test isolation
        self.user = self.prisma.user.create(
            data={
                "first_name": "Test",
                "last_name": "User", 
                "email": unique_email,
                "phone_number": unique_phone,
                "role": "STUDENT",
                "enabled": True
            }
        )
        logger.debug(f"Created new test user with ID: {self.user.id}")
        
        # Store for cleanup
        self.test_phone = unique_phone
        self.user_id = self.user.id
        
        # Always create new student to ensure test isolation
        logger.debug(f"Creating unique test student: {unique_student_id}")
        self.student = self.prisma.student.create(
            data={
                "student_id": unique_student_id,
                "users": {
                    "connect": {"id": self.user_id}
                },
                "grade": 11
            }
        )
        logger.debug(f"Created new student with ID: {self.student.id}")
        
        self.student_id = self.student.id
    
    def create_patch(self, target, **kwargs):
        """Create a patch that will automatically be cleaned up in tearDown
        
        Args:
            target: The target to patch (e.g., 'addie.api.sms_routes.process_sms_message')
            **kwargs: Additional keyword arguments passed to unittest.mock.patch
            
        Returns:
            The mock object created by the patch
        """
        patcher = patch(target, **kwargs)
        mock_obj = patcher.start()
        self.addCleanup(patcher.stop)
        return mock_obj
        
    def _setup_test_workflow(self):
        """Set up test workflow, workflow step, and student workflow"""
        # Find a valid owner ID for the workflow (use the first admin or counselor found)
        owner = self.prisma.user.find_first(
            where={
                "OR": [
                    {"role": "ADMIN"},
                    {"role": "COUNSELOR"}
                ]
            }
        )
        
        if not owner:
            # If no admin/counselor found, use the test user
            owner_id = self.user_id
        else:
            owner_id = owner.id
            
        # Create a new workflow for testing
        logger.debug("Creating test workflow...")
        
        # You can use any of the following mock workflow functions:
        # 1. Default unstructured SMS workflow:
        mock_workflow, mock_step = create_mock_unstructured_workflow()
        
        # 2. Or to use a specific workflow based on its ID (e.g., Big 5 Inventory):
       
        workflow_data = {
            "name": REAL_WORKFLOW_NAME,
            "owner": {"connect": {"id": owner_id}},
            "status": WorkflowStatus.PUBLISHED,
            "workflow_type": REAL_WORKFLOW_TYPE,
            "data": json.dumps(mock_workflow["data"]),
            "tags": ["test"]
        }
        logger.debug(f"Workflow data prepared with owner ID: {owner_id}")
        
        self.workflow = self.prisma.workflow.create(
            data=workflow_data
        )
        logger.debug(f"Created test workflow with ID: {self.workflow.id}")
        
        self.workflow_id = self.workflow.id
        self.workflow_name = self.workflow.name
        
        # Create a step for the workflow
        step_data = {
            "name": REAL_STEP_NAME,
            "goal": REAL_STEP_GOAL,
            "index": 0,
            "data": json.dumps(mock_step["data"]),
            "parent_workflow": {"connect": {"id": self.workflow_id}}
        }
        
        self.step = self.prisma.workflowstep.create(
            data=step_data
        )
        
        self.workflow_step_id = self.step.id
        
        # Create or find a student workflow
        existing_student_workflow = self.prisma.studentworkflow.find_first(
            where={
                "student_id": self.student_id,
                "workflow_id": self.workflow_id
            }
        )
        
        if existing_student_workflow:
            self.student_workflow = existing_student_workflow
            
            # Update mode and status
            self.student_workflow = self.prisma.studentworkflow.update(
                where={"id": existing_student_workflow.id},
                data={
                    "mode": "sms",
                    "status": "IN_PROGRESS"
                }
            )
        else:
            # Create a new student workflow
            self.student_workflow = self.prisma.studentworkflow.create(
                data={
                    "student": {"connect": {"id": self.student_id}},
                    "workflow": {"connect": {"id": self.workflow_id}},
                    "mode": "sms",
                    "status": "IN_PROGRESS"
                }
            )
        
        self.student_workflow_id = self.student_workflow.id
        
        # Set up session ID for checking message history
        self.session_id = f"{self.workflow_id}-{self.user_id}"


class UnstructuredWorkflowTestCase(BaseWorkflowTestCase):
    """
    Base test case for unstructured SMS workflow tests.
    Uses the shared fixture functionality for consistent unstructured workflow setup.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set default workflow name suffix - can be overridden by subclasses
        self._workflow_name_suffix = getattr(self, '_workflow_name_suffix', 'Unstructured Test')

    def _setup_test_workflow(self):
        """Set up unstructured test workflow using shared fixture"""
        from tests.utils.test_helpers import setup_unstructured_sms_workflow
        
        self.workflow_id, self.workflow_step_id, self.student_workflow_id, self.workflow, self.step, self.student_workflow = setup_unstructured_sms_workflow(
            self.prisma, self.user_id, self.student_id, self._workflow_name_suffix
        )
        
        self.workflow_name = self.workflow.name
        # Set up session ID for checking message history
        self.session_id = f"{self.workflow_id}-{self.user_id}"
    
    def tearDown(self):
        """Clean up after each test method completes using shared cleanup"""
        from tests.utils.test_helpers import cleanup_workflow_test_data
        
        logger.debug(f"===== Cleaning up after unstructured workflow test: {self._testMethodName} =====")
        
        # Use shared cleanup function
        try:
            cleanup_workflow_test_data(self.prisma, self.workflow_id, self.workflow_step_id, self.student_workflow_id)
        except Exception as e:
            logger.warning(f"Shared cleanup failed, using parent cleanup: {e}")
            # Fall back to parent cleanup
            super().tearDown()


class StructuredWorkflowTestCase(BaseWorkflowTestCase):
    """
    Base test case for structured SMS workflow tests.
    Uses the shared fixture functionality for consistent structured workflow setup.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set default workflow name suffix - can be overridden by subclasses
        self._workflow_name_suffix = getattr(self, '_workflow_name_suffix', 'Structured Test')

    def _setup_test_workflow(self):
        """Set up structured test workflow using shared fixture"""
        from tests.utils.test_helpers import setup_structured_sms_workflow
        
        self.workflow_id, self.workflow_step_id, self.student_workflow_id, self.workflow, self.step, self.student_workflow = setup_structured_sms_workflow(
            self.prisma, self.user_id, self.student_id, self._workflow_name_suffix
        )
        
        self.workflow_name = self.workflow.name
        # Set up session ID for checking message history
        self.session_id = f"{self.workflow_id}-{self.user_id}"
    
    def tearDown(self):
        """Clean up after each test method completes using shared cleanup"""
        from tests.utils.test_helpers import cleanup_structured_workflow_test_data
        
        logger.debug(f"===== Cleaning up after structured workflow test: {self._testMethodName} =====")
        
        # Use shared cleanup function
        try:
            cleanup_structured_workflow_test_data(self.prisma, self.workflow_id, self.workflow_step_id, self.student_workflow_id)
        except Exception as e:
            logger.warning(f"Shared cleanup failed, using parent cleanup: {e}")
            # Fall back to parent cleanup
            super().tearDown()


# The specialized test cases are located in the tests/test_cases directory:
# - Unstructured SMS tests should inherit from UnstructuredWorkflowTestCase
# - Structured SMS tests should inherit from StructuredWorkflowTestCase