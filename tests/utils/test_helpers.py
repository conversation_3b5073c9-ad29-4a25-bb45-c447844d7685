import alog
import uuid
import j<PERSON>
from typing import List, Optional, Union
from addie.history import Embedded<PERSON><PERSON><PERSON>, get_history
from addie.data_model.prompt import Prompt
from addie.student_agent.state import StudentAgentState
from addie.lib import prisma_client
from prisma.enums import WorkflowType
from langchain_core.messages.base import (
    BaseMessage,
    BaseMessageChunk,
    merge_content,
)
from tests.utils.mock_workflows import (
    REAL_WORKFLOW_NAME,
    REAL_STEP_GOAL,
    REAL_STEP_NAME,
    create_mock_unstructured_workflow,
)


def print_message_history(msgs: List[BaseMessage], use_alog: bool = False, max_preview_length: int = 50):
    """
    Print a simplified message history with sender names and content previews.
    
    Args:
        msgs: List of Message objects to print
        use_alog: Whether to use alog for logging (True) or print (False)
        max_preview_length: Maximum length of content preview before truncation
    """
    log_func = alog.info if use_alog else print
    
    log_func("\n=== MESSAGE HISTORY ===")
    for i, msg in enumerate(msgs):
        if not hasattr(msg, 'content') or not msg.content:
            continue
            
        if msg.type == "ai":
            name = "AI"
        elif msg.type == "tool":
            name = f"Tool:{msg.name if hasattr(msg, 'name') else 'unknown'}"
        elif msg.type == "human":
            name = "Human"
        else:
            name = f"{msg.type.capitalize()}"
            
        content_preview = msg.content[:max_preview_length] + "..." if len(msg.content) > max_preview_length else msg.content
        log_func(f"{i+1}. {name}: {content_preview}")
    log_func("=== END MESSAGE HISTORY ===")


def get_and_print_message_history(session_id: str, use_alog: bool = False, max_preview_length: int = 50):
    """
    Get the message history for a user and print it in a simplified format.
    
    Args:
        user_id: The user ID to get message history for
        use_alog: Whether to use alog for logging (True) or print (False)
        max_preview_length: Maximum length of content preview before truncation
        
    Returns:
        The list of messages retrieved from history
    """
    history: EmbeddedHistory = get_history(session_id)
    msgs = history.get_messages()
    print_message_history(msgs, use_alog, max_preview_length)
    return msgs


def format_message_for_display(msg: BaseMessage, max_preview_length: Optional[int] = None) -> str:
    """
    Format a single message for display with sender name and content.
    
    Args:
        msg: The message to format
        max_preview_length: If provided, truncate content to this length
        
    Returns:
        A formatted string representation of the message
    """
    if not hasattr(msg, 'content') or not msg.content:
        return ""
        
    name = "AI" if msg.type == "ai" else "Human"
    
    if max_preview_length and len(msg.content) > max_preview_length:
        content = msg.content[:max_preview_length] + "..."
    else:
        content = msg.content
        
    return f"{name}: {content}"


def create_student_agent_config(content: str, role: str = "student"):
    """
    Create a student agent config with a prompt for testing.
    
    Args:
        content: The prompt content for the student agent
        role: The role for the student agent config (default: "student")
        
    Returns:
        The created StudentAgentConfig object
    """
    import time
    import hashlib
    
    prisma = prisma_client()
    
    # Add unique identifier to content to avoid race conditions in parallel tests
    test_id = f"{int(time.time() * 1000000)}_{hashlib.md5(content.encode()).hexdigest()[:8]}"
    unique_content = f"{content} [test_id: {test_id}]"
    
    # Create prompt with error handling
    try:
        prompt = Prompt(content=unique_content)
        student_prompt_id = prompt.id
        
        # Verify prompt was created successfully
        created_prompt = prisma.prompt.find_unique(where={"id": student_prompt_id})
        if not created_prompt:
            raise RuntimeError(f"Failed to create or find prompt with ID: {student_prompt_id}")
            
    except Exception as e:
        alog.error(f"Error creating prompt: {e}")
        raise

    try:
        config_data = dict(prompt_id=student_prompt_id, role=role)
        config = prisma.studentagentconfig.create(data=config_data)
        return config
    except Exception as e:
        alog.error(f"Error creating student agent config: {e}")
        # Clean up the prompt if config creation failed
        try:
            prisma.prompt.delete(where={"id": student_prompt_id})
        except:
            pass
        raise


def create_student_agent_with_state(student_id: str, user_id: str, prompt_content: str, role: str = "student"):
    """
    Create a complete student agent setup with config, agent, and state.
    
    Args:
        student_id: The student ID
        user_id: The user ID  
        prompt_content: The prompt content for the student agent
        role: The role for the student agent config (default: "student")
        
    Returns:
        Tuple of (student_agent, student_state, config_id) for cleanup
    """
    prisma = prisma_client()
    
    # Create config
    config = create_student_agent_config(prompt_content, role)
    
    # Create student agent
    student_agent = prisma.studentagent.create(
        data=dict(
            config_id=config.id, 
            prompt_id=config.prompt_id, 
            student_id=student_id
        ),
        include=dict(prompt=True),
    )

    # Create student session ID and state
    student_session_id = uuid.uuid4().hex
    student_state = StudentAgentState.init(
        session_id=student_session_id,
        user_id=user_id,
        id=student_id,
        student_agent=student_agent,
    )
    
    return student_agent, student_state, config.id


def cleanup_student_agent_config(config_id: str):
    """
    Clean up a student agent config by deleting it.
    
    Args:
        config_id: The ID of the config to delete
    """
    try:
        prisma = prisma_client()
        prisma.studentagentconfig.delete(where=dict(id=config_id))
        alog.info(f"Cleaned up student agent config: {config_id}")
    except Exception as e:
        alog.error(f"Failed to clean up student agent config {config_id}: {e}")


def setup_unstructured_sms_workflow(prisma, user_id: str, student_id: str, workflow_name_suffix: str = ""):
    """
    Set up an unstructured SMS workflow for testing. This is a shared fixture function
    that can be used by multiple unstructured SMS test classes.
    
    Args:
        prisma: Prisma client instance
        user_id: User ID for the test
        student_id: Student ID for the test  
        workflow_name_suffix: Optional suffix to add to workflow name for uniqueness
        
    Returns:
        Tuple of (workflow_id, workflow_step_id, student_workflow_id, workflow, step, student_workflow)
    """
    logger = alog.getLogger()
    
    # Find a valid owner ID for the workflow
    owner = prisma.user.find_first(
        where={"OR": [{"role": "ADMIN"}, {"role": "COUNSELOR"}]}
    )

    if not owner:
        owner_id = user_id
    else:
        owner_id = owner.id

    # Create unstructured workflow
    logger.debug("Creating unstructured SMS workflow using shared fixture...")
    mock_workflow, mock_step = create_mock_unstructured_workflow()

    workflow_name = REAL_WORKFLOW_NAME
    if workflow_name_suffix:
        workflow_name = f"{workflow_name} - {workflow_name_suffix}"

    workflow_data = {
        "name": workflow_name,
        "owner": {"connect": {"id": owner_id}},
        "status": "PUBLISHED",
        "workflow_type": WorkflowType.UNSTRUCTURED,
        "data": json.dumps(mock_workflow["data"]),
        "tags": ["test", "unstructured", "sms"],
    }

    workflow = prisma.workflow.create(data=workflow_data)
    logger.debug(f"Created unstructured workflow with ID: {workflow.id}")

    workflow_id = workflow.id

    # Create workflow step
    step_data = {
        "name": REAL_STEP_NAME,
        "goal": REAL_STEP_GOAL,
        "index": 0,
        "data": json.dumps(mock_step["data"]),
        "parent_workflow": {"connect": {"id": workflow_id}},
    }

    step = prisma.workflowstep.create(data=step_data)
    workflow_step_id = step.id

    # Create or find a student workflow
    existing_student_workflow = prisma.studentworkflow.find_first(
        where={"student_id": student_id, "workflow_id": workflow_id}
    )

    if existing_student_workflow:
        student_workflow = prisma.studentworkflow.update(
            where={"id": existing_student_workflow.id},
            data={"mode": "sms", "status": "IN_PROGRESS"},
        )
    else:
        student_workflow = prisma.studentworkflow.create(
            data={
                "student": {"connect": {"id": student_id}},
                "workflow": {"connect": {"id": workflow_id}},
                "mode": "sms",
                "status": "IN_PROGRESS",
            }
        )

    student_workflow_id = student_workflow.id

    # Create student workflow step if needed
    existing_step = prisma.studentworkflowstep.find_first(
        where={
            "student_id": student_id,
            "student_workflow_id": student_workflow_id
        }
    )

    if not existing_step:
        prisma.studentworkflowstep.create(
            data={
                "student": {"connect": {"id": student_id}},
                "step": {"connect": {"id": workflow_step_id}},
                "student_workflow": {"connect": {"id": student_workflow_id}},
                "completed": False,
                "data": json.dumps({"test": "data"})
            }
        )
        logger.info(f"Created student workflow step for workflow {workflow_id}")

    return workflow_id, workflow_step_id, student_workflow_id, workflow, step, student_workflow


def setup_structured_sms_workflow(prisma, user_id: str, student_id: str, workflow_name_suffix: str = ""):
    """
    Set up a structured SMS workflow for testing. This is a shared fixture function
    that can be used by multiple structured SMS test classes.
    
    Args:
        prisma: Prisma client instance
        user_id: User ID for the test
        student_id: Student ID for the test  
        workflow_name_suffix: Optional suffix to add to workflow name for uniqueness
        
    Returns:
        Tuple of (workflow_id, workflow_step_id, student_workflow_id, workflow, step, student_workflow)
    """
    from tests.utils.mock_workflows import mock_structured_workflow
    
    logger = alog.getLogger()
    
    # Find a valid owner ID for the workflow
    owner = prisma.user.find_first(
        where={"OR": [{"role": "ADMIN"}, {"role": "COUNSELOR"}]}
    )

    if not owner:
        owner_id = user_id
    else:
        owner_id = owner.id

    # Create structured workflow using the mock_structured_workflow function
    logger.debug("Creating structured SMS workflow using shared fixture...")
    
    workflow_name = "Conversation: Big 5 Inventory"
    if workflow_name_suffix:
        workflow_name = f"{workflow_name} - {workflow_name_suffix}"
    
    workflow_id, workflow_step_id, student_workflow_id, workflow_dict, step_dict = mock_structured_workflow(
        student_id=student_id,
        prisma=prisma,
        new_name=workflow_name,
        owner_id=owner_id
    )
    
    logger.debug(f"Created structured workflow with ID: {workflow_id}")
    
    # Fetch the created objects for return
    workflow = prisma.workflow.find_unique(where={"id": workflow_id})
    step = prisma.workflowstep.find_unique(where={"id": workflow_step_id}) if workflow_step_id else None
    student_workflow = prisma.studentworkflow.find_unique(where={"id": student_workflow_id})
    
    return workflow_id, workflow_step_id, student_workflow_id, workflow, step, student_workflow


def cleanup_structured_workflow_test_data(prisma, workflow_id: str, workflow_step_id: str, student_workflow_id: str):
    """
    Clean up structured workflow test data created by setup_structured_sms_workflow.
    
    Args:
        prisma: Prisma client instance
        workflow_id: ID of the workflow to delete
        workflow_step_id: ID of the workflow step to delete
        student_workflow_id: ID of the student workflow to delete
    """
    logger = alog.getLogger()
    
    try:
        # Clean up in reverse order of creation
        logger.debug(f"Cleaning up structured workflow test data...")
        
        # Get student ID from the student workflow before deletion
        student_workflow = prisma.studentworkflow.find_unique(
            where={"id": student_workflow_id}
        )
        
        if student_workflow:
            student_id = student_workflow.student_id
            
            # Delete student workflow steps first
            prisma.studentworkflowstep.delete_many(
                where={"student_workflow_id": student_workflow_id}
            )
            
            # Delete question responses associated with this student
            prisma.questionresponse.delete_many(
                where={"student_id": student_id}
            )
            
            # Delete the student workflow
            prisma.studentworkflow.delete(where={"id": student_workflow_id})
            logger.debug("Student workflow deleted successfully")
        
        # Delete the workflow step
        if workflow_step_id:
            prisma.workflowstep.delete(where={"id": workflow_step_id})
            logger.debug("Workflow step deleted successfully")
        
        # Delete the workflow
        prisma.workflow.delete(where={"id": workflow_id})
        logger.debug("Workflow deleted successfully")
        
    except Exception as e:
        logger.warning(f"Error during structured workflow cleanup: {e}")


def cleanup_workflow_test_data(prisma, workflow_id: str, workflow_step_id: str, student_workflow_id: str):
    """
    Clean up workflow test data created by setup_unstructured_sms_workflow.
    
    Args:
        prisma: Prisma client instance
        workflow_id: ID of the workflow to delete
        workflow_step_id: ID of the workflow step to delete
        student_workflow_id: ID of the student workflow to delete
    """
    logger = alog.getLogger()
    
    try:
        # Clean up in reverse order of creation
        logger.debug(f"Cleaning up workflow test data...")
        
        # Delete student workflow steps first
        prisma.studentworkflowstep.delete_many(
            where={"student_workflow_id": student_workflow_id}
        )
        
        # Delete the student workflow
        prisma.studentworkflow.delete(where={"id": student_workflow_id})
        logger.debug("Student workflow deleted successfully")
        
        # Delete the workflow step
        prisma.workflowstep.delete(where={"id": workflow_step_id})
        logger.debug("Workflow step deleted successfully")
        
        # Delete the workflow
        prisma.workflow.delete(where={"id": workflow_id})
        logger.debug("Workflow deleted successfully")
        
    except Exception as e:
        logger.warning(f"Error during workflow cleanup: {e}")
