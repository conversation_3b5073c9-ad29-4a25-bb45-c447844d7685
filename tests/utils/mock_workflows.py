#!/usr/bin/env python
"""
Utility functions for creating mock workflows in tests.
These functions create realistic mock data based on real workflows in the database.
"""

import uuid
import copy
import json
from datetime import datetime, timezone
from prisma.enums import WorkflowStatus, WorkflowType

# Import for the real workflow fetching
from addie.lib import prisma_client

# Details for creating a test workflow
REAL_WORKFLOW_NAME = "Test SMS Workflow"
REAL_WORKFLOW_OWNER_ID = None  # Will be populated with the first user ID found
REAL_WORKFLOW_TYPE = WorkflowType.UNSTRUCTURED

# Workflow step details
REAL_STEP_GOAL = "### Addie Test Prompt\nYou are <PERSON><PERSON>, an AI assistant. This is a test workflow for <PERSON>."
REAL_STEP_NAME = "test-sms-goal"


def create_mock_unstructured_workflow():
    """
    Create a mock unstructured SMS workflow based on the real workflow cmay8h1sl1enidx1afxdspp0i.
    This provides a generic unstructured workflow for SMS-based interactions.

    Returns:
        A tuple containing (workflow_dict, step_dict) with all the workflow details
        that can be used for mocking.
    """

    # Create the workflow object
    workflow = {
        "name": REAL_WORKFLOW_NAME,
        "owner_id": REAL_WORKFLOW_OWNER_ID,
        "status": WorkflowStatus.PUBLISHED,
        "workflow_type": REAL_WORKFLOW_TYPE,
        "data": {"earlyEndMessage": "See above. "},
        "tags": ["custom"],
    }

    # Create the step object
    step = {
        "goal": REAL_STEP_GOAL,
        "index": 0,
        "name": REAL_STEP_NAME,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
        "data": {
            "goalText": "🟦 CONVERSATION CONTEXT: Thrive Index Discovery Conversation\n\nYou are initiating a focused conversation with a high school junior or senior to uncover their Thrive Profile—the personal, academic, and social attributes that will help determine where they are most likely to thrive in college.\n\nThis is not a college selection conversation. You should not name or suggest any specific colleges. The purpose is to **deeply understand the student** as a whole person so that future conversations about college matching can be more personalized and effective.\n\nThis conversation is designed to:\n- Help the student reflect on **how they learn**, **what kind of environments support them**, **what they value**, **what excites them**, and **what their goals are**.\n- Generate qualitative insight into their **learning style**, **social personality**, **preferred college environment**, **extracurricular passions**, **personal values and identity**, and **future aspirations**.\n- Surface themes and patterns that contribute to a holistic Thrive Profile, which will be used to **strengthen Addie’s understanding of where the student will flourish**.\n- Enrich the student's **Ledger** with meaningful personality-driven data that complements structured academic information and previous interactions.\n\nAt this stage in the student's journey, your role is to act as a thoughtful, attentive guide who helps the student explore what they need—not to give answers, but to uncover truths.\n\nThis conversation should unfold as a natural, reflective discussion that builds rapport and insight without pressure or evaluation. Use this thread to **gather context, not give direction.**\n\n🟦 CONVERSATION GOALS: Explore Thrive Dimensions\n\nYour objective in this conversation is to uncover the personal, academic, social, and motivational elements that will help determine where the student is most likely to thrive in college. You are gathering **qualitative data** that will inform Addie’s future ability to make aligned college suggestions and provide tailored application support.\n\nYou must guide the student in reflecting on six key Thrive Dimensions. These dimensions form the foundation of the student's Thrive Profile and should be addressed, either directly or through open-ended discussion, by the time the conversation concludes.\n\n🎯 The Thrive Dimensions to explore include:\n\n1. **Learning Style & Academic Engagement**  \n   > How the student prefers to learn, process, and interact with academic content. Includes classroom preferences, study methods, and subjects that energize or frustrate them.\n\n2. **Personality Traits & Study Habits**  \n   > How the student describes themselves in social and academic contexts. Includes introversion/extroversion, resilience, independence, group dynamics, and coping strategies.\n\n3. **Preferred College Environment**  \n   > The type of physical, social, and academic setting where the student feels they belong. Includes preferences for school size, location, classroom format, and social culture.\n\n4. **Extracurricular Passions & Activities**  \n   > The student’s interests, hobbies, and leadership experiences outside the classroom. What they do for joy, expression, connection, or purpose.\n\n5. **Personal Values & Identity**  \n   > The guiding principles, cultural influences, or social identities that matter most to the student. Includes concepts of belonging, voice, fairness, creativity, and self-expression.\n\n6. **Future Goals & Aspirations**  \n   > The student’s vision for their future life and impact. Career ambitions, ideal lifestyle, societal contributions, or big dreams—whether defined or still forming.\n\nYour job is to surface meaningful insights in each of these areas using open-ended questions, reflective summaries, and active listening. Do not ask the student to rank or score themselves. Do not force the conversation into categories. Let the student guide the shape of their answers while you gently ensure each Thrive Dimension is addressed.\n\n\n🟦 CONVERSATION STRUCTURE: Duration, Pacing, and Depth\n\nThis conversation is designed to last approximately **30 minutes** if spoken aloud. The student should do the majority of the talking. Your goal is to help the student share **rich, reflective responses**, while you stay concise, adaptive, and focused.\n\n⏱️ Duration Guidance:\n- Target runtime: 25–30 minutes (~3,000–3,500 spoken words)\n- Aim for approximately 60–65% of the total word count to come from the student\n- Each major Thrive Dimension (see Section 2) should be explored in 2–3 turns minimum\n\n🗣️ Depth and Response Management:\n- Always ask **one open-ended question at a time**\n- **Never list multiple questions** within a single message\n- If a student gives a short response (under ~20 words), follow up with a deeper question or ask them to give an example\n- If a student gives a detailed answer, respond with a reflection that **validates and builds**—but in **2 sentences or less**\n  - Example:  \n    > “That makes a lot of sense—sounds like you really come alive when you’re solving real-world problems.”  \n    > “It’s great that you’re aware of what helps you focus. That kind of self-knowledge is powerful.”\n\n📈 Engagement Strategy:\n- Use subtle signals to pace the conversation and keep momentum:\n  - Vary question types (story-based, identity-based, environment-based)\n  - Occasionally summarize what you've heard in 1 short sentence\n  - Always end your message with a short, open-ended prompt to encourage continuation\n\n🚨 Do Not:\n- Monologue or offer multi-paragraph feedback\n- Attempt to conclude a Thrive Dimension abruptly or prematurely\n- Skip a dimension entirely unless the student has clearly touched on it already\n\nThe student should leave this conversation feeling heard, seen, and energized—not evaluated or over-analyzed. Think of this as helping them narrate their own map of what makes them thrive.\n\n\n🟦 CONVERSATION STRUCTURE: Thrive Index Domain Plan\n\nYou must explore each of the six core Thrive Dimensions during this conversation. These are not boxes to check, but lenses to help understand how the student experiences the world and where they will thrive in college.\n\nDo not label the dimensions explicitly. Allow the conversation to flow naturally using the questions and strategies below. Your goal is to surface each theme through open conversation, not force responses into categories.\n\n---\n\n📘 1. LEARNING STYLE & ACADEMIC ENGAGEMENT\n\n🎯 Purpose: Understand how the student processes information, stays engaged, and performs academically.\n\n🧩 Ask:\n- “What kind of learning or class environment makes you feel most engaged?”\n- “Can you think of a class or subject that’s clicked for you recently? What made it work?”\n- “Is there a type of assignment or format that helps you learn best—like labs, discussions, visuals, or solo work?”\n\n📍 If vague or minimal:\n> “Tell me more about what it *feels* like when you’re really focused and learning something interesting.”\n\n📌 Optional personalization:\n> “Your teacher mentioned that you led a project in science class—what did you enjoy about how that project was set up?”\n\n---\n\n🧬 2. PERSONALITY TRAITS & STUDY HABITS\n\n🎯 Purpose: Discover how the student works, interacts socially, and handles stress or complexity.\n\n🧩 Ask:\n- “How would you describe yourself when working on a team—or studying alone?”\n- “When something gets tough, like a test or a stressful situation, how do you usually deal with it?”\n- “Do you feel more energized when you’re around people, or when you have time to yourself?”\n\n📍 If vague or flat:\n> “Can you think of a moment where you really felt in your element, either socially or academically?”\n\n📌 Optional personalization:\n> “I noticed you’re part of [activity]—how does that match your personality?”\n\n---\n\n🏫 3. PREFERRED COLLEGE ENVIRONMENT\n\n🎯 Purpose: Identify the external conditions (social, geographic, academic) where the student feels supported and inspired.\n\n🧩 Ask:\n- “When you picture your future college, what kind of place do you see—big or small, city or more quiet?”\n- “Do you prefer campuses with lots of clubs and events, or ones where things are more low-key and personal?”\n- “How important is being close to nature, a city, or home?”\n\n📍 If hesitant:\n> “It’s okay if you don’t know yet—what kind of spaces make you feel comfortable and energized right now?”\n\n📌 Optional personalization:\n> “You mentioned liking outdoor education—would that be something you’d want in your college life too?”\n\n---\n\n🎭 4. EXTRACURRICULAR PASSIONS & ACTIVITIES\n\n🎯 Purpose: Understand what drives the student outside of academics—what they do for joy, connection, or growth.\n\n🧩 Ask:\n- “What do you love doing outside of class?”\n- “Are there any activities, clubs, or hobbies that you’d really want to continue in college?”\n- “Have you ever taken on a leadership role—or started something yourself?”\n\n📍 If they’re unsure:\n> “What’s something you always look forward to—whether it’s something you do solo, with friends, or with a group?”\n\n📌 Optional personalization:\n> “I saw that you’re involved in [X]. What’s your favorite part about it?”\n\n---\n\n💬 5. PERSONAL VALUES & IDENTITY\n\n🎯 Purpose: Surface the student’s internal compass—what matters to them, where they feel belonging, and how they define meaning.\n\n🧩 Ask:\n- “What values are most important to you—like fairness, creativity, loyalty, growth, or something else?”\n- “Is there a community, tradition, or culture you feel deeply connected to?”\n- “What kind of environment helps you feel safe and fully yourself?”\n\n📍 If they struggle:\n> “Think about a moment when you felt proud of who you are—what made that moment feel right?”\n\n📌 Optional personalization:\n> “In your earlier essays, you talked about [X]—has that shaped your values in any way?”\n\n---\n\n🚀 6. FUTURE GOALS & ASPIRATIONS\n\n🎯 Purpose: Explore what the student envisions for their future—career, impact, lifestyle, or dreams—even if undefined.\n\n🧩 Ask:\n- “When you imagine your future, what kind of life do you see?”\n- “Is there a cause or issue you want to work on—or something you hope to create?”\n- “If nothing could stop you, what would you go out and do?”\n\n📍 If abstract or generalized:\n> “Can you tell me about a moment when you felt like you were doing something that *mattered* to you?”\n\n📌 Optional personalization:\n> “You mentioned being interested in both [X] and [Y]—have you ever thought about combining them?”\n\n---\n\n🔁 FLOW REMINDER\n\nYou do not need to ask these questions in this exact order. Let the student’s responses guide the transitions. Use reflections to highlight themes, connect insights, and reinforce self-awareness—always in 2 sentences or fewer.\n\nYou may now proceed with the conversation using these Thrive Dimensions as your guiding framework.\n🟦 FALLBACK PROTOCOL: Managing Minimal, Vague, or Missing Input\n\nIn this conversation, not all students will immediately offer deep or complete responses. Your job is to gently re-engage them, deepen their reflection, and keep the conversation on track—**without sounding corrective or robotic**.\n\nIf a student provides a short, vague, or off-topic answer, follow these principles:\n\n---\n\n❗ SHORT RESPONSES (under ~20 words)\n\n🧭 Action:\n- Prompt the student with a gentle, targeted follow-up to deepen reflection.\n- Ask for an example, feeling, memory, or comparison to help them elaborate.\n- Never say “Can you say more?” without context. Always anchor your request.\n\n✅ Example Follow-Ups:\n> “That’s a good start—can you tell me about a moment when that really showed up for you?”  \n> “Interesting—what do you think made that stand out?”  \n> “What did that experience feel like for you?”\n\n---\n\n❓ VAGUE RESPONSES (generic or surface-level)\n\n🧭 Action:\n- Ask for clarification by rephrasing the question in simpler or more specific terms.\n- Gently anchor it to something the student has already said or done (if possible).\n\n✅ Example Reframes:\n> “You mentioned you like group work—what role do you usually take when you're in a group?”  \n> “When you say it was 'fun,' what part of it made it feel that way—was it the topic, the people, or something else?”  \n> “Let’s get a little more specific—can you describe how that experience challenged or changed you?”\n\n---\n\n⏳ INCOMPLETE DOMAIN COVERAGE\n\n🧭 Action:\n- If one of the six Thrive Dimensions has not been explored by the midpoint of the conversation, introduce it gently using a transition or thematic segue.\n- Do **not** announce it as a “new section” or checklist item.\n- Instead, use a bridge phrase to make it feel organic.\n\n✅ Example Transitions:\n> “You’ve shared a lot about what energizes you academically. I’m also curious—what do you love doing outside of school?”  \n> “That gives me a sense of how you like to learn—what kind of college *setting* do you think would support that?”  \n> “This all ties into what kind of environment helps you thrive. Let’s talk a bit about where you feel most yourself.”\n\n---\n\n🔁 IF A STUDENT GOES OFF-TOPIC\n\n🧭 Action:\n- Acknowledge their input, then redirect back to the Thrive theme with curiosity.\n- Do not shut down tangents—bridge them.\n\n✅ Redirect Strategy:\n> “That’s an interesting story! I wonder—what did that experience teach you about what kind of setting you feel most comfortable in?”  \n> “Sounds like that was a meaningful moment. Would you say it reflects something you value or care about deeply?”\n\n---\n\n🧘‍♀️ EMOTIONALLY GUARDED OR OVERLY MODEST STUDENTS\n\n🧭 Action:\n- Normalize hesitation. Affirm the difficulty of these questions.\n- Start smaller, or offer framing that allows vulnerability safely.\n\n✅ Gentle On-Ramps:\n> “These can be big questions—it’s totally okay if you’re not sure yet.”  \n> “Let’s start with something simple: what’s something you’ve been drawn to lately, even just for fun?”  \n> “Think about a time you felt really comfortable. What made that setting feel like home?”\n\n---\n\n🟦 REFLECTION & RAPPORT MOMENTS: Building Connection Through Brief Insight\n\nThroughout this conversation, your role is not only to ask questions—but also to **listen actively, notice patterns**, and reflect what the student is revealing about themselves. This builds trust and helps the student feel seen, while also helping them recognize their own strengths and values.\n\nBut your reflections must be **short, authentic, and student-centered**. Always stay under 2 sentences.\n\n---\n\n🧭 MID-CONVERSATION REFLECTIONS (THEME SPOTTING)\n\nIf you notice a recurring theme in the student’s responses—such as creativity, leadership, helping others, or love of nature—name it briefly and check for alignment.\n\n✅ When to reflect:\n- After the student has spoken in 2 or more dimensions\n- When a consistent value, trait, or motivation surfaces\n- When you want to help the student connect the dots\n\n✅ Example Reflections:\n> “A lot of what you’ve said connects to creativity and collaboration. Would you say those are important parts of who you are?”  \n> “I’m hearing that you light up when you’re solving problems that impact others—does that feel true to you?”\n\nDo not force a theme if one hasn’t emerged yet. You may skip the mid-reflection if the student hasn’t opened up enough for it to feel natural.\n\n---\n\n🔁 DOMAIN-BY-DOMAIN REINFORCEMENT (CONVERSATIONAL BACKING)\n\nAt the end of each Thrive Dimension, it’s helpful to reflect back *one* clear takeaway from the student’s answer. These help affirm their voice and improve self-awareness without slowing pace.\n\n✅ Mini-Reflections (always < 2 sentences):\n> “Sounds like you focus best when you can move around and create something hands-on.”  \n> “It’s great that you know you recharge by spending time alone—that’s a strength.”\n\n🛑 Do not repeat the student’s answer. Instead, reframe it as an insight or theme.\n\n---\n\n🧩 FINAL REFLECTION: SYNTHESIZE & VALIDATE\n\nBefore ending the conversation, offer a **concise, personalized summary** of what you’ve learned about what helps the student thrive. Use this to affirm their self-knowledge and preview the value of this conversation moving forward.\n\n✅ Final Summary Template (1–2 sentences max):\n> “From everything you’ve shared, it sounds like you thrive in environments where [insert pattern: ‘creativity, collaboration, and impact’] come together. Does that feel accurate?”\n\n📌 This is the only summary that should cover *multiple domains* at once.\n\n---\n\n🗣️ ALWAYS FOLLOW REFLECTIONS WITH A QUESTION\n\nAfter every reflection—whether mid-convo or final—ask a **short, open-ended question** to keep momentum.\n\n❌ Do not end with a compliment alone.  \n✅ Do end with curiosity.\n\n🟦 CONVERSATION CLOSURE: Ending the Thrive Index Session\n\nWhen the Thrive Index conversation reaches its configured length or token threshold, your goal is to **conclude with warmth and gratitude**, affirm what the student has shared, and gently exit the session.\n\nDo **not** introduce any new prompts, topics, or follow-up questions once you’ve reached the final turn.\n\n---\n\n🧭 Your Final Response Should:\n- Reflect on the insight gained from the student’s sharing\n- Affirm the importance of what the student has contributed\n- Reassure the student that their responses will help Addie better support them in the future\n- Transition toward closure without requesting additional information\n\n---\n\n✅ Closure Template (Use natural variations):\n\n> “Thanks again for sharing so much today—it really helps me understand what matters most to you.”  \n> “This has been a meaningful conversation. You’ve shared a lot that will help guide us moving forward.”  \n> “I appreciate you opening up—everything you’ve said gives me a clearer picture of where you’ll thrive.”\n\n❌ Do Not:\n- Ask another question\n- Invite another topic\n- Suggest a new task or step\n\n---\n\n\n",
            "exampleAnswer": "Any",
            "discussionTime": "30",
            "acceptanceCriteria": "Any",
        },
    }

    # Add steps to workflow
    workflow["steps"] = [step]

    return workflow, step


def create_mock_unstructured_workflow_object(mock_class):
    """
    Create a mock unstructured workflow object from the Mock class, based on the real workflow.

    Args:
        mock_class: The Mock class to use for creating the objects (usually MagicMock)

    Returns:
        A tuple containing (workflow_mock, step_mock) with all the workflow details as mocks.
    """
    workflow_dict, step_dict = create_mock_unstructured_workflow()

    # Create mock workflow object
    mock_workflow = mock_class()
    for key, value in workflow_dict.items():
        setattr(mock_workflow, key, value)

    # Create mock step object
    mock_step = mock_class()
    for key, value in step_dict.items():
        setattr(mock_step, key, value)

    # Set workflow steps
    mock_workflow.steps = [mock_step]

    return mock_workflow, mock_step


def mock_big5_inventory_workflow(new_name=None):
    """
    Creates a hardcoded mock of the Big 5 Inventory workflow
    
    Args:
        new_name: Optional new name for the workflow. If None, uses the original name.
        
    Returns:
        A tuple containing (workflow_dict, steps_list) with all the workflow details
    """
    # Create the workflow object
    workflow_name = "Conversation: Big 5 Inventory" if new_name is None else new_name
    workflow = {
        "id": "cmabe6znv0000i1wgelu8u2mu",
        "name": workflow_name,
        "owner_id": "5b91d6a4-1aa5-4dce-8b00-e816a9fd0f2e",
        "status": WorkflowStatus.PUBLISHED,
        "workflow_type": WorkflowType.STRUCTURED,
        "data": {"survey_type": "likert", "visible_in_admin": True},
        "tags": ["test"],
        "created_at": datetime(2025, 5, 5, 18, 6, 42, 187000, tzinfo=timezone.utc),
        "updated_at": datetime(2025, 6, 12, 19, 8, 16, 166000, tzinfo=timezone.utc),
    }
    
    # All 50 Big Five Inventory questions
    big5_questions = [
        "Am the life of the party.",
        "Feel little concern for others.",
        "Am always prepared.",
        "Get stressed out easily.",
        "Have a rich vocabulary.",
        "Don't talk a lot.",
        "Am interested in people.",
        "Leave my belongings around.",
        "Am relaxed most of the time.",
        "Have difficulty understanding abstract ideas.",
        "Feel comfortable around people.",
        "Insult people.",
        "Pay attention to details.",
        "Worry about things.",
        "Have a vivid imagination.",
        "Keep in the background.",
        "Sympathize with others' feelings.",
        "Make a mess of things.",
        "Seldom feel blue.",
        "Am not interested in abstract ideas.",
        "Start conversations.",
        "Am not interested in other people's problems.",
        "Get chores done right away.",
        "Am easily disturbed.",
        "Have excellent ideas.",
        "Have little to say.",
        "Have a soft heart.",
        "Often forget to put things back in their proper place.",
        "Get upset easily.",
        "Do not have a good imagination.",
        "Talk to a lot of different people at parties.",
        "Am not really interested in others.",
        "Like order.",
        "Change my mood a lot.",
        "Am quick to understand things.",
        "Don't like to draw attention to myself.",
        "Take time out for others.",
        "Shirk my duties.",
        "Have frequent mood swings.",
        "Use difficult words.",
        "Don't mind being the center of attention.",
        "Feel others' emotions.",
        "Follow a schedule.",
        "Get irritated easily.",
        "Spend time reflecting on things.",
        "Am quiet around strangers.",
        "Make people feel at ease.",
        "Am exacting in my work.",
        "Often feel blue.",
        "Am full of ideas."
    ]
    
    # Create steps for all 50 questions
    steps = []
    for i, question in enumerate(big5_questions):
        step = {
            "id": f"cmabe75x9000{i:02d}i1wgrfgxdkne",
            "name": f"Question-LikertQuestion-cmabe73tq000{i:02d}i1wgu5bfd946",
            "goal": "Answer the Likert scale question",
            "index": i,
            "data": {
                "table": "LikertQuestion",
                "canSkip": True,
                "options": [
                    "Strongly Disagree",
                    "Disagree",
                    "Neutral",
                    "Agree",
                    "Strongly Agree"
                ],
                "question": question,
                "questionId": f"cmabe73tq000{i:02d}i1wgu5bfd946"
            },
            "created_at": datetime(2025, 5, 5, 18, 6, 50, 301000, tzinfo=timezone.utc),
            "updated_at": datetime(2025, 6, 12, 17, 20, 48, 142000, tzinfo=timezone.utc),
        }
        steps.append(step)
    
    # Add steps to workflow
    workflow["steps"] = steps
    
    return workflow, steps


def mock_structured_workflow(student_id, prisma=None, new_name=None, owner_id=None, workflow_id=None):

    """
    Create a mock structured workflow in the database.
    This function creates the workflow, workflow steps, and student workflow records
    using the complete Big 5 Inventory data with all 50 steps.
    
    Args:
        student_id: The student ID to associate with this workflow
        prisma: The Prisma client to use. If None, a new client will be created and disconnected.
        new_name: Optional new name for the workflow. If None, uses the original name.
        owner_id: Optional owner ID for the workflow. If None, uses the original owner ID.
        workflow_id: Optional ID to use for workflow. Mostly for backward compatibility.
    
    Returns:
        A tuple containing (workflow_id, step_id, student_workflow_id, workflow_dict, step_dict) with
        IDs of created records and dictionaries with workflow details.
    """
    import json
    import alog
    from prisma.enums import WorkflowType
    
    # Set up logger and prisma client
    logger = alog.getLogger(__name__)
    should_disconnect = False
    
    if prisma is None:
        from addie.lib import prisma_client
        prisma = prisma_client()
        should_disconnect = True
        
    try:
        # Get the complete Big 5 Inventory workflow from the original
        original_workflow = prisma.workflow.find_unique(
            where={"id": "cmabe6znv0000i1wgelu8u2mu"},
            include={"steps": True}
        )
        
        if not original_workflow:
            logger.error("Original Big 5 Inventory workflow not found!")
            # Fall back to mock version with all 50 steps
            workflow_dict, steps_list = mock_big5_inventory_workflow(new_name)
            
            # Create workflow
            new_workflow_data = {
                "name": workflow_dict["name"],
                "owner": {"connect": {"id": owner_id if owner_id else workflow_dict["owner_id"]}},
                "status": "PUBLISHED",
                "workflow_type": WorkflowType.STRUCTURED,
                "data": json.dumps(workflow_dict["data"]) if isinstance(workflow_dict["data"], dict) else workflow_dict["data"],
                "tags": workflow_dict.get("tags", ["test", "structured"])
            }
            
            new_workflow = prisma.workflow.create(data=new_workflow_data)
            new_workflow_id = new_workflow.id
            
            # Create all 50 steps
            step_id = None
            for step_dict in steps_list:
                new_step_data = {
                    "name": step_dict["name"],
                    "goal": step_dict["goal"],
                    "index": step_dict["index"],
                    "data": json.dumps(step_dict["data"]) if isinstance(step_dict["data"], dict) else step_dict["data"],
                    "parent_workflow": {"connect": {"id": new_workflow_id}}
                }
                
                new_step = prisma.workflowstep.create(data=new_step_data)
                
                # Set step_id to the first step for compatibility
                if step_id is None:
                    step_id = new_step.id
            
            # Use the first step for step_dict compatibility
            step_dict = steps_list[0]
        else:
            # Create workflow with all 50 steps from the original
            workflow_name = new_name if new_name else original_workflow.name
            
            new_workflow_data = {
                "name": workflow_name,
                "owner": {"connect": {"id": owner_id if owner_id else original_workflow.owner_id}},
                "status": "PUBLISHED",
                "workflow_type": WorkflowType.STRUCTURED,
                "data": json.dumps(original_workflow.data) if isinstance(original_workflow.data, dict) else original_workflow.data,
                "tags": ["test", "structured"]
            }
            
            logger.debug(f"Creating Big 5 Inventory workflow with {len(original_workflow.steps)} steps")
            new_workflow = prisma.workflow.create(data=new_workflow_data)
            new_workflow_id = new_workflow.id
            
            # Create all workflow steps
            step_id = None  # Will be set to the first step's ID
            sorted_steps = sorted(original_workflow.steps, key=lambda x: x.index)
            
            for step in sorted_steps:
                new_step_data = {
                    "name": step.name,
                    "goal": step.goal,
                    "index": step.index,
                    "data": json.dumps(step.data) if isinstance(step.data, dict) else step.data,
                    "parent_workflow": {"connect": {"id": new_workflow_id}}
                }
                
                new_step = prisma.workflowstep.create(data=new_step_data)
                
                # Set step_id to the first step for compatibility
                if step_id is None:
                    step_id = new_step.id
            
            # Create workflow_dict and step_dict for compatibility
            workflow_dict = {
                "name": workflow_name,
                "owner_id": owner_id if owner_id else original_workflow.owner_id,
                "status": "PUBLISHED",
                "workflow_type": "STRUCTURED",
                "data": original_workflow.data,
                "tags": ["test", "structured"]
            }
            
            # Use the first step for step_dict
            first_step = sorted_steps[0]
            step_dict = {
                "name": first_step.name,
                "goal": first_step.goal,
                "index": first_step.index,
                "data": first_step.data
            }
        
        # Create the student workflow record
        student_workflow = prisma.studentworkflow.create(
            data={
                "student": {"connect": {"id": student_id}},
                "workflow": {"connect": {"id": new_workflow_id}},
                "mode": "sms",  # Use SMS mode since this is for SMS testing
                "status": "IN_PROGRESS"
            }
        )
        student_workflow_id = student_workflow.id
        
        # Create student workflow steps for all workflow steps
        workflow_steps = prisma.workflowstep.find_many(
            where={"parent_workflow_id": new_workflow_id},
            order={"index": "asc"}
        )
        
        logger.debug(f"Creating {len(workflow_steps)} student workflow steps")
        
        for step in workflow_steps:
            student_step = prisma.studentworkflowstep.create(
                data={
                    "student": {"connect": {"id": student_id}},
                    "step": {"connect": {"id": step.id}},
                    "student_workflow": {"connect": {"id": student_workflow_id}},
                    "completed": False,
                    "data": step.data if isinstance(step.data, str) else json.dumps(step.data)
                }
            )
        
        # Verify the student workflow has the expected number of steps
        updated_sw = prisma.studentworkflow.find_unique(
            where={"id": student_workflow_id},
            include={"steps": True}
        )
        
        logger.debug(f"Student workflow has {len(updated_sw.steps)} steps")
        
        # Verify no steps are marked as completed
        completed_steps = [step for step in updated_sw.steps if step.completed]
        if completed_steps:
            logger.warning(f"Found {len(completed_steps)} completed steps - this may cause issues")
    
        return new_workflow_id, step_id, student_workflow_id, workflow_dict, step_dict
        
    finally:
        # Disconnect prisma client if we created it here
        if should_disconnect and prisma:
            prisma.disconnect()


def create_mock_structured_workflow_object(mock_class, workflow_id, new_name=None):
    """
    Create a mock workflow object from the Mock class, based on a real workflow from the database.
    
    Args:
        mock_class: The Mock class to use for creating the objects (usually MagicMock)
        workflow_id: The ID of the workflow to mimic (e.g., "cmabe6znv0000i1wgelu8u2mu")
        new_name: Optional new name for the workflow. If None, uses the original name.
    
    Returns:
        A tuple containing (workflow_mock, step_mock) with all the workflow details as mocks.
    """
    workflow_dict, step_dict = mock_structured_workflow(workflow_id, new_name)
    
    # Create mock workflow object
    mock_workflow = mock_class()
    for key, value in workflow_dict.items():
        setattr(mock_workflow, key, value)
    
    # Create mock step object
    mock_step = mock_class()
    if step_dict:
        for key, value in step_dict.items():
            setattr(mock_step, key, value)
        
        # Set workflow steps
        mock_workflow.steps = [mock_step]
    else:
        mock_workflow.steps = []
    
    return mock_workflow, mock_step
