"""
Structured SMS conversation runner for end-to-end testing.

This module provides a runner class that conducts realistic SMS conversations
between a student agent and the structured SMS agent for Big Five Inventory workflow.
"""

import json
import alog
import asyncio
from typing import Dict, List, Optional, Callable
from langchain_core.messages import HumanMessage
from addie.sms_agent.agent import process_sms_message, get_session_id_for_sms
from addie.student_agent.invoke_student import invoke_student
from tests.utils.test_helpers import (
    create_student_agent_with_state,
    cleanup_student_agent_config,
)
from addie.lib import prisma_client

# Configure logger
logger = alog.getLogger()


class StructuredSMSConversationRunner:
    """
    Runs realistic SMS conversations between a student agent and structured SMS agent.
    
    This class encapsulates the logic for conducting turn-based conversations where:
    - Student agent generates authentic student responses to Big Five questions
    - Structured SMS agent provides questions and processes answers
    - Conversation continues until all 50 questions are answered
    """
    
    def __init__(self, student_id: str, user_id: str, workflow_id: str, phone_number: str = "+15551234567"):
        self.student_id = student_id
        self.user_id = user_id
        self.workflow_id = workflow_id
        self.phone_number = phone_number
        self.conversation_log = []
        self.student_agent = None
        self.student_state = None
        self.config_id = None
        
        # Track workflow progress
        self.questions_answered = 0
        self.total_questions = 50
        self.workflow_completed = False
        
    def setup_student_agent(self, prompt_content: str, role: str = "student"):
        """Initialize the student agent with specified persona
        
        Args:
            prompt_content: The persona/prompt for the student agent
            role: The role for the agent (default: "student")
        """
        self.student_agent, self.student_state, self.config_id = create_student_agent_with_state(
            student_id=self.student_id,
            user_id=self.user_id,
            prompt_content=prompt_content,
            role=role
        )
        
        # Start with an initial prompt to the student agent
        initial_prompt = HumanMessage(content="Hi! I'm ready to start the Big Five Personality Inventory. Let's begin!")
        student_msgs = [initial_prompt]
        self.student_state["messages"] = student_msgs
    
    def check_workflow_completion(self) -> bool:
        """Check if the structured workflow is completed
        
        Returns:
            True if all questions are answered, False otherwise
        """
        try:
            prisma = prisma_client()
            
            # Check how many questions have been answered
            question_responses = prisma.questionresponse.find_many(
                where={"student_id": self.student_id}
            )
            
            self.questions_answered = len(question_responses)
            
            # Check if all student workflow steps are completed
            student_steps = prisma.studentworkflowstep.find_many(
                where={
                    "student_id": self.student_id,
                    "student_workflow": {
                        "workflow_id": self.workflow_id
                    }
                }
            )
            
            completed_steps = [step for step in student_steps if step.completed]
            
            logger.info(f"Workflow progress: {len(completed_steps)}/{len(student_steps)} steps completed, {self.questions_answered} responses saved")
            
            # Workflow is completed if all steps are completed or we have 50 responses
            self.workflow_completed = len(completed_steps) == len(student_steps) or self.questions_answered >= self.total_questions
            
            return self.workflow_completed
            
        except Exception as e:
            logger.error(f"Error checking workflow completion: {e}")
            return False
    
    def run_conversation(self, max_turns: int = 200, progress_callback: Optional[Callable] = None):
        """
        Run the conversation between student and structured SMS agent
        
        Args:
            max_turns: Maximum number of conversation turns
            progress_callback: Optional function to check workflow completion
            
        Returns:
            Dict with conversation results and statistics
        """
        turn = 0
        
        logger.info("Starting structured SMS conversation for Big Five Inventory")
        
        while turn < max_turns:
            logger.info(f"\n--- Turn {turn + 1} ---")
            
            # Check workflow completion
            if self.check_workflow_completion():
                logger.info("Structured workflow completed! All questions answered.")
                break
            
            if turn % 2 == 0:
                # Student turn - generate response using student agent
                logger.info("Student agent generating response...")
                self.student_state = invoke_student(self.student_state)
                student_msgs = self.student_state["messages"]

                # Get the last message from student
                last_student_msg = student_msgs[-1]
                student_message = last_student_msg.content

                logger.info(f"Student: {student_message}")

                # Store in conversation log
                self.conversation_log.append({
                    "speaker": "student",
                    "message": student_message,
                    "turn": turn + 1
                })

            else:
                # Structured SMS Agent turn - get response from structured SMS agent
                logger.info("Structured SMS agent responding...")
                
                # Get the most recent student message
                last_student_message = self.conversation_log[-1]["message"]
                
                # Call structured SMS agent
                try:
                    sms_response = process_sms_message(
                        student_id=self.student_id,
                        message_text=last_student_message,
                        phone_number=self.phone_number,
                        workflow_id=self.workflow_id
                    )

                    logger.info(f"Structured SMS Agent: {sms_response}")

                    # Store in conversation log
                    self.conversation_log.append({
                        "speaker": "structured_sms_agent",
                        "message": sms_response,
                        "turn": turn + 1,
                        "questions_answered": self.questions_answered
                    })

                    # Feed SMS agent response back to student agent for next turn
                    agent_msg = HumanMessage(content=sms_response)
                    student_msgs = self.student_state["messages"]
                    student_msgs.append(agent_msg)
                    self.student_state["messages"] = student_msgs
                    
                except Exception as e:
                    logger.error(f"Error calling structured SMS agent: {e}")
                    break
            
            # Increment turn counter
            turn += 1
            
            # Check completion after each exchange
            if progress_callback and progress_callback():
                logger.info("Custom progress callback indicates completion")
                break
        
        # Calculate results
        total_turns = len(self.conversation_log)
        student_turns = len([msg for msg in self.conversation_log if msg["speaker"] == "student"])
        agent_turns = len([msg for msg in self.conversation_log if msg["speaker"] == "structured_sms_agent"])
        
        results = {
            "total_turns": total_turns,
            "student_turns": student_turns,
            "agent_turns": agent_turns,
            "questions_answered": self.questions_answered,
            "workflow_completed": self.workflow_completed,
            "conversation_log": self.conversation_log,
            "completion_percentage": (self.questions_answered / self.total_questions) * 100
        }
        
        logger.info(f"Conversation completed: {total_turns} turns, {self.questions_answered}/{self.total_questions} questions answered")
        
        return results
    
    def cleanup(self):
        """Clean up resources"""
        if self.config_id:
            cleanup_student_agent_config(self.config_id)
    
    def get_conversation_summary(self) -> Dict:
        """Get a summary of the conversation"""
        return {
            "total_messages": len(self.conversation_log),
            "questions_answered": self.questions_answered,
            "workflow_completed": self.workflow_completed,
            "last_10_messages": self.conversation_log[-10:] if len(self.conversation_log) > 10 else self.conversation_log
        }


# Example student personas for testing
STUDENT_PERSONAS = {
    "extrovert": """
    You are a high school senior who is outgoing, talkative, and energetic. You love being around people,
    starting conversations, and being the center of attention. You're confident, enthusiastic, and social.
    When answering Big Five Personality Inventory questions, respond authentically as this extroverted student.
    You can answer with numbers (1-5) or words (Strongly Disagree, Disagree, Neutral, Agree, Strongly Agree).
    Keep your responses concise but authentic to your personality.
    """,
    
    "introvert": """
    You are a high school senior who is quiet, thoughtful, and prefers smaller groups or alone time.
    You're introspective, careful with words, and more comfortable listening than speaking. You're organized,
    detail-oriented, and thoughtful in your responses.
    When answering Big Five Personality Inventory questions, respond authentically as this introverted student.
    You can answer with numbers (1-5) or words (Strongly Disagree, Disagree, Neutral, Agree, Strongly Agree).
    Keep your responses concise but authentic to your personality.
    """,
    
    "balanced": """
    You are a high school senior with a balanced personality. You're sometimes outgoing and sometimes quiet,
    depending on the situation. You're reasonably organized but not obsessive, generally agreeable but can
    stand up for yourself when needed.
    When answering Big Five Personality Inventory questions, respond authentically as this balanced student.
    You can answer with numbers (1-5) or words (Strongly Disagree, Disagree, Neutral, Agree, Strongly Agree).
    Keep your responses concise but authentic to your personality.
    """
}