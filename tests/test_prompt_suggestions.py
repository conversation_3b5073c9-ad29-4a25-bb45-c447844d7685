from addie.api.app import router
from addie.lib import prisma_client
from starlette.testclient import TestClient
import alog

client = TestClient(router)


def test_student_prompt_suggestions():
    student = prisma_client().student.find_first(
        where=dict(
            question_response=dict(some=dict()),
            teacher_comments=dict(some=dict())
        ),
        include=dict(question_response=True)
    )

    params = dict(student_id=student.id)

    # No API key needed in development environment - the verify_api_key function bypasses validation
    response = client.post("/student/prompt_suggestions", json=params)
    alog.info(response.status_code)
    alog.info(response.json())

    assert response.status_code == 200
    response = response.json()

    assert len(response['suggestions']) > 0
