#!/usr/bin/env python3
"""
TestCase-based tests for disable_system_prompt functionality
Usage: python -m pytest tests/test_disable_system_prompt.py -v
       or: python tests/test_disable_system_prompt.py
"""

import sys
import os
import unittest
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'addie'))

from addie.agent.call_model import gen_system_prompt
from tests.utils.base_test_cases import BaseWorkflowTestCase

class TestDisableSystemPrompt(BaseWorkflowTestCase):
    """Test cases for disable_system_prompt functionality"""

    def setUp(self):
        """Set up before each test method"""
        # Call parent setup to create workflow, user, student
        super().setUp()

        # Standard student context for testing
        self.student_context = {
            "workflow_id": self.workflow_id,
            "mode": "web",
            "current_question": {
                "step_id": "test_step_123"
            }
        }

        # Reset workflow to enabled state before each test
        self.prisma.workflow.update(
            where={"id": self.workflow_id},
            data={"disable_system_prompt": False}
        )

    def test_system_prompt_enabled_generates_longer_prompt(self):
        """Test that enabled system prompt generates longer prompts"""
        # Generate prompt with system prompt enabled
        prompt_enabled = gen_system_prompt(self.student_context)

        # Basic assertions
        self.assertIsInstance(prompt_enabled, str)
        self.assertGreater(len(prompt_enabled), 0)

        # Should contain mode information
        self.assertIn("web", prompt_enabled)

        print(f"✅ Enabled prompt length: {len(prompt_enabled)} characters")

    def test_system_prompt_disabled_generates_shorter_prompt(self):
        """Test that disabled system prompt generates shorter prompts"""
        # First get enabled prompt for comparison
        prompt_enabled = gen_system_prompt(self.student_context)

        # Disable system prompt
        self.prisma.workflow.update(
            where={"id": self.workflow_id},
            data={"disable_system_prompt": True}
        )

        # Generate prompt with system prompt disabled
        prompt_disabled = gen_system_prompt(self.student_context)

        # Assertions
        self.assertIsInstance(prompt_disabled, str)
        self.assertGreater(len(prompt_disabled), 0)
        self.assertLess(len(prompt_disabled), len(prompt_enabled))

        print(f"✅ Disabled prompt length: {len(prompt_disabled)} characters")
        print(f"✅ Difference: {len(prompt_enabled) - len(prompt_disabled)} characters")

    def test_prompts_are_different_when_disabled(self):
        """Test that enabled and disabled prompts have different content"""
        # Get enabled prompt
        prompt_enabled = gen_system_prompt(self.student_context)

        # Disable system prompt and get disabled prompt
        self.prisma.workflow.update(
            where={"id": self.workflow_id},
            data={"disable_system_prompt": True}
        )
        prompt_disabled = gen_system_prompt(self.student_context)

        # Prompts should be different
        self.assertNotEqual(prompt_enabled, prompt_disabled)

        print("✅ Enabled and disabled prompts are different")

    def test_system_prompt_content_removed_when_disabled(self):
        """Test that system prompt content is actually removed when disabled"""
        # Get AddieConfig to check system prompt content
        addie_config = self.prisma.addieconfig.find_first(
            include={"prompt": True, "system_prompt": True}
        )

        if not addie_config or not addie_config.system_prompt:
            self.skipTest("No system prompt configured in AddieConfig")

        system_prompt_content = addie_config.system_prompt.content
        
        # Skip test if system prompt content is empty or None
        if not system_prompt_content or len(system_prompt_content.strip()) == 0:
            self.skipTest("System prompt content is empty")

        # Get enabled prompt
        prompt_enabled = gen_system_prompt(self.student_context)

        # Disable system prompt and get disabled prompt
        self.prisma.workflow.update(
            where={"id": self.workflow_id},
            data={"disable_system_prompt": True}
        )
        prompt_disabled = gen_system_prompt(self.student_context)

        # Check system prompt presence - use a meaningful substring, not empty string
        system_prompt_preview = system_prompt_content[:50] if len(system_prompt_content) > 50 else system_prompt_content

        # System prompt should be in enabled but not in disabled
        # Only test if we have a meaningful system prompt preview
        if system_prompt_preview.strip():
            self.assertIn(system_prompt_preview, prompt_enabled)
            self.assertNotIn(system_prompt_preview, prompt_disabled)
            print(f"✅ System prompt ({len(system_prompt_content)} chars) correctly removed when disabled")
        else:
            self.skipTest("System prompt preview is empty or only whitespace")

    def test_session_prompt_removed_when_disabled(self):
        """Test that both system and session prompts are removed when system prompt is disabled"""
        # Get AddieConfig to check session prompt content
        addie_config = self.prisma.addieconfig.find_first(
            include={"prompt": True, "system_prompt": True}
        )

        if not addie_config or not addie_config.prompt:
            self.skipTest("No session prompt configured in AddieConfig")

        session_prompt_content = addie_config.prompt.content
        
        # Skip test if session prompt content is empty or None
        if not session_prompt_content or len(session_prompt_content.strip()) == 0:
            self.skipTest("Session prompt content is empty")
            
        session_prompt_preview = session_prompt_content[:50] if len(session_prompt_content) > 50 else session_prompt_content

        # Disable system prompt
        self.prisma.workflow.update(
            where={"id": self.workflow_id},
            data={"disable_system_prompt": True}
        )

        # Get disabled prompt
        prompt_disabled = gen_system_prompt(self.student_context)

        # Session prompt should NOT be present when disabled (new requirement)
        # Only test if we have a meaningful session prompt preview
        if session_prompt_preview.strip():
            self.assertNotIn(session_prompt_preview, prompt_disabled)
        else:
            self.skipTest("Session prompt preview is empty or only whitespace")

        # Should only contain basic context and mode info
        self.assertIn("You are currently running in", prompt_disabled)
        self.assertIn("## context start ##", prompt_disabled)
        self.assertIn("## context end ##", prompt_disabled)

        print(f"✅ Session prompt ({len(session_prompt_content)} chars) correctly removed when system prompt disabled")

    def test_workflow_disable_field_persistence(self):
        """Test that disable_system_prompt field persists correctly"""
        # Test setting to True
        self.prisma.workflow.update(
            where={"id": self.workflow_id},
            data={"disable_system_prompt": True}
        )

        workflow = self.prisma.workflow.find_unique(where={"id": self.workflow_id})
        self.assertTrue(workflow.disable_system_prompt)

        # Test setting to False
        self.prisma.workflow.update(
            where={"id": self.workflow_id},
            data={"disable_system_prompt": False}
        )

        workflow = self.prisma.workflow.find_unique(where={"id": self.workflow_id})
        self.assertFalse(workflow.disable_system_prompt)

        print("✅ disable_system_prompt field persists correctly")


if __name__ == "__main__":
    # Run tests with verbose output
    unittest.main(verbosity=2)
