"""
Tests for the engagement events backfill script
"""

import unittest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from tests.utils.base_test_cases import BaseWorkflowTestCase

from scripts.backfill_engagement_events import EngagementEventBackfiller
from prisma.enums import EngagementChannel, EngagementEventType, EngagementTier
from addie.lib import dict_to_json


class TestEngagementEventBackfiller(BaseWorkflowTestCase):
    """Test the EngagementEventBackfiller class"""
    
    def setUp(self):
        """Set up test environment"""
        super().setUp()
        self.backfiller = EngagementEventBackfiller(prisma=self.prisma, dry_run=True)
    
    def create_test_message_data(self, message_type="human", content="Test message", source=None):
        """Create properly formatted message data for Prisma"""
        additional_kwargs = {}
        if source:
            additional_kwargs['source'] = source
        
        return dict_to_json({
            'data': {
                'id': None, 
                'name': None, 
                'type': message_type, 
                'content': content, 
                'example': False, 
                'additional_kwargs': additional_kwargs, 
                'response_metadata': {}
            }, 
            'type': message_type
        })
    
    def test_determine_channel_from_session_sms(self):
        """Test channel determination for SMS sessions"""
        # Test various SMS session patterns
        test_cases = [
            ("sms-student-123", EngagementChannel.sms),
            ("workflow-sms-456", EngagementChannel.sms),
            ("SMS-WORKFLOW", EngagementChannel.sms),
        ]
        
        for session_id, expected_channel in test_cases:
            with self.subTest(session_id=session_id):
                result = self.backfiller.determine_channel_from_session(session_id)
                self.assertEqual(result, expected_channel)
    
    def test_determine_channel_from_session_counselor(self):
        """Test channel determination for counselor sessions"""
        test_cases = [
            ("counselor-123", EngagementChannel.web_counselor),
            ("workflow-counselor-456", EngagementChannel.web_counselor),
            ("COUNSELOR-SESSION", EngagementChannel.web_counselor),
        ]
        
        for session_id, expected_channel in test_cases:
            with self.subTest(session_id=session_id):
                result = self.backfiller.determine_channel_from_session(session_id)
                self.assertEqual(result, expected_channel)
    
    def test_determine_channel_from_session_voice(self):
        """Test channel determination for voice sessions"""
        test_cases = [
            ("voice-call-123", EngagementChannel.voice),
            ("call-session-456", EngagementChannel.voice),
            ("VOICE-WORKFLOW", EngagementChannel.voice),
        ]
        
        for session_id, expected_channel in test_cases:
            with self.subTest(session_id=session_id):
                result = self.backfiller.determine_channel_from_session(session_id)
                self.assertEqual(result, expected_channel)
    
    def test_determine_channel_from_session_default(self):
        """Test channel determination defaults to web_student"""
        test_cases = [
            ("regular-session", EngagementChannel.web_student),
            ("unknown-pattern", EngagementChannel.web_student),
            ("", EngagementChannel.web_student),
            (None, EngagementChannel.web_student),
        ]
        
        for session_id, expected_channel in test_cases:
            with self.subTest(session_id=session_id):
                result = self.backfiller.determine_channel_from_session(session_id)
                self.assertEqual(result, expected_channel)
    
    def test_determine_event_type_from_message(self):
        """Test event type determination from message data"""
        test_cases = [
            ({"type": "human"}, EngagementEventType.message_received),
            ({"type": "ai"}, EngagementEventType.message_sent),
            ({"type": "AI"}, EngagementEventType.message_sent),
            ({"type": "HUMAN"}, EngagementEventType.message_received),
            ({"type": "unknown"}, EngagementEventType.message_received),
            ({}, EngagementEventType.message_received),
        ]
        
        for message_data, expected_type in test_cases:
            with self.subTest(message_data=message_data):
                result = self.backfiller.determine_event_type_from_message(message_data)
                self.assertEqual(result, expected_type)
    
    def test_extract_student_id_from_session(self):
        """Test student ID extraction from session ID"""
        # Create a test student to verify against
        test_student = self.prisma.student.create(
            data={
                "grade": 12,
                "student_id": "test-student-123"
            }
        )
        
        try:
            # Test session with student ID
            session_with_student = f"workflow-{test_student.id}"
            result = self.backfiller.extract_student_id_from_session(session_with_student)
            self.assertEqual(result, test_student.id)
            
            # Test session without student ID
            result = self.backfiller.extract_student_id_from_session("short-session")
            self.assertIsNone(result)
            
            # Test empty/None session
            result = self.backfiller.extract_student_id_from_session("")
            self.assertIsNone(result)
            
            result = self.backfiller.extract_student_id_from_session(None)
            self.assertIsNone(result)
            
        finally:
            # Clean up test student
            self.prisma.student.delete(where={"id": test_student.id})
    
    def test_get_user_id_for_message_with_student(self):
        """Test getting user ID when student is provided"""
        # Create a test user and student with relationship
        test_user = self.prisma.user.create(
            data={
                "email": "<EMAIL>",
                "first_name": "Test",
                "last_name": "User",
                "role": "STUDENT"
            }
        )
        
        test_student = self.prisma.student.create(
            data={
                "grade": 12,
                "student_id": "test-student-123",
                "users": {
                    "connect": [{"id": test_user.id}]
                }
            }
        )
        
        try:
            result = self.backfiller.get_user_id_for_message({}, test_student.id)
            self.assertEqual(result, test_user.id)
            
        finally:
            # Clean up
            self.prisma.student.delete(where={"id": test_student.id})
            self.prisma.user.delete(where={"id": test_user.id})
    
    def test_get_user_id_for_message_fallback(self):
        """Test user ID fallback when no student provided"""
        result = self.backfiller.get_user_id_for_message({}, None)
        # Should return either first available user or system fallback
        self.assertIsNotNone(result)
    
    def test_message_already_has_event(self):
        """Test checking if message already has engagement event"""
        # Create a test message
        test_message = self.prisma.messages.create(
            data={
                "session_id": "test-session",
                "message": self.create_test_message_data("human", "test message")
            }
        )
        
        try:
            # Initially should not have event
            result = self.backfiller.message_already_has_event(test_message.id)
            self.assertFalse(result)
            
            # Create an engagement event for this message
            self.prisma.engagementevent.create(
                data={
                    "user_id": self.user_id,
                    "student_id": self.student_id,
                    "session_id": "test-session",
                    "channel": EngagementChannel.web_student,
                    "event_type": EngagementEventType.message_received,
                    "message_id": test_message.id,
                    "engaged_at": datetime.utcnow()
                }
            )
            
            # Now should have event
            result = self.backfiller.message_already_has_event(test_message.id)
            self.assertTrue(result)
            
        finally:
            # Clean up
            self.prisma.engagementevent.delete_many(
                where={"message_id": test_message.id}
            )
            self.prisma.messages.delete(where={"id": test_message.id})
    
    def test_process_message_dry_run(self):
        """Test processing a message in dry run mode"""
        # Create a test message
        test_message = self.prisma.messages.create(
            data={
                "session_id": f"test-{self.student_id}",
                "message": self.create_test_message_data("human", "Hello, I need help with college applications")
            }
        )
        
        try:
            # Process message (dry run)
            result = self.backfiller.process_message(test_message)
            self.assertTrue(result)
            
            # Verify statistics were updated
            self.assertEqual(self.backfiller.stats['events_created'], 1)
            
            # Verify no actual event was created (dry run)
            events = self.prisma.engagementevent.find_many(
                where={"message_id": test_message.id}
            )
            self.assertEqual(len(events), 0)
            
        finally:
            # Clean up
            self.prisma.messages.delete(where={"id": test_message.id})
    
    def test_process_message_actual_creation(self):
        """Test processing a message with actual event creation"""
        # Create backfiller without dry run
        backfiller = EngagementEventBackfiller(prisma=self.prisma, dry_run=False)
        
        # Create a test message
        test_message = self.prisma.messages.create(
            data={
                "session_id": f"test-{self.student_id}",
                "message": self.create_test_message_data("ai", "Hello! How can I help you today?")
            }
        )
        
        try:
            # Process message (actual creation)
            result = backfiller.process_message(test_message)
            self.assertTrue(result)
            
            # Verify event was created
            events = self.prisma.engagementevent.find_many(
                where={"message_id": test_message.id}
            )
            self.assertEqual(len(events), 1)
            
            event = events[0]
            self.assertEqual(event.event_type, EngagementEventType.message_sent)
            self.assertEqual(event.channel, EngagementChannel.web_student)
            self.assertEqual(event.student_id, self.student_id)
            self.assertIsNotNone(event.metadata)
            
            # Verify metadata contains backfill information
            metadata = event.metadata
            self.assertTrue(metadata.get("backfilled"))
            self.assertEqual(metadata.get("original_session_id"), test_message.session_id)
            
        finally:
            # Clean up
            self.prisma.engagementevent.delete_many(
                where={"message_id": test_message.id}
            )
            self.prisma.messages.delete(where={"id": test_message.id})
    
    def test_process_message_skip_existing(self):
        """Test that processing skips messages that already have events"""
        # Create a test message
        test_message = self.prisma.messages.create(
            data={
                "session_id": "test-session",
                "message": self.create_test_message_data("human", "test message")
            }
        )
        
        # Create existing engagement event
        existing_event = self.prisma.engagementevent.create(
            data={
                "user_id": self.user_id,
                "student_id": self.student_id,
                "session_id": "test-session",
                "channel": EngagementChannel.web_student,
                "event_type": EngagementEventType.message_received,
                "message_id": test_message.id,
                "engaged_at": datetime.utcnow()
            }
        )
        
        try:
            # Process message
            result = self.backfiller.process_message(test_message)
            self.assertFalse(result)
            
            # Verify statistics
            self.assertEqual(self.backfiller.stats['skipped_existing'], 1)
            self.assertEqual(self.backfiller.stats['events_created'], 0)
            
        finally:
            # Clean up
            self.prisma.engagementevent.delete(where={"id": existing_event.id})
            self.prisma.messages.delete(where={"id": test_message.id})
    
    def test_process_message_error_handling(self):
        """Test error handling during message processing"""
        # Create a message that will cause an error during student lookup
        # Use a session with an invalid student ID pattern
        test_message = self.prisma.messages.create(
            data={
                "session_id": "workflow-invalid-student-id-123456789012345678901234567890",  # Too long, invalid format
                "message": self.create_test_message_data("human", "test message")
            }
        )
        
        # Mock the extract_student_id_from_session to raise an exception
        original_method = self.backfiller.extract_student_id_from_session
        def mock_extract_error(session_id):
            raise Exception("Simulated database error")
        
        self.backfiller.extract_student_id_from_session = mock_extract_error
        
        try:
            # Process should handle the error gracefully
            result = self.backfiller.process_message(test_message)
            self.assertFalse(result)
            
            # Verify error was tracked
            self.assertEqual(self.backfiller.stats['errors'], 1)
        finally:
            # Restore original method
            self.backfiller.extract_student_id_from_session = original_method
            # Clean up
            self.prisma.messages.delete(where={"id": test_message.id})
    
    def test_update_student_tiers_dry_run(self):
        """Test updating student tiers in dry run mode"""
        # Should not make any changes in dry run
        self.backfiller.update_student_tiers()
        
        # No actual changes should be made
        self.assertEqual(self.backfiller.stats['students_updated'], 0)
    
    def test_run_with_limit(self):
        """Test running backfill with message limit"""
        # Create multiple test messages
        messages = []
        for i in range(5):
            message = self.prisma.messages.create(
                data={
                    "session_id": f"test-session-{i}",
                    "message": self.create_test_message_data("human", f"message {i}")
                }
            )
            messages.append(message)
        
        try:
            # Run with limit of 3
            self.backfiller.run(limit=3, batch_size=2)
            
            # Should have processed exactly 3 messages
            self.assertEqual(self.backfiller.stats['messages_processed'], 3)
            
        finally:
            # Clean up
            for message in messages:
                self.prisma.messages.delete(where={"id": message.id})
    
    @patch('scripts.backfill_engagement_events.logger')
    def test_log_final_stats(self, mock_logger):
        """Test that final statistics are logged correctly"""
        # Set some statistics
        self.backfiller.stats = {
            'messages_processed': 100,
            'events_created': 85,
            'students_updated': 25,
            'errors': 2,
            'skipped_existing': 13
        }
        
        # Call log_final_stats
        self.backfiller.log_final_stats()
        
        # Verify logging calls were made with expected content
        mock_logger.info.assert_called()
        
        # Check that key statistics were logged
        log_calls = [call[0][0] for call in mock_logger.info.call_args_list]
        log_content = ' '.join(log_calls)
        
        self.assertIn('100', log_content)  # messages_processed
        self.assertIn('85', log_content)   # events_created
        self.assertIn('25', log_content)   # students_updated
        self.assertIn('2', log_content)    # errors
        self.assertIn('13', log_content)   # skipped_existing
    
    def test_batch_processing(self):
        """Test that batch processing works correctly"""
        # Create 7 test messages
        messages = []
        for i in range(7):
            message = self.prisma.messages.create(
                data={
                    "session_id": f"batch-test-{i}",
                    "message": self.create_test_message_data("human", f"batch message {i}")
                }
            )
            messages.append(message)
        
        try:
            # Run with batch size of 3, limit to just our test messages
            self.backfiller.run(batch_size=3, limit=7)
            
            # Should have processed all messages
            self.assertEqual(self.backfiller.stats['messages_processed'], 7)
            
        finally:
            # Clean up
            for message in messages:
                self.prisma.messages.delete(where={"id": message.id})


class TestEngagementEventBackfillerIntegration(BaseWorkflowTestCase):
    """Integration tests for the backfill script"""
    
    def setUp(self):
        """Set up test environment"""
        super().setUp()
        # Use actual database operations for integration tests
        self.backfiller = EngagementEventBackfiller(prisma=self.prisma, dry_run=False)
    
    def create_test_message_data(self, message_type="human", content="Test message", source=None):
        """Create properly formatted message data for Prisma"""
        additional_kwargs = {}
        if source:
            additional_kwargs['source'] = source
        
        return dict_to_json({
            'data': {
                'id': None, 
                'name': None, 
                'type': message_type, 
                'content': content, 
                'example': False, 
                'additional_kwargs': additional_kwargs, 
                'response_metadata': {}
            }, 
            'type': message_type
        })
    
    def test_full_backfill_flow(self):
        """Test complete backfill process with real data"""
        # Create test messages with different patterns
        test_messages = [
            {
                "session_id": f"{self.workflow_id}-{self.user_id}",  # Using standard format
                "message": self.create_test_message_data("human", "SMS message from student", source="sms")
            },
            {
                "session_id": f"web-{self.student_id}",
                "message": self.create_test_message_data("ai", "AI response to student")
            },
            {
                "session_id": f"counselor-session-123",
                "message": self.create_test_message_data("human", "Counselor message")
            }
        ]
        
        created_messages = []
        for msg_data in test_messages:
            message = self.prisma.messages.create(data=msg_data)
            created_messages.append(message)
        
        try:
            # First check that our messages don't already have events
            existing_events_before = self.prisma.engagementevent.find_many(
                where={
                    "message_id": {
                        "in": [msg.id for msg in created_messages]
                    }
                }
            )
            self.assertEqual(len(existing_events_before), 0)
            
            # Process each of our test messages specifically
            events_created_count = 0
            for message in created_messages:
                result = self.backfiller.process_message(message)
                if result:
                    events_created_count += 1
            
            # Verify events were created for our messages
            events = self.prisma.engagementevent.find_many(
                where={
                    "message_id": {
                        "in": [msg.id for msg in created_messages]
                    }
                }
            )
            
            self.assertEqual(len(events), 3)
            self.assertEqual(events_created_count, 3)
            
            # Check that channels were determined correctly
            sms_events = [e for e in events if e.channel == EngagementChannel.sms]
            web_events = [e for e in events if e.channel == EngagementChannel.web_student]
            counselor_events = [e for e in events if e.channel == EngagementChannel.web_counselor]
            
            self.assertEqual(len(sms_events), 1)
            self.assertEqual(len(web_events), 1)
            self.assertEqual(len(counselor_events), 1)
            
        finally:
            # Clean up
            self.prisma.engagementevent.delete_many(
                where={
                    "message_id": {
                        "in": [msg.id for msg in created_messages]
                    }
                }
            )
            for message in created_messages:
                self.prisma.messages.delete(where={"id": message.id})
    
    def test_idempotency(self):
        """Test that running backfill multiple times is idempotent"""
        # Create a test message
        test_message = self.prisma.messages.create(
            data={
                "session_id": f"idempotency-{self.student_id}",
                "message": self.create_test_message_data("human", "Idempotency test message")
            }
        )
        
        try:
            # Process our test message first time
            first_run_created = self.backfiller.process_message(test_message)
            
            # Process same message again - should be skipped
            second_run_created = self.backfiller.process_message(test_message)
            
            # First run should create event, second run should skip
            self.assertTrue(first_run_created)
            self.assertFalse(second_run_created)
            
            # Check stats
            self.assertEqual(self.backfiller.stats['events_created'], 1)
            self.assertEqual(self.backfiller.stats['skipped_existing'], 1)
            
            # Verify only one event exists
            events = self.prisma.engagementevent.find_many(
                where={"message_id": test_message.id}
            )
            self.assertEqual(len(events), 1)
            
        finally:
            # Clean up
            self.prisma.engagementevent.delete_many(
                where={"message_id": test_message.id}
            )
            self.prisma.messages.delete(where={"id": test_message.id})


class TestBackfillScriptMainFunction(unittest.TestCase):
    """Test the main function and argument parsing"""
    
    @patch('scripts.backfill_engagement_events.EngagementEventBackfiller')
    @patch('sys.argv', ['backfill_engagement_events.py', '--dry-run', '--limit', '100'])
    def test_main_with_dry_run_and_limit(self, mock_backfiller_class):
        """Test main function with dry-run and limit arguments"""
        from scripts.backfill_engagement_events import main
        
        mock_backfiller = Mock()
        mock_backfiller_class.return_value = mock_backfiller
        
        # Call main
        main()
        
        # Verify backfiller was created with dry_run=True
        mock_backfiller_class.assert_called_once_with(dry_run=True)
        
        # Verify run was called with correct parameters
        mock_backfiller.run.assert_called_once_with(limit=100, batch_size=1000)
    
    @patch('scripts.backfill_engagement_events.EngagementEventBackfiller')
    @patch('sys.argv', ['backfill_engagement_events.py', '--batch-size', '500'])
    def test_main_with_custom_batch_size(self, mock_backfiller_class):
        """Test main function with custom batch size"""
        from scripts.backfill_engagement_events import main
        
        mock_backfiller = Mock()
        mock_backfiller_class.return_value = mock_backfiller
        
        # Call main
        main()
        
        # Verify backfiller was created with dry_run=False (default)
        mock_backfiller_class.assert_called_once_with(dry_run=False)
        
        # Verify run was called with custom batch size
        mock_backfiller.run.assert_called_once_with(limit=None, batch_size=500)


if __name__ == '__main__':
    unittest.main()