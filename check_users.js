import { PrismaClient } from '@prisma/client';

async function checkUsers() {
  const prisma = new PrismaClient({
    datasources: {
      db: {
        url: process.env.DATABASE_URL || "postgresql://postgres:password@localhost:5432/addie"
      }
    }
  });

  try {
    console.log('Checking users in database...');
    
    const users = await prisma.user.findMany({
      where: {
        OR: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ]
      },
      select: {
        id: true,
        email: true,
        role: true,
        enabled: true,
        first_name: true,
        last_name: true
      }
    });
    
    console.log('Found users:', JSON.stringify(users, null, 2));
    
    // Also check accounts table for Google provider accounts
    const accounts = await prisma.account.findMany({
      where: {
        provider: 'google'
      },
      select: {
        id: true,
        userId: true,
        provider: true,
        providerAccountId: true
      }
    });
    
    console.log('Found Google accounts:', JSON.stringify(accounts, null, 2));
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUsers();