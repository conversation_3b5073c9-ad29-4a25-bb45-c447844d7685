version: '3.8'

services:
  beat-fixer:
    deploy:
      replicas: 1
    image: us-docker.pkg.dev/addie-440119/addie/addie
    env_file:
      - .env.prod
    networks:
      - qa-network
    command: -c "celery -A addie.tasks.app beat --loglevel=error"

  default:
    deploy:
      replicas: 1
    image: us-docker.pkg.dev/addie-440119/addie/addie
    env_file:
      - .env.prod
    networks:
      - qa-network
    command: -c "celery -A addie.tasks.app worker --loglevel=error -c 2"

  api:
    deploy:
      replicas: 1
    image: us-docker.pkg.dev/addie-440119/addie/addie
    ports:
      - "8000:8000"
    env_file:
      - .env.prod
    networks:
      - qa-network
    command: -c "uvicorn addie.api.app:router --host 0.0.0.0 --port 8000"


networks:
  qa-network:
    external: true

