################################## NETWORK #####################################
bind 0.0.0.0
protected-mode no
port 6379
tcp-backlog 511
timeout 0
tcp-keepalive 300

################################# GENERAL #####################################
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""

################################ SECURITY ####################################
requirepass addie!==

################################ SNAPSHOTTING ################################
# Using mixed persistence strategy with less frequent RDB snapshots
# Format: save <seconds> <changes>
save 3600 1000
save 7200 100

stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./

################################# REPLICATION #################################
# If you're running in a cluster, uncomment and configure these
# replicaof <masterip> <masterport>
# masterauth <master-password>

################################## CLIENTS ####################################
# Set a reasonable client limit
maxclients 10000

# Output buffer limits for different types of clients
# Format: client-output-buffer-limit <class> <hard limit> <soft limit> <soft seconds>
# Increased limits for pubsub clients to prevent disconnections due to buffer overflow
client-output-buffer-limit normal 512mb 256mb 60
client-output-buffer-limit slave 1gb 512mb 180
client-output-buffer-limit pubsub 256mb 64mb 60

################################## MEMORY ####################################
# Set memory limit to prevent OOM issues
# Adjust the value based on your server's available memory
maxmemory 2gb

# Memory policy: how Redis will select what to remove when maxmemory is reached
maxmemory-policy allkeys-lru
# Sample rate for LRU eviction algorithm (improves performance)
maxmemory-samples 10

################################ APPEND ONLY MODE ############################
# Enable AOF for better durability
appendonly yes
appendfilename "appendonly.aof"
# Only sync to disk once per second to reduce I/O pressure
appendfsync everysec

# Reduce disk I/O during rewrites
no-appendfsync-on-rewrite yes
# Only trigger rewrite when AOF file grows significantly larger (500%)
auto-aof-rewrite-percentage 500
# Larger minimum size to prevent frequent rewrites
auto-aof-rewrite-min-size 256mb

# Use the more efficient AOF format (Redis 7.0+)
aof-use-rdb-preamble yes

################################ LUA SCRIPTING ###############################
lua-time-limit 5000

################################## SLOW LOG ##################################
slowlog-log-slower-than 10000
slowlog-max-len 128

################################ LATENCY MONITOR ##############################
latency-monitor-threshold 0

################################ OTHER OPTIMIZATIONS #########################
# Disable expensive commands if not needed
# rename-command KEYS ""
# rename-command FLUSHALL ""
# rename-command FLUSHDB ""

# Improve memory usage
activedefrag yes
active-defrag-ignore-bytes 100mb
active-defrag-threshold-lower 10
active-defrag-threshold-upper 30
active-defrag-cycle-min 5
active-defrag-cycle-max 75

# IO threads for non-blocking operations (Redis 6.0+)
io-threads 4
io-threads-do-reads yes
