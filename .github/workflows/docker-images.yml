name: Build and Push to Artifact Registry
on:
  push:
  workflow_dispatch:
# Global workflow configuration
env:
  # Non-sensitive configuration
  PROJECT_ID: addie-440119
  REGION: us
  GAR_LOCATION: us-docker.pkg.dev/addie-440119/addie/image-name
  # Build environment settings
  NODE_ENV: production
  BUILD_ENV: dev
jobs:
  test:
    runs-on: ubuntu-latest

    env:
      # Secrets
      GCLOUD_SERVICE_KEY: ${{ secrets.GCLOUD_SERVICE_KEY }}
      AUTH_SECRET: ${{ secrets.AUTH_SECRET }}
      GOOGLE_CLIENT_ID: ${{ secrets.GOOGLE_CLIENT_ID }}
      GOOGLE_CLIENT_SECRET: ${{ secrets.GOOGLE_CLIENT_SECRET }}
      GOOGLE_API_KEY: ${{ secrets.GOOGLE_API_KEY }}
      RESEND_API_KEY: ${{ secrets.RESEND_API_KEY }}
      ADDIE_API_KEY: ${{ secrets.ADDIE_API_KEY }}
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      NEXT_PUBLIC_USERPILOT_TOKEN: ${{ secrets.NEXT_PUBLIC_USERPILOT_TOKEN }}

      # Vars
      ADDIE_API_HOST: ${{ vars.ADDIE_API_HOST }}
      EMAIL_FROM: ${{ vars.EMAIL_FROM }}
      NODE_ENV: "staging"
      RUN_MODE: "dev"

      # Postgres - local service
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: postgres
      DATABASE_PORT: 5432
      POSTGRES_HOST: ${{ github.actor == 'nektos/act' && 'postgres' || '127.0.0.1' }}
      DATABASE_URL: postgres://user:password@${{ github.actor == 'nektos/act' && 'postgres' || '127.0.0.1' }}:5432/postgres

      # Redis
      CELERY_BROKER_URL: redis://localhost:6379/0

    services:
      postgres:
        image: pgvector/pgvector:0.8.0-pg14
        ports:
          - 5432:5432
        env:
          POSTGRES_USER: user
          POSTGRES_PASSWORD: password
          POSTGRES_DB: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Cache pip
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -e .

      - name: Set up pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8.6.10

      - name: Get pnpm store path
        id: pnpm-cache
        run: echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_ENV

      - name: Cache pnpm store
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-

      - name: Install pnpm dependencies
        run: pnpm install prisma

      - name: Create Ephemeral Database
        run: prisma migrate deploy

      - name: Run pytest
        id: pytest
        # TODO: disable this once we get tests working reliably.
        continue-on-error: true
        run: |
          prisma generate
          ./scripts/test-pytest.sh -p 8


  build-push-artifact:
    # Only run this job when manually triggered on dev branch or on any push
    if: github.event_name != 'workflow_dispatch' || github.ref == 'refs/heads/dev'
    runs-on: ubuntu-latest
    environment: dev
    needs: test
    # Add job-level environment variables for both sensitive and non-sensitive data
    env:
      # Service account key for GCP authentication
      GCLOUD_SERVICE_KEY: ${{ secrets.GCLOUD_SERVICE_KEY }}

      # Sensitive data passed as secrets to Docker build process
      AUTH_SECRET: ${{ secrets.AUTH_SECRET }}
      GOOGLE_CLIENT_ID: ${{ secrets.GOOGLE_CLIENT_ID }}
      GOOGLE_CLIENT_SECRET: ${{ secrets.GOOGLE_CLIENT_SECRET }}
      GOOGLE_API_KEY: ${{ secrets.GOOGLE_API_KEY }}
      DATABASE_URL: ${{ secrets.DATABASE_URL }}
      RESEND_API_KEY: ${{ secrets.RESEND_API_KEY }}

      # PostgreSQL database connection variables
      POSTGRES_USER: ${{ secrets.POSTGRES_USER }}
      POSTGRES_PASSWORD: ${{ secrets.POSTGRES_PASSWORD }}
      POSTGRES_HOST: ${{ secrets.POSTGRES_HOST }}
      POSTGRES_DB: ${{ github.ref == 'refs/heads/master' && 'qa' || 'qa_dev' }}
      DATABASE_PORT: ${{ secrets.DATABASE_PORT || '5432' }}
      ADDIE_API_KEY: ${{ secrets.ADDIE_API_KEY }}
      # Non-sensitive configuration from repository variables
      ADDIE_API_HOST: ${{ vars.ADDIE_API_HOST }}
      EMAIL_FROM: ${{ vars.EMAIL_FROM }}
      # These environment variables should be set in the repository variables
      NODE_ENV: "production"
      NEXT_PUBLIC_USERPILOT_TOKEN: ${{ secrets.NEXT_PUBLIC_USERPILOT_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      # Install Python dependencies for the build script and pytest
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      # Cache pip dependencies
      - name: Cache pip
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-
      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install --cache-dir ~/.cache/pip -e .

      # Set up Cloud SDK first
      - name: Install gcloud CLI
        uses: google-github-actions/setup-gcloud@v1

      # Using service account key for authentication
      - name: Authenticate with gcloud
        id: auth
        run: |
          # Using the service account key passed via environment variable
          if [ -n "$GCLOUD_SERVICE_KEY" ]; then
            echo "Using service account key from environment variable"
            echo "$GCLOUD_SERVICE_KEY" > /tmp/gcloud-key.json
            gcloud auth activate-service-account --key-file=/tmp/gcloud-key.json
            rm -f /tmp/gcloud-key.json
          else
            echo "No GCLOUD_SERVICE_KEY environment variable found"
            echo "Environment variables:"
            env | grep -i gcloud || true
            exit 1
          fi

      # SDK already set up above
      - name: Docker auth
        run: gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev --quiet

      - name: Build and Push
        run: |
          echo "Starting Docker build with secure secret handling..."
          echo "Building from branch: ${GITHUB_REF#refs/heads/}"
          # The Python build script will handle passing secrets securely to Docker
          python build.py --build-env dev --force-build --max-workers 4

  # Job to handle deployment of services for merge commits to dev/master
  deploy-services:
    runs-on: ubuntu-latest
    # Run this job on any push or pull_request merge to dev or master branches
    if: |
      (github.event_name == 'push' ||
       (github.event_name == 'pull_request' && github.event.action == 'closed' && github.event.pull_request.merged == true)) &&
      (github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/master' ||
       endsWith(github.ref, '/dev') || endsWith(github.ref, '/master'))
    needs: build-push-artifact
    environment: ${{ github.ref == 'refs/heads/master' && 'prod' || 'dev' }}
    env:
      # Service account key for GCP authentication
      GCLOUD_SERVICE_KEY: ${{ secrets.GCLOUD_SERVICE_KEY }}
      # Docker certificate content stored in secrets - using new env vars
      DOCKER_CA_CERT: ${{ secrets.DOCKER_CA_CERT }}
      DOCKER_CERT: ${{ secrets.DOCKER_CERT }}
      DOCKER_KEY: ${{ secrets.DOCKER_KEY }}
      # Slack notification secrets
      SLACK_TOKEN: ${{ secrets.SLACK_TOKEN }}
      SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      # Twilio API credentials
      TWILIO_ACCOUNT_SID: ${{ secrets.TWILIO_ACCOUNT_SID }}
      TWILIO_AUTH_TOKEN: ${{ secrets.TWILIO_AUTH_TOKEN }}
      TWILIO_VERIFY_SERVICE_SID: ${{ secrets.TWILIO_VERIFY_SERVICE_SID }}
      NEXT_PUBLIC_USERPILOT_TOKEN: ${{ secrets.NEXT_PUBLIC_USERPILOT_TOKEN }}

      # Sensitive data for service operation
      DATABASE_URL: ${{ secrets.DATABASE_URL }}
      REDIS_CONN: ${{ secrets.REDIS_CONN }}
      AUTH_SECRET: ${{ secrets.AUTH_SECRET }}
      # PostgreSQL database connection variables
      POSTGRES_USER: ${{ vars.POSTGRES_USER }}
      POSTGRES_PASSWORD: ${{ secrets.POSTGRES_PASSWORD }}
      POSTGRES_HOST: ${{ vars.POSTGRES_HOST }}
      POSTGRES_DB: ${{ github.ref == 'refs/heads/master' && 'qa' || 'qa_dev' }}
      DATABASE_PORT: ${{ vars.DATABASE_PORT || '5432' }}
      # Environment settings
      NODE_ENV: ${{ github.ref == 'refs/heads/master' && 'production' || 'staging' }}
      DEPLOYMENT_ENVIRONMENT: ${{ github.ref == 'refs/heads/master' && 'prod' || 'dev' }}

    steps:
      - name: Checkout code with history
        uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Fetch all history to compare changes
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -e .

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
      - name: Authenticate with gcloud
        run: |
          echo "$GCLOUD_SERVICE_KEY" > /tmp/gcloud-key.json
          gcloud auth activate-service-account --key-file=/tmp/gcloud-key.json
          rm -f /tmp/gcloud-key.json

      - name: Run deployment CLI
        run: |
          # Extract branch name from GitHub ref
          BRANCH_NAME=${GITHUB_REF#refs/heads/}

          # Configure environment based on branch
          ENV_NAME="${{ github.ref == 'refs/heads/master' && 'prod' || 'dev' }}"

          echo "Running deployment CLI for branch $BRANCH_NAME with environment $ENV_NAME"

          # Run the Python CLI deployment tool with the required parameters
          ./scripts/deploy/cli.py deploy-all \
            --branch "$BRANCH_NAME" --force-all \
            --env "$ENV_NAME" --skip-tls-verify \
            --verbose \
            --use-llm \
            --slack-notification

          echo "Deployment completed"
