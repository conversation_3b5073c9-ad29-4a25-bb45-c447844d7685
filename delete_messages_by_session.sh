#!/bin/bash

# <PERSON>ript to delete messages by session_id from production database
# Usage: ./delete_messages_by_session.sh <session_id>
# Requires environment variables: POSTGRES_PASSWORD, POSTGRES_HOST, POSTGRES_USER, POSTGRES_DB
# Example: source .env.prod && ./delete_messages_by_session.sh <session_id>

if [ $# -eq 0 ]; then
    echo "Usage: $0 <session_id>"
    echo "Example: $0 0ec5d9a1-d346-446b-b40b-ce5e086e8ba0"
    exit 1
fi

if [ -z "$POSTGRES_PASSWORD" ]; then
    echo "Error: POSTGRES_PASSWORD environment variable is not set"
    echo "Please set it with: export POSTGRES_PASSWORD=your_password"
    exit 1
fi

if [ -z "$POSTGRES_HOST" ]; then
    echo "Error: POSTGRES_HOST environment variable is not set"
    exit 1
fi

if [ -z "$POSTGRES_USER" ]; then
    echo "Error: POSTGRES_USER environment variable is not set"
    exit 1
fi

if [ -z "$POSTGRES_DB" ]; then
    echo "Error: POSTGRES_DB environment variable is not set"
    exit 1
fi

SESSION_ID="$1"
PGPASSWORD="$POSTGRES_PASSWORD"
HOST="$POSTGRES_HOST"
USER="$POSTGRES_USER"
DATABASE="$POSTGRES_DB"

echo "Deleting messages with session_id containing: $SESSION_ID"
echo "Database: $DATABASE at $HOST"
echo ""

# Delete dependent records first
echo "Step 1: Deleting PromptSuggestion records..."
DELETED_PROMPT_SUGGESTIONS=$(PGPASSWORD="$PGPASSWORD" psql -h "$HOST" -U "$USER" -d "$DATABASE" -t -c "WITH deleted AS (DELETE FROM \"PromptSuggestion\" WHERE message_id IN (SELECT id FROM messages WHERE session_id LIKE '%$SESSION_ID%') RETURNING *) SELECT count(*) FROM deleted;" | tr -d ' ')

echo "Step 2: Deleting engagement_events records..."
DELETED_ENGAGEMENT=$(PGPASSWORD="$PGPASSWORD" psql -h "$HOST" -U "$USER" -d "$DATABASE" -t -c "WITH deleted AS (DELETE FROM engagement_events WHERE message_id IN (SELECT id FROM messages WHERE session_id LIKE '%$SESSION_ID%') RETURNING *) SELECT count(*) FROM deleted;" | tr -d ' ')

echo "Step 3: Deleting system_prompt_context records..."
DELETED_CONTEXT=$(PGPASSWORD="$PGPASSWORD" psql -h "$HOST" -U "$USER" -d "$DATABASE" -t -c "WITH deleted AS (DELETE FROM system_prompt_context WHERE message_id IN (SELECT id FROM messages WHERE session_id LIKE '%$SESSION_ID%') RETURNING *) SELECT count(*) FROM deleted;" | tr -d ' ')

# Finally delete the messages
echo "Step 4: Deleting messages..."
DELETED_MESSAGES=$(PGPASSWORD="$PGPASSWORD" psql -h "$HOST" -U "$USER" -d "$DATABASE" -t -c "WITH deleted AS (DELETE FROM messages WHERE session_id LIKE '%$SESSION_ID%' RETURNING *) SELECT count(*) FROM deleted;" | tr -d ' ')

echo ""
echo "Deletion completed:"
echo "- PromptSuggestion records deleted: $DELETED_PROMPT_SUGGESTIONS"
echo "- Engagement events deleted: $DELETED_ENGAGEMENT"  
echo "- System prompt context records deleted: $DELETED_CONTEXT"
echo "- Messages deleted: $DELETED_MESSAGES"