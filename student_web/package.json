{"name": "student-web", "version": "0.1.0", "private": true, "author": {"name": "chinnno15"}, "scripts": {"dev": "next dev", "build": "next build", "turbo": "next dev --turbo", "start": "next start", "lint": "next lint", "preview": "next build && next start", "email": "email dev --dir emails --port 3333", "test": "jest"}, "dependencies": {"@auth/core": "^0.37.4", "@auth/prisma-adapter": "2.7.4", "@babel/preset-env": "^7.26.0", "@hey-api/client-fetch": "^0.5.0", "@hey-api/openapi-ts": "^0.58.0", "@hookform/resolvers": "^3.9.1", "@jest/globals": "^29.7.0", "@opentelemetry/api": ">=1.0.0 <1.9.0", "@prisma/client": "5.17.0", "@radix-ui/react-accessible-icon": "^1.1.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-email/button": "0.0.18", "@react-email/components": "0.0.28", "@react-email/html": "0.0.10", "@t3-oss/env-nextjs": "^0.11.1", "@tanstack/react-table": "^8.20.5", "@tiptap/core": "^2.11.5", "@tiptap/extension-bold": "^2.11.5", "@tiptap/extension-bullet-list": "^2.11.5", "@tiptap/extension-heading": "^2.11.5", "@tiptap/extension-italic": "^2.11.5", "@tiptap/extension-list-item": "^2.11.5", "@tiptap/extension-paragraph": "^2.11.5", "@tiptap/extension-text": "^2.11.5", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@typescript-eslint/parser": "^8.15.0", "@vercel/analytics": "^1.4.1", "@vercel/og": "^0.6.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "concurrently": "^9.1.0", "contentlayer2": "^0.5.3", "date-fns": "^4.1.0", "env-cmd": "^10.1.0", "framer-motion": "^11.15.0", "googleapis": "^144.0.0", "gray-matter": "^4.0.3", "lodash": "^4.17.21", "lucide-react": "^0.460.0", "markdown-it": "^14.1.0", "ms": "^2.1.3", "next": "15.1.6", "next-auth": "5.0.0-beta.25", "next-client-cookies": "^2.0.0", "next-contentlayer2": "^0.5.3", "next-themes": "^0.4.3", "prop-types": "^15.8.1", "qs": "^6.13.1", "react": "18.3.1", "react-day-picker": "^9.3.2", "react-dom": "18.3.1", "react-email": "3.0.2", "react-hook-form": "^7.53.2", "react-markdown": "^9.0.1", "react-phone-number-input": "^3.4.12", "react-syntax-highlighter": "^15.6.1", "react-textarea-autosize": "^8.5.5", "recharts": "^2.13.3", "resend": "^4.0.1", "shadcn": "^2.1.6", "sharp": "^0.33.5", "shiki": "^1.23.1", "sonner": "^1.7.0", "stripe": "^17.4.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.2.5", "tslog": "^4.9.3", "turndown": "^7.2.0", "use-debounce": "^10.0.4", "userpilot": "^1.3.9", "uuid": "^11.0.3", "vaul": "^1.1.1", "zod": "^3.23.8"}, "devDependencies": {"@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.0", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.15", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@types/node": "^22.9.1", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@types/turndown": "^5.0.5", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "15.1.6", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-tailwindcss": "^3.17.5", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mdast-util-toc": "^7.1.0", "npm-check-updates": "^17.1.11", "postcss": "^8.4.49", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.9", "pretty-quick": "^4.0.0", "prisma": "5.17.0", "tailwindcss": "^3.4.15", "ts-node": "^10.9.2", "typescript": "5.6.3", "unist-util-visit": "^5.0.0"}}