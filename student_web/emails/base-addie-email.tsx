import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Hr,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

import { env } from "@/env.mjs";

type BaseAddieEmailProps = {
  firstName: string;
  previewText: string;
  subject: string;
  children: React.ReactNode;
  ctaText: string;
  ctaLink: string;
};

export const BaseAddieEmail = ({
  firstName,
  previewText,
  subject,
  children,
  ctaText,
  ctaLink,
}: BaseAddieEmailProps) => (
  <Html>
    <Head />
    <Preview>{previewText}</Preview>
    <Tailwind>
      <Body className="w-full bg-white font-sans">
        <Section className="mx-auto text-center">
          <img
            src={`${env.AUTH_URL}/_static/logo-text-blue.png`}
            alt="Addie Logo"
            className="mx-auto my-4"
          />
          <Hr className="my-6 border-t-2 border-gray-300" />
        </Section>
        <Section className="mx-auto w-max text-center">
          <img
            src={`${env.AUTH_URL}/_static/hero-1.png`}
            alt="Hero Image"
            className="mb-6 w-full"
          />
        </Section>
        <div className="mx-auto w-max rounded-lg px-6 py-5 pb-12">
          <Text className="mb-4 text-center text-2xl font-bold text-black">
            {subject}
          </Text>
          <Text className="mb-6 text-lg text-black">
            Hi {firstName || "there"},
          </Text>
          {children}
          <Section className="my-5 w-full text-center">
            <Button
              className="inline-block rounded-md bg-blue-600 px-6 py-3 text-base font-medium text-white no-underline"
              href={ctaLink}
            >
              {ctaText}
            </Button>
            <div className="ml-2.5 flex flex-col items-start">
              <Text className="h-3 text-base text-black">Best,</Text>
              <Text className="h-3 text-base text-black">Team Addie</Text>
            </div>
          </Section>

          <Hr className="my-6 border-t-2 border-gray-300" />
          {/* Updated footer */}
          <Section className="w-full text-center">
            <Text className="mb-2 text-xs text-gray-600">
              Privacy Policy • Contact us
            </Text>
            <Text className="mb-4 text-xs text-gray-600">
              Copyright 2025 GetAddie. All rights reserved.
            </Text>
            <img
              src={`${env.AUTH_URL}/_static/logo-text-grey.png`}
              alt="GetAddie Logo"
              className="mx-auto mb-4"
            />
          </Section>
        </div>
      </Body>
    </Tailwind>
  </Html>
);