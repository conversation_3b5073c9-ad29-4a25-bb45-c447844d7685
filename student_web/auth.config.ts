import { User<PERSON><PERSON> } from "@prisma/client";
import type { NextAuthConfig } from "next-auth";
import Credentials from "next-auth/providers/credentials";
import Google from "next-auth/providers/google";
import Resend from "next-auth/providers/resend";

import { env } from "@/env.mjs";
import { isAllowedEmailDomain } from "@/common/auth-utils";
import { prisma } from "@/common/db";
import { sendVerificationRequest } from "@/common/email";

import log from "./common/logger";

export default {
  providers: [
    Google({
      clientId: env.GOOGLE_CLIENT_ID,
      clientSecret: env.GOOGLE_CLIENT_SECRET,
      authorization: {
        params: {
          prompt: "select_account",
          access_type: "offline",
          response_type: "code",
        },
      },
    }),
    Resend({
      apiKey: env.RESEND_API_KEY,
      from: env.EMAIL_FROM,
      sendVerificationRequest,
    }),
    /*
    Credentials({
      // The name to display on the sign in form (e.g. 'Sign in with...')
      name: "Credentials",
      // The credentials is used to generate a suitable form on the sign in page.
      // You can specify whatever fields you are expecting to be submitted.
      // e.g. domain, username, password, 2FA token, etc.
      // You can pass any HTML attribute to the <input> tag through the object.
      credentials: {
        username: { label: "Username", type: "text", placeholder: "jsmith" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials, req) {
        log.debug("## authorize ##");

        // const email = "<EMAIL>";
        const email = "<EMAIL>";

        const data = {
          // first_name: "Jose",
          // last_name: "Oliveros",
          role: UserRole.STUDENT,
          email,
        };

        const user = await prisma.user.update({
          where: {
            email,
          },
          data,
        });

        // You need to provide your own logic here that takes the credentials
        // submitted and returns either a object representing a user or value
        // that is false/null if the credentials are invalid.
        // e.g. return { id: 1, name: 'J Smith', email: '<EMAIL>' }
        // You can also use the `req` object to obtain additional parameters
        // (i.e., the request IP address)
        // const res = await fetch("/api/auth/", {
        //   method: "POST",
        //   body: JSON.stringify(credentials),
        //   headers: { "Content-Type": "application/json" },
        // });
        // const user = await res.json();
        //
        // // If no error and we have user data, return it
        // if (res.ok && user) {
        //   return user;
        // }
        // // Return null if user data could not be retrieved
        // return null;
        return user;
      },
    }),

  */

    Credentials({
      name: "Credentials",
      credentials: {
        email: {
          label: "Email",
          type: "text",
          placeholder: "<EMAIL>",
        },
        password: {
          label: "Password",
          type: "password",
        },
        impersonate: {
          label: "Impersonate",
          type: "text",
          placeholder: "true/false",
          required: false,
        },
      },
      async authorize(credentials, req) {
        log.debug("## authorize credentials ##", credentials);
        if (!credentials || !credentials.email) return null;

        if (credentials.impersonate === "true") {
          const studentEmail = (credentials.email as string).toLowerCase();

          // Apply domain whitelist check for login using shared utility
          if (!isAllowedEmailDomain(studentEmail, true, "Unauthorized")) {
            log.debug("Unauthorized attempt with domain: " + studentEmail);
            return null; // Function already logs warning
          }

          const user = await prisma.user.findUnique({
            where: { email: studentEmail },
          });
          if (user) {
            return { ...user, impersonated: true };
          }
          return null;
        } else {
          const adminEmail = "<EMAIL>";
          const user = await prisma.user.findUnique({
            where: { email: adminEmail },
          });
          if (user && user.role === UserRole.ADMIN) {
            return user;
          }
          return null;
        }
      },
    }),
  ],
} satisfies NextAuthConfig;
