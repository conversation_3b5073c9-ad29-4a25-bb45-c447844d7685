generator client_js {
  provider = "prisma-client-js"
}

generator client {
  provider                    = "prisma-client-py"
  enable_experimental_decimal = "true"
  interface                   = "sync"
  recursive_type_depth        = "5"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                     String                  @id @default(uuid())
  created_at             DateTime                @default(now())
  date_of_birth          DateTime?
  email                  String?                 @unique
  emailVerified          DateTime?
  image                  String?
  first_name             String
  last_name              String
  middle_name            String?
  role                   UserRole
  updated_at             DateTime                @updatedAt
  token_usage            Int                     @default(0)
  token_usage_30_days    Int                     @default(0)
  token_usage_7_days     Int                     @default(0)
  isProfileComplete      Boolean                 @default(false)
  phone_number           String?
  gender                 String?
  pronouns               String?
  timezone               Timezone?
  last_active            DateTime?
  date_last_modified     DateTime?
  enabled                <PERSON><PERSON>an                 @default(false)
  family_name            String?
  given_name             String?
  grades                 String[]                @default([])
  metadata               Json?
  middle_name_oneroster  String?
  oneroster_status       String?
  password               String?
  profile_sourced_ids    String[]                @default([])
  resource_sourced_ids   String[]                @default([])
  sourced_id             String?                 @unique
  user_ids               Json?
  user_master_identifier String?
  username               String?                 @unique
  accounts               Account[]
  ChatEvaluatorConfig    ChatEvaluatorConfig[]
  chatEvaluatorRuns      ChatEvaluatorRun[]
  Counselor              Counselor?
  demographics           Demographic[]
  preferred_availability PreferredAvailability[]
  prompt_suggestions     PromptSuggestion[]
  questionnaires         Questionnaire[]
  created_reminders      Reminder[]
  sessions               Session[]
  workflows              Workflow[]
  students               Student[]               @relation("StudentToUser")
  user_activities        UserActivity[]          @relation("UserActivities")
  performed_activities   UserActivity[]          @relation("PerformedActivities")
  engagement_events      EngagementEvent[]

  @@index([token_usage])
  @@index([sourced_id])
  @@index([username])
}

model UserActivity {
  id           String   @id @default(cuid())
  user_id      String
  action       String
  performed_by String
  performed_at DateTime @default(now())
  metadata     Json?

  user          User @relation("UserActivities", fields: [user_id], references: [id], onDelete: Cascade)
  performed_by_user User @relation("PerformedActivities", fields: [performed_by], references: [id], onDelete: Restrict)

  @@index([user_id])
  @@index([performed_by])
  @@index([action])
  @@index([performed_at])
}

model PreferredAvailability {
  id         String            @id @default(cuid())
  user_id    String
  day        AvailabilityDay
  block      AvailabilityBlock
  created_at DateTime          @default(now())
  updated_at DateTime          @updatedAt
  user       User              @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, day, block])
}

model Account {
  id                String   @id @default(cuid())
  access_token      String?
  created_at        DateTime @default(now())
  expires_at        Int?
  id_token          String?
  provider          String
  providerAccountId String
  refresh_token     String?
  scope             String?
  session_state     String?
  token_type        String?
  type              String
  updated_at        DateTime @default(now())
  userId            String
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model messages {
  id                   Int                     @id @default(autoincrement())
  session_id           String
  message              Json
  created_at           DateTime                @default(now()) @db.Timestamptz(6)
  prompt_suggestion_id String?
  prompt_suggestions   PromptSuggestion?
  engagement_events    EngagementEvent[]

  @@index([session_id], map: "idx_messages_session_id")
}

model SystemPromptContext {
  id              Int      @id @default(autoincrement())
  messageId       Int      @map("message_id")
  systemPrompt    String   @map("system_prompt") @db.Text
  studentContext  Json?    @map("student_context")
  agentType       String   @map("agent_type") @db.VarChar(50)
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  @@map("system_prompt_context")
}

model Student {
  id                                      String                                     @id @default(cuid())
  created_at                              DateTime                                   @default(now())
  grade                                   Int
  questionaire_id                         String?
  student_id                              String?                                    @unique
  updated_at                              DateTime                                   @updatedAt
  school_id                               String?
  engagement_tier                         EngagementTier?
  tier_updated_at                         DateTime?
  last_activity_at                        DateTime?
  last_activity_channel                   String?
  academic_achievements                   AcademicAchievement[]
  current_class_schedule                  AcademicRecord[]                           @relation("CurrentClassSchedule")
  application_docs                        ApplicationDoc[]
  application_status                      ApplicationStatus[]
  attendance                              Attendance[]
  counselor_notes                         CounselorNote[]
  emotional_wellness                      EmotionalWellness[]
  enrollments                             Enrollment[]
  extra_curriculars                       Extracurricular[]                          @relation("extracurriculars")
  gen_student_conversation_summaries      GeneratedStudentConversationSummary[]
  gen_teacher_comments_summaries          GeneratedTeacherCommentsSummary[]
  gen_unstructured_conversation_summaries GeneratedUnstructuredConversationSummary[]
  growth_opportunities                    GrowthOpportunity[]
  interaction_logs                        InteractionLog[]
  milestone_tracking                      MilestoneTracking[]
  narrative_themes                        NarrativeTheme[]
  personal_experiences                    PersonalExperience[]
  question_response                       QuestionResponse[]
  recommendations                         Recommendation[]
  reminders                               Reminder[]
  scholarships                            Scholarship[]
  skills                                  Skill[]
  school                                  School?                                    @relation(fields: [school_id], references: [id], onDelete: Cascade)
  st_agents                               StudentAgent[]
  workflow_steps                          StudentWorkFlowStep[]
  student_workflow                        StudentWorkflow[]
  call_records                            CallRecord[]
  teacher_comments                        TeacherComment[]
  counselors                              Counselor[]                                @relation("CounselorToStudent")
  questionnaire                           Questionnaire[]                            @relation("QuestionnaireToStudent")
  users                                   User[]                                     @relation("StudentToUser")
  engagement_events                       EngagementEvent[]
  onboarding_activity                     OnboardingActivity?
  ocean_scores                            OceanScores?
  student_majors                          StudentMajors[]
}

model Counselor {
  id               String           @id @default(cuid())
  user_id          String           @unique
  created_at       DateTime?        @default(now())
  school_id        String?
  updated_at       DateTime?        @updatedAt
  school           School?          @relation(fields: [school_id], references: [id], onDelete: Cascade)
  user             User             @relation(fields: [user_id], references: [id], onDelete: Cascade)
  notes            CounselorNote[]
  interaction_logs InteractionLog[]
  students         Student[]        @relation("CounselorToStudent")
}

model AcademicRecord {
  id              String   @id @default(cuid())
  student_id      String
  past_student_id String   @unique
  subject         String
  grade           String
  grade_level     String
  gpa             Float?
  teacher_notes   String?
  teacher_name    String
  department      String
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  student         Student  @relation("CurrentClassSchedule", fields: [student_id], references: [id], onDelete: Cascade, map: "AcademicRecord_studentId_fk_unique")
}

model AcademicAchievement {
  id          String   @id @unique
  student_id  String
  title       String
  description String
  data        Json?
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
  student     Student  @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model Extracurricular {
  id          String    @id @default(cuid())
  student_id  String
  activity    String
  role        String?
  description String?
  start_date  DateTime
  end_date    DateTime?
  student     Student   @relation("extracurriculars", fields: [student_id], references: [id], onDelete: Cascade)
}

model PersonalExperience {
  id          String   @id @default(cuid())
  student_id  String
  title       String
  description String
  date        DateTime
  student     Student  @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model Skill {
  id          String  @id @default(cuid())
  student_id  String
  name        String
  proficiency String
  category    String
  student     Student @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model Recommendation {
  id         String   @id @default(cuid())
  student_id String
  author     String
  content    String
  date       DateTime
  student    Student  @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model ApplicationDoc {
  id         String   @id @default(cuid())
  student_id String
  type       String
  content    String
  date       DateTime
  student    Student  @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model Attendance {
  id         String   @id @default(cuid())
  student_id String
  date       DateTime
  status     String
  student    Student  @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model GrowthOpportunity {
  id                    String   @id @unique
  student_id            String
  area                  String
  description           String
  suggested_improvement String
  progress              String?
  data                  Json
  created_at            DateTime @default(now())
  updated_at            DateTime @updatedAt
  student               Student  @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model CounselorNote {
  id           String    @id @default(cuid())
  student_id   String
  counselor_id String
  topic        String
  content      String
  created_at   DateTime  @default(now())
  counselor    Counselor @relation(fields: [counselor_id], references: [id], onDelete: Cascade)
  student      Student   @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model Scholarship {
  id          String  @id @default(cuid())
  student_id  String
  name        String
  match_level String
  criteria    String?
  student     Student @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model EmotionalWellness {
  id                String   @id @default(cuid())
  student_id        String
  wellness_score    Float?
  behavioral_alerts String?
  check_in_date     DateTime
  student           Student  @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model ApplicationStatus {
  id             String   @id @default(cuid())
  student_id     String
  college        String
  status         String
  early_decision Boolean
  deadline       DateTime
  student        Student  @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model OceanScores {
  id                      String          @id @default(cuid())
  student_id              String          @unique
  extroversion            Float
  agreeableness           Float
  conscientiousness       Float
  neuroticism             Float
  openness_to_experience  Float
  created_at              DateTime        @default(now())
  updated_at              DateTime        @updatedAt
  student                 Student         @relation(fields: [student_id], references: [id], onDelete: Cascade)
  student_majors          StudentMajors[]
}

model StudentMajors {
  id              String                @id @default(cuid())
  student_id      String
  ocean_score_id  String
  summary         String?
  created_at      DateTime              @default(now())
  updated_at      DateTime              @updatedAt
  student         Student               @relation(fields: [student_id], references: [id], onDelete: Cascade)
  ocean_score     OceanScores           @relation(fields: [ocean_score_id], references: [id], onDelete: Cascade)
  matches         StudentMajorMatches[]

  @@unique([ocean_score_id])
  @@index([student_id])
  @@index([ocean_score_id])
}

model StudentMajorMatches {
  id                 String        @id @default(cuid())
  student_major_id   String
  major_name         String
  match_percentage   Float
  liked              Boolean?
  disliked           Boolean?
  created_at         DateTime      @default(now())
  updated_at         DateTime      @updatedAt
  student_major      StudentMajors @relation(fields: [student_major_id], references: [id], onDelete: Cascade)

  @@unique([student_major_id, major_name])
  @@index([student_major_id])
  @@index([match_percentage])
}

model NarrativeTheme {
  id           String  @id @default(cuid())
  student_id   String
  theme        String
  description  String
  derived_from String?
  student      Student @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model MilestoneTracking {
  id                String   @id @default(cuid())
  student_id        String
  milestone         String
  due_date          DateTime
  status            String
  notification_sent Boolean
  student           Student  @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model InteractionLog {
  id           String    @id @default(cuid())
  student_id   String
  counselor_id String
  date         DateTime
  content      String
  counselor    Counselor @relation(fields: [counselor_id], references: [id], onDelete: Cascade)
  student      Student   @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model TeacherComment {
  id             String   @id @default(cuid())
  student_id     String
  comment        String
  teacher_name   String
  context        String?
  comment_hash   String   @unique
  counselor_name String
  department     String
  grade_level    Int
  grading_period String
  school_year    String
  class_name     String
  letter_grade   String
  created_at     DateTime @default(now())
  updated_at     DateTime @updatedAt
  student        Student  @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model Questionnaire {
  id                        String                   @id @default(cuid())
  created_at                DateTime                 @default(now())
  updated_at                DateTime                 @updatedAt
  name                      String
  description               String?
  creator_id                String?
  binary_questions          BinaryQuestion[]
  ChatEvaluatorConfig       ChatEvaluatorConfig[]
  multiple_choice_questions MultipleChoiceQuestion[]
  questions                 Question[]
  responses                 QuestionResponse[]
  creator                   User?                    @relation(fields: [creator_id], references: [id], onDelete: Cascade)
  workflow                  Workflow[]
  students                  Student[]                @relation("QuestionnaireToStudent")

  @@unique([name, creator_id])
}

model Question {
  id               String             @id @default(cuid())
  question         String
  question_hash    String
  questionnaire_id String
  created_at       DateTime           @default(now())
  updated_at       DateTime           @updatedAt
  index            Int                @default(0)
  slug             String             @default("")
  can_skip         Boolean            @default(false)
  questionnaire    Questionnaire      @relation(fields: [questionnaire_id], references: [id], onDelete: Cascade)
  response         QuestionResponse[]

  @@unique([questionnaire_id, question_hash])
}

model QuestionResponse {
  id                 String                  @id @default(cuid())
  student_id         String
  question_id        String?
  response           String?
  questionnaire_id   String?
  created_at         DateTime                @default(now())
  updated_at         DateTime                @updatedAt
  step_id            String                  @default("")
  chatEvaluatorRunId String?
  binary_question_id String?
  response_data      Json?
  mc_question_id     String?
  binary_question    BinaryQuestion?         @relation(fields: [binary_question_id], references: [id], onDelete: Cascade)
  ChatEvaluatorRun   ChatEvaluatorRun?       @relation(fields: [chatEvaluatorRunId], references: [id])
  mc_question        MultipleChoiceQuestion? @relation(fields: [mc_question_id], references: [id], onDelete: Cascade)
  question           Question?               @relation(fields: [question_id], references: [id], onDelete: Cascade)
  questionnaire      Questionnaire?          @relation(fields: [questionnaire_id], references: [id], onDelete: Cascade)
  student            Student                 @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model Workflow {
  id                                      String                                     @id @default(cuid())
  name                                    String
  parent_step_id                          String?
  created_at                              DateTime                                   @default(now())
  updated_at                              DateTime                                   @updatedAt
  owner_id                                String?
  status                                  WorkflowStatus                             @default(DRAFT)
  questionnaire_id                        String?
  workflow_type                           WorkflowType                               @default(STRUCTURED)
  data                                    Json?
  tags                                    String[]                                   @default([])
  disable_system_prompt                   Boolean                                    @default(false)
  evaluator_runs                          ChatEvaluatorRun[]
  gen_unstructured_conversation_summaries GeneratedUnstructuredConversationSummary[]
  student_workflows                       StudentWorkflow[]
  owner                                   User?                                      @relation(fields: [owner_id], references: [id], onDelete: Cascade)
  parent_step                             WorkflowStep?                              @relation("child_workflows", fields: [parent_step_id], references: [id])
  questionnaire                           Questionnaire?                             @relation(fields: [questionnaire_id], references: [id], onDelete: Cascade)
  steps                                   WorkflowStep[]                             @relation("steps")
  engagement_events                       EngagementEvent[]
}

model WorkflowStep {
  id                 String                @id @default(cuid())
  goal               String
  name               String
  setter             String?
  data               Json
  parent_workflow_id String
  created_at         DateTime              @default(now())
  updated_at         DateTime              @updatedAt
  index              Int                   @default(0)
  tags               String[]              @default([])
  student_steps      StudentWorkFlowStep[]
  child_workflows    Workflow[]            @relation("child_workflows")
  parent_workflow    Workflow              @relation("steps", fields: [parent_workflow_id], references: [id], onDelete: Cascade)

  @@unique([parent_workflow_id, name])
}

model StudentWorkFlowStep {
  id                  String          @id @default(cuid())
  completed           Boolean         @default(false)
  created_at          DateTime        @default(now())
  data                Json
  step_id             String
  updated_at          DateTime        @updatedAt
  student_id          String
  student_workflow_id String
  step                WorkflowStep    @relation(fields: [step_id], references: [id], onDelete: Cascade)
  student             Student         @relation(fields: [student_id], references: [id], onDelete: Cascade)
  student_workflow    StudentWorkflow @relation(fields: [student_workflow_id], references: [id], onDelete: Cascade)

  @@unique([student_id, step_id])
}

model StudentWorkflow {
  id             String                @id @default(cuid())
  created_at     DateTime              @default(now())
  updated_at     DateTime              @updatedAt
  student_id     String
  workflow_id    String
  init_steps     Boolean               @default(false)
  status         StudentWorkflowStatus @default(NOT_STARTED)
  step_data_hash String?
  last_reminder  DateTime              @default(now())
  mode           StudentWorkflowMode   @default(web)
  steps          StudentWorkFlowStep[]
  call_records   CallRecord[]
  student        Student               @relation(fields: [student_id], references: [id], onDelete: Cascade)
  workflow       Workflow              @relation(fields: [workflow_id], references: [id], onDelete: Cascade)

  @@unique([student_id, workflow_id])
}

model CallRecord {
  id                  String          @id @default(cuid())
  student             Student         @relation(fields: [student_id], references: [id], onDelete: Cascade)
  student_id          String
  student_workflow    StudentWorkflow @relation(fields: [student_workflow_id], references: [id], onDelete: Cascade)
  student_workflow_id String
  twilio_call_sid     String          @unique
  status              CallStatus      @default(INITIATED)
  start_time          DateTime        @default(now())
  end_time            DateTime?
  duration            Int? // Duration in seconds
  transcript          String?         @db.Text
  failure_reason      String?         @db.Text
  retry_count         Int             @default(0)
  created_at          DateTime        @default(now())
  updated_at          DateTime        @updatedAt

  @@index([student_id])
  @@index([student_workflow_id])
  @@index([twilio_call_sid])
}

model StudentAgent {
  id          String             @id @default(cuid())
  student_id  String
  prompt_id   String
  config_id   String
  created_at  DateTime           @default(now())
  updated_at  DateTime           @updatedAt
  messages_id String?
  config      StudentAgentConfig @relation(fields: [config_id], references: [id], onDelete: Cascade)
  prompt      Prompt             @relation(fields: [prompt_id], references: [id], onDelete: Cascade)
  student     Student            @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model StudentAgentConfig {
  id             String         @id @default(cuid())
  role           String
  prompt_id      String
  created_at     DateTime       @default(now())
  updated_at     DateTime       @updatedAt
  past_prompts   Prompt[]       @relation("past_student_prompts")
  student_agents StudentAgent[]
  prompt         Prompt         @relation(fields: [prompt_id], references: [id], onDelete: Cascade)
}

model Prompt {
  id                            String               @id @default(cuid())
  content                       String
  created_at                    DateTime             @default(now())
  updated_at                    DateTime             @updatedAt
  past_student_agent_config_id  String?
  past_addie_config_id          String?
  past_chat_evaluator_config_id String?
  past_system_addie_config_id   String?
  tags                          String[]
  addie_config                  AddieConfig?         @relation("addie_prompt")
  addie_system_configs          AddieConfig[]        @relation("addie_system_prompts")
  chatEvaluatorConfig           ChatEvaluatorConfig?
  past_addie_config             AddieConfig?         @relation("past_addie_prompts", fields: [past_addie_config_id], references: [id], onDelete: Cascade)
  past_chat_evaluator_config    ChatEvaluatorConfig? @relation("past_chateval_prompts", fields: [past_chat_evaluator_config_id], references: [id], onDelete: Cascade)
  past_student_agent_config     StudentAgentConfig?  @relation("past_student_prompts", fields: [past_student_agent_config_id], references: [id], onDelete: Cascade)
  past_system_addie_config      AddieConfig?         @relation("past_addie_system_prompts", fields: [past_system_addie_config_id], references: [id], onDelete: Cascade)
  student_agents                StudentAgent[]
  student_agent_config          StudentAgentConfig[]
  prompt_suggestions            PromptSuggestion[]   @relation("PromptToPromptSuggestion")
}

model AddieConfig {
  id                  String   @id @default(cuid())
  prompt_id           String   @unique
  created_at          DateTime @default(now())
  updated_at          DateTime @updatedAt
  system_prompt_id    String?
  prompt              Prompt   @relation("addie_prompt", fields: [prompt_id], references: [id], onDelete: Cascade)
  system_prompt       Prompt?  @relation("addie_system_prompts", fields: [system_prompt_id], references: [id], onDelete: Cascade)
  past_prompts        Prompt[] @relation("past_addie_prompts")
  past_system_prompts Prompt[] @relation("past_addie_system_prompts")
}

model StudentContext {
  id         String   @id @default(cuid())
  data       Json
  message_id String   @unique
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
}

model ChatEvaluatorConfig {
  id               String             @id @default(cuid())
  name             String?
  prompt_id        String             @unique
  user_id          String
  questionnaire_id String?
  created_at       DateTime           @default(now())
  updated_at       DateTime           @updatedAt
  msg_type         MsgType            @default(AI)
  is_raw_prompt    Boolean            @default(false)
  prompt           Prompt             @relation(fields: [prompt_id], references: [id], onDelete: Cascade)
  questionnaire    Questionnaire?     @relation(fields: [questionnaire_id], references: [id], onDelete: Cascade)
  user             User               @relation(fields: [user_id], references: [id], onDelete: Cascade)
  ChatEvaluatorRun ChatEvaluatorRun[]
  past_prompts     Prompt[]           @relation("past_chateval_prompts")
}

model ChatEvaluatorRun {
  id          String              @id @default(cuid())
  config_id   String
  created_at  DateTime            @default(now())
  updated_at  DateTime            @updatedAt
  score       Float
  user_id     String
  workflow_id String
  config      ChatEvaluatorConfig @relation(fields: [config_id], references: [id], onDelete: Cascade)
  user        User                @relation(fields: [user_id], references: [id], onDelete: Cascade)
  workflow    Workflow            @relation(fields: [workflow_id], references: [id], onDelete: Cascade)
  responses   QuestionResponse[]
}

model GeneratedTeacherCommentsSummary {
  id         String   @id @default(cuid())
  student_id String
  content    String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  student    Student  @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model GeneratedStudentConversationSummary {
  id         String   @id @default(cuid())
  student_id String
  content    String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  student    Student  @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model PromptSuggestion {
  id              String    @id @default(cuid())
  user_id         String
  message_id      Int?      @unique
  created_at      DateTime  @default(now())
  updated_at      DateTime  @updatedAt
  name            String
  used_by_message messages? @relation(fields: [message_id], references: [id])
  user            User      @relation(fields: [user_id], references: [id], onDelete: Cascade)
  prompt          Prompt[]  @relation("PromptToPromptSuggestion")
}

model School {
  id              String        @id @default(cuid())
  name            String
  created_at      DateTime      @default(now())
  updated_at      DateTime      @updatedAt
  organization_id String?
  counselors      Counselor[]
  courses         Course[]
  enrollments     Enrollment[]
  organization    Organization? @relation(fields: [organization_id], references: [id])
  classes         SchoolClass[]
  students        Student[]
}

model BinaryQuestion {
  id               String             @id @default(cuid())
  slug             String             @default("")
  question         String
  question_hash    String
  questionnaire_id String
  can_skip         Boolean            @default(false)
  index            Int                @default(0)
  questionnaire    Questionnaire      @relation(fields: [questionnaire_id], references: [id], onDelete: Cascade)
  responses        QuestionResponse[]

  @@unique([questionnaire_id, question_hash])
}

model MultipleChoiceQuestion {
  id               String                 @id @default(cuid())
  slug             String                 @default("")
  question         String
  question_hash    String
  questionnaire_id String
  index            Int                    @default(0)
  created_at       DateTime               @default(now())
  updated_at       DateTime               @updatedAt
  can_skip         Boolean                @default(false)
  type             MultipleChoiceType     @default(STANDARD)
  choices          MultipleChoiceOption[] @relation("mc_options")
  questionnaire    Questionnaire          @relation(fields: [questionnaire_id], references: [id], onDelete: Cascade)
  responses        QuestionResponse[]

  @@unique([questionnaire_id, question_hash])
}

model MultipleChoiceOption {
  id          String                 @id @default(cuid())
  question_id String
  option      String
  created_at  DateTime               @default(now())
  updated_at  DateTime               @updatedAt
  question    MultipleChoiceQuestion @relation("mc_options", fields: [question_id], references: [id], onDelete: Cascade)
}

model GeneratedUnstructuredConversationSummary {
  id          String            @id @default(cuid())
  student_id  String
  workflow_id String
  content     String
  created_at  DateTime          @default(now())
  updated_at  DateTime          @updatedAt
  student     Student           @relation(fields: [student_id], references: [id], onDelete: Cascade)
  workflow    Workflow          @relation(fields: [workflow_id], references: [id], onDelete: Cascade)
  insights    SummaryInsights[]
}

model SummaryInsights {
  id         String                                   @id @default(cuid())
  summary_id String
  goal       String
  insight    String
  created_at DateTime                                 @default(now())
  updated_at DateTime                                 @updatedAt
  summary    GeneratedUnstructuredConversationSummary @relation(fields: [summary_id], references: [id], onDelete: Cascade)
}

model ReminderTemplate {
  id         String     @id @default(cuid())
  name       String     @db.VarChar(100)
  tier       String?    @db.VarChar(50)
  content    String
  created_at DateTime   @default(now())
  updated_at DateTime   @updatedAt
  reminders  Reminder[]

  @@index([tier])
}

model Reminder {
  id             String           @id @default(cuid())
  student_id     String
  template_id    String
  scheduled_time DateTime?
  status         ReminderStatus   @default(PENDING)
  sent_time      DateTime?
  created_by_id  String?
  created_at     DateTime         @default(now())
  updated_at     DateTime         @updatedAt
  final_message  String
  tier           String?
  created_by     User?            @relation(fields: [created_by_id], references: [id])
  student        Student          @relation(fields: [student_id], references: [id], onDelete: Cascade)
  template       ReminderTemplate @relation(fields: [template_id], references: [id])
  engagement_events EngagementEvent[]

  @@index([student_id])
  @@index([template_id])
  @@index([scheduled_time])
  @@index([status])
}

model ExternalSystemMapping {
  id            String   @id @default(cuid())
  internal_id   String
  external_id   String
  external_type String
  entity_type   String
  channel_id    String?
  datasource_id String?
  sourced_id    String?
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt

  @@unique([internal_id, external_id, entity_type], name: "external_mapping_unique")
  @@index([external_id, entity_type])
  @@index([internal_id, entity_type])
}

model Course {
  id                 String        @id @default(cuid())
  sourced_id         String?       @unique
  title              String
  course_code        String?
  subject_area       String?
  grade_levels       String[]
  org_id             String?
  status             String        @default("active")
  date_last_modified DateTime?
  created_at         DateTime      @default(now())
  updated_at         DateTime      @updatedAt
  school             School?       @relation(fields: [org_id], references: [id])
  classes            SchoolClass[]

  @@index([course_code])
  @@index([org_id])
}

model SchoolClass {
  id                 String       @id @default(cuid())
  sourced_id         String?      @unique
  title              String
  class_code         String?
  class_type         String?
  location           String?
  periods            String[]
  subjects           String[]
  course_id          String?
  school_id          String?
  status             String       @default("active")
  date_last_modified DateTime?
  created_at         DateTime     @default(now())
  updated_at         DateTime     @updatedAt
  enrollments        Enrollment[]
  course             Course?      @relation(fields: [course_id], references: [id])
  school             School?      @relation(fields: [school_id], references: [id])

  @@index([class_code])
  @@index([course_id])
  @@index([school_id])
}

model Enrollment {
  id                 String      @id @default(cuid())
  sourced_id         String?     @unique
  student_id         String
  class_id           String
  school_id          String?
  role               String      @default("student")
  primary            Boolean     @default(false)
  begin_date         DateTime?
  end_date           DateTime?
  status             String      @default("active")
  date_last_modified DateTime?
  created_at         DateTime    @default(now())
  updated_at         DateTime    @updatedAt
  school_class       SchoolClass @relation(fields: [class_id], references: [id], onDelete: Cascade)
  school             School?     @relation(fields: [school_id], references: [id])
  student            Student     @relation(fields: [student_id], references: [id], onDelete: Cascade)

  @@unique([student_id, class_id])
  @@index([student_id])
  @@index([class_id])
  @@index([school_id])
}

model Organization {
  id                 String         @id @default(cuid())
  sourced_id         String?        @unique
  name               String
  org_type           String
  identifier         String?
  parent_id          String?
  status             String         @default("active")
  date_last_modified DateTime?
  created_at         DateTime       @default(now())
  updated_at         DateTime       @updatedAt
  parent             Organization?  @relation("OrganizationHierarchy", fields: [parent_id], references: [id])
  children           Organization[] @relation("OrganizationHierarchy")
  schools            School[]

  @@index([org_type])
  @@index([parent_id])
}

model RosterStreamWebhook {
  id            String   @id @default(cuid())
  webhook_id    String   @unique
  datasource_id String
  entity_type   String
  action        String
  sourced_id    String?
  processed     Boolean  @default(false)
  error_message String?
  payload       Json
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt

  @@index([entity_type, action])
  @@index([datasource_id])
  @@index([processed])
}

model Demographic {
  id                 String    @id @default(cuid())
  sourced_id         String?   @unique
  user_id            String
  birth_date         DateTime?
  sex                String?
  race_ethnicity     String?
  native_language    String?
  status             String    @default("active")
  date_last_modified DateTime?
  created_at         DateTime  @default(now())
  updated_at         DateTime  @updatedAt
  user               User      @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
}

enum Timezone {
  UTC_MINUS_05_00 @map("UTC-05:00")
  UTC_MINUS_06_00 @map("UTC-06:00")
  UTC_MINUS_07_00 @map("UTC-07:00")
  UTC_MINUS_08_00 @map("UTC-08:00")
  UTC_MINUS_09_00 @map("UTC-09:00")
  UTC_MINUS_10_00 @map("UTC-10:00")
  UTC_PLUS_01_00  @map("UTC+01:00")
  UTC_MINUS_03_00 @map("UTC-03:00")
}

enum AvailabilityDay {
  WEEKDAY
  WEEKEND
}

enum AvailabilityBlock {
  MORNING
  AFTERNOON
  EVENING
}

enum UserRole {
  STUDENT
  COUNSELOR
  ADMIN
}

enum WorkflowStatus {
  INACTIVE
  DRAFT
  PUBLISHED
}

enum WorkflowType {
  STRUCTURED
  UNSTRUCTURED
  ASSIGNMENT
}

enum StudentWorkflowStatus {
  IN_PROGRESS
  NOT_STARTED
  COMPLETED
  COUNSELOR_UNPUBLISHED
}

enum StudentWorkflowMode {
  web
  sms
  voice
}

enum CallStatus {
  INITIATED
  IN_PROGRESS
  COMPLETED
  FAILED
}
enum MsgType {
  Human
  AI
  Tool
}

enum MultipleChoiceType {
  STANDARD
  LIKERT
}

enum ReminderStatus {
  PENDING
  SENT
  FAILED
  CANCELLED
}

enum EngagementTier {
  Active
  At_Risk @map("At-Risk")
  Dormant
}

enum EngagementChannel {
  sms
  web_student
  web_counselor
  voice
  email
}

enum EngagementEventType {
  message_sent
  message_received
  session_started
  session_ended
  reminder_sent
  reminder_engaged
  email_sent
  email_opened
  email_clicked
}

model EngagementEvent {
  id                    String               @id @default(uuid())
  user_id              String
  student_id           String?
  session_id           String?
  channel              EngagementChannel
  event_type           EngagementEventType
  reminder_id          String?
  template_id          String?
  message_id           Int?
  workflow_id          String?
  email_id             String?
  email_template_name  String?
  engaged_at           DateTime             @default(now())
  metadata             Json?
  created_at           DateTime             @default(now())
  
  user                 User                 @relation(fields: [user_id], references: [id], onDelete: Cascade)
  student              Student?             @relation(fields: [student_id], references: [id], onDelete: Cascade)
  message              messages?            @relation(fields: [message_id], references: [id], onDelete: SetNull)
  workflow             Workflow?            @relation(fields: [workflow_id], references: [id], onDelete: SetNull)
  reminder             Reminder?            @relation(fields: [reminder_id], references: [id], onDelete: SetNull)
  
  @@index([user_id, engaged_at(sort: Desc)])
  @@index([student_id, engaged_at(sort: Desc)])
  @@index([session_id, engaged_at(sort: Desc)])
  @@index([reminder_id, engaged_at(sort: Desc)])
  @@index([channel, event_type, engaged_at(sort: Desc)])
  @@index([email_id, event_type])
  @@index([email_template_name, engaged_at(sort: Desc)])
  @@map("engagement_events")
}

model OnboardingActivity {
  id                    String    @id @default(cuid())
  student_id            String    @unique
  conversation_id       String?
  onboarding_started_at DateTime?
  engagement_tier       String    @default("Inactive")
  status                String    @default("pending")
  created_at            DateTime  @default(now())
  updated_at            DateTime  @updatedAt

  student               Student   @relation(fields: [student_id], references: [id], onDelete: Cascade)

  @@index([student_id])
  @@index([conversation_id])
  @@index([engagement_tier])
}
