import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { initiateSmsConversation as initiateSmsAction } from "@/actions/sms";

export const useSmsInitiation = () => {
  const router = useRouter();

  const initiateSmsConversation = async (studentConversationId: string, phoneNumber?: string | null) => {
    try {
      const result = await initiateSmsAction(studentConversationId);

      toast.message(`Hold Tight, <PERSON><PERSON> is Reaching Out`, {
        description: `You'll receive an SMS from <PERSON><PERSON> shortly at ${phoneNumber || "your phone"}.`,
      });

      router.refresh();
      return true;
    } catch (error) {
      console.error("Error initiating SMS conversation:", error);
      toast.error("Failed to initiate SMS conversation");
      return false;
    }
  };

  return { initiateSmsConversation };
};
