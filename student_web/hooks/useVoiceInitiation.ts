import { useState, useCallback } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { isValidPhoneNumber } from "react-phone-number-input";
import { initiateVoiceCall, getCallStatus } from "@/actions/voice";

export const useVoiceInitiation = () => {
  const router = useRouter();
  const [isInitiating, setIsInitiating] = useState(false);

  const initiateCall = async (
    studentId: string,
    studentWorkflowId: string,
    phoneNumber?: string | null
  ) => {
    try {
      setIsInitiating(true);

      // Validate phone number - clean it first to remove any invisible characters
      const cleanedPhoneNumber = phoneNumber?.trim().replace(/[\u200E\u200F\u202A-\u202E]/g, '');
      if (!cleanedPhoneNumber || !isValidPhoneNumber(cleanedPhoneNumber)) {
        toast.error(`Please provide a valid phone number. Current: "${phoneNumber}"`);
        return false;
      }

      // Check if there's already an active call
      const existingCallStatus = await getCallStatus(studentId, studentWorkflowId);
      if (existingCallStatus && (
        existingCallStatus.status === "INITIATED" ||
        existingCallStatus.status === "IN_PROGRESS"
      )) {
        toast.error("You already have an active call in progress");
        return false;
      }

      const result = await initiateVoiceCall(studentId, studentWorkflowId);

      toast.message(`Addie is Calling You Now!`, {
        description: `You'll receive a call from Addie shortly at ${phoneNumber || "your phone"}.`,
      });

      router.refresh();
      return true;
    } catch (error) {
      console.error("Error initiating voice call:", error);
      toast.error("Failed to initiate voice call");
      return false;
    } finally {
      setIsInitiating(false);
    }
  };

  const checkCallStatus = useCallback(async (
    studentId: string,
    studentWorkflowId: string
  ) => {
    try {
      const status = await getCallStatus(studentId, studentWorkflowId);
      return status;
    } catch (error) {
      console.error("Error checking call status:", error);
      return null;
    }
  }, []);

  return {
    initiateCall,
    checkCallStatus,
    isInitiating
  };
};
