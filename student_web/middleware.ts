// Use NextAuth's built-in middleware (no Prisma dependency)
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";
import { env } from "./env.mjs";
import { prisma } from "./common/db";
import log from "./common/logger";

// Routes that require authentication
const protectedRoutes = ["/", "/dashboard", "/chat", "/settings", "/onboarding"];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Skip auth checks for auth API routes, static assets and favicon
  if (
    pathname.startsWith("/api/auth") ||
    pathname.startsWith("/_next") ||
    pathname.includes("/favicon.ico") ||
    pathname.includes("/site.webmanifest")
  ) {
    return NextResponse.next();
  }

  // Create a modified request to ensure cookies are processed correctly with tunnelmole
  const isTunnelmole = request.headers.get("host")?.includes("tunnelmole");
  
  // Get the session token from the request (no database access)
  const token = await getToken({ 
    req: request, 
    secret: env.AUTH_SECRET,
    secureCookie: process.env.NODE_ENV === "production" || isTunnelmole
  });
  
  // Check if route requires authentication
  const isProtectedRoute = protectedRoutes.some(route => pathname === route || pathname.startsWith(`${route}/`));
  
  // Check if user is on auth routes (login/register) when already authenticated
  const isAuthRoute = pathname.startsWith("/login") || pathname.startsWith("/register");
  
  // Redirect register attempts to login page
  if (pathname.startsWith("/register")) {
    log.info("Register page accessed, redirecting to login", {
      userId: token?.sub,
      email: token?.email,
      path: pathname,
      redirectTo: "/login"
    });
    return NextResponse.redirect(new URL("/login", request.url));
  }
  
  if (isAuthRoute && token) {
    // User is already authenticated, redirect to home
    log.info("Authenticated user trying to access auth route, redirecting to home", {
      userId: token.sub,
      email: token.email,
      path: pathname,
      redirectTo: "/"
    });
    return NextResponse.redirect(new URL("/", request.url));
  }
  
  // Allow unauthenticated users to access login route
  if (pathname.startsWith("/login") && !token) {
    return NextResponse.next();
  }
  
  // Handle authentication for protected routes
  if (isProtectedRoute && !token) {
    // Log unauthenticated access attempt
    log.info("Unauthenticated access attempt to protected route", {
      path: pathname,
      userAgent: request.headers.get("user-agent"),
      ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown",
      timestamp: new Date().toISOString()
    });
    
    // Redirect to login if no auth token
    const url = new URL("/login", request.url);
    
    // Use AUTH_URL as the base for the callback URL, preserving the path
    const authUrl = env.AUTH_URL;
    if (authUrl) {
      const callbackUrl = new URL(request.nextUrl.pathname, authUrl);
      url.searchParams.set("callbackUrl", encodeURI(callbackUrl.toString()));
    } else {
      // Fallback to the original behavior if AUTH_URL is not set
      url.searchParams.set("callbackUrl", encodeURI(request.url));
    }
    
    return NextResponse.redirect(url);
  }
  
  // Check if user is disabled for protected routes
  // Only block if explicitly disabled (enabled === false), allow if enabled is undefined/null
  if (isProtectedRoute && token && token.enabled === false) {
    // Check database to see if user has been re-enabled since token was created
    if (token.email) {
      try {
        const dbUser = await prisma.user.findUnique({
          where: { email: token.email },
          select: { enabled: true }
        });
        
        // If user is now enabled in database, allow them through
        // The JWT will be refreshed on next auth operation
        if (dbUser && dbUser.enabled === true) {
          log.info("User re-enabled in database, allowing access despite stale token", {
            userId: token.sub,
            email: token.email,
            path: pathname
          });
          return NextResponse.next();
        }
      } catch (error) {
        log.error("Error checking user enabled status in middleware", { error });
      }
    }
    
    // Log disabled account access attempt
    log.warn("Disabled account attempted to access protected route", {
      userId: token.sub,
      email: token.email,
      path: pathname,
      userAgent: request.headers.get("user-agent"),
      ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown",
      timestamp: new Date().toISOString()
    });
    
    // Redirect to login with disabled message
    const url = new URL("/login", request.url);
    url.searchParams.set("error", "account_disabled");
    return NextResponse.redirect(url);
  }
  
  // We have token OR non-protected route - let the request through
  return NextResponse.next();
}

export const config = {
  matcher: [
    "/((?!_next/static|_next/image|favicon.ico).*)",
  ],
};