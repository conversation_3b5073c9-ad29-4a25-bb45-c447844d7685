import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
  server: {
    // This is optional because it's only used in development.
    // See https://next-auth.js.org/deployment.
    AUTH_URL: z.string().url().optional(),
    AUTH_SECRET: z.string().min(1),
    GOOGLE_CLIENT_ID: z.string().min(1),
    GOOGLE_CLIENT_SECRET: z.string().min(1),
    DATABASE_URL: z.string().min(1),
    RESEND_API_KEY: z.string().min(1),
    EMAIL_FROM: z.string().min(1),
    GOOGLE_API_KEY: z.string().min(1),
    ADDIE_API_HOST: z.string().min(1),
    ADDIE_API_KEY: z.string().min(1),
    LOG_LEVEL: z.enum(["trace", "debug", "info", "warn", "error", "fatal"]).optional(),
  },
  client: {
    URL: z.string().url().optional(),
    NEXT_PUBLIC_APP_URL: z.string().min(1),
    NEXT_PUBLIC_USERPILOT_TOKEN: z.string().min(1),
    NEXT_PUBLIC_DEBUG_MAGIC_LINK: z.string().transform((val) => val === "true").default("false"),
  },
  runtimeEnv: {
    AUTH_URL: process.env.AUTH_URL,
    URL: process.env.AUTH_URL,
    AUTH_SECRET: process.env.AUTH_SECRET,
    GOOGLE_API_KEY: process.env.GOOGLE_API_KEY,
    GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
    DATABASE_URL: process.env.DATABASE_URL,
    RESEND_API_KEY: process.env.RESEND_API_KEY,
    EMAIL_FROM: process.env.EMAIL_FROM,
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    ADDIE_API_HOST: process.env.ADDIE_API_HOST,
    ADDIE_API_KEY: process.env.ADDIE_API_KEY,
    NEXT_PUBLIC_USERPILOT_TOKEN: process.env.NEXT_PUBLIC_USERPILOT_TOKEN,
    LOG_LEVEL: process.env.LOG_LEVEL,
    NEXT_PUBLIC_DEBUG_MAGIC_LINK: process.env.NEXT_PUBLIC_DEBUG_MAGIC_LINK,
  },
});
