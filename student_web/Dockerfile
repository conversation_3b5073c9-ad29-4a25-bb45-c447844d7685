# Build stage
FROM us-docker.pkg.dev/addie-440119/addie/student-web:base AS builder

WORKDIR /app

# Define build arguments
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

ARG BUILD_ENV=dev
ENV BUILD_ENV=${BUILD_ENV}

ARG EMAIL_FROM
ARG ADDIE_API_HOST
ARG ADDIE_API_KEY
ARG AUTH_URL
ARG NEXTAUTH_URL
ARG NEXT_PUBLIC_APP_URL

# Copy package files for better layer caching
COPY --chown=nextjs package.json pnpm-lock.yaml ./

# Copy prisma schema
COPY --chown=nextjs prisma ./prisma/

# Generate Prisma client
RUN pnpm prisma generate --generator client_js

# Verify the generated client includes the latest schema
RUN node -e "const { PrismaClient } = require('@prisma/client'); console.log('Prisma client generated successfully')"

# Copy the rest of the application
COPY --chown=nextjs . .

RUN echo "### NODE_ENV: ${NODE_ENV} ###"
RUN echo "### BUILD_ENV: ${BUILD_ENV} ###"

# Build the application with cache mounted and secrets accessible to the nextjs user
RUN --mount=type=secret,id=auth_secret,mode=0400,uid=1001,gid=1001 \
    --mount=type=secret,id=google_client_id,mode=0400,uid=1001,gid=1001 \
    --mount=type=secret,id=google_client_secret,mode=0400,uid=1001,gid=1001 \
    --mount=type=secret,id=google_api_key,mode=0400,uid=1001,gid=1001 \
    --mount=type=secret,id=database_url,mode=0400,uid=1001,gid=1001 \
    --mount=type=secret,id=resend_api_key,mode=0400,uid=1001,gid=1001 \
    --mount=type=secret,id=next_public_userpilot_token,mode=0400,uid=1001,gid=1001 \
    --mount=type=cache,id=pnpm,target=/root/.local/share/pnpm/store \
    AUTH_SECRET=$(cat /run/secrets/auth_secret 2>&1 || echo 'default_auth_secret') \
    GOOGLE_CLIENT_ID=$(cat /run/secrets/google_client_id 2>&1 || echo '') \
    GOOGLE_CLIENT_SECRET=$(cat /run/secrets/google_client_secret 2>&1 || echo '') \
    GOOGLE_API_KEY=$(cat /run/secrets/google_api_key 2>&1 || echo '') \
    DATABASE_URL=$(cat /run/secrets/database_url 2>&1 || echo '') \
    EMAIL_FROM=$EMAIL_FROM \
    ADDIE_API_HOST=$ADDIE_API_HOST \
    ADDIE_API_KEY=$ADDIE_API_KEY \
    AUTH_URL=$(echo $AUTH_URL | tr -d '"') \
    NEXTAUTH_URL=$(echo $NEXTAUTH_URL | tr -d '"') \
    NEXT_PUBLIC_APP_URL=$(echo $NEXT_PUBLIC_APP_URL | tr -d '"') \
    RESEND_API_KEY=$(cat /run/secrets/resend_api_key 2>&1 || echo '') \
    NEXT_PUBLIC_USERPILOT_TOKEN=$(cat /run/secrets/next_public_userpilot_token 2>&1 || echo '') \
    pnpm run build --debug

# Remove dev dependencies to reduce size
RUN pnpm prune --prod

# Clean up unnecessary files
RUN rm -rf node_modules/.cache

# Production stage
FROM us-docker.pkg.dev/addie-440119/addie/student-web:base AS runner

# No need to install dependencies as they should already be in the base image

WORKDIR /app

ARG NODE_ENV
ENV NODE_ENV=${NODE_ENV}
ARG BUILD_ENV
ENV BUILD_ENV=${BUILD_ENV}

# User should be set before cleaning artifacts
USER nextjs

# Clean any existing NextJS build artifacts from base image to prevent conflicts
RUN rm -rf .next .next-build public/sw.js public/workbox-*.js || true

# Copy only built files from builder stage
COPY --from=builder --chown=nextjs:nodejs /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder --chown=nextjs:nodejs /app/next.config.js ./
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./

# Copy prisma files and generated client to runtime stage
COPY --from=builder --chown=nextjs:nodejs /app/prisma ./prisma

# Generate Prisma client at runtime to ensure latest schema
RUN cd /app && pnpm prisma generate --generator client_js

EXPOSE 3001

CMD ["node_modules/.bin/next", "start", "-p", "3001"]
