/**
 * OCEAN Scores utilities for frontend
 */

/**
 * Check if a workflow is a Big Five Inventory (BFI) survey
 */
export function isBFIWorkflow(workflow: any): boolean {
  if (!workflow || !workflow.tags) {
    return false;
  }

  const tags = Array.isArray(workflow.tags) ? workflow.tags : [];
  return tags.includes("type:The Big Five Personality Test (BFPT)");
}

// Re-export server actions for convenience
export { triggerOceanCalculation } from "@/actions/ocean-scores";
