# Build dependencies stage
FROM node:22-alpine AS builder

WORKDIR /app

# Install system dependencies required for builds
RUN apk add --no-cache libc6-compat python3 make g++ openssl openssl-dev zlib-dev g++ curl

# Enable corepack for pnpm
RUN corepack enable

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001 -G nodejs && \
    chown -R nextjs:nodejs /app

# Use non-root user for the rest of the build
USER nextjs

# Copy only the package files first for better caching
COPY --chown=nextjs package.json pnpm-lock.yaml ./

# Install dependencies in development mode for build tools
ENV NODE_ENV=development
RUN pnpm install --frozen-lockfile

# Copy the rest of the files
COPY --chown=nextjs . .

# Generate Prisma client
RUN pnpm prisma generate --generator client_js

# Export as base image
FROM node:22-alpine AS base

WORKDIR /app

# Install only production system dependencies
RUN apk add --no-cache libc6-compat openssl zlib

# Enable pnpm
RUN corepack enable

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001 -G nodejs && \
    chown -R nextjs:nodejs /app

# Use non-root user
USER nextjs

# Copy node_modules and lockfile from builder
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./package.json
COPY --from=builder --chown=nextjs:nodejs /app/pnpm-lock.yaml ./pnpm-lock.yaml

# Copy Prisma generated files and outputs
# First run ls command to see if the prisma files exist and their actual location
RUN --mount=from=builder,source=/app/node_modules,target=/tmp-node-modules ls -la /tmp-node-modules || echo "Listing node_modules content"

# Create node_modules directory if it doesn't exist
RUN mkdir -p /app/node_modules

# Copy the entire prisma directory including generated client
COPY --from=builder --chown=nextjs:nodejs /app/prisma ./prisma

# Use RUN with shell for conditional copying
RUN --mount=from=builder,source=/app,target=/tmp-source \
    if [ -d /tmp-source/node_modules/.prisma ]; then \
      cp -R /tmp-source/node_modules/.prisma /app/node_modules/; \
    fi && \
    if [ -d /tmp-source/node_modules/@prisma ]; then \
      cp -R /tmp-source/node_modules/@prisma /app/node_modules/; \
    fi

# Expose the port
EXPOSE 3000
