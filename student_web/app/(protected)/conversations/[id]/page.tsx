import React from "react";
import { notFound, redirect } from "next/navigation";
import {
  getStudentAnswers,
  getStudentWorkflow,
} from "@/actions/student-conversation";
import { WorkflowType } from "@prisma/client";

import { getInitialChatContext } from "@/common/chat";
import { prisma } from "@/common/db";
import log from "@/common/logger";
import { getCurrentUser } from "@/common/session";
import { Chat } from "@/components/chat";
import { ConversationShell } from "@/components/Conversations/StudentConversation/ConversationShell";

export default async function ConversationDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const resolvedParams = await params;
  const user = await getCurrentUser();
  if (!user) {
    // redirect to not authorized
    throw new Error("Not Authorized");
  }

  const studentId = user?.students[0].id!;
  const workflowId = resolvedParams.id;
  let studentWorkflow;
  try {
    studentWorkflow = await getStudentWorkflow(studentId, workflowId);
  } catch (e) {
    log.error("StudentWorkflow not found", e);
    notFound();
  }

  const conversationType = studentWorkflow.workflow.workflow_type;
  // console.log("studentWorkflow ===>", studentWorkflow);

  // console.log(studentId);
  // TODO: need to test if actual students have answers
  const { answerMap: studentAnswers } = await getStudentAnswers(
    studentId,
    workflowId,
  );

  const { authToken, tzOffset } = await getInitialChatContext();

  const sessionId = `${workflowId}-${user.id}`;

  const messages = await prisma.messages.findMany({
    where: {
      session_id: sessionId,
    },
    orderBy: {
      id: "asc",
    },
  });

  let minMessages = messages.map((msg) => {
    const message = msg.message as any;
    const msgData = message?.data as any;

    return {
      type: msgData.type,
      content: msgData.content,
    };
  });

  // Get tool messages
  const toolMessages = messages.filter((msg) => {
    const message = msg.message as any;
    const msgData = message?.data as any;
    return msgData?.type === "tool";
  });

  // Verify all steps at once
  const stepIds = toolMessages.map((msg) => {
    try {
      const message = msg.message as any;
      const msgData = message?.data as any;
      const content = JSON.parse(msgData.content);

      return content.id;
    } catch (error) {
      console.warn("Failed to parse tool message content:", error);
      return null;
    }
  }).filter((id) => id !== undefined && id !== null); // Filter out undefined/null values

  const completedSteps = stepIds.length > 0 ? await prisma.studentWorkFlowStep.findMany({
    where: {
      id: {
        in: stepIds,
      },
      completed: true,
    },
  }) : [];

  // Filter out empty messages and tools
  minMessages = minMessages.filter((msg) => msg.content.length > 0);
  minMessages = minMessages.filter((msg) => msg.type !== "tool");

  // console.log("Student answers:==>", studentAnswers);
  // console.log("individual student WF ==>", studentWorkflow);

  // Separate conversations based on conversation.workflow.workflow_type here
  if (conversationType === WorkflowType.STRUCTURED) {
    // please see ConversationShell props for required props for each stru./unstru. conversations
    return (
      <ConversationShell
        conversation={studentWorkflow}
        answers={studentAnswers}
        studentId={studentId}
        workflowId={workflowId}
        workflowType={conversationType}
        user={user}
      />
    );
  } else if (
    conversationType === WorkflowType.UNSTRUCTURED ||
    conversationType === WorkflowType.ASSIGNMENT
  ) {
    return (
      <ConversationShell
        conversation={studentWorkflow}
        studentId={studentId}
        workflowId={workflowId}
        workflowType={conversationType}
        authToken={authToken}
        sessionId={sessionId}
        tzOffset={tzOffset}
        user={user}
        messages={minMessages}
      />
    );
  } else {
    notFound();
  }
}
