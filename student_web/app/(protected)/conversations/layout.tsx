import React, { Suspense } from "react";
import { redirect } from "next/navigation";
import {
  getPublishedStudentConversations,
  getStudentAnswers,
} from "@/actions/student-conversation";

import { getCurrentUser } from "@/common/session";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { ConversationLayoutClient } from "@/components/Conversations/ConversationLayoutClient";

export default async function ConversationsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Get user and studentId when page loads
  const user = await getCurrentUser();
  if (!user) {
    redirect("/login");
  }
  const studentId = user!.students![0].id!;

  // const conversations = await getAllConversations();

  const studentConversations =
    await getPublishedStudentConversations(studentId);

  const conversationsWithAnswers = await Promise.all(
    studentConversations.map(async (conversation) => {
      const { answerMap } = await getStudentAnswers(
        studentId,
        conversation.workflow.id,
      );
      return { ...conversation, answerMap };
    }),
  );
  // console.log("studentConvo ==>", conversationsWithAnswers);

  return (
    <Suspense
      fallback={
        <div className="flex h-screen w-full items-center justify-center">
          <div className="flex flex-col items-center justify-center">
            <LoadingSpinner />
            <div>Loading conversations...</div>
          </div>
        </div>
      }
    >
      <ConversationLayoutClient
        initialConversations={conversationsWithAnswers}
        studentId={studentId}
      >
        {children}
      </ConversationLayoutClient>
    </Suspense>
  );
}
