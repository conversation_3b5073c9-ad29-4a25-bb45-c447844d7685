import Image from "next/image";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";

export default function ConversationNotFound() {
  return (
    <div className="mx-auto my-auto flex h-screen max-w-md flex-col items-center justify-center py-12 text-center">
      <div className="relative mb-6">
        <Image
          className="opacity-70"
          alt="conversation not found"
          src="/images/conversation_little-girl.png"
          width={132}
          height={156}
        />
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-5xl font-bold text-destructive">404</div>
        </div>
      </div>

      <h2 className="mb-4 text-2xl font-semibold">Conversation Not Found</h2>
      <p className="mb-8 text-muted-foreground">
        We couldn&#39;t find the conversation you&#39;re looking for. It may
        have been removed or you might not have access to it. Please select a
        conversation from the list.
      </p>

      <Button asChild variant="outline" className="gap-2">
        <Link href="/conversations">
          <ArrowLeft className="h-4 w-4" />
          Back to Conversations
        </Link>
      </Button>
    </div>
  );
}
