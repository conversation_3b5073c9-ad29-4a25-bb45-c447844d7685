import { NextRequest, NextResponse } from "next/server";

import { sendEmail } from "@/common/send-email";
import { getCurrentUser } from "@/common/session";

export async function POST(req: NextRequest) {
  try {
    const { message } = await req.json();
    const user = await getCurrentUser();
    const userId = user?.id;
    const studentId = user?.students[0].id;
    const studentEmail = user?.email;
    const studentName = `${user?.first_name} ${user?.last_name}`;
    if (!message) {
      return NextResponse.json(
        { message: "All fields are required" },
        { status: 400 },
      );
    }

    await sendEmail(
      "<EMAIL>", // support email that linked to slack channel
      `Help Request from ${studentName} (${studentEmail})`,
      studentEmail,
      `
        <p><strong>Message:</strong> ${message}</p>
        <hr />
        <p><strong>Student Name:</strong> ${studentName}</p>
        <p><strong>Student Email:</strong> ${studentEmail}</p>
        <p><strong>Student ID:</strong> ${studentId}</p>
        <p><strong>User ID:</strong> ${userId}</p>
      `,
    );

    return NextResponse.json(
      { message: "Email sent successfully" },
      { status: 200 },
    );
  } catch (error) {
    console.error("Error sending email:", error);
    return NextResponse.json(
      { message: "Failed to send email" },
      { status: 500 },
    );
  }
}
