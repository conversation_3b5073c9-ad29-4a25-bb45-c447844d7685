import { UserRole } from "@prisma/client";
import getServerSession from "next-auth";
import { signIn } from "next-auth/react";

import { prisma } from "@/common/db";
import log from "@/common/logger";

import { User } from "./types";

export const getUserByEmail = async (email: string) => {
  try {
    const user = await prisma.user.findUnique({
      where: {
        email: email,
      },
      select: {
        first_name: true,
        emailVerified: true,
      },
    });

    return user;
  } catch {
    return null;
  }
};

export const getUserById = async (id: string) => {
  try {
    const user = await prisma.user.findUniqueOrThrow({
      where: { id },
      include: {
        students: true,
        preferred_availability: true,
      },
    });

    return user;
  } catch {
    return null;
  }
};
