import { JsonValue } from "@prisma/client/runtime/library";

export interface StudentAgentConfig {
  id: string;
  role: string;
  prompt: Prompt;
  prompt_id: string;
  student_agents?: any | null;
  created_at: Date;
  updated_at: Date;
}

export interface StudentAgent {
  config: any;
  completedWFSteps: number;
  created_at: Date;
  date_of_birth: Date;
  email: string;
  config_id: string;
  emailVerified: boolean;
  first_name: string;
  grade: number;
  id: number;
  image?: string;
  last_name: string;
  messages_id?: string | null;
  middle_name: string;
  msgsCount: number;
  prompt: any;
  prompt_id: string;
  questionaire_id: string | null;
  role: string;
  student_agent_id: string;
  student_id: number;
  updated_at: Date;
  user_id: string;
}

export interface Prompt {
  id: string;
  content: string;
  created_at: Date;
  updated_at: Date;
}

export interface Config {
  id: string;
  prompt_id: string;
  prompt: Prompt;
  created_at: Date;
  updated_at: Date;
}

export interface Workflow {
  steps?: WorkflowStep[];
  id: string;
  name: string;
  parent_step_id: string | null;
  created_at: Date;
  updated_at: Date;
  parent_step?: WorkflowStep | null;
}

export interface WorkflowStep {
  index: number;
  id: string;
  goal: string;
  name: string;
  setter: string | null;
  data: JsonValue;
  parent_workflow_id: string;
  created_at: Date;
  updated_at: Date;
  parent_step?: WorkflowStep | null;
}
