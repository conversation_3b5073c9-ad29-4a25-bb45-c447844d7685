import {google} from "googleapis";

import log from "@/common/logger";

export async function googleProfileImage(account) {
    // set google api key
    const oAuth2Client = new google.auth.OAuth2(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
    );
    oAuth2Client.setCredentials({
        access_token: account.access_token,
        refresh_token: account.refresh_token,
    });

    const peopleApi = google.people({version: "v1", auth: oAuth2Client});

    // Retrieve the user's profile image
    const response = await peopleApi.people.get({
        key: process.env.GOOGLE_API_KEY,
        resourceName: "people/me",
        personFields: "photos",
        auth: account?.access_token,
    });

    log.debug(response);
}