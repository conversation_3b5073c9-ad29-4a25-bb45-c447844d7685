import { Resend } from "resend";

import { env } from "@/env.mjs";

const resend = new Resend(env.RESEND_API_KEY);

export const sendEmail = async (to, subject, email, htmlContent) => {
  try {
    const response = await resend.emails.send({
      // from: "<EMAIL>",
      from: env.EMAIL_FROM,
      to: to,
      replyTo: email,
      subject: subject,
      html: htmlContent,
    });

    console.log("Email sent:", response);
    return response;
  } catch (error) {
    console.error("Error sending email:", error);
    throw error;
  }
};
