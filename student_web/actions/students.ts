"use server";

import { getStudentAnswers } from "@/actions/student-conversation";
import { UserRole } from "@prisma/client";

import { prisma } from "@/common/db";

interface FetchStudentsParams {
  grade?: string | null | undefined;
  counselorId?: string | null | undefined;
  search?: string | null | undefined;
  user: { role: UserRole };
}

export interface StudentWithWorkflows {
  id: string;
  user_id: string;
  grade: number;
  first_name: string;
  last_name: string;
  email: string | null;
  config_id: string | null;
  updated_at: Date;
  student_agent_id: string | null;
  workflow_steps: any[];
  student_workflow: any[];
  onboardingAnsweredCount: number;
  onboardingTotalQuestions: number;
}

/**
 * Checks if a user's email domain is valid
 * @param email - The email to check
 * @param user - The user object containing role information
 * @returns boolean indicating if the domain is valid
 */
const isValidEmailDomain = (
  email: string,
  user: { role: UserRole },
): boolean => {
  const domain = email.split("@")[1];
  if (user?.role === UserRole.ADMIN) {
    return domain !== "example.com";
  }
  const domains = [
    "gmail.com",
    "getaddie.com",
    "example.com",
    "futuresight.ventures",
    "brewery.agency",
  ];
  return !domains.includes(domain);
};

/**
 * Fetches filtered students from the database
 * @param params - Filter parameters including grade, counselorId, search, and user role
 * @returns Promise containing array of student records
 */
const getFilteredStudents = async (params: FetchStudentsParams) => {
  return prisma.student.findMany({
    where: {
      AND: [
        params.grade
          ? { grade: parseInt(params.grade.replace("Grade ", "")) }
          : {},

        params.counselorId && params.user?.role == UserRole.COUNSELOR
          ? { counselors: { some: { id: params.counselorId } } }
          : {},
        params.search
          ? {
              OR: [
                { id: { contains: params.search } },
                {
                  users: {
                    some: {
                      OR: [
                        {
                          first_name: {
                            contains: params.search,
                            mode: "insensitive",
                          },
                        },
                        {
                          last_name: {
                            contains: params.search,
                            mode: "insensitive",
                          },
                        },
                        // Support multi-word search by concatenating first and last name
                        {
                          AND: [
                            {
                              first_name: {
                                contains: params.search.split(" ")[0] || "",
                                mode: "insensitive",
                              },
                            },
                            {
                              last_name: {
                                contains: params.search.split(" ").slice(1).join(" ") || "",
                                mode: "insensitive",
                              },
                            },
                          ],
                        },
                      ],
                    },
                  },
                },
              ],
            }
          : {},
        {
          st_agents: {
            none: {},
          },
        },
      ],
    },
    include: {
      st_agents: true,
      users: {
        select: {
          id: true,
          email: true,
          first_name: true,
          last_name: true,
        },
      },
      workflow_steps: true,
      student_workflow: {
        include: {
          workflow: {
            include: {
              steps: true,
            },
          },
        },
      },
    },
    orderBy: {
      updated_at: "desc",
    },
  });
};

export const fetchStudentsBasicInfo = async (
  params: FetchStudentsParams,
): Promise<StudentWithWorkflows[]> => {
  let students = await getFilteredStudents(params);

  students = students.filter((student) => {
    if (student?.users?.length === 0) return false;
    const user = student?.users[0];
    return (
      user.first_name !== "none" &&
      user.email &&
      isValidEmailDomain(user.email, params.user)
    );
  });

  return students.map(
    ({
      id,
      updated_at,
      grade,
      users,
      st_agents,
      workflow_steps,
      student_workflow,
    }) => {
      const { id: user_id, first_name, last_name, email } = users[0];
      const { id: student_agent_id, config_id } = st_agents[0] || {};

      return {
        id,
        updated_at,
        user_id,
        config_id,
        student_agent_id,
        grade,
        first_name,
        last_name,
        email,
        workflow_steps,
        student_workflow,
        onboardingTotalQuestions: 0,
        onboardingAnsweredCount: 0,
      };
    },
  );
};
