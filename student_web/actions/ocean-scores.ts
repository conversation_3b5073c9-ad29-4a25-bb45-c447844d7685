"use server";

import { baseApiUrl } from "@/common/api";
import { env } from "@/env.mjs";

/**
 * Server action to trigger OCEAN scores calculation for a student
 */
export async function triggerOceanCalculation(studentId: string): Promise<{
  success: boolean;
  message: string;
  task_queued?: boolean;
}> {
  try {
    if (!studentId) {
      return {
        success: false,
        message: "Student ID is required",
      };
    }

    const response = await fetch(`${baseApiUrl}/api/ocean-scores/calculate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": env.ADDIE_API_KEY,
      },
      body: JSON.stringify({
        student_id: studentId,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to trigger OCEAN calculation");
    }

    const data = await response.json();
    return {
      success: data.success,
      message: data.message,
      task_queued: data.task_queued,
    };
  } catch (error) {
    console.error("Error triggering OCEAN calculation:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}
