"use server";

import { env } from "@/env.mjs";
import { prisma } from "@/common/db";

export const updateProfile = async (userId: string, newProfileData: any) => {
  if (!userId) {
    console.error("Missing userId");
    return { success: false, message: "Missing userId" };
  }

  try {
    // Delete existing preferred availability
    await prisma.preferredAvailability.deleteMany({
      where: {
        user_id: userId,
      },
    });

    // Map new availability with explicit user_id
    const newAvailability = newProfileData.preferred_availability.map(
      (time) => {
        return {
          day: time.day,
          block: time.block,
        };
      },
    );

    // Debug log to help troubleshoot
    console.log("Updating profile with data:", {
      first_name: newProfileData.first_name,
      last_name: newProfileData.last_name,
      phone_number: newProfileData.phone_number,
      timezone: newProfileData.timezone,
      availability_count: newAvailability.length,
    });

    // Update user with new data
    const updatedUser = await prisma.user.update({
      where: {
        id: userId,
      },
      data: {
        first_name: newProfileData.first_name.trim(),
        last_name: newProfileData.last_name.trim(),
        phone_number: newProfileData.phone_number,
        preferred_availability: {
          createMany: {
            data: newAvailability,
          },
        },
        gender: newProfileData.gender,
        pronouns: newProfileData.pronouns,
        timezone: newProfileData.timezone,
        isProfileComplete: true,
      },
      include: {
        preferred_availability: true, // Include to verify it was saved
      },
    });

    console.log("Profile updated successfully:", {
      timezone: updatedUser.timezone,
      availability_count: updatedUser.preferred_availability.length,
    });

    return { success: true, message: "Profile updated successfully" };
  } catch (e) {
    console.error("Error updating profile:", e);
    return {
      success: false,
      message: e instanceof Error ? e.message : "Unknown error occurred",
    };
  }
};

// Update phone_number after verification
export const updatePhoneNumber = async (
  userId: string,
  phoneNumber: string,
) => {
  if (!userId) {
    console.error("Missing userId");
    return;
  }
  try {
    await prisma.user.update({
      where: {
        id: userId,
      },
      data: {
        phone_number: phoneNumber,
      },
    });
  } catch (e) {
    console.error("Error updating profile:", e);
  }
};

/**
 * Send a verification code to the provided phone number
 */
export async function sendPhoneVerificationCode(phoneNumber: string): Promise<{
  success: boolean;
  message: string;
}> {
  "use server";

  const protocol = env.ADDIE_API_HOST.includes('localhost') || env.ADDIE_API_HOST.includes('0.0.0.0') ? 'http' : 'https';
  const apiUrl = `${protocol}://${env.ADDIE_API_HOST}`;

  console.log(`${apiUrl}/verify-phone-send`);

  try {
    const response = await fetch(`${apiUrl}/verify-phone-send`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": env.ADDIE_API_KEY,
      },
      body: JSON.stringify({ phone_number: phoneNumber }),
    });

    if (!response.ok) {
      throw new Error("Failed to send verification code");
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("Error sending verification code:", error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Verify the code sent to the phone number
 */
export async function verifyPhoneCode(
  phoneNumber: string,
  code: string,
): Promise<{
  success: boolean;
  message: string;
}> {
  "use server";

  try {
    // Get API URL from environment variable
    const protocol = env.ADDIE_API_HOST.includes('localhost') || env.ADDIE_API_HOST.includes('0.0.0.0') ? 'http' : 'https';
    const apiUrl = `${protocol}://${env.ADDIE_API_HOST}`;

    const response = await fetch(`${apiUrl}/verify-phone-check`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": env.ADDIE_API_KEY,
      },
      body: JSON.stringify({
        phone_number: phoneNumber,
        code: code,
      }),
    });

    if (!response.ok) {
      throw new Error("Failed to verify code");
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("Error verifying code:", error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

//Reset onboarding process based on userId
export const resetOnboarding = async (userId: string) => {
  if (!userId) {
    console.error("Missing userId");
    return { success: false, message: "Missing userId" };
  }
  try {
    await prisma.user.update({
      where: {
        id: userId,
      },
      data: {
        isProfileComplete: false,
      },
    });

    return { success: true, message: "Onboarding reset successfully" };
  } catch (e) {
    console.error("Error resetting onboarding:", e);
    return {
      success: false,
      message: e instanceof Error ? e.message : "Unknown error occurred",
    };
  }
};
