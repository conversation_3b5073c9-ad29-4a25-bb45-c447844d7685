"use server";

import { WorkflowStatus } from "@prisma/client";

import { prisma } from "@/common/db";
import log from "@/common/logger";

export interface PublishedWorkflowCheckResult {
  newWorkflowsAssigned: number;
  totalPublishedWorkflows: number;
  success: boolean;
  error?: string;
}

/**
 * Checks for published workflows that the student doesn't have assigned yet
 * and creates StudentWorkflow records for any missing ones.
 *
 * DISABLED: This feature is disabled to prevent auto-assignment of conversations.
 * Only directly assigned conversations should be visible to students.
 */
export async function checkAndAssignPublishedWorkflows(
  studentId: string,
): Promise<PublishedWorkflowCheckResult> {
  try {
    // Disable this feature for now - no auto-assignment
    log.info(`Auto-assignment disabled for student ${studentId}`);

    return {
      newWorkflowsAssigned: 0,
      totalPublishedWorkflows: 0,
      success: true,
    };

    /* DISABLED AUTO-ASSIGNMENT LOGIC
    // Get the student to check their school affiliation
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      select: { school_id: true },
    });

    if (!student) {
      return {
        newWorkflowsAssigned: 0,
        totalPublishedWorkflows: 0,
        success: false,
        error: "Student not found",
      };
    }

    // Find published workflows that should be available to this student
    const publishedWorkflows = await prisma.workflow.findMany({
      where: {
        status: WorkflowStatus.PUBLISHED,
        OR: [
          // Global workflows (owned by ADMIN users)
          {
            owner: {
              role: "ADMIN",
            },
          },
          // School-specific workflows (owned by counselors from the same school)
          {
            AND: [
              {
                owner: {
                  role: "COUNSELOR",
                },
              },
              {
                owner: {
                  Counselor: {
                    school_id: student.school_id,
                  },
                },
              },
            ],
          },
        ],
      },
      include: {
        steps: true,
      },
      orderBy: {
        updated_at: "desc",
      },
    });
    */

    /* DISABLED AUTO-ASSIGNMENT LOGIC CONTINUED
    // Get existing StudentWorkflows for this student
    const existingStudentWorkflows = await prisma.studentWorkflow.findMany({
      where: {
        student_id: studentId,
      },
      select: {
        workflow_id: true,
      },
    });

    const existingWorkflowIds = new Set(
      existingStudentWorkflows.map((sw) => sw.workflow_id),
    );

    // Filter out workflows the student already has
    const newWorkflows = publishedWorkflows.filter(
      (workflow) => !existingWorkflowIds.has(workflow.id),
    );

    let newWorkflowsAssigned = 0;

    // Create StudentWorkflow records for new workflows
    for (const workflow of newWorkflows) {
      try {
        // Create StudentWorkflow record
        const studentWorkflow = await prisma.studentWorkflow.create({
          data: {
            student_id: studentId,
            workflow_id: workflow.id,
            status: "NOT_STARTED",
          },
        });

        // Create StudentWorkflowStep records for each step
        for (const step of workflow.steps) {
          await prisma.studentWorkFlowStep.create({
            data: {
              student_workflow_id: studentWorkflow.id,
              step_id: step.id,
              student_id: studentId,
              completed: false,
              data: {},
            },
          });
        }

        newWorkflowsAssigned++;
        log.info(
          `Assigned workflow ${workflow.id} (${workflow.name}) to student ${studentId}`,
        );
      } catch (error) {
        log.error(
          `Failed to assign workflow ${workflow.id} to student ${studentId}:`,
          error,
        );
        // Continue with other workflows even if one fails
      }
    }

    return {
      newWorkflowsAssigned,
      totalPublishedWorkflows: publishedWorkflows.length,
      success: true,
    };
    */
  } catch (error) {
    log.error("Error checking published workflows:", error);
    return {
      newWorkflowsAssigned: 0,
      totalPublishedWorkflows: 0,
      success: false,
      error: "Failed to check published workflows",
    };
  }
}
