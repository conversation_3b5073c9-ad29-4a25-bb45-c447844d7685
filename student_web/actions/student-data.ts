"use server";

import { prisma } from "@/common/db";
import { getCurrentUser } from "@/common/session";

export const getStudentData = async () => {
  const user = await getCurrentUser();

  if (!user) {
    return { success: false, error: "Unauthorized" };
  }
  console.log("user ==>", user);

  const studentId = user.students[0].id;
  const student = await prisma.student.findUnique({
    where: { id: studentId },
    include: {
      users: true,
      workflow_steps: true,
      academic_achievements: true,
      counselors: {
        include: {
          user: true,
        },
      },
    },
  });

  if (!student) {
    return { success: false, error: "Student not found" };
  }

  return {
    success: true,
    data: student,
  };
};
