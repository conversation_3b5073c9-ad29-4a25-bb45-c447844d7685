"use server";

//Function create custom conversation workflow
import { WorkflowStatus } from "@prisma/client";

import { prisma } from "@/common/db";
import log from "@/common/logger";
import { convertQuestion } from "@/common/questionConverters";
import { formatDate, hash, slugify } from "@/common/utils";

export const fetchOnboardingQuestions = async () => {
  const onboardingWorkflow = await prisma.workflow.findFirst({
    where: { name: "Onboarding Questionnaire" },
    include: { steps: true },
  });

  if (!onboardingWorkflow || !onboardingWorkflow.steps.length) {
    throw new Error("Onboarding Workflow or steps not found.");
  }

  const parentStepId = onboardingWorkflow.steps[0].id;

  const subWorkflows = await prisma.workflow.findMany({
    where: { parent_step_id: parentStepId },
    include: { steps: true },
    orderBy: { created_at: "asc" },
  });

  // Combine all steps into one array
  const allQuestions = subWorkflows
    .flatMap((workflow) => workflow.steps)
    .sort((a, b) => a.index - b.index)
    .map((step: any) => ({
      id: step.id,
      name: step.name,
      question: step.data?.question || "",
    }))
    .filter((question) => question.question);

  return allQuestions;
};

export const getOrCreateOnboardingWorkflow = async () => {
  // Step 1: Check if the Onboarding Conversation already exists
  let onboardingWorkflow = await prisma.workflow.findFirst({
    where: { name: "Onboarding Conversation" },
    include: { steps: true },
  });

  // Step 2: If it exists, return it
  if (onboardingWorkflow) {
    // console.log("exisitng onboarding workflow ==>", onboardingWorkflow);
    return onboardingWorkflow;
  }

  // Step 3: Fetch all 51 questions
  const questions = await fetchOnboardingQuestions();

  // Step 4: Create the Onboarding Conversation workflow
  onboardingWorkflow = await prisma.workflow.create({
    data: {
      name: "Onboarding Conversation",
      // status: "DRAFT", // Default status
      steps: {
        create: questions.map((q, index) => ({
          goal: "Ask question",
          name: `${q.name}-${index}`,
          index: index,
          data: { question: q.question },
        })),
      },
    },
    include: { steps: true },
  });

  // console.log("onboarding conversation ===>", onboardingWorkflow);
  return onboardingWorkflow;
};

// export interface CustomConversation {
//   id: string;
//   name: string;
//   type?: string;
//   status: string;
//   updatedAt: string;
//   questions: {
//     question: string;
//     table: string;
//     questionId: string;
//   }[];
// }

export const getCustomConversations = async () => {
  const customConversations: any = await prisma.workflow.findMany({
    where: {
      name: {
        startsWith: "Conversation:",
      },
    },
    include: { steps: true },
    orderBy: { updated_at: "desc" }, // Sort by updated time
  });

  return customConversations.map((conversation) => ({
    id: conversation.id,
    name: conversation.name.replace("Conversation: ", ""), // Remove the prefix for UI
    type: "custom", // Identify as a custom conversation
    status: conversation.status || WorkflowStatus.DRAFT,
    updatedAt: conversation.updated_at || new Date(),
    questions: conversation.steps.map((step: any) => step.data?.question || ""),
  })) as CustomConversation[];
};

export const getAllConversations = async () => {
  // Fetch the onboarding workflow
  const onboardingWorkflow = await getOrCreateOnboardingWorkflow();

  // Format the onboarding workflow
  const onboardingConversation = {
    id: onboardingWorkflow.id,
    name: onboardingWorkflow.name,
    type: "onboarding",
    status: onboardingWorkflow.status || WorkflowStatus.DRAFT,
    updatedAt: onboardingWorkflow.updated_at || new Date().toISOString(),
    questions: onboardingWorkflow.steps.map(
      (step: any) => step.data?.question || "",
    ),
  };

  // Fetch custom conversations
  const customConversations = await getCustomConversations();

  // Combine both into a single array
  return [
    onboardingConversation,
    ...customConversations,
  ] as CustomConversation[];
};

export interface CustomConversation {
  id: string;
  name: string;
  type?: string;
  status: string;
  updatedAt: string;
  questions: QuestionItem[]; // now using the full structure
}

// getConversation by thier Ids, used for dynamic routing
export const getConversationById = async (
  id: string,
): Promise<CustomConversation> => {
  const conversation = await prisma.workflow.findUniqueOrThrow({
    where: { id },
    include: { steps: true },
  });

  // Convert each step's data (raw question) into a QuestionItem.
  const convertedQuestions = conversation.steps.map((step: any) =>
    convertQuestion({
      question: step.data?.question || "",
      table: step.data?.table || "",
      questionId: step.data?.questionId || "",
    }),
  );

  if (conversation.name === "Onboarding Conversation") {
    return {
      id: conversation.id,
      name: conversation.name,
      type: "onboarding",
      status: conversation.status || WorkflowStatus.DRAFT,
      updatedAt: conversation.updated_at
        ? conversation.updated_at.toISOString()
        : new Date().toISOString(),
      questions: convertedQuestions,
    } as CustomConversation;
  } else if (conversation.name.startsWith("Conversation:")) {
    return {
      id: conversation.id,
      name: conversation.name.replace("Conversation: ", ""),
      type: "custom",
      status: conversation.status || WorkflowStatus.DRAFT,
      updatedAt: conversation.updated_at
        ? conversation.updated_at.toISOString()
        : new Date().toISOString(),
      questions: convertedQuestions,
    } as CustomConversation;
  } else {
    return {
      id: conversation.id,
      name: conversation.name,
      type: "unknown",
      status: conversation.status || WorkflowStatus.DRAFT,
      updatedAt: conversation.updated_at
        ? conversation.updated_at.toISOString()
        : new Date().toISOString(),
      questions: convertedQuestions,
    } as CustomConversation;
  }
};

export const createConversation = async (name: string) => {
  try {
    const existingConversation = await prisma.workflow.findFirst({
      where: { name: `Conversation: ${name}` },
    });

    if (existingConversation) {
      console.log("existing convo");
      return {
        success: false,
        error: "A conversation with this name already exists!"
      };
    }

    const customConversation = await prisma.workflow.create({
      data: {
        name: `Conversation: ${name}`,
        steps: {
          create: [],
        },
      },
    });

    console.log("new convo is created!");

    return {
      success: true,
      data: customConversation
    };
  } catch (error) {
    console.error("Error creating conversation:", error);
    return {
      success: false,
      error: "Failed to create conversation"
    };
  }
};

//Publish conversation

export const publishConversation = async (workflowId: string) => {
  const alreadyPublishedConversation = await prisma.workflow.findFirst({
    where: {
      id: workflowId,
    },
  });
  if (!alreadyPublishedConversation) {
    throw new Error("Workflow not found");
  }

  if (alreadyPublishedConversation.status === "PUBLISHED") {
    return { error: "This conversation is already published" };
  }
  const selectedWorkflow = await prisma.workflow.update({
    where: {
      id: workflowId,
    },
    data: {
      status: "PUBLISHED",
    },
  });

  return { success: true, data: selectedWorkflow };
};

// Define the valid question types
export type QuestionType =
  | "multiple-choice"
  | "binary"
  | "short-answer"
  | "long-answer";

// Frontend QuestionItem interface
export interface QuestionItem {
  id: string; // temporary ID from front end
  text: string;
  type: "binary" | "multiple-choice" | "short-answer" | "long-answer";
  options?: string[]; // only for multiple-choice questions
  canSkip: boolean; // extra field (not used in saving schema)
  characterLimit?: { min?: number; max?: number }; // extra field (not used in schema)
}

/**
 * Creates a question record (based on the question type) and the corresponding
 * WorkflowStep for that question.
 *
 * @param workflowId - The ID of the workflow.
 * @param questionnaireId - The ID of the questionnaire to connect to.
 * @param question - A QuestionItem from the front end.
 * @param indexOffset - The index to use (existing steps count + current index).
 * @returns A promise that resolves to an object containing the table name and question record ID.
 */
async function createQuestionWorkflowStep(
  workflowId: string,
  questionnaireId: string,
  question: QuestionItem,
  indexOffset: number,
): Promise<{ table: string; questionId: string }> {
  let questionRef: { table: string; questionId: string } = {
    table: "",
    questionId: "",
  };

  if (question.type === "binary") {
    const binaryQuestion = await prisma.binaryQuestion.create({
      data: {
        question: question.text,
        slug: slugify(question.text),
        question_hash: hash(question.text),
        questionnaire: { connect: { id: questionnaireId } },
        index: indexOffset,
      },
    });
    questionRef = { table: "BinaryQuestion", questionId: binaryQuestion.id };
  } else if (question.type === "multiple-choice") {
    const mcQuestion = await prisma.multipleChoiceQuestion.create({
      data: {
        question: question.text,
        slug: slugify(question.text),
        question_hash: hash(question.text),
        questionnaire: { connect: { id: questionnaireId } },
        index: indexOffset,
        choices: {
          create: question.options?.map((option) => ({ option })) || [],
        },
      },
    });
    questionRef = {
      table: "MultipleChoiceQuestion",
      questionId: mcQuestion.id,
    };
  } else if (
    question.type === "short-answer" ||
    question.type === "long-answer"
  ) {
    const textQuestion = await prisma.question.create({
      data: {
        question: question.text,
        slug: slugify(question.text),
        question_hash: hash(question.text),
        questionnaire: { connect: { id: questionnaireId } },
        index: indexOffset,
      },
    });
    questionRef = { table: "Question", questionId: textQuestion.id };
  } else {
    // Fallback: treat unknown type as a generic Question.
    const genericQuestion = await prisma.question.create({
      data: {
        question: question.text,
        slug: slugify(question.text),
        question_hash: hash(question.text),
        questionnaire: { connect: { id: questionnaireId } },
        index: indexOffset,
      },
    });
    questionRef = { table: "Question", questionId: genericQuestion.id };
  }

  // Create the WorkflowStep record linking the question to the workflow.
  await prisma.workflowStep.create({
    data: {
      name: `Question ${indexOffset + 1}`, // Using indexOffset for unique naming.
      goal: "Ask question",
      index: indexOffset,
      data: {
        question: question.text,
        table: questionRef.table,
        questionId: questionRef.questionId,
      },
      parent_workflow: { connect: { id: workflowId } },
    },
  });

  return questionRef;
}

/**
 * Save an array of questions into the workflow's steps.
 *
 * This function:
 * 1. Retrieves the workflow.
 * 2. Creates a new Questionnaire for this workflow if one does not already exist.
 * 3. Iterates over each QuestionItem and, based on its type, calls createQuestionWorkflowStep().
 *
 * @param workflowId - The ID of the workflow.
 * @param questions - An array of QuestionItem objects from the front end.
 * @returns A Promise<void>.
 */
export async function saveQuestionsToWorkflow(
  workflowId: string,
  questions: QuestionItem[],
): Promise<void> {
  // Retrieve the workflow (including its existing steps)
  const workflow = await prisma.workflow.findUnique({
    where: { id: workflowId },
    include: { steps: true },
  });
  if (!workflow) {
    throw new Error("Workflow not found");
  }

  // Determine the questionnaire name based on the workflow name.
  const questionnaireName = `${workflow.name} Questionnaire`;

  // Check if a Questionnaire with that name already exists.
  let questionnaire = await prisma.questionnaire.findFirstOrThrow({
    where: { name: questionnaireName },
  });

  // Create a new Questionnaire if needed.
  if (!questionnaire) {
    questionnaire = await prisma.questionnaire.create({
      data: {
        name: questionnaireName,
        description: `Auto-created questionnaire for workflow ${workflow.name}`,
      },
    });
  }

  // Use the number of existing steps as an offset for new questions.
  const existingStepsCount = workflow.steps?.length || 0;

  // Process each question item.
  for (let i = 0; i < questions.length; i++) {
    const q = questions[i];
    const indexOffset = existingStepsCount + i;
    // Create the question record and corresponding workflow step.
    await createQuestionWorkflowStep(
      workflow.id,
      questionnaire.id,
      q,
      indexOffset,
    );
  }

  return;
}
