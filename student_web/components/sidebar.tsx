"use client";

import * as React from "react";
import { useEffect, useState } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { setTzOffset } from "@/actions/chat";
import { resetOnboarding } from "@/actions/profile";
import { UserRole } from "@prisma/client";
import {
  BookOpen,
  ChevronDown,
  HelpCircle,
  LogOut,
  MessageSquare,
  Settings,
  TimerReset,
} from "lucide-react";
import { useCookies } from "next-client-cookies";
import { toast } from "sonner";

import { env } from "@/env.mjs";
import { siteConfig } from "@/config/site";
import urls from "@/common/config/urls.json";
import { cn } from "@/common/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarProvider,
  SidebarRail,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import HelpModal from "@/components/HelpModal/HelpModal";
import ImpersonateDropDown from "@/components/ImpersonateStudents/ImpersonateDropDown";
import DestructiveModal from "@/components/modals/destructive-modal";

export const isDevelopment =
  env.NEXT_PUBLIC_APP_URL.includes("localhost") ||
  window.location.hostname.includes("dev-");

interface SideBarProps extends React.HTMLAttributes<HTMLDivElement> {
  user: any;
  isDev: boolean;
  authToken: string;
}

export default function Component({
  user,
  children,
  isDev,
  authToken,
}: SideBarProps) {
  const cookies = useCookies();
  useEffect(() => {
    const tzOffset = cookies.get("tz-offset");
    if (!tzOffset) {
      setTzOffset();
    }
  });

  const names = [user.first_name, user.last_name];
  const pathname = usePathname();
  const router = useRouter();
  const initials = names
    .filter((name) => name !== undefined && name !== "")
    .filter((name) => name !== undefined && name !== "")
    .map((name) => name.charAt(0).toUpperCase())
    .reverse()
    .join("");

  const [isSettingsOpen, setIsSettingsOpen] = React.useState(false);
  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);

  const { isMobile } = useSidebar();
  const handleToggleModal = () => {
    setIsHelpModalOpen((prev) => !prev);
  };

  const handleResetOnboarding = async () => {
    try {
      console.log("resetting onboarding");
      const result = await resetOnboarding(user.id);

      if (!result || !result.success) {
        toast.error(result?.message || "Failed to reset onboarding");
        return;
      }

      router.push("/onboarding");
      toast.success("Onboarding reset successfully!");
    } catch (e) {
      console.error("Error resetting onboarding:", e);
      toast.error("Failed to reset onboarding.");
    }
  };

  // Define button size for sidebar menu items
  const menuButtonSize = isMobile ? "lg" : "default";

  return (
    <>
      <Sidebar collapsible="icon" variant="sidebar">
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton size="lg" asChild>
                <Link href="/" className="flex items-center gap-2">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src="/placeholder.svg" alt="User Avatar Img" />
                    <AvatarFallback>{initials}</AvatarFallback>
                  </Avatar>
                  <div className="flex flex-col gap-0.5 text-sm">
                    <span className="font-semibold">{siteConfig.name}</span>
                  </div>
                  {/* {isDev && <Badge variant="destructive">Development</Badge>} */}
                </Link>
              </SidebarMenuButton>
              <span className="mb-4 text-sm font-medium">
                <span className="flex flex-col space-y-1">
                  {user?.role === UserRole.ADMIN && (
                    <span className="truncate rounded bg-primary px-2 py-1 text-xs font-bold text-white">
                      ADMIN
                    </span>
                  )}
                  {isDevelopment && (
                    <span className="truncate rounded bg-red-100 px-2 py-1 text-xs font-bold text-white">
                      DEVELOPMENT
                    </span>
                  )}
                </span>
              </span>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent>
          <SidebarMenu>
            {/*Side bar tabs*/}
            <SidebarMenuItem>
              <SidebarMenuButton
                isActive={pathname == urls.routes.conversations}
                asChild
                className={cn(isMobile && "py-4")}
                tooltip="Conversations"
                size={menuButtonSize}
              >
                <Link
                  href={urls.routes.conversations}
                  className="flex items-center gap-2"
                >
                  <MessageSquare
                    className={cn("h-4 w-4", isMobile && "h-5 w-5")}
                  />
                  <span className={cn(isMobile && "text-base")}>
                    Conversations
                  </span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>

            {/*Collapsible colleges*/}
            <SidebarMenuItem>
              <Collapsible>
                <CollapsibleTrigger asChild>
                  <div className="group">
                    <SidebarMenuButton
                      isActive={pathname.startsWith(urls.routes.colleges.index)}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        <BookOpen
                          className={cn("h-4 w-4", isMobile && "h-5 w-5")}
                        />
                        <span>Colleges</span>
                      </div>
                      <ChevronDown
                        className={cn(
                          "h-4 w-4 transition-transform duration-200 group-data-[state=open]:rotate-180",
                          isMobile && "h-5 w-5",
                        )}
                      />
                    </SidebarMenuButton>
                  </div>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <SidebarMenuSub>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton
                        isActive={pathname === urls.routes.colleges.majors}
                        asChild
                      >
                        <Link href={urls.routes.colleges.majors}>Majors</Link>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  </SidebarMenuSub>
                </CollapsibleContent>
              </Collapsible>
            </SidebarMenuItem>

            {/*Collaspible settings*/}
            <Collapsible defaultOpen={false} className="group/collapsible">
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton
                    isActive={pathname.startsWith(urls.routes.settings.index)}
                    className={cn(isMobile && "py-4")}
                    size={menuButtonSize}
                  >
                    <div className="flex items-center gap-2">
                      <Settings
                        className={cn("h-4 w-4", isMobile && "h-5 w-5")}
                      />
                      <span className={cn(isMobile && "text-base")}>
                        Settings
                      </span>
                    </div>
                    <ChevronDown
                      className={cn(
                        "ml-auto h-4 w-4 transition-transform group-data-[state=open]/collapsible:rotate-180",
                        isMobile && "h-5 w-5",
                      )}
                    />
                  </SidebarMenuButton>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <SidebarMenuSub>
                    <Link href={urls.routes.settings.profile}>
                      <SidebarMenuSubItem className="cursor-pointer">
                        <SidebarMenuSubButton
                          isActive={pathname === urls.routes.settings.profile}
                          className={cn(isMobile && "py-3 text-base")}
                        >
                          Profile
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    </Link>
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>

            {/*Hide this from the side bar for now, but we can still enter the url to access it*/}
            {/*<SidebarMenuItem>*/}
            {/*  <SidebarMenuButton isActive={pathname == "/"} asChild>*/}
            {/*    <a href="/chat" className="flex items-center gap-2">*/}
            {/*      <Bot className="h-4 w-4" />*/}
            {/*      <span>Addie Chat</span>*/}
            {/*    </a>*/}
            {/*  </SidebarMenuButton>*/}
            {/*</SidebarMenuItem>*/}
          </SidebarMenu>
        </SidebarContent>
        <SidebarFooter className="mt-auto">
          <SidebarMenu>
            {/*Impersonate students here*/}
            {isDevelopment &&
              (user?.role === UserRole.ADMIN || user?.impersonated) && (
                <ImpersonateDropDown user={user} />
              )}

            <SidebarMenuItem onClick={handleToggleModal}>
              <SidebarMenuButton asChild>
                <button
                  className={cn(
                    "flex w-full items-center justify-center gap-4 rounded-md bg-red-600 p-4 text-lg font-bold text-white shadow-lg transition-all duration-200 hover:bg-red-700",
                    isMobile && "p-5 text-xl",
                  )}
                >
                  <HelpCircle
                    className={cn("h-6 w-6", isMobile && "h-7 w-7")}
                  />
                  Help
                </button>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton asChild className={cn(isMobile && "py-4")}>
                <Link href={urls.api.signout}>
                  <button className="flex w-full items-center gap-2 text-destructive">
                    <LogOut className={cn("h-4 w-4", isMobile && "h-5 w-5")} />
                    <span className={cn(isMobile && "text-base")}>Logout</span>
                  </button>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
          <SidebarRail className="items-start pr-5 pt-8 hover:cursor-default hover:border-0 hover:bg-transparent">
            <div className="z-10 h-8 w-8 rounded-full border bg-background">
              <SidebarTrigger className="hover:bg-transparent" />
            </div>
          </SidebarRail>
        </SidebarFooter>
      </Sidebar>
      {children}
      {isHelpModalOpen && (
        <HelpModal
          onToggle={handleToggleModal}
          authToken={authToken}
          userId={user.id}
          studentId={user.students[0].id}
        />
      )}
    </>
  );
}
