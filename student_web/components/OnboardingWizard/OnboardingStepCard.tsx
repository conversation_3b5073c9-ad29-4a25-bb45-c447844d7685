// components/Onboarding/OnboardingStepCard.tsx
"use client";

import { Button } from "@/components/ui/button";

interface OnboardingStepCardProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  onNext?: () => void;
  onBack?: () => void;
  nextText?: string;
  disableNext?: boolean;
}

export const OnboardingStepCard = ({
  title,
  description,
  children,
  onNext,
  onBack,
  nextText = "Continue",
  disableNext = false,
}: OnboardingStepCardProps) => {
  return (
    <div className="space-y-4 rounded-sm border bg-white p-6">
      <div className="space-y-1">
        <h4 className="text-base font-semibold">{title}</h4>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>

      <div>{children}</div>

      <div className="mt-6 flex justify-end gap-4">
        {onBack && (
          <Button variant="outline" onClick={onBack}>
            Back
          </Button>
        )}
        {onNext && (
          <Button onClick={onNext} disabled={disableNext}>
            {nextText}
          </Button>
        )}
      </div>
    </div>
  );
};
