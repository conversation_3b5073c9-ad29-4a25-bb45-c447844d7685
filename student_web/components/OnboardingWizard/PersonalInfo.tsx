"use client";

import { useState } from "react";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { OnboardingStepCard } from "@/components/OnboardingWizard/OnboardingStepCard";

interface PersonalInfoProps {
  onNext?: () => void;
  onBack?: () => void;
  formData?: any;
  setFormData: (data: any) => void;
}

export const PersonalInfo = ({
  onNext,
  onBack,
  formData = {},
  setFormData = () => {},
}: PersonalInfoProps) => {
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [gender, setGender] = useState(formData.gender || "");
  const [pronouns, setPronouns] = useState(formData.pronouns || "");
  const handleFirstNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFirstName(value);
    setFormData({ ...formData, first_name: value });
  };

  const handleLastNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLastName(value);
    setFormData({ ...formData, last_name: value });
  };

  const handleGenderChange = (value: string) => {
    setGender(value);
    setFormData({ ...formData, gender: value });
  };

  const handlePronounsChange = (value: string) => {
    setPronouns(value);
    setFormData({ ...formData, pronouns: value });
  };

  // Check if form is valid to enable the continue button
  const isFormValid = firstName.trim() !== "" && lastName.trim() !== "";

  return (
    <OnboardingStepCard
      title="Tell us about yourself"
      description="This helps us personalize your experience and address you correctly."
      disableNext={!isFormValid}
      nextText="Continue"
      onNext={onNext}
      onBack={onBack}
    >
      <div className="space-y-6">
        <div className="space-y-3">
          <h3 className="text-base font-medium">Name</h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <div>
                <Label htmlFor="first-name">First Name</Label>
                <span className="ml-1 text-red-500">*</span>
              </div>
              <Input
                id="first-name"
                value={firstName}
                onChange={handleFirstNameChange}
                placeholder="Enter your first name"
                className="mt-1"
                required
              />
              {(firstName.trim() === "" || firstName.trim().length === 0) && (
                <span className="text-xs text-red-500">
                  First Name cannot be empty
                </span>
              )}
            </div>
            <div>
              <div>
                <Label htmlFor="last-name">Last Name</Label>
                <span className="ml-1 text-red-500">*</span>
              </div>
              <Input
                id="last-name"
                value={lastName}
                onChange={handleLastNameChange}
                placeholder="Enter your last name"
                className="mt-1"
                required
              />
              {(lastName.trim() === "" || lastName.trim().length === 0) && (
                <span className="text-xs text-red-500">
                  Last Name cannot be empty
                </span>
              )}
            </div>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center">
            <h3 className="text-base font-medium">Gender</h3>
          </div>
          <RadioGroup
            value={gender}
            onValueChange={handleGenderChange}
            className="grid grid-cols-2 gap-y-3"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="male" id="male" />
              <Label htmlFor="male" className="font-normal">
                Male
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="female" id="female" />
              <Label htmlFor="female" className="font-normal">
                Female
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="non-binary" id="non-binary" />
              <Label htmlFor="non-binary" className="font-normal">
                Non-binary
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="prefer-not-to-say"
                id="prefer-not-to-say"
              />
              <Label htmlFor="prefer-not-to-say" className="font-normal">
                Prefer not to say
              </Label>
            </div>
          </RadioGroup>
        </div>

        <div className="space-y-3">
          <h3 className="text-base font-medium">Pronouns</h3>
          <Select value={pronouns} onValueChange={handlePronounsChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select your pronouns" />
            </SelectTrigger>
            <SelectContent className="cursor-pointer">
              <SelectItem className="cursor-pointer" value="she/her">
                She/Her
              </SelectItem>
              <SelectItem className="cursor-pointer" value="he/him">
                He/Him
              </SelectItem>
              <SelectItem className="cursor-pointer" value="they/them">
                They/Them
              </SelectItem>
              <SelectItem className="cursor-pointer" value="other">
                Other
              </SelectItem>
              <SelectItem className="cursor-pointer" value="prefer-not-to-say">
                Prefer not to say
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </OnboardingStepCard>
  );
};
