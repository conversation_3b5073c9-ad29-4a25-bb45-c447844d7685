import { BookText, Check } from "lucide-react";

interface StepIndicatorProps {
  currentStep: number;
  completedGroups: boolean[];
  labels: string[];
}

export function StepIndicator({
  currentStep,
  completedGroups,
  labels,
}: StepIndicatorProps) {
  return (
    <div className="my-6 flex w-full max-w-md flex-col items-center justify-center">
      <div className="mb-4 flex size-12 items-center justify-center rounded-lg bg-yellow-500 p-2">
        <BookText className="size-6 text-yellow-900" />
      </div>
      <h3 className="text-lg font-medium">Profile Setup</h3>
      <p className="mb-4 text-sm">Let&#39;s get to know you better</p>
      <div className="flex w-full items-center justify-between rounded-full border bg-white px-4 py-2">
        {labels.map((label, index) => {
          const stepNumber = index + 1;
          const isActive = stepNumber === currentStep;
          const isCompleted = completedGroups[index];

          return (
            <div key={label} className="flex items-center">
              {/* Step circle - using fixed dimensions and flex-shrink-0 to prevent distortion */}
              <div
                className={`flex h-6 w-6 shrink-0 items-center justify-center rounded-full text-xs font-medium ${isCompleted ? "bg-green-500 text-white" : ""} ${isActive && !isCompleted ? "bg-black text-white" : ""} ${!isActive && !isCompleted ? "bg-gray-200 text-gray-600" : ""} `}
                style={{ minWidth: "1.5rem", minHeight: "1.5rem" }} // Ensure minimum dimensions
              >
                {isCompleted ? <Check className="h-3 w-3" /> : stepNumber}
              </div>

              {/* Label */}
              <span
                className={`ml-2 shrink-0 text-xs ${isActive ? "font-medium text-gray-900" : "text-gray-500"} `}
              >
                {label}
              </span>

              {/* Connector line - only if not the last item */}
              {index < labels.length - 1 && (
                <div className="relative mx-2 w-4 shrink-0 grow">
                  <div
                    className={`absolute top-1/2 h-px w-full -translate-y-1/2 ${
                      isCompleted ? "bg-green-500" : "bg-gray-200"
                    }`}
                  ></div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
