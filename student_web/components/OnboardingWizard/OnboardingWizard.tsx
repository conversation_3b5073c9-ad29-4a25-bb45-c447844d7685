"use client";

import React, { useState } from "react";
import { updateProfile } from "@/actions/profile";
import { toast } from "sonner";

import { Availability } from "@/components/OnboardingWizard/Availablity";
import { CompleteScreen } from "@/components/OnboardingWizard/CompleteScreen";
import { PersonalInfo } from "@/components/OnboardingWizard/PersonalInfo";
import { PhoneNumber } from "@/components/OnboardingWizard/PhoneNumber";
import { StepIndicator } from "@/components/OnboardingWizard/StepIndicator";

export type OnboardingStep = "phone-number" | "personal-info" | "availability";

const STEP_LABELS = ["Phone Number", "Personal Info", "Availability"];

export const OnboardingWizard = ({ initialData }) => {
  const [currentStep, setCurrentStep] = useState(0); // 0: phone, 1: info, 2: avail
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [formData, setFormData] = useState(initialData ?? {});

  // console.log("formData", formData);

  // Reset wizard (for edit phone)
  const resetWizard = () => {
    setCurrentStep(0);
    setCompletedSteps(new Set());
    setFormData(initialData ?? {});
  };

  // Step navigation
  const goToNextStep = () => {
    setCompletedSteps((prev) => new Set(prev).add(currentStep));
    setCurrentStep((prev) => prev + 1);
  };
  const goToPrevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  const isComplete = currentStep >= STEP_LABELS.length;

  const handleComplete = async () => {
    try {
      await updateProfile(initialData.id, formData);
      goToNextStep();
      toast.success("Onboarding completed!");
    } catch (e) {
      return toast.error("Failed to update profile.");
    }
  };

  // Step rendering
  let stepContent: React.ReactNode = null;
  if (currentStep === 0) {
    stepContent = (
      <PhoneNumber
        value={formData.phone_number ?? ""}
        onChange={(val: string) =>
          setFormData((f) => ({ ...f, phone_number: val }))
        }
        onNext={goToNextStep}
        onEditPhone={resetWizard}
        userId={initialData.id}
      />
    );
  } else if (currentStep === 1) {
    stepContent = (
      <PersonalInfo
        onNext={goToNextStep}
        onBack={resetWizard}
        formData={formData}
        setFormData={setFormData}
        // Pass formData, setFormData as needed
      />
    );
  } else if (currentStep === 2) {
    stepContent = (
      <Availability
        availability={formData.preferred_availability ?? []}
        timezone={formData.timezone ?? ""}
        onTimezoneChange={(val: string) =>
          setFormData((prev) => ({ ...prev, timezone: val }))
        }
        onNext={handleComplete}
        onBack={goToPrevStep}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, preferred_availability: value }))
        }
      />
    );
  }

  return (
    <div className="flex h-screen w-screen flex-col items-center justify-center bg-[#FAFAFA]">
      {!isComplete && (
        <StepIndicator
          labels={STEP_LABELS}
          currentStep={currentStep + 1}
          completedGroups={STEP_LABELS.map((_, i) => completedSteps.has(i))}
        />
      )}
      {isComplete ? <CompleteScreen /> : stepContent}
    </div>
  );
};
