import type React from "react";
import { useEffect, useRef, useState } from "react";
import {
  sendPhoneVerificationCode,
  updatePhoneNumber,
  verifyPhoneCode,
} from "@/actions/profile";
import { isValidPhoneNumber } from "react-phone-number-input";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import { CardDescription, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { OnboardingStepCard } from "@/components/OnboardingWizard/OnboardingStepCard";
import { PhoneInput } from "@/components/PhoneInput/PhoneInput";

interface PhoneNumberProps {
  value: string;
  onChange: (val: string) => void;
  onNext: () => void;
  onEditPhone: () => void;
  userId: string;
}

export const PhoneNumber = ({
  value,
  onChange,
  onNext,
  onEditPhone,
  userId,
}: PhoneNumberProps) => {
  const [mode, setMode] = useState<"input" | "verification">("input");
  const [phoneError, setPhoneError] = useState("");
  const [codeError, setCodeError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Use an array of 6 digits for the verification code
  const [codeDigits, setCodeDigits] = useState<string[]>(Array(6).fill(""));
  const inputRefs = useRef<(HTMLInputElement | null)[]>(Array(6).fill(null));

  // Add resend timer state
  const [resendTimer, setResendTimer] = useState(0);
  const resendIntervalRef = useRef<number | null>(null);

  // Handle input change for each digit
  const handleCodeChange = (index: number, value: string) => {
    // Only allow digits
    if (!/^\d*$/.test(value)) return;

    // Create a new array with the updated digit
    const newCodeDigits = [...codeDigits];
    newCodeDigits[index] = value.slice(0, 1);
    setCodeDigits(newCodeDigits);

    // Auto-focus next input if current input is filled
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Handle backspace key
  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    if (e.key === "Backspace") {
      if (!codeDigits[index] && index > 0) {
        // If current input is empty and backspace is pressed, focus previous input
        inputRefs.current[index - 1]?.focus();
      }
    }
  };

  // Handle paste event for the entire code
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData
      .getData("text")
      .replace(/\D/g, "")
      .slice(0, 6);
    if (pastedData) {
      const newCodeDigits = [...codeDigits];
      for (let i = 0; i < pastedData.length; i++) {
        if (i < 6) newCodeDigits[i] = pastedData[i];
      }
      setCodeDigits(newCodeDigits);

      // Focus the next empty input or the last input if all are filled
      const nextEmptyIndex = newCodeDigits.findIndex((digit) => !digit);
      if (nextEmptyIndex !== -1 && nextEmptyIndex < 6) {
        inputRefs.current[nextEmptyIndex]?.focus();
      } else {
        inputRefs.current[5]?.focus();
      }
    }
  };

  // Start resend timer
  const startResendTimer = () => {
    setResendTimer(120);
    if (resendIntervalRef.current)
      clearInterval(resendIntervalRef.current ?? undefined);
    resendIntervalRef.current = window.setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          if (resendIntervalRef.current)
            clearInterval(resendIntervalRef.current ?? undefined);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Send verification code using server action
  const handleSendCode = async () => {
    if (!value || !isValidPhoneNumber(value)) {
      setPhoneError("Please enter a valid phone number");
      return;
    }

    setIsSubmitting(true);
    setPhoneError("");

    try {
      const result = await sendPhoneVerificationCode(value);

      if (result.success) {
        setMode("verification");
        toast.success("Verification code sent to your phone");
        startResendTimer(); // Start timer here
      } else {
        setPhoneError(result.message || "Failed to send verification code");
        toast.error(result.message || "Failed to send verification code");
      }
    } catch (error) {
      setPhoneError("An error occurred while sending the verification code");
      toast.error("Failed to send verification code");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Verify code using server action
  const handleVerify = async () => {
    const code = codeDigits.join("");
    if (code.length !== 6) {
      setCodeError("Please enter the 6-digit code");
      return;
    }

    setIsSubmitting(true);
    setCodeError("");

    try {
      const result = await verifyPhoneCode(value, code);

      if (result.success) {
        toast.success("Phone number verified successfully");
        if (userId) {
          try {
            await updatePhoneNumber(userId, value);
          } catch (e) {
            return toast.error("Failed to update profile.");
          }
        }
        onNext();
      } else {
        setCodeError(result.message || "Invalid verification code");
        toast.error(result.message || "Invalid verification code");
      }
    } catch (error) {
      setCodeError("An error occurred while verifying the code");
      toast.error("Failed to verify code");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Update handleResendCode to start timer
  const handleResendCode = async () => {
    setIsSubmitting(true);
    setCodeDigits(Array(6).fill(""));
    inputRefs.current[0]?.focus();
    try {
      const result = await sendPhoneVerificationCode(value);
      if (result.success) {
        toast.success("Verification code resent to your phone");
        startResendTimer(); // Start timer here
      } else {
        toast.error(result.message || "Failed to resend verification code");
      }
    } catch (error) {
      toast.error("Failed to resend verification code");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditPhone = () => {
    setMode("input");
    setCodeDigits(Array(6).fill(""));
    setCodeError("");
    onEditPhone();
  };

  // Clean up interval on unmount
  useEffect(() => {
    return () => {
      if (resendIntervalRef.current)
        clearInterval(resendIntervalRef.current ?? undefined);
    };
  }, []);

  if (mode === "input") {
    return (
      <OnboardingStepCard
        title="What's your phone number?"
        description="We'll use this to send you important updates about your college application process."
        onNext={handleSendCode}
        disableNext={!value || !isValidPhoneNumber(value) || isSubmitting}
        nextText={isSubmitting ? "Sending..." : "Send Verification Code"}
      >
        <div className="space-y-4">
          <div className="space-y-2">
            <p className="text-sm font-medium">Phone Number</p>
            <PhoneInput
              defaultCountry="US"
              value={value}
              international
              onChange={onChange}
              countries={["US", "CA", "BR", "IT"]}
            />
            {phoneError && (
              <div className="mt-1 text-xs text-red-500">{phoneError}</div>
            )}
          </div>
          <div className="rounded-md bg-zinc-100 p-4 text-sm text-gray-700">
            <p className="mb-2 font-medium">
              Your phone number helps us keep you in the loop with:
            </p>
            <ul className="list-disc pl-4">
              <li className="mb-1">Time-sensitive application updates</li>
              <li className="mb-1">Conversations with Addie</li>
              <li>Important document reminders</li>
            </ul>
          </div>
        </div>
      </OnboardingStepCard>
    );
  } else {
    return (
      <OnboardingStepCard
        title="What's your phone number?"
        description="We'll use this to send you important updates about your college application process."
        onNext={handleVerify}
        disableNext={codeDigits.join("").length !== 6 || isSubmitting}
        nextText={isSubmitting ? "Verifying..." : "Verify"}
      >
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg font-medium">
                Verification Code
              </CardTitle>
              <CardDescription>
                We sent a 6-digit code to {value}
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleEditPhone}
              className="h-8 px-3"
              disabled={isSubmitting}
            >
              Edit
            </Button>
          </div>

          <div>
            <div className="flex justify-center gap-1 sm:gap-2">
              {Array(6)
                .fill(0)
                .map((_, index) => (
                  <Input
                    key={index}
                    ref={(el) => {
                      inputRefs.current[index] = el;
                    }}
                    type="text"
                    inputMode="numeric"
                    maxLength={1}
                    value={codeDigits[index]}
                    onChange={(e) => handleCodeChange(index, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(index, e)}
                    onPaste={index === 0 ? handlePaste : undefined}
                    className="h-10 w-10 p-0 text-center text-xl sm:w-12 md:w-16"
                    disabled={isSubmitting}
                  />
                ))}
            </div>

            {codeError && (
              <p className="mt-2 text-xs text-destructive">{codeError}</p>
            )}
          </div>

          <div className="flex items-center justify-between">
            <Button
              type="button"
              variant="link"
              className="h-auto p-0"
              onClick={handleResendCode}
              disabled={isSubmitting || resendTimer > 0}
            >
              {resendTimer > 0 ? `Resend Code (${resendTimer})` : "Resend Code"}
            </Button>
          </div>
        </div>
      </OnboardingStepCard>
    );
  }
};
