"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

// Route configuration for breadcrumb generation
const routeConfig: Record<string, { label: string; href?: string }> = {
  "/": { label: "Home", href: "/" },
  "/settings": { label: "Settings", href: "/settings" },
  "/settings/profile": { label: "Profile" },
  "/colleges": { label: "Colleges", href: "/colleges" },
  "/colleges/majors": { label: "Majors" },
  "/conversations": { label: "Conversations", href: "/conversations" },
  "/chat": { label: "Chat", href: "/chat" },
};

interface BreadcrumbHeaderProps {
  // Optional props to override automatic detection
  parentPage?: string;
  currentPage?: string;
  customRoutes?: Array<{ label: string; href?: string }>;
}

export function BreadcrumbHeader({ 
  parentPage, 
  currentPage, 
  customRoutes 
}: BreadcrumbHeaderProps) {
  const pathname = usePathname();

  // If custom routes provided, use them
  if (customRoutes) {
    return (
      <div className="border-b px-6 py-4">
        <Breadcrumb>
          <BreadcrumbList>
            {customRoutes.map((route, index) => (
              <div key={index} className="flex items-center">
                {index > 0 && <BreadcrumbSeparator />}
                <BreadcrumbItem>
                  {route.href && index < customRoutes.length - 1 ? (
                    <BreadcrumbLink asChild>
                      <Link href={route.href}>{route.label}</Link>
                    </BreadcrumbLink>
                  ) : (
                    <BreadcrumbPage>{route.label}</BreadcrumbPage>
                  )}
                </BreadcrumbItem>
              </div>
            ))}
          </BreadcrumbList>
        </Breadcrumb>
      </div>
    );
  }

  // If manual props provided, use them (backward compatibility)
  if (parentPage && currentPage) {
    return (
      <div className="border-b px-6 py-4">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink className="text-muted-foreground">
                {parentPage}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{currentPage}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
    );
  }

  // Auto-generate breadcrumbs from current pathname
  const pathSegments = pathname.split("/").filter(Boolean);
  const breadcrumbs: Array<{ label: string; href?: string }> = [];

  // Build breadcrumb path progressively
  let currentPath = "";
  for (let i = 0; i < pathSegments.length; i++) {
    currentPath += `/${pathSegments[i]}`;
    const routeInfo = routeConfig[currentPath];
    
    if (routeInfo) {
      breadcrumbs.push({
        label: routeInfo.label,
        href: i < pathSegments.length - 1 ? routeInfo.href : undefined, // Last item has no href
      });
    } else {
      // Fallback: capitalize the segment name
      const label = pathSegments[i]
        .split("-")
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");
      
      breadcrumbs.push({
        label,
        href: i < pathSegments.length - 1 ? currentPath : undefined,
      });
    }
  }

  // If no breadcrumbs generated or only one item, don't show breadcrumb
  if (breadcrumbs.length <= 1) {
    return null;
  }

  return (
    <div className="border-b px-6 py-4">
      <Breadcrumb>
        <BreadcrumbList>
          {breadcrumbs.map((crumb, index) => (
            <div key={index} className="flex items-center">
              {index > 0 && <BreadcrumbSeparator />}
              <BreadcrumbItem>
                {crumb.href ? (
                  <BreadcrumbLink asChild>
                    <Link href={crumb.href}>{crumb.label}</Link>
                  </BreadcrumbLink>
                ) : (
                  <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
                )}
              </BreadcrumbItem>
            </div>
          ))}
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
}
