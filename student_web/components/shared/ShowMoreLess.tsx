"use client";

import React, { ReactNode, useState } from "react";
import { ChevronDown } from "lucide-react";

import { Button } from "@/components/ui/button";
import { TooltipWrapper } from "@/components/shared/TooltipWrapper";

interface ShowMoreLessProps {
  /**
   * Full content to display
   */
  content: ReactNode;
  /**
   * Truncated content (if not provided, will be auto-generated)
   */
  truncatedContent?: ReactNode;
  /**
   * Content length threshold, above which the "Show more" button will be displayed
   */
  threshold?: number;

  className?: string;
 
  isLong?: boolean;
}

export function ShowMoreLess({
  content,
  truncatedContent,
  threshold = 300,
  className = "",
  isLong,
}: ShowMoreLessProps) {
  const [showMore, setShowMore] = useState(false);


  const contentIsLong =
    isLong !== undefined
      ? isLong
      : typeof content === "string" && content.length > threshold;

  return (
    <div className={`overflow-hidden ${className}`}>
      <div className="text-start transition-all">
        {contentIsLong && !showMore ? truncatedContent || content : content}
      </div>

      {contentIsLong && (
        <div className="mt-2 flex justify-center">
          <TooltipWrapper tooltipContent={showMore ? "Show less" : "Show more"}>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowMore(!showMore)}
              className="mt-2"
            >
              <ChevronDown
                className={`mx-auto h-4 w-4 text-gray-700 transition-all ${
                  showMore ? "rotate-180" : ""
                }`}
              />
              <span className="ml-2">
                {showMore ? "Show less" : "Show more"}
              </span>
            </Button>
          </TooltipWrapper>
        </div>
      )}
    </div>
  );
}
