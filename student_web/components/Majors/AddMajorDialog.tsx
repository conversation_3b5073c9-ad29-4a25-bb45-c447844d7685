import React from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface SelectedMajor {
  name: string;
  matchScore: number;
  isCustom?: boolean;
}

interface AddMajorDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedMajor: SelectedMajor | null;
  studentName: string;
  customMajorName: string;
  onCustomMajorNameChange: (name: string) => void;
  userRationale: string;
  onUserRationaleChange: (rationale: string) => void;
  onSave: () => void;
}

export const AddMajorDialog: React.FC<AddMajorDialogProps> = ({
  isOpen,
  onOpenChange,
  selectedMajor,
  studentName,
  customMajorName,
  onCustomMajorNameChange,
  userRationale,
  onUserRationaleChange,
  onSave,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {selectedMajor?.isCustom
              ? "Add Major"
              : `Add ${selectedMajor?.name}`}
          </DialogTitle>
          {!selectedMajor?.isCustom && selectedMajor && (
            <div className="mt-1 text-sm">
              Match Score: {selectedMajor.matchScore}%
            </div>
          )}
          <DialogDescription>
            {selectedMajor?.isCustom
              ? "Enter your custom major details"
              : `Why do you think this major is a good match for ${studentName || "Student"}?`}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {selectedMajor?.isCustom && (
            <div className="grid gap-2">
              <label htmlFor="major-name" className="text-sm font-medium">
                Major Name
              </label>
              <Input
                id="major-name"
                placeholder="Enter major name..."
                value={customMajorName}
                onChange={(e) => onCustomMajorNameChange(e.target.value)}
              />
            </div>
          )}
          <div className="grid gap-2">
            <label htmlFor="major-rationale" className="text-sm font-medium">
              {selectedMajor?.isCustom
                ? `Why do you think this major is a good match for ${studentName || "Student"}?`
                : ""}
            </label>
            <Textarea
              id="major-rationale"
              placeholder="Share your thoughts on why this major aligns with their interests, goals, and personality..."
              value={userRationale}
              onChange={(e) => onUserRationaleChange(e.target.value)}
              className="min-h-[150px]"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={onSave}>Add Major</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddMajorDialog;
