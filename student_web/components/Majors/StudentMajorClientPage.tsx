"use client";

import { StudentMajorsTab } from "@/components/Majors/StudentMajorsTab";
import { BreadcrumbHeader } from "@/components/shared/BreadcrumbHeader";

interface StudentMajorClientPageProps {
  student: any;
}

export const StudentMajorClientPage = ({
  student,
}: StudentMajorClientPageProps) => {
  return (
    <div className="w-full">
      <BreadcrumbHeader parentPage="Colleges" currentPage="Majors" />
      <div className="w-full py-6">
        <StudentMajorsTab student={student} />
      </div>
    </div>
  );
};

export default StudentMajorClientPage;
