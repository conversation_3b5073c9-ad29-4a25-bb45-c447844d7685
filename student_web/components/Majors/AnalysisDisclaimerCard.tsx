import React from "react";
import { <PERSON><PERSON><PERSON> } from "lucide-react";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

export const AnalysisDisclaimerCard = () => {
  return (
    <Card className="border-muted bg-muted/30">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          <Sparkles className="h-5 w-5 text-blue-500" />
          Why This Analysis Works
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <p className="text-sm leading-relaxed text-muted-foreground">
          The OCEAN personality model has been validated across decades of
          research as the most reliable predictor of academic and career
          success. By analyzing your unique personality profile, we can identify
          majors where you&#39;ll not only excel academically but also find
          genuine fulfillment.
        </p>
        <div className="border-l-4 border-blue-200 pl-4">
          <p className="text-sm font-medium text-foreground">
            &#34;Personality-major fit is one of the strongest predictors of
            academic satisfaction and career success.&#34;
          </p>
          <p className="mt-1 text-xs text-muted-foreground">
            Dr. <PERSON>, Educational Psychology • Stanford University
          </p>
        </div>
      </Card<PERSON><PERSON>nt>
    </Card>
  );
};
