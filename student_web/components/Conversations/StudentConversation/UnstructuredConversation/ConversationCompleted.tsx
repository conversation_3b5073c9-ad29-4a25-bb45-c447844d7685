import React from "react";
import Image from "next/image";
import { clearChatHistory } from "@/actions/chat";
import { UserRole } from "@prisma/client";

import DestructiveModal from "@/components/modals/destructive-modal";

interface ConversationCompletedProps {
  earlyEndMessage?: string;
  user?: {
    role: UserRole;
    students?: [{ id: string }];
  };
  sessionId: string;
  id: string;
}

export function ConversationCompleted({
  earlyEndMessage,
  user,
  sessionId,
  id,
}: ConversationCompletedProps) {
  const studentId = user?.students?.[0]?.id;

  return (
    <div className="flex h-screen flex-col items-center justify-center px-4 text-center">
      <Image
        alt="conversation completed"
        src="/images/conversation_little-girl.png"
        width={132}
        height={156}
        className="mb-6"
      />
      <h2 className="mb-2 text-2xl font-semibold">Conversation Complete</h2>
      <p className="max-w-sm text-muted-foreground">
        {earlyEndMessage
          ? earlyEndMessage
          : "Thank you for chatting! You have completed this conversation"}
      </p>

      {user?.role !== UserRole.STUDENT && studentId && (
        <div className="mt-6 flex justify-center">
          <DestructiveModal
            title="Clear Chat History"
            description="Are you sure you want to clear your chat history? This action cannot be undone."
            handler={() => clearChatHistory(sessionId, studentId)}
            btnTitle="Clear History"
            isDeleteButton={true}
            className="text-sm"
            variant="default"
            action="Clearing..."
          />
        </div>
      )}
    </div>
  );
}
