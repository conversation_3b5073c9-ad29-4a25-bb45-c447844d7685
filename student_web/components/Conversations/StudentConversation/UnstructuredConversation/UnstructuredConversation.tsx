"use client";

import { startTransition, useCallback, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  beginConversation,
  checkAllCompletedStepsAndFinishConversation,
} from "@/actions/student-conversation";
import {
  StudentWorkflowStatus,
  WorkflowStatus,
  WorkflowType,
} from "@prisma/client";
import {
  ArrowUpRight,
  Globe,
  MessageCircle,
  MessageSquare,
  Pause,
  Phone,
} from "lucide-react";
import { toast } from "sonner";

import log from "@/common/logger";
import { cn } from "@/common/utils";
import { useSmsInitiation } from "@/hooks/useSmsInitiation";
import { useVoiceInitiation } from "@/hooks/useVoiceInitiation";
import { Button } from "@/components/ui/button";
import { useSidebar } from "@/components/ui/sidebar";
import { Chat, type Message } from "@/components/chat";
import { ConversationCompleted } from "@/components/Conversations/StudentConversation/UnstructuredConversation/ConversationCompleted";
import { TooltipWrapper } from "@/components/shared/TooltipWrapper";

interface UnstructuredConversationProps {
  conversation: any;
  messages: Message[];
  user: any;
  authToken?: string;
  sessionId: string;
  tzOffset?: number;
  justStarted?: boolean;
  showOverlay?: boolean;
  setShowOverlay?: (showOverlay: boolean) => void;
}

export function UnstructuredConversation({
  conversation,
  messages,
  user,
  authToken,
  sessionId,
  tzOffset,
  justStarted,
  showOverlay = conversation.status === StudentWorkflowStatus.IN_PROGRESS, // Default fallback if not provided
  setShowOverlay,
}: UnstructuredConversationProps) {
  const studentId = user?.students[0].id!;
  // console.log("conversation from UnstructuredConversation", conversation);

  const conversationTitle = conversation.workflow.name;
  const router = useRouter();
  // console.log("showOverlay", showOverlay);
  const earlyEndMessage = conversation.workflow.data?.earlyEndMessage;

  const finishConversation = useCallback(async () => {
    startTransition(async () => {
      const result = await checkAllCompletedStepsAndFinishConversation(
        studentId,
        conversation.workflow.id,
      );

      if (result.success) {
        toast.success(
          earlyEndMessage ? earlyEndMessage : "Conversation is completed!",
        );
        router.refresh();

        // Force refresh the parent layout to update the conversation list
        setTimeout(() => {
          router.refresh();
        }, 500);
      } else {
        return;
      }
    });
  }, [conversation.workflow.id, earlyEndMessage, router, studentId]);

  useEffect(() => {
    if (conversation.status === StudentWorkflowStatus.IN_PROGRESS) {
      finishConversation();
    }
  }, [conversation.status, finishConversation]);

  // Real time updates on the progress of the conversation when in sms and voice mode, every 5 seconds
  useEffect(() => {
    if (
      (conversation.mode !== "sms" && conversation.mode !== "voice") ||
      conversation.status !== StudentWorkflowStatus.IN_PROGRESS
    ) {
      return;
    }

    if (
      (conversation.mode === "sms" || conversation.mode === "voice") &&
      conversation.status === StudentWorkflowStatus.IN_PROGRESS
    ) {
      const refreshInterval = setInterval(() => {
        router.refresh();
      }, 5000);
      return () => {
        clearInterval(refreshInterval);
      };
    }
  }, [conversation.mode, conversation.status, router]);

  const isCompleted = conversation.status === StudentWorkflowStatus.COMPLETED;

  const { initiateSmsConversation } = useSmsInitiation();
  const { initiateCall } = useVoiceInitiation();

  const handleSmsButton = async () => {
    await initiateSmsConversation(conversation.id, user?.phone_number);
  };

  const handleCallButton = async () => {
    await initiateCall(studentId, conversation.id, user?.phone_number);
  };

  // Handle continuing conversation within the app
  const handleContinueInApp = async () => {
    try {
      // Update conversation mode to web
      await beginConversation(studentId, conversation.workflow.id);

      // Hide the overlay
      if (setShowOverlay) {
        setShowOverlay(false);
      }

      // Refresh the page to show updated state
      router.refresh();
    } catch (error) {
      console.error("Error continuing conversation in app:", error);
      toast.error("Failed to continue conversation in app");
    }
  };

  const { isMobile } = useSidebar();

  if (isCompleted) {
    return (
      <ConversationCompleted
        earlyEndMessage={earlyEndMessage || ""}
        user={user}
        id={conversation.id}
        sessionId={sessionId}
      />
    );
  }

  return (
    <div className="relative flex h-full w-full flex-col">
      <div className="relative flex-1 overflow-hidden">
        <Chat
          sessionId={sessionId}
          tzOffset={tzOffset ?? 0}
          user={user}
          messages={messages ?? []}
          isUnstructuredConvo={true}
          conversation={conversation}
          setShowOverlay={setShowOverlay ? setShowOverlay : undefined}
        />
      </div>

      {/* Overlay for IN_PROGRESS mode AND SMS mode */}
      {showOverlay &&
        (!justStarted ||
          conversation.mode === "sms" ||
          conversation.mode === "voice") && (
          <>
            {/* Full screen overlay with blur effect */}
            <div
              className="absolute inset-0 z-10 bg-white/50 backdrop-blur-sm"
              onClick={(e) => e.preventDefault()}
            ></div>

            {/* Bottom notification banner - positioned to match Figma */}
            <div
              className={cn(
                "absolute left-4 right-4 z-20 rounded-lg border border-gray-200 bg-white shadow-sm",
                isMobile ? "bottom-1/3" : "bottom-6",
              )}
            >
              <div className="flex items-center justify-between p-4">
                <div className="flex items-center gap-3">
                  <div className="flex h-6 w-6 items-center justify-center rounded bg-gray-100 text-gray-500">
                    <Pause size={16} />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      You left the conversation mid way
                    </p>
                    <p className="text-sm text-gray-600">
                      Carry on the conversation with Addie seamlessly from where
                      you left off.
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {/*Three options to continue the conversation*/}
                  <TooltipWrapper tooltipContent="Continue conversation within the app">
                    <Button
                      variant="outline"
                      onClick={handleContinueInApp}
                      className="flex h-10 w-10 items-center justify-center rounded-md bg-muted"
                    >
                      <Globe size={20} />
                    </Button>
                  </TooltipWrapper>

                  <TooltipWrapper tooltipContent="Continue conversation via SMS">
                    <Button
                      variant="outline"
                      onClick={handleSmsButton}
                      className="flex h-10 w-10 items-center justify-center rounded-md bg-muted"
                    >
                      <MessageSquare size={20} />
                    </Button>
                  </TooltipWrapper>

                  <TooltipWrapper tooltipContent="Continue conversation via call">
                    <Button
                      variant="outline"
                      onClick={handleCallButton}
                      disabled={!user?.phone_number}
                      className="flex h-10 w-10 items-center justify-center rounded-md bg-muted disabled:opacity-50"
                    >
                      <Phone size={20} />
                    </Button>
                  </TooltipWrapper>
                </div>
              </div>
            </div>
          </>
        )}
    </div>
  );
}
