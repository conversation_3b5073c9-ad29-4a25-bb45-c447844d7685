"use client";

import { MessageCircle, MessageSquare } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { useSmsInitiation } from "@/hooks/useSmsInitiation";

interface ContinueConversationOptionsProps {
  studentConversationId: string;
  phoneNumber?: string | null;
  onContinueInApp: () => void;
}

export function ContinueConversationOptions({
  studentConversationId,
  phoneNumber,
  onContinueInApp,
}: ContinueConversationOptionsProps) {
  const { initiateSmsConversation } = useSmsInitiation();

  const handleSmsButton = async () => {
    await initiateSmsConversation(studentConversationId, phoneNumber);
  };

  return (
    <div className="flex items-center justify-end gap-2 p-2">
      <div className="flex items-center">
        <span className="mr-2 text-sm font-medium text-muted-foreground">Continue Conversation</span>
        <Button
          onClick={onContinueInApp}
          variant="outline"
          className="flex items-center gap-1 rounded-r-none border-r-0"
          size="sm"
        >
          <MessageCircle className="h-4 w-4" />
          <span>Continue in App</span>
        </Button>
        <Button
          onClick={handleSmsButton}
          variant="outline"
          className="flex items-center gap-1 rounded-l-none"
          size="sm"
        >
          <MessageSquare className="h-4 w-4" />
          <span>SMS</span>
        </Button>
      </div>
    </div>
  );
}
