"use client";

import { useEffect, useState } from "react";
import { WorkflowType } from "@prisma/client";
import { Globe, MessageSquare, Phone } from "lucide-react";

import { useSmsInitiation } from "@/hooks/useSmsInitiation";
import { useVoiceInitiation } from "@/hooks/useVoiceInitiation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface ConversationOptionsProps {
  studentConversationId: string;
  studentId: string;
  onStartChatting: () => void;
  setShowQuestions: (showQuestion: boolean) => void;
  phoneNumber?: string | null;
  workflowType: WorkflowType;
  setShowOverlay?: (showOverlay: boolean) => void;
}

export function ConversationOptions({
  studentConversationId,
  studentId,
  onStartChatting,
  setShowQuestions,
  phoneNumber,
  workflowType,
  setShowOverlay,
}: ConversationOptionsProps) {
  const { initiateSmsConversation } = useSmsInitiation();
  const { initiateCall, checkCallStatus, isInitiating } = useVoiceInitiation();
  const [hasActiveCall, setHasActiveCall] = useState(false);

  // Check for active calls only on component mount (no continuous polling)
  useEffect(() => {
    const checkActiveCall = async () => {
      const callStatus = await checkCallStatus(
        studentId,
        studentConversationId,
      );
      setHasActiveCall(callStatus !== null);
    };

    checkActiveCall();
  }, [studentId, studentConversationId, checkCallStatus]);

  const handleReceiveSMS = async () => {
    const success = await initiateSmsConversation(
      studentConversationId,
      phoneNumber,
    );
    if (success) {
      setShowOverlay && setShowOverlay(true);
      setShowQuestions(true);
    }
  };

  const handleCallMe = async () => {
    // Check for active calls before initiating a new one
    const callStatus = await checkCallStatus(studentId, studentConversationId);
    setHasActiveCall(callStatus !== null);

    // If there's already an active call, don't initiate a new one
    if (callStatus !== null) {
      return;
    }

    const success = await initiateCall(
      studentId,
      studentConversationId,
      phoneNumber,
    );

    if (success) {
      setHasActiveCall(true);
      // Show the conversation UI when initiating a voice call
      setShowQuestions(true);
      // Show the overlay for voice mode
      setShowOverlay && setShowOverlay(true);
    }
  };

  return (
    <div className="mx-auto w-full max-w-lg px-4">
      <Card className="border">
        <CardHeader className="pb-2">
          <CardTitle className="text-md font-medium">
            How would you like to talk to Addie?
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 pt-0">
          {/* Start Chatting Option */}
          <Button
            onClick={onStartChatting}
            variant="outline"
            className="flex h-auto w-full items-start justify-start rounded-lg border p-4 text-left transition-colors hover:bg-black hover:text-white"
          >
            <div className="flex min-w-[40px] items-center justify-center pt-1">
              <Globe className="h-6 w-6" />
            </div>
            <div className="ml-3 min-w-0 flex-1">
              <div className="font-medium">Start Chatting</div>
              <div className="overflow-wrap-anywhere max-w-full whitespace-normal break-words text-sm text-muted-foreground group-hover:text-white">
                Text-based conversation within the app
              </div>
            </div>
          </Button>

          {/* Receive SMS Option */}
          {(workflowType === WorkflowType.STRUCTURED ||
            workflowType === WorkflowType.UNSTRUCTURED) && (
            <Button
              onClick={handleReceiveSMS}
              variant="outline"
              className="flex h-auto w-full items-start justify-start rounded-lg border p-4 text-left transition-colors hover:bg-black hover:text-white"
            >
              <div className="flex min-w-[40px] items-center justify-center pt-1">
                <MessageSquare className="h-6 w-6" />
              </div>
              <div className="ml-3 min-w-0 flex-1">
                <div className="font-medium">Receive SMS</div>
                <div className="overflow-wrap-anywhere max-w-full whitespace-normal break-words text-sm text-muted-foreground group-hover:text-white">
                  Get text messages from Addie on your phone
                </div>
              </div>
            </Button>
          )}

          {/* Call Me Option */}
          {workflowType === WorkflowType.UNSTRUCTURED && phoneNumber && (
            <Button
              onClick={handleCallMe}
              variant="outline"
              disabled={hasActiveCall || isInitiating}
              className="flex h-auto w-full items-start justify-start rounded-lg border p-4 text-left transition-colors hover:bg-black hover:text-white disabled:cursor-not-allowed disabled:opacity-50"
            >
              <div className="flex min-w-[40px] items-center justify-center pt-1">
                <Phone className="h-6 w-6" />
              </div>
              <div className="ml-3 min-w-0 flex-1">
                <div className="font-medium">
                  {hasActiveCall
                    ? "Call in Progress"
                    : isInitiating
                      ? "Initiating Call..."
                      : "Receive Call"}
                </div>
                <div className="overflow-wrap-anywhere max-w-full whitespace-normal break-words text-sm text-muted-foreground group-hover:text-white">
                  {hasActiveCall
                    ? "You have an active call with Addie"
                    : "Talk to Addie over a voice call"}
                </div>
              </div>
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
