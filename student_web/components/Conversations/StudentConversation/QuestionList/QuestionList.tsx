"use client";

import { useEffect, useRef, useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import {
  completeStudentWorkflow,
  upsertQuestionResponse,
} from "@/actions/student-conversation";
import { StudentWorkflowStatus } from "@prisma/client";
import { EllipsisVertical, Globe, MessageSquare, Search } from "lucide-react";
import { toast } from "sonner";

import { cn } from "@/common/utils";
import { useSmsInitiation } from "@/hooks/useSmsInitiation";
import { isBFIWorkflow, triggerOceanCalculation } from "@/lib/ocean-scores";
import { Button } from "@/components/ui/button";
import { Markdown } from "@/components/ui/content/markdown";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { useSidebar } from "@/components/ui/sidebar";
import { QuestionEditor } from "@/components/Conversations/QuestionEditor/QuestionEditor";
import DestructiveModal from "@/components/modals/destructive-modal";
import { ShowMoreLess } from "@/components/shared/ShowMoreLess";

interface QuestionListProps {
  conversation: any;
  answers: any;
  studentId: string;
  onCompleteConversation?: () => Promise<void>;
  user: any;
}

export function QuestionList({
  conversation,
  answers,
  studentId,
  onCompleteConversation,
  user,
}: QuestionListProps) {
  const steps = conversation.steps;
  const questions = steps.map((step) => step.data);
  const conversationTitle = conversation.workflow.name;
  const [searchQuery, setSearchQuery] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  const { initiateSmsConversation } = useSmsInitiation();

  // **CHANGE:** Set up pagination states
  const pageSize = 10; // Number of questions per page
  const [currentPage, setCurrentPage] = useState(0);
  const totalPages = Math.ceil(questions.length / pageSize);

  // **CHANGE:** Paginate questions (if no search query, otherwise paginate filtered results)
  const allFilteredQuestions = searchQuery
    ? questions.filter((q) =>
        q.question.toLowerCase().includes(searchQuery.toLowerCase()),
      )
    : questions;
  const paginatedQuestions = allFilteredQuestions.slice(
    currentPage * pageSize,
    (currentPage + 1) * pageSize,
  );

  // SMS INITIATION
  const handleClickSMS = async () => {
    const success = await initiateSmsConversation(
      conversation.id,
      user?.phone_number,
    );
    if (success) {
      setIsEditing(false);
    }
  };

  const handleEditQuestion = () => {
    setIsEditing(true);
  };

  const handleSaveAnswer = async (questionId: string, answer: string) => {
    // Update the answer in the answers object (ensure it's updated immutably if needed)
    answers[questionId] = answer;
    // log.debug("Updated answers:==>", answers);

    // Find the question object that matches the questionId
    const questionObj = questions.find((q) => q.questionId === questionId);
    if (!questionObj) {
      console.error(`Question with ID ${questionId} not found`);
      return;
    }

    // Call the server action to save the answer
    try {
      const result = await upsertQuestionResponse(
        studentId,
        questionObj,
        answer,
      );
      if (!result.success) {
        console.error("Failed to save answer:", result.error);
      }
    } catch (error) {
      console.error("Error saving answer:", error);
    }

    // Refresh the data after saving
    startTransition(() => {
      router.refresh();
    });
  };

  const handleSaveAndExit = () => {
    setIsEditing(false);
    startTransition(() => {
      router.refresh();
    });
  };

  const handleSubmitQuestions = async () => {
    startTransition(() => {
      router.refresh();
    });

    // each question validation for skip and character length
    for (const step of steps) {
      const q = step.data; // e.g. { questionId, question, canSkip, table, characterLimit }
      const qId = q.questionId;
      const ans = answers[qId] || "";

      if (q.canSkip === false) {
        if (!ans.trim() || ans.trim() === "No answer" || ans.trim() === "N/A") {
          toast.error(
            `Question "${q.question}" is required. Please provide an answer.`,
          );
          return;
        }

        // Rule B: If it's a text question => check min/max
        if (q.table === "Question" && q.characterLimit) {
          const length = ans.trim().length;
          const min = q.characterLimit.min ?? 0;
          const max = q.characterLimit.max ?? 1000;

          if (min > 0 && length < min) {
            toast.error(
              `Your answer to "${q.question}" is too short. Minimum is ${min} characters.`,
            );
            return;
          }

          if (max !== undefined && length > max) {
            toast.error(
              `Your answer to "${q.question}" is too long. Maximum is ${max} characters.`,
            );
            return;
          }
        }
      }
    }
    // Calculate answered count based on answers from the backend
    const requiredSteps = steps.filter((s) => s.data.canSkip === false);
    const requiredAnsweredCount = requiredSteps.reduce((count, step) => {
      const ans = answers[step.data.questionId] || "";
      if (ans.trim() && ans.trim() !== "No answer" && ans.trim() !== "N/A") {
        return count + 1;
      }
      return count;
    }, 0);

    if (requiredAnsweredCount !== requiredSteps.length) {
      toast.error("Please complete all required questions before submitting");
      return;
    }

    // Calculate answered count based on answers from the backend

    // Here, I commented this out for now, could you please check if this is working on your end
    // const { allAnswered } = getAnswerStats();
    // if (!allAnswered) {
    //   toast.error("Please complete all questions before submitting");
    //   return;
    // }
    handleSaveAndExit();
    try {
      const result = await completeStudentWorkflow(
        studentId,
        conversation.workflow.id,
      );
      if (!result.success) {
        throw new Error(result.error);
      }

      // Check if this is a BFI survey and trigger OCEAN calculation
      if (isBFIWorkflow(conversation.workflow)) {
        console.log("BFI survey completed, triggering OCEAN calculation...");
        try {
          const oceanResult = await triggerOceanCalculation(studentId);
          if (oceanResult.success && oceanResult.task_queued) {
            console.log("OCEAN calculation task queued successfully");
            toast.success("Survey completed! Your personality scores are being calculated.");
          } else {
            console.warn("OCEAN calculation not queued:", oceanResult.message);
            toast.success("Conversation completed successfully!");
          }
        } catch (oceanError) {
          console.error("Failed to trigger OCEAN calculation:", oceanError);
          // Don't fail the whole submission if OCEAN calculation fails
          toast.success("Conversation completed successfully!");
        }
      } else {
        toast.success("Conversation completed successfully!");
      }

      router.refresh();
    } catch (error) {
      console.error("Failed to complete conversation:", error);
      toast.error("Failed to complete conversation.");
    }
  };

  const getAnswerStats = () => {
    const answeredCount = Object.entries(answers).filter(
      ([questionId, answer]: [string, string]) => {
        const step = steps.find((step) => step.data.questionId === questionId);
        const canSkip = step?.data?.canSkip;
        // I deleted "canSkip" because it would not calculate the "correct" answered questions number, because when I answer "optional" questions, it wouldn't count as answered
        return answer && answer.trim() !== "No answer";
        // return (answer && answer.trim() !== "No answer") || canSkip;
      },
    ).length;

    const skippedCount = steps.filter(
      (step) => step.data?.canSkip && !answers[step.data.questionId],
    ).length;

    return {
      answeredCount,
      skippedCount,
      allAnswered: answeredCount + skippedCount === steps.length,
    };
  };

  // Compute answer statistics
  const { answeredCount, skippedCount, allAnswered } = getAnswerStats();

  // **CHANGE:** Pagination handlers
  const goToPreviousPage = () => {
    if (currentPage > 0) setCurrentPage(currentPage - 1);
  };

  const goToNextPage = () => {
    if (currentPage < totalPages - 1) setCurrentPage(currentPage + 1);
  };

  // Add this new function:
  const goToPage = (page: number) => {
    if (page >= 0 && page < totalPages) {
      setCurrentPage(page);
    }
  };

  const scrollContainerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    (scrollContainerRef.current as any)?.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, [currentPage, isEditing]);

  // refresh the page to get the latest answers from the backend when the conversation is in SMS mode
  useEffect(() => {
    if (
      conversation.mode === "sms" &&
      conversation.status === "IN_PROGRESS" &&
      !isEditing
    ) {
      const refreshInterval = setInterval(() => {
        router.refresh();
      }, 5000);

      return () => clearInterval(refreshInterval);
    }
  }, [conversation.mode, conversation.status, isEditing, router]);

  // Get mobile state from sidebar context
  const { isMobile } = useSidebar();

  return (
    <div className="flex w-full grow flex-col">
      {/* Fixed Header */}
      <div className="shrink-0 border-b px-6 py-4">
        <h2 className="text-center text-lg text-muted-foreground">
          {conversationTitle}
        </h2>
        {/*<p className="mt-2 text-sm text-muted-foreground">*/}
        {/*  To submit the questionnaire, no field can be left blank. If you are*/}
        {/*  skipping a question, simply write &#34;N/A.&#34;*/}
        {/*</p>*/}
      </div>

      {/* Fixed Progress and Search */}
      <div
        className={cn(
          "shrink-0 border-b px-6 py-4",
          isMobile
            ? "flex flex-col space-y-3"
            : "flex items-center justify-between",
        )}
      >
        <div className="text-sm text-muted-foreground">
          {answeredCount}/{steps.length} answered
        </div>

        {!isEditing ? (
          <div
            className={cn(
              isMobile
                ? "flex w-full flex-col space-y-2"
                : "flex items-center gap-2",
            )}
          >
            {conversation.status === StudentWorkflowStatus.COMPLETED && (
              <div className="relative w-[280px]">
                <Input
                  placeholder="Search"
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    // Reset to first page if search query changes
                    setCurrentPage(0);
                  }}
                  className="py-2 pl-9 pr-4"
                />
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              </div>
            )}

            {/*Continue conversation buttons*/}
            {conversation.status === StudentWorkflowStatus.IN_PROGRESS && (
              <div
                className={cn(
                  isMobile
                    ? "flex w-full flex-col items-center space-y-2"
                    : "flex items-center gap-3",
                )}
              >
                <span className="cursor-default text-sm font-medium">
                  Continue Conversation
                </span>
                <div
                  className={cn(
                    "flex",
                    isMobile ? "w-full space-x-2" : "items-center gap-1",
                  )}
                >
                  <Button
                    onClick={handleEditQuestion}
                    variant="outline"
                    className={cn(
                      "bg-muted text-sm",
                      isMobile ? "flex-1" : "px-6",
                    )}
                  >
                    <Globe className="mr-2 h-6 w-6" />
                    <span>Continue in App</span>
                  </Button>
                  <Button
                    variant="outline"
                    className={cn(
                      "flex items-center gap-2 bg-muted text-sm",
                      isMobile ? "flex-1" : "",
                    )}
                    onClick={handleClickSMS}
                  >
                    <MessageSquare className="h-6 w-6" />
                    <span>SMS</span>
                  </Button>
                </div>
              </div>
            )}
            {conversation.status === StudentWorkflowStatus.NOT_STARTED && (
              <Button
                onClick={handleEditQuestion}
                variant="default"
                className="px-6"
              >
                Begin conversation
              </Button>
            )}
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <Button
              onClick={handleSaveAndExit}
              variant="outline"
              className="bg-muted px-6"
            >
              Continue later
            </Button>
            <DestructiveModal
              title="Submit"
              description="Are you sure to submit? Once submitted, answers cannot be changed"
              handler={handleSubmitQuestions}
              btnTitle="Submit"
              isDeleteButton={false}
              className="px-6"
              variant="default"
              action="Submitting"
              // disabled={!allAnswered}
            />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="bg-muted" size="icon">
                  <EllipsisVertical className="h-2 w-2" />
                </Button>
              </DropdownMenuTrigger>

              <DropdownMenuContent className="w-64" align="end">
                <DropdownMenuLabel className="text-sm font-medium text-muted-foreground">
                  Continue conversation on
                </DropdownMenuLabel>
                <DropdownMenuItem
                  className="flex cursor-pointer items-center bg-muted"
                  onClick={handleClickSMS}
                >
                  <MessageSquare className="mr-2 h-4 w-4" />
                  <span>SMS</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {/* Main content with fixed height calculation */}
      <div className="flex grow flex-col overflow-hidden">
        {/* Scrollable Questions Area */}
        <div className="grow overflow-y-auto" ref={scrollContainerRef}>
          <div className="divide-y divide-border">
            {/* Conversation Description */}
            {conversation.workflow.data?.description && (
              <div className="bg-muted/50 px-6 py-4">
                <div className="mb-2 flex items-center">
                  <h3 className="text-base font-bold">
                    About This Conversation
                  </h3>
                </div>
                <style jsx global>{`
                  .markdown-list-fix ol {
                    padding-left: 2.5rem !important;
                  }
                  .markdown-list-fix ol li {
                    margin-left: 0.5rem;
                  }
                  .markdown-list-fix ul {
                    padding-left: 2rem !important;
                  }
                `}</style>
                <ShowMoreLess
                  content={
                    <Markdown
                      content={conversation.workflow.data.description}
                      className="hyphens-auto break-all text-muted-foreground"
                    />
                  }
                  truncatedContent={
                    <Markdown
                      content={`${conversation.workflow.data.description.slice(0, 150)}...`}
                      className="hyphens-auto break-all text-muted-foreground"
                    />
                  }
                  threshold={150}
                  className="markdown-list-fix break-words"
                  isLong={conversation.workflow.data.description.length > 150}
                />
              </div>
            )}

            {paginatedQuestions.map((question, index) => {
              // Calculate overall question number based on current page
              const questionNumber = currentPage * pageSize + index + 1;
              const qId = question.questionId;
              const studentAnswer = answers[qId] || "No answer";
              return (
                <div
                  key={question.questionId}
                  className="px-6 py-4 hover:bg-accent/50"
                >
                  <h3 className="mb-1 text-base font-bold">
                    {question.canSkip === false && (
                      <span className="mr-1 text-red-500">*</span>
                    )}
                    {questionNumber}. {question.question}
                    {question.canSkip || question.canSkip === undefined ? (
                      <span className="ml-2 text-sm text-muted-foreground">
                        (Optional)
                      </span>
                    ) : (
                      <span className="ml-2 text-sm text-red-500">
                        (Required)
                      </span>
                    )}
                  </h3>
                  {isEditing ? (
                    <QuestionEditor
                      question={question}
                      answer={studentAnswer}
                      onSaveAnswer={handleSaveAnswer}
                      studentId={studentId}
                    />
                  ) : studentAnswer &&
                    studentAnswer.trim() !== "" &&
                    studentAnswer !== "No answer" &&
                    studentAnswer !== "N/A" ? (
                    // If there is a real answer
                    <p className="text-base leading-relaxed text-foreground">
                      {studentAnswer}
                    </p>
                  ) : (
                    <p className="text-sm italic text-gray-400">No answer</p>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Fixed Pagination at bottom */}
        <div className="shrink-0 border-t bg-background py-4">
          <div className="flex flex-col items-center justify-center gap-3">
            {/* Page numbers row */}
            <div className="flex items-center justify-center gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={goToPreviousPage}
                disabled={currentPage === 0}
                className="h-9 rounded-md px-4"
              >
                Previous
              </Button>

              {Array.from({ length: Math.min(totalPages, 10) }).map((_, i) => {
                const pageNumber = i + 1;
                const isCurrentPage = currentPage + 1 === pageNumber;

                return (
                  <Button
                    key={pageNumber}
                    variant={
                      isCurrentPage
                        ? ("default" as "default")
                        : ("outline" as "outline")
                    }
                    size="sm"
                    className={`h-9 w-9 rounded-md ${isCurrentPage ? "bg-primary text-primary-foreground" : ""}`}
                    onClick={() => setCurrentPage(pageNumber - 1)}
                  >
                    {pageNumber}
                  </Button>
                );
              })}

              <Button
                variant="outline"
                size="sm"
                onClick={goToNextPage}
                disabled={currentPage >= totalPages - 1}
                className="h-9 rounded-md px-4"
              >
                Next
              </Button>
            </div>

            {/* Page indicator */}
            <div className="text-sm text-muted-foreground">
              Page {currentPage + 1} of {totalPages}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
