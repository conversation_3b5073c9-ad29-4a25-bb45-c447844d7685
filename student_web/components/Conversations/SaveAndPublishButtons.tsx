import { useState } from "react";
import { useRouter } from "next/navigation";
import { publishConversation } from "@/actions/conversation-workflow";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

interface SaveAndPublishButtonsProps {
  type?: string;
  conversationId: string;
  onSave?: () => Promise<void>;
  isSaving?: boolean;
}

const SaveAndPublishButtons = ({
  type,
  conversationId,
  onSave,
  isSaving,
}: SaveAndPublishButtonsProps) => {
  const [isPublishing, setIsPublishing] = useState(false);
  const router = useRouter();
  const handlePublish = async () => {
    setIsPublishing(true);
    const result = await publishConversation(conversationId);
    if (result?.error) {
      toast.error("This conversation is already published");
    }
    if (result?.success) {
      toast.success("Conversation published successfully!");
    }
    setIsPublishing(false);
    router.refresh();
  };

  return (
    <div className="flex items-center justify-between p-4">
      {/*need to replace with real updatedAt*/}
      {type !== "onboarding" ? (
        <span className="text-sm"> </span>
      ) : (
        <span> </span>
      )}

      {/*Button groups TODO: need to add two server actions */}
      <div className="flex gap-2">
        {type !== "onboarding" && (
          <Button
            variant="outline"
            onClick={onSave}
            disabled={isSaving || isPublishing}
          >
            {isSaving ? <LoadingSpinner /> : "Save"}
          </Button>
        )}
        <Button onClick={handlePublish} disabled={isPublishing || isSaving}>
          {isPublishing ? <LoadingSpinner /> : "Publish"}
        </Button>
      </div>
    </div>
  );
};

export default SaveAndPublishButtons;
