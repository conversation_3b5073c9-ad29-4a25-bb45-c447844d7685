"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  createConversation,
  getAllConversations,
} from "@/actions/conversation-workflow";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Icons } from "@/components/shared/icons";

export function CreateConversationButton() {
  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const router = useRouter();
  const handleCreate = async () => {
    if (!title) {
      toast.warning("Please enter a conversation name");
      return;
    }

    setIsCreating(true);

    try {
      // Attempt to create the conversation
      const result = await createConversation(title);
      if (result.success && result.data) {
        toast.success("New conversation created!");
        setOpen(false);
        setTitle("");
        router.refresh(); // Refresh to update the UI with the new conversation
      } else {
        toast.error(result.error || "Failed to create conversation");
      }
    } catch (error) {
      // Handle any unexpected errors
      console.error("Unexpected error creating conversation:", error);
      toast.error("An unexpected error occurred. Please try again.");
    } finally {
      setIsCreating(false); // Reset loading state
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <div className="flex items-center justify-between p-4">
          <span className="font-semibold">Conversations</span>
          <Button>
            <Icons.add />
            Create
          </Button>
        </div>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Conversation</DialogTitle>
          <DialogDescription>
            Create a custom conversation with your own questions
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <Input
            placeholder="Enter conversation name"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
          />
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button disabled={isCreating} onClick={handleCreate}>
              {isCreating ? <LoadingSpinner /> : "Create"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
