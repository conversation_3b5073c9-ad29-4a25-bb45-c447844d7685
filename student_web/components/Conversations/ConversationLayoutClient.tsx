"use client";

import React, { useCallback, useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { SquareMenu } from "lucide-react";

import { Button } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import {
  She<PERSON>,
  <PERSON>et<PERSON>ontent,
  SheetHeader,
  Sheet<PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useSidebar } from "@/components/ui/sidebar";
import { ConversationList } from "@/components/Conversations/ConversationList";
import { WorkflowChecker } from "@/components/Conversations/WorkflowChecker";

interface ConversationLayoutClientProps {
  children: React.ReactNode;
  initialConversations: any[];
  studentId: string;
}

export function ConversationLayoutClient({
  children,
  initialConversations,
  studentId,
}: ConversationLayoutClientProps) {
  const [conversations, setConversations] = useState(initialConversations);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [hasAutoOpened, setHasAutoOpened] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const { isMobile } = useSidebar();
  const refreshConversations = useCallback(async () => {
    setIsRefreshing(true);
    try {
      // Refresh the current page to get updated conversations
      router.refresh();
    } catch (error) {
      console.error("Error refreshing conversations:", error);
    } finally {
      setIsRefreshing(false);
    }
  }, [router]);

  // Add effect to update conversations when initialConversations changes
  useEffect(() => {
    setConversations(initialConversations);
  }, [initialConversations]);

  // Add effect to force refresh when pathname changes (navigation between conversations)
  useEffect(() => {
    const refreshTimer = setTimeout(() => {
      refreshConversations();
    }, 300);

    return () => clearTimeout(refreshTimer);
  }, [pathname, refreshConversations]);

  // Auto-open sheet on mobile when first visiting conversations page
  useEffect(() => {
    const isConversationsRoot = pathname === "/conversations";

    if (isMobile && isConversationsRoot && !hasAutoOpened) {
      setIsSheetOpen(true);
      setHasAutoOpened(true);
    }
  }, [pathname, hasAutoOpened, isMobile]);

  const conversationListContent = (
    <>
      {/* Disable auto-assignment feature for now */}
      {/* <WorkflowChecker
        studentId={studentId}
        onWorkflowsUpdated={refreshConversations}
      /> */}
      {isRefreshing ? (
        <div className="flex flex-col items-center justify-center p-4">
          <LoadingSpinner />
          <div className="text-sm text-muted-foreground">
            Updating conversations...
          </div>
        </div>
      ) : (
        <ConversationList
          conversations={conversations}
          onConversationSelect={() => setIsSheetOpen(false)}
        />
      )}
    </>
  );

  return (
    <>
      {/* Mobile view (< md) */}
      <div className="flex h-screen w-full flex-col md:hidden">
        {/* Navigation */}
        <div className="flex items-center justify-between border-b p-4">
          <h3 className="mx-auto flex items-center justify-center text-lg font-medium">
            Conversations
          </h3>

          {/* Conversation list menu button on right */}
          <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                aria-label="Open conversation list"
              >
                <SquareMenu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent
              side="right"
              className="w-70vw flex flex-col p-0 sm:w-[300px]"
            >
              <SheetHeader className="shrink-0 border-b p-4">
                <SheetTitle>Conversations</SheetTitle>
              </SheetHeader>
              <div className="flex-1 overflow-hidden">
                {conversationListContent}
              </div>
            </SheetContent>
          </Sheet>
        </div>

        {/* Mobile Content */}
        <div className="flex-1 overflow-auto">{children}</div>
      </div>

      {/* Desktop view (>= md) */}
      <div className="hidden h-screen w-full md:flex">
        {/* Left Sidebar */}
        <div className="flex w-80 flex-col overflow-hidden border-r">
          <h3 className="shrink-0 p-4 pb-0">Conversations</h3>
          <div className="flex-1 overflow-hidden">
            {conversationListContent}
          </div>
        </div>

        {/* Desktop Content - Ensure proper overflow handling */}
        <div className="flex-1 overflow-auto">{children}</div>
      </div>
    </>
  );
}
