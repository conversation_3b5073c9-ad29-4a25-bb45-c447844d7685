"use client";

import React from "react";
import {
  CustomConversation,
  QuestionItem,
} from "@/actions/conversation-workflow";

import MultiQuestionBuilder from "@/components/Conversations/QuestionBuilder/MultiQuestionBuilder";

interface BuildQuestionProps {
  conversation: CustomConversation;
  onQuestionsChange: (questions: QuestionItem[]) => void;
}

export default function BuildQuestion({
  conversation,
  onQuestionsChange,
}: BuildQuestionProps) {
  // BuildQuestion simply wraps MultiQuestionBuilder and passes down a callback
  // that receives the full list of unsaved builder forms.
  const handleChange = (forms: QuestionItem[]) => {
    onQuestionsChange(forms);
  };

  return (
    <div className="flex h-full flex-col">
      <div className="flex-1 overflow-auto p-6">
        <MultiQuestionBuilder onChange={handleChange} />
      </div>
    </div>
  );
}
