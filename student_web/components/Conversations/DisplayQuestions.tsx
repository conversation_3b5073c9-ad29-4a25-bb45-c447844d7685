"use client";

import React from "react";
import { QuestionItem } from "@/actions/conversation-workflow";

interface DisplayQuestionsProps {
  questions: QuestionItem[];
}

export function DisplayQuestions({ questions }: DisplayQuestionsProps) {
  // console.log("current question format ==>,", questions);
  return (
    <div className="space-y-4">
      {questions && questions.length > 0 ? (
        questions.map((question, index) => (
          <div key={index} className="flex justify-between rounded border p-4">
            <div>
              <strong>Question {index + 1}:</strong> {question.text}
            </div>
            <div>Type: {question.type}</div>
          </div>
        ))
      ) : (
        <div>No questions available.</div>
      )}
    </div>
  );
}
