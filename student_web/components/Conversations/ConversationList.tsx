"use client";

import { useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { CustomConversation } from "@/actions/conversation-workflow";
import { WorkflowType } from "@prisma/client";

import { formatTime, formatTimeAgo } from "@/common/utils";
import { SearchInput } from "@/components/Conversations/SearchInput";
import StatusBadge from "@/components/Conversations/StatusBadge";

interface ConversationListProps {
  conversations: any;
  onConversationSelect?: () => void;
}

// Helper function to calcuate the number of completed questions
// export const calculateFinishedSteps = (steps) => {
//   const numberOfFinishedQuestions = steps.filter(
//     (step) => step.completed,
//   ).length;
//   // console.log(numberOfFinishedQuestions);
//   return numberOfFinishedQuestions;
// };

function cleanWorkflowName(name: string): string {
  const prefixes = ["Unstructured Conversation:", "Conversation:"];
  for (const prefix of prefixes) {
    if (name.startsWith(prefix)) {
      return name.replace(prefix, "").trim();
    }
  }
  return name;
}

export function ConversationList({ conversations, onConversationSelect }: ConversationListProps) {
  const pathName = usePathname();
  const currentConvoId = pathName.split("/")[2] || "";

  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");

  // sort conversations based on their updated_at time
  const sortedConversations = [...conversations].sort((a, b) => {
    return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
  });

  // Filter conversations based on search query -- either name or conversation.id
  const filteredConversations = sortedConversations.filter((conversation) => {
    const query = searchQuery.toLowerCase();
    return (
      conversation.workflow.name.toLowerCase().includes(query) ||
      conversation.workflow.id.toLowerCase().includes(query)
    );
  });
  return (
    <div className="flex h-full flex-col">
      <div className="shrink-0 p-2">
        <SearchInput value={searchQuery} onChange={setSearchQuery} />
      </div>
      <div className="flex-1 overflow-y-auto">
        {filteredConversations.map((conversation, index) => {
        const workflow = conversation.workflow;
        const workflowType = workflow.workflow_type;
        const answerMap = conversation.answerMap || {};

        const answeredCount = Object.values(answerMap).filter(
          (answer: string) => answer && answer.trim() !== "No answer",
        ).length;

        const isSelected = currentConvoId === workflow.id;
        return (
          <button
            key={workflow.id}
            onClick={() => {
              // Close the sheet on mobile when conversation is selected
              onConversationSelect?.();
              // Navigate to the conversation
              router.push(`/conversations/${workflow.id}`);
            }}
            className={`flex w-full flex-col gap-2 ${index === 0 ? "border-t" : ""} ${isSelected ? "bg-gray-200" : ""} border-b border-gray-300 px-4 py-3 text-left hover:bg-accent`}
          >
            {/* Header Row */}
            <div className="mb-2 flex w-full items-center justify-between">
              {/*status*/}
              <StatusBadge status={conversation.status} />
              {/*updated time*/}
              <div className="text-sm font-light">
                {formatTimeAgo(conversation.updated_at)}
              </div>
            </div>
            {/*Middle row*/}
            <div className="text-sm font-medium">
              {cleanWorkflowName(workflow.name)}
            </div>
            {/* Footer Row */}
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              {workflowType === WorkflowType.STRUCTURED && (
                <div>{`${answeredCount} out of ${conversation.steps.length} ${conversation.steps.length === 1 ? "question" : "questions"} answered`}</div>
              )}
              {workflowType === WorkflowType.UNSTRUCTURED && (
                <span>Open-ended</span>
              )}
              {workflowType === WorkflowType.ASSIGNMENT && (
                <span>Assignment</span>
              )}
            </div>
          </button>
        );
      })}
      </div>
    </div>
  );
}
