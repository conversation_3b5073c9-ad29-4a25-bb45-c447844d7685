"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
// Placeholder API functions (to be implemented later)
import {
  CustomConversation,
  // saveQuestionsToWorkflow,
  publishConversation,
  QuestionItem,
  saveQuestionsToWorkflow,
} from "@/actions/conversation-workflow";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { DisplayQuestions } from "@/components/Conversations/DisplayQuestions";
import BuildQuestion from "@/components/Conversations/QuestionBuilder/BuildQuestion";
import SaveAndPublishButtons from "@/components/Conversations/SaveAndPublishButtons";

interface CustomConversationDetailProps {
  conversation: CustomConversation;
}

export function CustomConversationDetail({
  conversation,
}: CustomConversationDetailProps) {
  // Unified state for all questions (array of QuestionItem)
  const [questions, setQuestions] = useState<QuestionItem[]>(
    conversation.questions || [],
  );
  const [isSaving, setIsSaving] = useState(false);
  // For now, if no questions exist, start in editing mode
  const [isEditing, setIsEditing] = useState<boolean>(questions.length === 0);
  const router = useRouter();

  // Global save handler using the unified questions state
  const handleSaveQuestions = async () => {
    console.log("questions saved!, currently saved Questions ==>", questions);
    setIsSaving(true);
    try {
      await saveQuestionsToWorkflow(conversation.id, questions);
      toast.success("Questions saved successfully!");
      router.refresh();
      setIsEditing(false);
    } catch (error) {
      toast.error("Failed to save questions.");
    } finally {
      setIsSaving(false);
    }
  };

  // Callback to update questions state from BuildQuestion (which gets data from MultiQuestionBuilder)
  const handleQuestionsChange = (newQuestions: QuestionItem[]) => {
    setQuestions(newQuestions);
  };

  return (
    <div className="flex h-full flex-col">
      {/* Header: conversation title and (optionally) an Edit button */}
      <div className="flex items-center justify-between border-b p-4">
        <h1 className="text-center text-2xl font-bold">{conversation.name}</h1>
        {/*{!isEditing && <Button onClick={() => setIsEditing(true)}>Edit</Button>}*/}
      </div>

      {/* Main Content Area */}
      <div className="flex-1 overflow-auto p-6">
        {/* BuildQuestion receives onQuestionsChange callback to update the*/}
        {/*unified questions state*/}
        {isEditing && (
          <BuildQuestion
            conversation={conversation}
            onQuestionsChange={handleQuestionsChange}
          />
        )}

        {/*Display saved questions in read-only mode*/}
        <DisplayQuestions questions={questions} />
      </div>
    </div>
  );
}
