"use client";

import { useEffect, useMemo, useRef, useState } from "react";
import TextStyle from "@tiptap/extension-text-style";
import Underline from "@tiptap/extension-underline";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { Bold, Italic, List, UnderlineIcon } from "lucide-react";
import markdownIt from "markdown-it";
import TurndownService from "turndown";

import { Button } from "@/components/ui/button";

interface TextEditorProps {
  value: string | undefined;
  onChange: (value: string) => void;
}

export function TextEditor({ value, onChange }: TextEditorProps) {
  const [isMounted, setIsMounted] = useState(false);
  const [isUpdatingContent, setIsUpdatingContent] = useState(false);

  // Memoize these instances to prevent recreation on every render
  const md = useMemo(() => markdownIt(), []);
  const turndownService = useMemo(() => new TurndownService(), []);

  const editorContainerRef = useRef<HTMLDivElement | null>(null);

  // Ensure value is always a string
  const safeValue =
    typeof value === "string" && value.trim() !== "No answer" ? value : "";

  // On mount, set isMounted to true to avoid SSR mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          HTMLAttributes: { class: "list-disc ml-5" }, // ✅ Configure BulletList styling
        },
        listItem: {}, // Ensure list items are properly configured
      }),
      TextStyle,
      Underline,
    ],
    content: md.render(safeValue),
    onUpdate: ({ editor }) => {
      // Only trigger onChange if we're not currently updating from an external source
      if (!isUpdatingContent) {
        const updatedContent = turndownService.turndown(editor.getHTML());
        if (updatedContent !== safeValue) {
          onChange(updatedContent);
        }
      }
    },
    // autofocus: true,
    immediatelyRender: false, // ✅ Fix SSR hydration issue
  });

  // Synchronize content when value prop changes
  useEffect(() => {
    if (!editor || !safeValue) return;

    const currentContent = turndownService.turndown(editor.getHTML());

    // Only update if the content is different to avoid loops
    if (safeValue !== currentContent) {
      // Set flag to prevent onUpdate from triggering onChange
      setIsUpdatingContent(true);

      // Update the editor content
      editor.commands.setContent(md.render(safeValue), false);

      // Reset the flag after a short delay to ensure the update has completed
      setTimeout(() => {
        setIsUpdatingContent(false);
      }, 0);
    }
  }, [safeValue, editor, turndownService, md]);

  const handleFocusEditor = () => {
    if (editor) {
      editor.commands.focus();
    }
  };
  if (!isMounted || !editor) return null; // ✅ Fix SSR hydration mismatch

  return (
    <div
      ref={editorContainerRef}
      className="cursor-text rounded-md border border-gray-200"
      onClick={handleFocusEditor}
    >
      {/* Toolbar */}
      <div className="flex items-center gap-1 border-b p-2">
        <Button
          variant={editor.isActive("bold") ? "default" : "ghost"}
          size="icon"
          className="h-8 w-8"
          onClick={() => editor.chain().focus().toggleBold().run()}
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button
          variant={editor.isActive("italic") ? "default" : "ghost"}
          size="icon"
          className="h-8 w-8"
          onClick={() => editor.chain().focus().toggleItalic().run()}
        >
          <Italic className="h-4 w-4" />
        </Button>
        <Button
          variant={editor.isActive("underline") ? "default" : "ghost"}
          size="icon"
          className="h-8 w-8"
          onClick={() => editor.chain().focus().toggleUnderline().run()}
        >
          <UnderlineIcon className="h-4 w-4" />
        </Button>
        <Button
          variant={editor.isActive("bulletList") ? "default" : "ghost"}
          size="icon"
          className="h-8 w-8"
          onClick={() => editor.chain().focus().toggleBulletList().run()}
        >
          <List className="h-4 w-4" />
        </Button>
      </div>

      {/* Editor */}
      <EditorContent
        editor={editor}
        className="min-h-[150px] p-3 focus:outline-none"
      />
      <style jsx global>{`
        .ProseMirror {
          min-height: 100%;
          outline: none;
        }

        .ProseMirror p {
          margin: 0;
        }

        .ProseMirror ul {
          list-style-type: disc; /* Ensures bullet points are visible */
          margin-left: 1.25rem; /* Aligns with Tailwind’s ml-5 */
        }

        .ProseMirror p.is-editor-empty:first-child::before {
          color: #adb5bd;
          content: "Type your answer here...";
          float: left;
          height: 0;
          pointer-events: none;
        }
      `}</style>
    </div>
  );
}
