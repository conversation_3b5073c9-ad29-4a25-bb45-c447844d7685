"use client";

import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface MultipleChoiceEditorProps {
  value: string;
  onChange: (value: string) => void;
  options: string[];
  questionId: string;
}

export function MultipleChoiceEditor({
  value,
  onChange,
  options,
  questionId,
}: MultipleChoiceEditorProps) {
  return (
    <RadioGroup
      value={value}
      onValueChange={onChange}
      className="space-y-2"
      name={`mc-editor-${questionId}`}
    >
      {options.map((option, index) => {
        const radioId = `option-${questionId}-${index}`;
        return (
          <Label
            key={radioId}
            htmlFor={radioId}
            className="flex w-full cursor-pointer items-center space-x-2 rounded-md border p-3 hover:bg-accent/50"
          >
            <RadioGroupItem value={option} id={radioId} />
            <span className="w-full">{option}</span>
          </Label>
        );
      })}
    </RadioGroup>
  );
}
