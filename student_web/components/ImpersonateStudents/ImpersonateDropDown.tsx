"use client";

import { useCallback, useEffect, useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import {
  fetchStudentsBasicInfo,
  StudentWithWorkflows,
} from "@/actions/students";
import { UserRole } from "@prisma/client";
import { ChevronDown, Search, UserRound, X } from "lucide-react";
import { signIn } from "next-auth/react";
import { toast } from "sonner";
import { useDebounce } from "use-debounce";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";

export default function ImpersonateDropDown({ user }) {
  const [students, setStudents] = useState<StudentWithWorkflows[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStudent, setSelectedStudent] =
    useState<StudentWithWorkflows | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedGrade, setSelectedGrade] = useState<string>("all");
  const [hasFetched, setHasFetched] = useState(false);

  const { state } = useSidebar();

  const router = useRouter();
  // debounced search query
  const [debouncedSearchQuery] = useDebounce(searchQuery, 300);
  const loadStudents = useCallback(async () => {
    try {
      setLoading(true);
      const gradeParam = selectedGrade === "all" ? null : selectedGrade;
      if (user) {
        const data = await fetchStudentsBasicInfo({
          grade: gradeParam,
          search: debouncedSearchQuery || null,
          user,
        });

        const students = Array.isArray(data) ? data : [];
        setStudents(students);
        setHasFetched(true);
      }
    } catch (error) {
      console.error("Failed to fetch students:", error);
    } finally {
      setLoading(false);
    }
  }, [debouncedSearchQuery, selectedGrade, user]);

  useEffect(() => {
    if (user?.role !== UserRole.ADMIN) return;

    if (isOpen && !hasFetched) {
      loadStudents();
    }
  }, [
    hasFetched,
    isOpen,
    loadStudents,
    searchQuery,
    selectedGrade,
    user?.role,
  ]);

  const filteredStudents: StudentWithWorkflows[] = useMemo(() => {
    let filtered = [...students];

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter((student: any) => {
        const fullName =
          `${student?.first_name} ${student?.last_name}`.toLowerCase();
        const studentId = student.student_id?.toLowerCase() || "";
        return fullName.includes(query) || studentId.includes(query);
      });
    }

    if (selectedGrade !== "all") {
      filtered = filtered.filter(
        (student: any) => student.grade.toString() === selectedGrade,
      );
    }
    return filtered;
  }, [searchQuery, selectedGrade, students]);

  const handleImpersonateStudent = async (student) => {
    setSelectedStudent(student);
    setIsOpen(false);
    // set admin email address to localstorage, so when resetting, they can switch back to this email
    const originalAdminEmail = localStorage.getItem("originalAdminEmail");
    if (!originalAdminEmail || originalAdminEmail !== user.email) {
      localStorage.setItem("originalAdminEmail", user.email);
    }
    try {
      const result = await signIn("credentials", {
        email: student.email,
        impersonate: "true",
        redirect: false,
      });
      if (result?.ok) {
        toast.success(
          `Now impersonating ${student.first_name} ${student.last_name}`,
        );
        router.refresh();
      } else {
        toast.error("Impersonation failed");
      }
    } catch (error) {
      console.error("Error during impersonation", error);
      toast.error("Error during impersonation");
    }
  };

  const handleResetImpersonation = async () => {
    setSelectedStudent(null);
    const adminEmail = localStorage.getItem("originalAdminEmail");
    try {
      const result = await signIn("credentials", {
        email: adminEmail,
        redirect: false,
        impersonate: "false",
      });
      if (result?.ok) {
        toast.success("Switching back to admin account");
        router.refresh();
      } else {
        toast.error("Failed to switch back to admin account");
      }
    } catch (e) {
      console.log("Error during switching back to admin account", e);
      toast.error("Error during switching account");
    }
  };

  return (
    <>
      {user?.role === UserRole.ADMIN && (
        <SidebarMenuItem>
          <DropdownMenu modal={false} open={isOpen} onOpenChange={setIsOpen}>
            <DropdownMenuTrigger asChild>
              <SidebarMenuButton
                className="flex items-center justify-between"
                tooltip="Impersonate Student"
              >
                <div className="flex items-center gap-2">
                  <UserRound className="h-4 w-4" />
                  {state === "expanded" && <span>Impersonate Student</span>}
                </div>
                <ChevronDown className="h-4 w-4" />
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="start"
              className="w-72 p-0"
              onCloseAutoFocus={(e) => e.preventDefault()}
            >
              <div className="flex items-center border-b p-2">
                <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                <Input
                  placeholder="Search students..."
                  value={searchQuery}
                  autoFocus={true}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onMouseDown={(e) => e.stopPropagation()}
                  className="h-8 border-0 bg-transparent p-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    onClick={() => setSearchQuery("")}
                    className="h-8 px-2 py-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <ScrollArea className="max-h-[300px] w-full overflow-y-auto">
                {filteredStudents.length > 0 ? (
                  filteredStudents.map((student) => {
                    const fullname = `${student.first_name} ${student.last_name}`;
                    return (
                      <DropdownMenuItem
                        key={student.id}
                        onClick={() => handleImpersonateStudent(student)}
                        className={`flex items-center justify-between p-3 ${selectedStudent && selectedStudent.id === student.id ? "bg-green-100" : ""}`}
                      >
                        <div className="flex items-center gap-2">
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary">
                            {student.first_name?.charAt(0)?.toUpperCase() ||
                              "S"}
                          </div>
                          <div className="flex flex-col">
                            <span className="font-medium">{fullname}</span>
                            {student.email && (
                              <span className="text-xs text-muted-foreground">
                                {student.email}
                              </span>
                            )}
                          </div>
                        </div>
                      </DropdownMenuItem>
                    );
                  })
                ) : (
                  <div className="p-4 text-center text-sm text-muted-foreground">
                    {searchQuery ? "No students found" : "Loading students..."}
                  </div>
                )}
              </ScrollArea>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuItem>
      )}

      {(selectedStudent || user.impersonated) && (
        <div className="mb-2 rounded-md bg-amber-100 p-2 text-sm">
          <div className="flex flex-col items-center justify-between gap-2 truncate">
            {state === "collapsed" ? (
              <div>...</div>
            ) : (
              <div>
                <span className="font-medium text-amber-800">
                  Impersonating:
                </span>
                <span className="ml-2 text-amber-900">
                  {selectedStudent?.first_name || user.first_name}{" "}
                  {selectedStudent?.last_name || user.last_name}
                </span>
              </div>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleResetImpersonation}
              className="h-6 truncate rounded-md bg-amber-200 px-2 py-0 text-xs font-medium text-amber-800 hover:bg-amber-300"
            >
              Reset
            </Button>
          </div>
        </div>
      )}
    </>
  );
}
