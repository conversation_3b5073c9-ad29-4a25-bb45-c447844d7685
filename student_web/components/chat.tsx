"use client";

import * as React from "react";
import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { clearChatHistory, submitChatMessage } from "@/actions/chat";
import { getConversationById } from "@/actions/conversation-workflow";
import { getStudentWorkflow } from "@/actions/student-conversation";
import {
  StudentWorkflow,
  StudentWorkflowStatus,
  UserRole,
} from "@prisma/client";
import { EllipsisVertical, MessageSquare, Phone } from "lucide-react";

import log from "@/common/logger";
import { useSmsInitiation } from "@/hooks/useSmsInitiation";
import { useVoiceInitiation } from "@/hooks/useVoiceInitiation";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import { FullPageEditor } from "@/components/FullPageEditor/FullPageEditor";
import DestructiveModal from "@/components/modals/destructive-modal";
import { TypingIndicator } from "@/components/TypingIndicator";

import { Textarea } from "./ui/textarea";

export interface Message {
  content: string;
  type: "human" | "ai" | "tool";
}

interface ChatProps {
  sessionId: string;
  tzOffset: number;
  user;
  messages: Message[];
  isUnstructuredConvo?: boolean;
  conversation: StudentWorkflow;
  setShowOverlay?: (showOverlay: boolean) => void;
}

export function Chat({
  sessionId,
  tzOffset,
  user,
  messages,
  isUnstructuredConvo,
  conversation,
  setShowOverlay,
}: ChatProps) {
  const [triggeredAgent, setTriggeredAgent] = useState(false);
  const [chatMessages, setChatMessages] = useState<Message[]>(messages);
  const [isDisabled, setIsDisabled] = useState(false);
  const [showTypingIndicator, setShowTypingIndicator] = useState(false);
  const [input, setInput] = useState<string>("");
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  const studentId = user.students[0]?.id;
  const studentWorkflowId = conversation.id;

  const router = useRouter();

  // Filter out tool messages
  const visibleMessages = chatMessages.filter((m) => m.type !== "tool");

  // Log tool messages to console
  useEffect(() => {
    const toolMessages = chatMessages.filter((m) => m.type === "tool");
    if (toolMessages.length > 0) {
      log.debug("Tool messages:", toolMessages);
    }
  }, [chatMessages]);

  const scrollToBottom = () => {
    const scrollRef = chatContainerRef;
    if (scrollRef.current) {
      scrollRef.current.scrollIntoView(false);
    }
  };

  const submitMsg = useCallback(
    async (content: string) => {
      setShowTypingIndicator(true);
      setInput("");

      const newMessage: Message = {
        content,
        type: "human",
      };

      const params = {
        message: content,
        userId: user.id,
        sessionId,
        tzOffset,
        studentId: user.students[0]?.id,
        studentWorkflowId: conversation.id,
      };

      if (!params.studentId) {
        return;
      }

      setChatMessages([...chatMessages, newMessage]);

      try {
        const response = await submitChatMessage(params);
        setChatMessages((prev) => [...prev, ...response.messages]);

        // Check if conversation is completed after each message
        const convo = await getStudentWorkflow(
          conversation.student_id,
          conversation.workflow_id,
        );
        if (convo.status === StudentWorkflowStatus.COMPLETED) {
          router.refresh();
        }
      } catch (error) {
        log.error("Error submitting message:", error);
        // toast.error("Failed to send message");
      } finally {
        setShowTypingIndicator(false);
      }
    },
    [
      chatMessages,
      sessionId,
      tzOffset,
      conversation,
      router,
      user.students,
      user.id,
    ],
  );

  useEffect(() => {
    if (chatMessages.length === 0 && !triggeredAgent) {
      setTriggeredAgent(true);
      submitMsg("");
    }

    scrollToBottom();
  }, [
    chatContainerRef,
    scrollAreaRef,
    chatMessages,
    submitMsg,
    triggeredAgent,
  ]);

  // Automatically focus the Textarea whenever the messages change
  useEffect(() => {
    if (textAreaRef.current) {
      textAreaRef.current.focus();
    }
  }, [chatMessages]);

  const handleSendMessage = async (e: any) => {
    e.preventDefault();
    if (input.trim()) {
      await submitMsg(input);
    }
  };

  const handleFullPageSubmit = async (content: string) => {
    await submitMsg(content);

    if (textAreaRef.current) {
      textAreaRef.current.focus();
    }
  };

  function handleInputChange(e: React.ChangeEvent<HTMLTextAreaElement>) {
    setInput(e.target.value);
  }

  const handleClearChat = async () => {
    try {
      const result = await clearChatHistory(sessionId, user.students[0]?.id);
      if (result.success) {
        setChatMessages([]);
        setTriggeredAgent(false); // This will trigger the agent to start a new conversation
      } else {
        log.error("Failed to clear chat history:", result.error);
      }
    } catch (error) {
      log.error("Error clearing chat history:", error);
    }
  };

  // Check if user has permission to clear chat history
  const canClearHistory =
    user.role === UserRole.ADMIN || user.role === UserRole.COUNSELOR;

  // Use flex properties for the scroll area instead of hardcoded height calculations
  // This allows the content to properly adjust based on parent container size
  const scrollAreaClass = "grow overflow-y-auto pr-2";

  // SMS INITIATION
  const { initiateSmsConversation } = useSmsInitiation();

  // VOICE INITIATION
  const { initiateCall } = useVoiceInitiation();

  const handleClickSMS = async () => {
    const success = await initiateSmsConversation(
      conversation.id,
      user?.phone_number,
    );
    if (success) {
      if (setShowOverlay) {
        setShowOverlay(true);
      }
    }
  };

  const handleCallButton = async () => {
    const success = await initiateCall(
      studentId,
      conversation.id,
      user?.phone_number,
    );
    if (success) {
      if (setShowOverlay) {
        setShowOverlay(true);
      }
    }
  };

  return (
    // <div className="h-screen w-full px-4 py-4">
    <div className="flex h-full w-full flex-col px-4 py-4">
      {canClearHistory && (
        <div className="mb-2 flex justify-end gap-2">
          <DestructiveModal
            title="Clear Chat History"
            description="Are you sure you want to clear your chat history? This action cannot be undone."
            handler={handleClearChat}
            btnTitle="Clear History"
            isDeleteButton={true}
            className="text-sm"
            variant="default"
            action="Clearing..."
          />

          {/*Hide SMS feature for open ended conversations for now*/}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="bg-muted" size="icon">
                <EllipsisVertical className="h-2 w-2" />
              </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent className="w-64" align="end">
              <DropdownMenuLabel className="text-sm font-medium text-muted-foreground">
                Continue conversation on
              </DropdownMenuLabel>
              <DropdownMenuItem
                className="flex cursor-pointer items-center"
                onClick={handleClickSMS}
              >
                <MessageSquare className="mr-2 h-4 w-4" />
                <span>SMS</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="flex cursor-pointer items-center"
                onClick={handleCallButton}
                disabled={!user?.phone_number}
              >
                <Phone className="mr-2 h-4 w-4" />
                <span>Call</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}
      <ScrollArea className={scrollAreaClass}>
        <div>
          {visibleMessages.map((msg, index) => (
            <div
              key={index}
              className={`mb-4 rounded-lg p-3 ${
                msg.type === "ai"
                  ? "bg-blue-100 text-blue-800"
                  : "bg-gray-100 text-gray-800"
              }`}
            >
              <p className="mb-1 text-sm font-semibold">
                {msg.type === "ai" ? "Addie" : "You"}
              </p>
              <p>{msg.content}</p>
            </div>
          ))}
        </div>
        <div>
          {showTypingIndicator && <TypingIndicator />}
          <div ref={chatContainerRef}></div>
        </div>
      </ScrollArea>
      <div className="mb-2">
        <FullPageEditor
          value={input}
          onChange={setInput}
          onSubmit={submitMsg}
          isDisabled={isDisabled}
          studentId={studentId}
        />
      </div>

      <form
        onSubmit={handleSendMessage}
        className="border-1 flex w-full items-center space-x-2 p-2"
      >
        <Textarea
          autoFocus
          ref={textAreaRef}
          disabled={isDisabled}
          value={input}
          onChange={handleInputChange}
          placeholder="Shift + Enter for new line"
          className="grow"
          onKeyDownCapture={(e) => {
            if (e.key === "Enter" && !e.shiftKey) {
              handleSendMessage(e);
            }
          }}
        />
        <Button type="submit">Send</Button>
      </form>
    </div>
  );
}
