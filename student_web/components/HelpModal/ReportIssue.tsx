import React from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

interface ReportIssueProp {
  onToggle: () => void;
  handleReportIssue: () => void;
  message: string;
  setMessage: (value: string) => void;
  isSubmitting: boolean;
}
function ReportIssue({
  onToggle,
  handleReportIssue,
  message,
  setMessage,
  isSubmitting,
}) {
  return (
    <>
      <h2 className="mb-4 text-center text-xl font-bold text-gray-900 dark:text-gray-100">
        Send an email to Addie support now!
      </h2>
      <Textarea
        placeholder="Describe your issue"
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        className="mb-4 bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-gray-100"
        disabled={isSubmitting}
      />
      <div className="flex justify-end space-x-2">
        <Button
          variant="outline"
          onClick={onToggle}
          className="border-gray-300 text-gray-900 dark:border-gray-600 dark:text-gray-100"
        >
          Cancel
        </Button>
        <Button
          onClick={handleReportIssue}
          disabled={isSubmitting}
          className="bg-blue-500 text-white hover:bg-blue-600"
        >
          Submit
        </Button>
      </div>
    </>
  );
}

export default ReportIssue;
