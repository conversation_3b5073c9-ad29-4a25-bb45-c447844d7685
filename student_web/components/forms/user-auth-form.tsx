"use client";

import * as React from "react";
import { redirect, useSearchParams } from "next/navigation";
import { SubscriptionPlan } from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertCircle, HelpCircle } from "lucide-react";
import { signIn } from "next-auth/react";
import qs from "qs";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import * as z from "zod";

import { env } from "@/env.mjs";
import log from "@/common/logger";
import { cn } from "@/common/utils";

// Simple auth metrics function
function logAuthMetric(type: string, email: string, success: boolean, error?: string) {
  const timestamp = new Date().toISOString();
  const maskedEmail = email.replace(/(.{2}).*@/, '$1***@'); // Mask email for privacy
  console.log(`[AUTH_METRIC] ${timestamp} - ${type}`, {
    email: maskedEmail,
    success,
    error: error?.substring(0, 100) // Limit error length
  });
}
import { userAuthSchema } from "@/common/validations/auth";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { buttonVariants } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Icons } from "@/components/shared/icons";
import { TooltipWrapper } from "@/components/shared/TooltipWrapper";

interface UserAuthFormProps extends React.HTMLAttributes<HTMLDivElement> {
  type?: string;
  priceId?: string;
  redirectToChat?: boolean;
  baseUrl: string;
}

type FormData = z.infer<typeof userAuthSchema>;

export function UserAuthFormOld({
  className,
  type,
  priceId,
  redirectToChat,
  baseUrl,
  ...props
}: UserAuthFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(userAuthSchema),
  });
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [isGoogleLoading, setIsGoogleLoading] = React.useState<boolean>(false);
  const searchParams = useSearchParams();

  // Get error from URL parameters
  const error = searchParams?.get("error");

  async function onSubmit(data: FormData) {
    setIsLoading(true);
    const params = qs.stringify({ priceId, redirectToChat });
    const callbackUrl = `/`;

    let options = {
      email: data.email.toLowerCase(),
      redirect: false,
      callbackUrl: searchParams?.get("from") || callbackUrl,
    };

    log.info("Magic link request initiated from login form", {
      email: data.email.toLowerCase(),
      callbackUrl: options.callbackUrl,
      timestamp: new Date().toISOString(),
    });

    const signInResult = await signIn("resend", options);

    setIsLoading(false);

    if (!signInResult?.ok) {
      log.error("Magic link request failed", {
        email: data.email.toLowerCase(),
        signInResult,
        error: signInResult?.error,
      });
      logAuthMetric('auth_error_toast_shown', data.email.toLowerCase(), false, signInResult?.error);

      // Show specific error messages based on error type
      if (signInResult?.error?.includes('User not found')) {
        return toast.error("Account Not Found", {
          description: "No account found with this email. Please contact your counselor.",
        });
      }

      if (signInResult?.error?.includes('Account disabled')) {
        return toast.error("Account Disabled", {
          description: "Your account has been disabled. Please contact support.",
        });
      }

      // Generic fallback for other errors (Resend API issues, etc.)
      return toast.error("Sign In Error", {
        description: "Unable to send login email. Please try again.",
      });
    }

    if (env.NEXT_PUBLIC_DEBUG_MAGIC_LINK) {
      log.info("Magic link request successful", {
        email: data.email.toLowerCase(),
        callbackUrl: options.callbackUrl,
      });

      // Fetch and log the magic link in the browser console
      try {
        const response = await fetch("/api/debug/magic-link");
        if (response.ok) {
          const magicLinkData = await response.json();
          console.log("🔗 MAGIC LINK FOR BROWSER:", magicLinkData.url);
          console.log("📧 EMAIL:", magicLinkData.email);
          console.log("⏰ TIMESTAMP:", magicLinkData.timestamp);

          // Also show in a toast for easy access
          toast.success("Magic Link Generated!", {
            description: "Check browser console for the magic link URL",
          });
        }
      } catch (error) {
        console.error("Failed to fetch magic link:", error);
      }
    }

    logAuthMetric('auth_success_toast_shown', data.email.toLowerCase(), true);
    return toast.success("Please Check Your Email", {
      description: "You will receive an email with a link to sign in.",
    });
  }

  return (
    <div className={cn("grid gap-6", className)} {...props}>
      {error === "account_disabled" && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Account Disabled</AlertTitle>
          <AlertDescription>
            Your account has been disabled. Please contact support for
            assistance.
          </AlertDescription>
        </Alert>
      )}
      {error === "unauthorized" && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Unauthorized</AlertTitle>
          <AlertDescription>
            You are not authorized to access this page.
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid gap-2">
          <div className="grid gap-1">
            <Label className="sr-only" htmlFor="email">
              Email
            </Label>
            <Input
              id="email"
              placeholder="Email"
              type="email"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              disabled={isLoading || isGoogleLoading}
              {...register("email")}
            />
            {errors?.email && (
              <p className="px-1 text-xs text-red-600">
                {errors.email.message}
              </p>
            )}
          </div>
          <button className={cn(buttonVariants())} disabled={isLoading}>
            {isLoading && (
              <Icons.spinner className="mr-2 size-4 animate-spin" />
            )}
            {type === "register" ? "Sign Up" : "Sign In"}
          </button>
        </div>
      </form>
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Sign in with Google
          </span>
        </div>
      </div>
      <button
        type="button"
        className={cn(buttonVariants({ variant: "outline" }))}
        onClick={async () => {
          setIsGoogleLoading(true);
          if (baseUrl.includes("getaddie.com")) {
            await signIn("google");
          } else {
            // const token = await signIn("credentials", { redirect: false });
            const token = await signIn("credentials", {
              email: "<EMAIL>",
              impersonate: "false",
              redirect: false,
            });
            redirect("/");
          }
        }}
        disabled={isLoading || isGoogleLoading}
      >
        {isGoogleLoading ? (
          <Icons.spinner className="mr-2 size-4 animate-spin" />
        ) : (
          <Icons.google className="mr-2 size-4" />
        )}{" "}
        Google
      </button>
    </div>
  );
}

export function UserAuthForm({
  className,
  type,
  priceId,
  redirectToChat,
  baseUrl,
  ...props
}: UserAuthFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(userAuthSchema),
  });
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const searchParams = useSearchParams();

  // Get error from URL parameters
  const error = searchParams?.get("error");

  async function onSubmit(data: FormData) {
    setIsLoading(true);
    const email = data.email.toLowerCase();

    try {
      // Pre-validate user existence and status before calling NextAuth
      const validationResponse = await fetch("/api/auth/validate-user", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      if (!validationResponse.ok) {
        const validationError = await validationResponse.json();
        setIsLoading(false);

        logAuthMetric('auth_validation_failed', email, false, validationError.error);

        if (validationError.error === 'User not found') {
          return toast.error("Account Not Found", {
            description: validationError.message,
          });
        }

        if (validationError.error === 'Account disabled') {
          return toast.error("Account Disabled", {
            description: validationError.message,
          });
        }

        // Generic validation error
        return toast.error("Validation Error", {
          description: validationError.message || "Unable to validate account. Please try again.",
        });
      }

      // If validation passes, proceed with NextAuth
      const params = qs.stringify({ priceId, redirectToChat });
      const callbackUrl = `/`;

      let options = {
        email,
        redirect: false,
        callbackUrl: searchParams?.get("from") || callbackUrl,
      };

      log.info("Magic link request initiated from login form", {
        email,
        callbackUrl: options.callbackUrl,
        timestamp: new Date().toISOString()
      });

      const signInResult = await signIn("resend", options);

      setIsLoading(false);

      // Log auth attempt metric
      logAuthMetric('auth_attempt', email, !!signInResult?.ok, signInResult?.error);

      if (!signInResult?.ok) {
        log.error("Magic link request failed", {
          email,
          signInResult,
          error: signInResult?.error
        });
        logAuthMetric('auth_error_toast_shown', email, false, signInResult?.error);

        // Generic fallback for NextAuth/Resend API issues
        return toast.error("Sign In Error", {
          description: "Unable to send login email. Please try again.",
        });
      }

      if (env.NEXT_PUBLIC_DEBUG_MAGIC_LINK) {
        log.info("Magic link request successful", {
          email,
          callbackUrl: options.callbackUrl
        });

        // Fetch and log the magic link in the browser console
        try {
          const response = await fetch("/api/debug/magic-link");
          if (response.ok) {
            const magicLinkData = await response.json();
            console.log("🔗 MAGIC LINK FOR BROWSER:", magicLinkData.url);
            console.log("📧 EMAIL:", magicLinkData.email);
            console.log("⏰ TIMESTAMP:", magicLinkData.timestamp);

            // Also show in a toast for easy access
            toast.success("Magic Link Generated!", {
              description: "Check browser console for the magic link URL",
            });
          }
        } catch (error) {
          console.error("Failed to fetch magic link:", error);
        }
      }

      logAuthMetric('auth_success_toast_shown', email, true);
      return toast.success("Please Check Your Email", {
        description: "You will receive an email with a link to sign in.",
      });

    } catch (error) {
      setIsLoading(false);
      log.error("Error during login process", { email, error: error.message });
      logAuthMetric('auth_network_error', email, false, error.message);

      return toast.error("Network Error", {
        description: "Unable to connect to authentication service. Please check your connection and try again.",
      });
    }
  }

  return (
    <div className={cn("grid gap-6", className)} {...props}>
      {error === "account_disabled" && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Account Disabled</AlertTitle>
          <AlertDescription>
            Your account has been disabled. Please contact support for
            assistance.
          </AlertDescription>
        </Alert>
      )}
      {error === "unauthorized" && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Unauthorized</AlertTitle>
          <AlertDescription>
            You are not authorized to access this page.
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid gap-2">
          <div className="grid gap-1">
            <Label className="sr-only" htmlFor="email">
              Email
            </Label>
            <Input
              id="email"
              placeholder="Enter your school email"
              type="email"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              disabled={isLoading}
              {...register("email")}
            />
            {errors?.email && (
              <p className="px-1 text-xs text-red-600">
                {errors.email.message}
              </p>
            )}
          </div>
          <button className={cn(buttonVariants())} disabled={isLoading}>
            {isLoading && (
              <Icons.spinner className="mr-2 size-4 animate-spin" />
            )}
            {type === "register" ? "Sign Up" : "Send Login Email"}
          </button>

          <div className="mt-2 flex items-center justify-center gap-2">
            <span className="text-xs text-muted-foreground">
              Didn&#39;t receive the email?
            </span>
            <TooltipWrapper tooltipContent="Please check your spam or junk folder. If you still don't see the email, <NAME_EMAIL>">
              <HelpCircle className="h-4 w-4 cursor-help text-muted-foreground" />
            </TooltipWrapper>
          </div>
        </div>
      </form>
    </div>
  );
}
