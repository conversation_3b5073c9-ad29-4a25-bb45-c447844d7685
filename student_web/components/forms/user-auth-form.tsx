"use client";

import * as React from "react";
import { redirect, useSearchParams } from "next/navigation";
import { SubscriptionPlan } from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertCircle } from "lucide-react";
import { signIn } from "next-auth/react";
import qs from "qs";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import * as z from "zod";

import { env } from "@/env.mjs";
import log from "@/common/logger";
import { cn } from "@/common/utils";
import { userAuthSchema } from "@/common/validations/auth";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { buttonVariants } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Icons } from "@/components/shared/icons";

interface UserAuthFormProps extends React.HTMLAttributes<HTMLDivElement> {
  type?: string;
  priceId?: string;
  redirectToChat?: boolean;
  baseUrl: string;
}

type FormData = z.infer<typeof userAuthSchema>;

export function UserAuthFormOld({
  className,
  type,
  priceId,
  redirectToChat,
  baseUrl,
  ...props
}: UserAuthFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(userAuthSchema),
  });
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [isGoogleLoading, setIsGoogleLoading] = React.useState<boolean>(false);
  const searchParams = useSearchParams();

  // Get error from URL parameters
  const error = searchParams?.get("error");

  async function onSubmit(data: FormData) {
    setIsLoading(true);
    const params = qs.stringify({ priceId, redirectToChat });
    const callbackUrl = `/`;

    let options = {
      email: data.email.toLowerCase(),
      redirect: false,
      callbackUrl: searchParams?.get("from") || callbackUrl,
    };

    const signInResult = await signIn("resend", options);

    setIsLoading(false);

    if (!signInResult?.ok) {
      return toast.error("Sign In Error", {
        description: "Sign In Error",
      });
    }

    if (env.NEXT_PUBLIC_DEBUG_MAGIC_LINK) {
      log.info("Magic link request successful", {
        email: data.email.toLowerCase(),
        callbackUrl: options.callbackUrl
      });

      // Fetch and log the magic link in the browser console
      try {
        const response = await fetch("/api/debug/magic-link");
        if (response.ok) {
          const magicLinkData = await response.json();
          console.log("🔗 MAGIC LINK FOR BROWSER:", magicLinkData.url);
          console.log("📧 EMAIL:", magicLinkData.email);
          console.log("⏰ TIMESTAMP:", magicLinkData.timestamp);
          
          // Also show in a toast for easy access
          toast.success("Magic Link Generated!", {
            description: "Check browser console for the magic link URL",
          });
        }
      } catch (error) {
        console.error("Failed to fetch magic link:", error);
      }
    }

    return toast.success("Please Check Your Email", {
      description: "You will receive an email with a link to sign in.",
    });
  }

  return (
    <div className={cn("grid gap-6", className)} {...props}>
      {error === "account_disabled" && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Account Disabled</AlertTitle>
          <AlertDescription>
            Your account has been disabled. Please contact support for
            assistance.
          </AlertDescription>
        </Alert>
      )}
      {error === "unauthorized" && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Unauthorized</AlertTitle>
          <AlertDescription>
            You are not authorized to access this page.
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid gap-2">
          <div className="grid gap-1">
            <Label className="sr-only" htmlFor="email">
              Email
            </Label>
            <Input
              id="email"
              placeholder="Email"
              type="email"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              disabled={isLoading || isGoogleLoading}
              {...register("email")}
            />
            {errors?.email && (
              <p className="px-1 text-xs text-red-600">
                {errors.email.message}
              </p>
            )}
          </div>
          <button className={cn(buttonVariants())} disabled={isLoading}>
            {isLoading && (
              <Icons.spinner className="mr-2 size-4 animate-spin" />
            )}
            {type === "register" ? "Sign Up" : "Sign In"}
          </button>
        </div>
      </form>
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Sign in with Google
          </span>
        </div>
      </div>
      <button
        type="button"
        className={cn(buttonVariants({ variant: "outline" }))}
        onClick={async () => {
          setIsGoogleLoading(true);
          if (baseUrl.includes("getaddie.com")) {
            await signIn("google");
          } else {
            // const token = await signIn("credentials", { redirect: false });
            const token = await signIn("credentials", {
              email: "<EMAIL>",
              impersonate: "false",
              redirect: false,
            });
            redirect("/");
          }
        }}
        disabled={isLoading || isGoogleLoading}
      >
        {isGoogleLoading ? (
          <Icons.spinner className="mr-2 size-4 animate-spin" />
        ) : (
          <Icons.google className="mr-2 size-4" />
        )}{" "}
        Google
      </button>
    </div>
  );
}

export function UserAuthForm({
  className,
  type,
  priceId,
  redirectToChat,
  baseUrl,
  ...props
}: UserAuthFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(userAuthSchema),
  });
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const searchParams = useSearchParams();

  // Get error from URL parameters
  const error = searchParams?.get("error");

  async function onSubmit(data: FormData) {
    setIsLoading(true);
    const params = qs.stringify({ priceId, redirectToChat });
    const callbackUrl = `/`;

    let options = {
      email: data.email.toLowerCase(),
      redirect: false,
      callbackUrl: searchParams?.get("from") || callbackUrl,
    };

    const signInResult = await signIn("resend", options);

    setIsLoading(false);

    if (!signInResult?.ok) {
      return toast.error("Sign In Error", {
        description: "Sign In Error",
      });
    }

    if (env.NEXT_PUBLIC_DEBUG_MAGIC_LINK) {
      log.info("Magic link request successful", {
        email: data.email.toLowerCase(),
        callbackUrl: options.callbackUrl
      });

      // Fetch and log the magic link in the browser console
      try {
        const response = await fetch("/api/debug/magic-link");
        if (response.ok) {
          const magicLinkData = await response.json();
          console.log("🔗 MAGIC LINK FOR BROWSER:", magicLinkData.url);
          console.log("📧 EMAIL:", magicLinkData.email);
          console.log("⏰ TIMESTAMP:", magicLinkData.timestamp);
          
          // Also show in a toast for easy access
          toast.success("Magic Link Generated!", {
            description: "Check browser console for the magic link URL",
          });
        }
      } catch (error) {
        console.error("Failed to fetch magic link:", error);
      }
    }

    return toast.success("Please Check Your Email", {
      description: "You will receive an email with a link to sign in.",
    });
  }

  return (
    <div className={cn("grid gap-6", className)} {...props}>
      {error === "account_disabled" && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Account Disabled</AlertTitle>
          <AlertDescription>
            Your account has been disabled. Please contact support for
            assistance.
          </AlertDescription>
        </Alert>
      )}
      {error === "unauthorized" && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Unauthorized</AlertTitle>
          <AlertDescription>
            You are not authorized to access this page.
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid gap-2">
          <div className="grid gap-1">
            <Label className="sr-only" htmlFor="email">
              Email
            </Label>
            <Input
              id="email"
              placeholder="Email"
              type="email"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              disabled={isLoading}
              {...register("email")}
            />
            {errors?.email && (
              <p className="px-1 text-xs text-red-600">
                {errors.email.message}
              </p>
            )}
          </div>
          <button className={cn(buttonVariants())} disabled={isLoading}>
            {isLoading && (
              <Icons.spinner className="mr-2 size-4 animate-spin" />
            )}
            {type === "register" ? "Sign Up" : "Sign In"}
          </button>
        </div>
      </form>
    </div>
  );
}
