# Real Student A/B Testing System Design

## Executive Summary

This document outlines the design for an A/B testing system that enables running evaluators on real student conversations and conducting controlled experiments with both system prompts and conversation flows. The system focuses on leveraging existing conversation data to schedule and monitor A/B tests while maintaining data integrity and student experience quality.

## Current System Analysis

### Existing Infrastructure

**Evaluator System** (`addie/data_model/evaluator.py`):
- Currently supports "raw prompt" evaluators that can analyze comprehensive conversation context
- Includes chat logs, user info, workflow data, and agent configuration
- Generates scores (0-10) with detailed text responses
- Built on LangChain with OpenAI models
- Stores results in `ChatEvaluatorRun` table

**Conversation Storage**:
- Messages stored in PostgreSQL via LangChain's `PostgresChatMessageHistory`
- Session-based message tracking with format: `{workflow_id}-{user_id}`
- StudentWorkflow tracks conversation state and completion status

**Current Experiment System**:
- Limited to synthetic student agents via `addie/experiments/questionnaire.py`
- Runs questionnaire tests with generated students
- No real student A/B testing infrastructure

## System Design Overview

### Core Components

#### 1. Real Student Conversation A/B Testing Framework

**ConversationExperiment Model**:
```typescript
interface ConversationExperiment {
  id: string
  name: string
  description: string
  experiment_type: 'SYSTEM_PROMPT' | 'CONVERSATION_FLOW'
  status: 'DRAFT' | 'SCHEDULED' | 'ACTIVE' | 'COMPLETED' | 'CANCELLED'
  
  // For system prompt A/B tests
  system_prompt_a_id?: string  // AddieConfig ID
  system_prompt_b_id?: string  // AddieConfig ID
  
  // For conversation flow A/B tests  
  conversation_template_a?: Json  // Conversation template/script
  conversation_template_b?: Json  // Alternative conversation template/script
  
  // Targeting and scheduling
  target_config: ExperimentTargetConfig
  start_date: DateTime
  end_date: DateTime
  target_conversations: number
  
  // Results tracking
  conversations_a: number
  conversations_b: number
  evaluator_configs: string[]  // Which evaluators to run
  
  created_by: string
  created_at: DateTime
  updated_at: DateTime
}

interface ExperimentTargetConfig {
  // Student filtering
  school_ids?: string[]
  student_grades?: number[]
  conversation_modes?: ['web', 'sms', 'voice']
  
  // Workflow filtering  
  workflow_ids?: string[]
  workflow_types?: WorkflowType[]
  
  // Conversation criteria
  min_message_count?: number
  completed_workflows_only?: boolean
  
  // Sampling
  sample_percentage?: number  // e.g., 10% of matching conversations
  max_conversations?: number
}
```

#### 2. Conversation Assignment System

**Real-time Assignment Logic**:
```python
def get_experiment_variant_for_conversation(
    user_id: str, 
    workflow_id: str, 
    student_workflow: StudentWorkflow
) -> ExperimentVariant | None:
    """
    Determine if a real conversation should be part of an active experiment
    """
    active_experiments = get_active_conversation_experiments()
    
    for experiment in active_experiments:
        if conversation_matches_experiment_criteria(student_workflow, experiment):
            # Balanced assignment to A/B variants
            variant = assign_balanced_variant(experiment, user_id)
            
            # Log assignment for tracking
            log_experiment_assignment(experiment.id, user_id, workflow_id, variant)
            return variant
    
    return None

def conversation_matches_experiment_criteria(
    student_workflow: StudentWorkflow, 
    experiment: ConversationExperiment
) -> bool:
    """Check if conversation matches experiment targeting criteria"""
    # Implementation checks school, grade, workflow, mode, etc.
    pass
```

#### 3. Historical Conversation Analysis

**Batch Processing for Existing Conversations**:
```python
async def schedule_historical_conversation_analysis(
    experiment_id: str,
    lookback_days: int = 30,
    batch_size: int = 100
):
    """
    Schedule evaluator runs on historical conversations matching experiment criteria
    """
    experiment = get_experiment(experiment_id)
    
    # Find matching conversations from the past N days
    matching_conversations = find_conversations_by_criteria(
        target_config=experiment.target_config,
        start_date=datetime.now() - timedelta(days=lookback_days),
        end_date=datetime.now()
    )
    
    # Randomly assign conversations to A/B variants for historical analysis
    for batch in batch_conversations(matching_conversations, batch_size):
        for conversation in batch:
            variant = random.choice(['A', 'B'])
            
            # Queue evaluator runs with experiment context
            queue_evaluator_for_historical_conversation(
                experiment_id=experiment.id,
                conversation=conversation,
                variant=variant,
                evaluator_configs=experiment.evaluator_configs
            )
```

#### 4. Enhanced Evaluator System

**Experiment-Aware Evaluator**:
```python
@dataclass 
class ExperimentEvaluator(Evaluator):
    experiment_id: str = None
    experiment_variant: str = None  # 'A' or 'B'
    
    def _prepare_experiment_context(self, formatted_messages):
        """Add experiment context to evaluation data"""
        context = self._prepare_raw_prompt_context(formatted_messages)
        
        if self.experiment_id:
            experiment = self.prisma.conversationexperiment.find_unique(
                where={"id": self.experiment_id}
            )
            context["experiment_info"] = {
                "experiment_id": self.experiment_id,
                "experiment_name": experiment.name if experiment else None,
                "variant": self.experiment_variant,
                "experiment_type": experiment.experiment_type if experiment else None
            }
        
        return context
```

## Implementation Phases

### Phase 1: Database Schema & Models

**New Tables**:
```sql
-- Conversation experiments
CREATE TABLE "ConversationExperiment" (
    id VARCHAR PRIMARY KEY,
    name VARCHAR NOT NULL,
    description TEXT,
    experiment_type VARCHAR NOT NULL CHECK (experiment_type IN ('SYSTEM_PROMPT', 'CONVERSATION_FLOW')),
    status VARCHAR NOT NULL CHECK (status IN ('DRAFT', 'SCHEDULED', 'ACTIVE', 'COMPLETED', 'CANCELLED')),
    
    system_prompt_a_id VARCHAR,
    system_prompt_b_id VARCHAR,
    conversation_template_a JSON,
    conversation_template_b JSON,
    
    target_config JSON NOT NULL,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    target_conversations INTEGER,
    
    conversations_a INTEGER DEFAULT 0,
    conversations_b INTEGER DEFAULT 0,
    evaluator_configs TEXT[],
    
    created_by VARCHAR NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Conversation experiment assignments
CREATE TABLE "ConversationExperimentAssignment" (
    id VARCHAR PRIMARY KEY,
    experiment_id VARCHAR NOT NULL,
    user_id VARCHAR NOT NULL,
    workflow_id VARCHAR NOT NULL,
    student_workflow_id VARCHAR,
    variant VARCHAR NOT NULL CHECK (variant IN ('A', 'B')),
    assigned_at TIMESTAMP DEFAULT NOW(),
    
    FOREIGN KEY (experiment_id) REFERENCES "ConversationExperiment"(id),
    FOREIGN KEY (user_id) REFERENCES "User"(id),
    FOREIGN KEY (workflow_id) REFERENCES "Workflow"(id)
);

-- Enhanced evaluator runs with experiment context
ALTER TABLE "ChatEvaluatorRun" 
ADD COLUMN experiment_id VARCHAR,
ADD COLUMN experiment_variant VARCHAR;
```

### Phase 2: Core A/B Testing Logic

**Key Components**:
1. **Experiment Management API** - CRUD operations for conversation experiments
2. **Assignment Service** - Real-time variant assignment for new conversations  
3. **Historical Analysis Service** - Batch processing of existing conversations
4. **Evaluator Enhancement** - Experiment-aware evaluation runs

### Phase 3: Administrative Interface

**Admin Web Interface** (`/web`):
1. **Experiment Dashboard** - Create, manage, and monitor conversation experiments
2. **Results Analytics** - Statistical analysis of A/B test results
3. **Conversation Browser** - View conversations by experiment and variant
4. **Evaluator Configuration** - Manage which evaluators run for experiments

### Phase 4: Advanced Features

1. **Statistical Significance Testing** - Automated experiment conclusion
2. **Multi-variate Testing** - Support for A/B/C/D testing
3. **Sequential Testing** - Adaptive sample sizes based on results
4. **Segment Analysis** - Results breakdown by student demographics

## Benefits & Use Cases

### System Prompt A/B Testing
- **Use Case**: Test more empathetic vs. direct counselor prompts
- **Process**: 
  1. Create experiment with two AddieConfig variants
  2. System automatically assigns new conversations to variants
  3. Evaluators score conversations on engagement, helpfulness, completion rates
  4. Statistical analysis determines winning prompt

### Conversation Flow A/B Testing  
- **Use Case**: Test different conversation templates/scripts
- **Process**:
  1. Define alternative conversation flows or templates
  2. Assign students to experience different conversation structures
  3. Measure completion rates, satisfaction, and outcomes
  4. Optimize conversation design based on data

### Historical Analysis
- **Use Case**: Analyze past 30 days of conversations retrospectively
- **Process**:
  1. Create experiment targeting specific student populations
  2. Randomly assign historical conversations to variants
  3. Run evaluators on existing conversation data
  4. Compare hypothetical outcomes without affecting real students

## Safety & Ethical Considerations

### Student Experience Protection
- **Gradual Rollout**: Start with small percentages (5-10%) of conversations
- **Quality Monitoring**: Real-time alerts if variant B performs significantly worse
- **Automatic Fallback**: Switch all traffic to control if issues detected
- **Opt-out Mechanism**: Exclude students who request not to participate

### Data Privacy
- **Anonymized Analysis**: Student identifiers removed from evaluator results where possible
- **Consent Compliance**: Ensure A/B testing falls within existing user agreements
- **Data Retention**: Clear policies on experiment data storage and deletion

### Statistical Rigor
- **Power Analysis**: Proper sample size calculations before experiments
- **Multiple Testing Correction**: Adjust for multiple comparisons when running many evaluators
- **Bias Prevention**: Randomization validation and balance checks

## Success Metrics

### Primary Metrics
- **Conversation Completion Rate**: Percentage of conversations reaching successful conclusion
- **Student Engagement Score**: Average evaluator scores for engagement
- **Response Quality**: Evaluator scores for helpfulness and appropriateness

### Secondary Metrics  
- **Time to Completion**: How quickly students complete workflows
- **Follow-up Engagement**: Likelihood of students starting new conversations
- **Counselor Efficiency**: Time saved per successful student interaction

## Technical Requirements

### Performance Considerations
- **Evaluator Scaling**: Queue-based processing to handle batch evaluations
- **Database Optimization**: Indexes on experiment assignment queries
- **Real-time Assignment**: Sub-100ms experiment variant lookup

### Monitoring & Alerting
- **Experiment Health**: Dashboards showing A/B balance and performance
- **Quality Alerts**: Notifications when variants underperform significantly  
- **System Load**: Monitor impact of evaluator runs on system performance

## Future Enhancements

### Advanced Analytics
- **Cohort Analysis**: Track long-term student outcomes by experiment variant
- **Causal Inference**: Move beyond correlation to understand true impact
- **Personalization**: Use experiment results to customize experiences per student

### Integration Opportunities
- **CI/CD Pipeline**: Automated A/B testing for prompt changes
- **External Analytics**: Export results to data warehouse for deeper analysis
- **ML-Driven Optimization**: Use results to train better conversation models

---

This design provides a comprehensive foundation for real student A/B testing while maintaining the quality and safety standards required for educational technology. The phased implementation approach allows for gradual rollout and iterative improvement based on real-world usage.