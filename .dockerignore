# Version control
.git
.gitignore

# Python cache files
**/__pycache__
**/*.pyc
**/*.pyo
**/*.pyd
**/.cache
.pytest_cache/
.coverage
.mypy_cache
*.egg-info

# Development environments
.venv/
venv/
env/
.env
.cache
.devcontainer
.idea
.vscode

# JavaScript/TypeScript
node_modules
web
counsellor_web
student_web

# Explicitly include package files for Prisma
!package.json
!package-lock.json

# Build artifacts
dist/
build/

# Data and logs
**/*.log
logs/
data/

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# Documentation
docs/
*.md
!README.md
LICENSE

# Local development configurations
**/*.local
