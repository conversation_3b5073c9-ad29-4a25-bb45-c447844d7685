# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Addie is an educational counseling platform with multiple components:
- Backend API (Python/FastAPI) - `/addie`
- Counselor Web Interface - `/counsellor_web`
- Student Web Interface - `/student_web`
- Admin Web Interface - `/web`

The system uses an agent-based architecture with Lang<PERSON>hain and LangGraph for conversational AI capabilities.

## Environment Setup

### Environment Variables
The system requires numerous environment variables. Main categories include:
- Database credentials (PostgreSQL)
- Redis configuration
- Qdrant vector database
- OpenAI API keys
- Twi<PERSON> for phone verification

### Database Setup

```bash
# Set application directory
export APP_DIR=$PWD

# Start PostgreSQL and Redis
docker stack deploy -c docker-compose-data.yml addie_data

# Load data (if needed)
psql -h $HOST_IP -U $POSTGRES_USER -d $POSTGRES_DB -p $DATABASE_PORT < ~/addie_qa.sql
```

### API Server

```bash
# Run the API server with auto-reload
fastapi run addie/api/app.py --reload

# Or use Docker
docker stack deploy -c docker-compose-api-dev.yml addie_api
```

### Web Interfaces

Each web interface (counsellor_web, student_web, web) has similar commands:

```bash
# Navigate to the web directory
cd counsellor_web  # (or student_web, web)

# Install dependencies
pnpm install

# Development mode
pnpm run dev

# Build for production
export NODE_ENV=production
pnpm run build

# Start production server
pnpm run start
```

## Common Commands

### Python Backend

```bash
# Install dependencies
pip install -r requirements.txt
pip install -e .

# Run tests
# We use pytest with pytest-xdist for parallel testing (recommended)
./scripts/test-pytest.sh  # All tests in parallel
pytest tests/ -n 4 -v  # Direct pytest parallel execution
pytest tests/test_evaluator.py -n 4 -v  # Run evaluator tests
pytest tests/test_evaluator.py tests/test_voice_agent.py -n 4  # Specific files
PARALLEL_PROCESSES=8 ./scripts/test-pytest.sh  # Custom worker count

# Alternative: Sequential execution with nose2 (for debugging)
nose2  # Run all tests sequentially
nose2 tests.test_evaluator  # Run specific test file  
nose2 tests.test_evaluator.TestClass.test_function  # Run specific test

# Database management
prisma db push  # Sync schema with database
prisma migrate dev  # Create a new migration
prisma generate  # Generate Prisma client

# Run Streamlit app
streamlit run addie/streamlit/streamlit_app.py

# Engagement tracking utilities
python scripts/backfill_engagement_events.py --help  # View backfill options
python scripts/backfill_engagement_events.py --dry-run --limit 100  # Test backfill
python scripts/backfill_engagement_events.py  # Run full backfill
```

### Next.js Web Apps

```bash
# Lint code
pnpm run lint

# Run tests
pnpm run test

# Preview build locally
pnpm run preview
```

## Architecture Overview

### Core Components

1. **Agent System** (`addie/agent/`):
   - Uses LangGraph for agent state management
   - Handles conversation flows and tool calling

2. **Counselor Agent** (`addie/counselor_agent/`):
   - Specialized agent for counselor interactions
   - Manages counselor context and conversation history

3. **Student Agent** (`addie/student_agent/`):
   - Manages student context and interactions
   - Handles student-specific workflows

4. **Data Models** (`addie/data_model/`):
   - Defines structured data types (questions, workflows, etc.)
   - Handles context generation and summarization

5. **API** (`addie/api/`):
   - FastAPI endpoints for client applications
   - Handles authentication and data access

6. **Tasks** (`addie/tasks/`):
   - Celery background tasks
   - Handles asynchronous operations like embedding chat messages

7. **Engagement Tracking System** (`addie/services/`):
   - Tracks student engagement across multiple channels (SMS, web, email, voice)
   - Automatically categorizes students into engagement tiers (Active, At-Risk, Dormant)
   - Provides analytics and admin dashboard functionality
   - Uses existing ReminderTemplate table for tier-based messaging

### Database Structure

The system uses PostgreSQL with a comprehensive schema including:
- User management (students, counselors, admins)
- Conversation workflows and steps
- Questionnaires and responses
- Agent configurations and prompts
- Schools and teacher comments
- Engagement tracking (events, student tiers, analytics)
- Reminder templates for tier-based messaging

### Web Applications

Each web application (counselor, student, admin) is built with:
- Next.js 15.x
- TypeScript
- Prisma ORM for database access
- Authentication via NextAuth
- UI components with Radix UI and Tailwind

## Development Workflow

1. Make changes to the codebase
2. Run appropriate tests
3. For database changes, create migrations
4. For web UI changes, verify with `pnpm run dev`
5. Build and deploy as needed

## Important Notes

- The system uses OpenAI models by default, configured in `addie/settings.py`
- Qdrant is used for vector search of conversation history
- Redis is used for task queue and caching
- Celery handles background processing
- Next.js server components handle most UI rendering
- When making git commits, do not include Claude or Anthropic branding in commit messages

## Troubleshooting

### Authentication Issues

#### 401 Errors on /experiments Route

**Problem**: Getting HTTP 401 errors when accessing the admin web `/experiments` route.

**Common Causes & Solutions**:

1. **Missing or Incorrect API Key Configuration**
   - Check that `ADDIE_API_KEY` environment variable is set in both web app and FastAPI backend
   - Verify the API key values match between frontend and backend
   - Check logs for detailed authentication debugging (added logging in `web/actions/index.ts` and `addie/api/app.py`)

2. **Database Schema Issues**
   - If getting errors about missing `enabled` column, run database migrations
   - The `enabled` column was renamed from `enabled_user` in migration `20250103155030_rename_enabled_user_to_enabled_and_add_user_activity`
   - If schema drift occurs, manually apply the column rename: `ALTER TABLE "User" RENAME COLUMN "enabled_user" TO "enabled";`

3. **API Connectivity Issues**
   - Verify FastAPI server is running on the correct host/port
   - Check `ADDIE_API_HOST` environment variable configuration
   - Ensure CORS is properly configured for web app domains

**Debugging Steps**:
1. Check browser console logs for detailed API request/response information
2. Check FastAPI server logs for authentication attempts and failures
3. Verify environment variables are loaded correctly
4. Test API endpoint directly with curl/Postman

#### Database Migration Issues

**Problem**: Schema drift between Prisma migrations and actual database state.

**Solution**: 
1. Check current migration status: `npx prisma migrate status`
2. If drift detected, manually apply missing schema changes
3. Use `npx prisma db execute` to run SQL commands safely
4. Regenerate Prisma client: `npx prisma generate`

**Note**: Always run migrations from the base project directory, not from individual web app directories.