#!/bin/bash

# Script for authenticating with Google Cloud using a key file
# Usage: ./scripts/gcloud-auth.sh

# Variables
KEY_FILE="$HOME/.accounts/addie-gcloud.key"
PROJECT_ID="addie-440119"
REGION="us-central1"

# Check if the key file exists
if [ ! -f "$KEY_FILE" ]; then
  echo "Error: Key file not found at $KEY_FILE"
  exit 1
fi

# Authenticate with Google Cloud
echo "Authenticating with Google Cloud using key file at $KEY_FILE"
gcloud auth activate-service-account --key-file="$KEY_FILE"

# Configure Docker to use Google Container Registry
echo "Configuring Docker for Google Container Registry"
gcloud auth configure-docker "$REGION-docker.pkg.dev" --quiet

# Set project
echo "Setting project to $PROJECT_ID"
gcloud config set project "$PROJECT_ID"

echo "Authentication complete. You can now build and push Docker images locally."
