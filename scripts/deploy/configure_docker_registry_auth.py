#!/usr/bin/env python3
"""
Configure Docker registry authentication for Google Artifact Registry.

This script creates a Docker config.json file with credentials for 
Google Artifact Registry using a service account key.
"""
import os
import json
import argparse
import subprocess
import tempfile
import base64
import shutil
from pathlib import Path

import alog


def run_command(cmd, shell=False, check=True, capture_output=True):
    """Run a command and return its output."""
    try:
        result = subprocess.run(
            cmd,
            shell=shell,
            check=check,
            capture_output=capture_output,
            text=True
        )
        return result.stdout.strip() if capture_output else ""
    except subprocess.CalledProcessError as e:
        alog.error(f"Command failed: {e}")
        alog.error(f"Stderr: {e.stderr}")
        raise


def configure_docker_auth_remote(host, key_content, use_ssh=True):
    """
    Configure Docker authentication on a remote host.
    
    Args:
        host: Remote host address (IP or hostname)
        key_content: Google service account key JSON content
        use_ssh: Whether to use SSH for remote execution
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create a temporary file with the key content
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as temp_key_file:
            temp_key_file.write(key_content.encode('utf-8'))
            temp_key_file_path = temp_key_file.name
        
        # Create a shell script to run on the remote host
        with tempfile.NamedTemporaryFile(suffix='.sh', delete=False) as script_file:
            script_file.write(b"""#!/bin/bash
set -e

# Validate input
if [ $# -ne 1 ]; then
  echo "Usage: $0 <service-account-key-file>"
  exit 1
fi

KEY_FILE="$1"

# Make sure the key file exists
if [ ! -f "$KEY_FILE" ]; then
  echo "Error: Service account key file not found: $KEY_FILE"
  exit 1
fi

# Ensure the Docker config directory exists
DOCKER_CONFIG_DIR="$HOME/.docker"
mkdir -p "$DOCKER_CONFIG_DIR"

# Read the current Docker config if it exists
CONFIG_FILE="$DOCKER_CONFIG_DIR/config.json"
if [ -f "$CONFIG_FILE" ]; then
  CONFIG=$(cat "$CONFIG_FILE")
else
  CONFIG='{}'
fi

# Get a short-lived access token from the service account key
ACCESS_TOKEN=$(gcloud auth activate-service-account --key-file="$KEY_FILE" --quiet 2>/dev/null && gcloud auth print-access-token)

if [ -z "$ACCESS_TOKEN" ]; then
  echo "Error: Failed to obtain access token"
  exit 1
fi

# Encode the access token for Docker config
AUTH=$(echo -n "oauth2accesstoken:$ACCESS_TOKEN" | base64 -w 0)

# Update the Docker config with the authentication token
# Using Python for reliable JSON manipulation
python3 -c "
import json, sys

config = json.loads('$CONFIG')
if 'auths' not in config:
    config['auths'] = {}

# Add authentication for Artifact Registry domains
config['auths']['us-docker.pkg.dev'] = {
    'auth': '$AUTH',
    'email': '<EMAIL>'
}
config['auths']['gcr.io'] = {
    'auth': '$AUTH',
    'email': '<EMAIL>'
}
config['auths']['us.gcr.io'] = {
    'auth': '$AUTH',
    'email': '<EMAIL>'
}

# Write the updated config
with open('$CONFIG_FILE', 'w') as f:
    json.dump(config, f, indent=2)
"

# Verify the config
if [ -f "$CONFIG_FILE" ]; then
  echo "Docker registry authentication configured successfully"
  exit 0
else
  echo "Error: Failed to create Docker config file"
  exit 1
fi
""")
            script_file_path = script_file.name
        
        # Make the script executable
        os.chmod(script_file_path, 0o755)
        
        if use_ssh:
            # Copy the key file to the remote host
            remote_key_path = f"/tmp/gcloud-key-{os.path.basename(temp_key_file_path)}"
            scp_key_cmd = [
                "scp", 
                "-o", "StrictHostKeyChecking=no",
                temp_key_file_path, 
                f"root@{host}:{remote_key_path}"
            ]
            run_command(scp_key_cmd)
            
            # Copy the script to the remote host
            remote_script_path = f"/tmp/docker-auth-{os.path.basename(script_file_path)}"
            scp_script_cmd = [
                "scp", 
                "-o", "StrictHostKeyChecking=no",
                script_file_path, 
                f"root@{host}:{remote_script_path}"
            ]
            run_command(scp_script_cmd)
            
            # Run the script on the remote host
            ssh_cmd = [
                "ssh",
                "-o", "StrictHostKeyChecking=no",
                f"root@{host}",
                f"{remote_script_path} {remote_key_path}"
            ]
            output = run_command(ssh_cmd)
            
            # Clean up remote files
            cleanup_cmd = [
                "ssh",
                "-o", "StrictHostKeyChecking=no",
                f"root@{host}",
                f"rm -f {remote_script_path} {remote_key_path}"
            ]
            run_command(cleanup_cmd)
            
            alog.info(f"Docker authentication configured on remote host {host}: {output}")
        else:
            # For local execution (useful for testing)
            cmd = [script_file_path, temp_key_file_path]
            output = run_command(cmd)
            alog.info(f"Docker authentication configured locally: {output}")
            
        # Clean up temporary files
        os.unlink(temp_key_file_path)
        os.unlink(script_file_path)
        
        return True
    
    except Exception as e:
        alog.error(f"Error configuring Docker authentication: {e}")
        # Clean up any remaining temporary files
        for path in [temp_key_file_path, script_file_path]:
            if 'path' in locals() and os.path.exists(path):
                os.unlink(path)
        return False


def configure_docker_auth(args):
    """Configure Docker authentication based on command line arguments."""
    # Get the service account key content
    if args.key_file:
        with open(args.key_file, 'r') as f:
            key_content = f.read()
    elif args.key_env_var and args.key_env_var in os.environ:
        key_content = os.environ[args.key_env_var]
    else:
        alog.error("No service account key provided")
        return False
    
    # Configure authentication
    if args.remote_host:
        return configure_docker_auth_remote(args.remote_host, key_content, use_ssh=args.use_ssh)
    else:
        # Configure local Docker authentication (for testing)
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as temp_file:
            temp_file.write(key_content.encode('utf-8'))
            temp_path = temp_file.name
        
        try:
            # Activate the service account
            run_command(["gcloud", "auth", "activate-service-account", "--key-file", temp_path])
            
            # Get access token
            token = run_command(["gcloud", "auth", "print-access-token"])
            
            # Create Docker config directory if it doesn't exist
            docker_config_dir = os.path.expanduser("~/.docker")
            os.makedirs(docker_config_dir, exist_ok=True)
            
            # Load existing config if it exists
            config_path = os.path.join(docker_config_dir, "config.json")
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    try:
                        config = json.load(f)
                    except json.JSONDecodeError:
                        config = {}
            else:
                config = {}
            
            # Ensure auths section exists
            if 'auths' not in config:
                config['auths'] = {}
            
            # Add Docker registry authentication
            auth = base64.b64encode(f"oauth2accesstoken:{token}".encode()).decode()
            for registry in ["us-docker.pkg.dev", "gcr.io", "us.gcr.io"]:
                config['auths'][registry] = {
                    'auth': auth,
                    'email': '<EMAIL>'
                }
            
            # Write updated config
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2)
            
            alog.info("Docker authentication configured successfully")
            return True
            
        except Exception as e:
            alog.error(f"Error configuring Docker authentication: {e}")
            return False
            
        finally:
            # Clean up
            if os.path.exists(temp_path):
                os.unlink(temp_path)


def main():
    """Main function for command line execution."""
    parser = argparse.ArgumentParser(description='Configure Docker registry authentication')
    parser.add_argument('--remote-host', help='Remote host IP or hostname')
    parser.add_argument('--key-file', help='Path to service account key file')
    parser.add_argument('--key-env-var', help='Environment variable containing service account key')
    parser.add_argument('--use-ssh', action='store_true', help='Use SSH for remote execution')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    
    args = parser.parse_args()
    
    if args.verbose:
        alog.set_level('DEBUG')
    
    success = configure_docker_auth(args)
    return 0 if success else 1


if __name__ == '__main__':
    exit(main())
