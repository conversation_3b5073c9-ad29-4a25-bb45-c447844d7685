#!/usr/bin/env python3
"""
Docker client with TLS support for GetAddie deployment tool.
Handles secure connections to Docker daemons.
"""
from docker import errors
from docker.models.networks import Network
from docker.models.services import Service
from typing import Optional

import alog
import atexit
import docker
import os
import shutil
import subprocess
import tempfile

# Import error classes directly for convenience
APIError = errors.APIError
DockerException = errors.DockerException

# Disable urllib3 warnings for insecure requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class DockerClient:
    """Wrapper for Docker SDK client with TLS support."""
    
    def __init__(self, docker_host: str = None, docker_cert_path: str = None,
                 tls_verify: bool = True, verbose: bool = False):
        """
        Initialize Docker client.
        
        Args:
            docker_host: Docker daemon host:port
            docker_cert_path: Path to TLS certificates
            tls_verify: Whether to verify TLS certificates
            verbose: Whether to print verbose output
        """
        self.docker_host = docker_host
        self.docker_cert_path = docker_cert_path
        self.tls_verify = tls_verify
        
        # Extract host IP from docker_host for registry auth
        self.host_ip = None
        if docker_host and "tcp://" in docker_host:
            # Extract hostname/IP from tcp://hostname:port
            self.host_ip = docker_host.replace("tcp://", "").split(":")[0]
        self.verbose = verbose
        self.temp_cert_dir = None  # Will store path to temp cert directory if needed
        self.client = self._create_client()
        
    def _add_tls_options_to_cmd(self, cmd: list) -> bool:
        """
        Add TLS options to a docker command if needed.
        
        Args:
            cmd: Docker command list to modify
            
        Returns:
            bool: True if TLS options were added, False otherwise
        """
        # Only relevant for TCP connections
        if not self.docker_host or "tcp://" not in self.docker_host:
            return False
            
        cert_files_found = False
        
        # Try using the temporary cert directory first (from env vars)
        if self.temp_cert_dir:
            ca_path = os.path.join(self.temp_cert_dir, "ca.pem")
            cert_path = os.path.join(self.temp_cert_dir, "cert.pem")
            key_path = os.path.join(self.temp_cert_dir, "key.pem")
            
            if all(os.path.isfile(p) for p in [ca_path, cert_path, key_path]):
                if self.verbose:
                    alog.info(f"Using certificate files from temp directory: {self.temp_cert_dir}")
                cert_files_found = True
                if not self.tls_verify:
                    cmd.append("--tls")
                    cmd.append("--tlsverify=false")
                else:
                    cmd.append("--tlsverify")
                    
                cmd.extend([
                    f"--tlscacert={ca_path}",
                    f"--tlscert={cert_path}",
                    f"--tlskey={key_path}"
                ])
        
        # Fallback to the configured cert path if temp certs aren't available
        if not cert_files_found and self.docker_cert_path:
            ca_path = os.path.join(self.docker_cert_path, "ca.pem")
            cert_path = os.path.join(self.docker_cert_path, "cert.pem")
            key_path = os.path.join(self.docker_cert_path, "key.pem")
            
            if all(os.path.isfile(p) for p in [ca_path, cert_path, key_path]):
                if self.verbose:
                    alog.info(f"Using certificate files from config path: {self.docker_cert_path}")
                cert_files_found = True
                if not self.tls_verify:
                    cmd.append("--tls")
                    cmd.append("--tlsverify=false")
                else:
                    cmd.append("--tlsverify")
                    
                cmd.extend([
                    f"--tlscacert={ca_path}",
                    f"--tlscert={cert_path}",
                    f"--tlskey={key_path}"
                ])
        
        if not cert_files_found:
            alog.warning("No valid certificate files found for Docker TLS connection")
            
        return cert_files_found
        
    def _create_client(self) -> docker.DockerClient:
        """
        Create a Docker client with the appropriate configuration.
        
        Returns:
            docker.DockerClient: Configured Docker client
        """
        kwargs = {}
        
        # Expand user directory for cert path if needed
        if self.docker_cert_path:
            self.docker_cert_path = os.path.expanduser(self.docker_cert_path)
        
        if self.docker_host:
            # Add scheme if not present
            if "://" not in self.docker_host:
                self.docker_host = f"tcp://{self.docker_host}"
            kwargs["base_url"] = self.docker_host
            
            # For remote hosts, always use TLS for security
            # Check for TLS connection options
            if "tcp://" in self.docker_host:
                # Option 1: Use certificate files from provided path
                if self.docker_cert_path and os.path.isdir(self.docker_cert_path):
                    # Check if certificate files exist
                    ca_path = os.path.join(self.docker_cert_path, "ca.pem")
                    cert_path = os.path.join(self.docker_cert_path, "cert.pem")
                    key_path = os.path.join(self.docker_cert_path, "key.pem")
                    
                    cert_files_exist = all(os.path.isfile(p) for p in [ca_path, cert_path, key_path])
                    
                    if cert_files_exist:
                        if self.verbose:
                            alog.info(f"Using certificates from {self.docker_cert_path}")
                            
                        # Create TLS config
                        tls_config = docker.tls.TLSConfig(
                            verify=self.tls_verify,
                            ca_cert=ca_path,
                            client_cert=(cert_path, key_path)
                        )
                        kwargs["tls"] = tls_config
                    else:
                        alog.warning(f"Certificate files not found in {self.docker_cert_path}")
                        # Will fall through to environment variable check
                
                # Option 2: Use certificate data from environment variables
                if "tls" not in kwargs:
                    # Check if environment variables are set
                    docker_ca_cert = os.environ.get("DOCKER_CA_CERT")
                    docker_cert = os.environ.get("DOCKER_CERT")
                    docker_key = os.environ.get("DOCKER_KEY")
                    
                    if docker_ca_cert and docker_cert and docker_key:
                        if self.verbose:
                            alog.info("Using certificates from environment variables")
                        
                        # Create temporary files for certificates
                        import tempfile
                        
                        self.temp_cert_dir = tempfile.mkdtemp(prefix="docker-certs-")
                        try:
                            # Write certificate content to temporary files
                            ca_path = os.path.join(self.temp_cert_dir, "ca.pem")
                            cert_path = os.path.join(self.temp_cert_dir, "cert.pem")
                            key_path = os.path.join(self.temp_cert_dir, "key.pem")
                            
                            with open(ca_path, "w") as f:
                                f.write(docker_ca_cert)
                            with open(cert_path, "w") as f:
                                f.write(docker_cert)
                            with open(key_path, "w") as f:
                                f.write(docker_key)
                            
                            # Create TLS config
                            tls_config = docker.tls.TLSConfig(
                                verify=self.tls_verify,
                                ca_cert=ca_path,
                                client_cert=(cert_path, key_path)
                            )
                            kwargs["tls"] = tls_config
                            
                            # Clean up temporary files after client creation
                            # We'll register a cleanup function to be called when the object is deleted
                            import atexit
                            atexit.register(lambda: shutil.rmtree(self.temp_cert_dir, ignore_errors=True))
                        except Exception as e:
                            alog.error(f"Failed to create temporary certificate files: {e}")
                            shutil.rmtree(self.temp_cert_dir, ignore_errors=True)
                            self.temp_cert_dir = None
                
                if "tls" not in kwargs:
                    alog.warning("No valid TLS certificates found for remote Docker host")
                    if self.verbose and os.path.isdir(self.docker_cert_path):
                        alog.info(f"Files in {self.docker_cert_path}: {os.listdir(self.docker_cert_path)}")
                    # For security, abort if we can't secure the connection
                    raise Exception(f"Required certificate files not found in {self.docker_cert_path}. For secure remote connections, we need ca.pem, cert.pem, and key.pem.")
            elif "unix://" in self.docker_host:
                # Unix socket connections don't need TLS
                if self.verbose:
                    alog.info("Using Unix socket connection (TLS not needed)")
            else:
                # For local connections, no TLS is fine
                if self.verbose:
                    alog.info("Using local connection (TLS not needed)")
        else:
            # No Docker host specified, using local Docker socket
            if self.verbose:
                alog.info("No Docker host specified, using local Docker socket")

                
        if self.verbose:
            alog.info(f"Creating Docker client with: {kwargs}")
            
        try:
            return docker.DockerClient(**kwargs)
        except DockerException as e:
            alog.error(f"Failed to create Docker client: {e}")
            raise
            
    def test_connection(self) -> bool:
        """
        Test connection to Docker daemon.
        
        Returns:
            bool: True if connection is successful, False otherwise
        """
        try:
            info = self.client.info()
            if self.verbose:
                alog.info(f"Connected to Docker daemon: {info.get('Name', 'Unknown')}")
            return True
        except DockerException as e:
            alog.error(f"Failed to connect to Docker daemon: {e}")
            return False
            
    def get_network(self, name: str) -> Optional[Network]:
        """
        Get network by name.
        
        Args:
            name: Network name
            
        Returns:
            Network or None: The network if found, None otherwise
        """
        try:
            networks = self.client.networks.list(names=[name])
            return networks[0] if networks else None
        except DockerException as e:
            alog.error(f"Error fetching network {name}: {e}")
            return None
            
    def create_network(self, name: str, driver: str = "overlay", attachable: bool = True) -> Optional[Network]:
        """
        Create a Docker network.
        
        Args:
            name: Network name
            driver: Network driver ('overlay' or 'bridge')
            attachable: Whether the network is attachable (for overlay driver)
            
        Returns:
            Network or None: Created network or None on failure
        """
        try:
            options = {"driver": driver, "attachable": attachable} if driver == "overlay" else {"driver": driver}
            
            if self.verbose:
                alog.info(f"Creating network {name} with options: {options}")
                
            return self.client.networks.create(name, **options)
        except APIError as e:
            alog.error(f"Failed to create network {name} with driver {driver}: {e}")
            return None
            
    def remove_network(self, name: str) -> bool:
        """
        Remove a Docker network.
        
        Args:
            name: Network name
            
        Returns:
            bool: True if removal was successful, False otherwise
        """
        network = self.get_network(name)
        if not network:
            alog.info(f"Network {name} does not exist, no need to remove")
            return True
            
        try:
            # For a clean removal, first disconnect all containers
            if self.verbose:
                alog.info(f"Checking containers connected to network {name}")
                
            try:
                network_data = network.attrs
                containers = network_data.get("Containers", {}).keys()
                
                for container_id in containers:
                    alog.info(f"Disconnecting container {container_id[:12]} from network {name}")
                    try:
                        network.disconnect(container_id)
                    except APIError as e:
                        alog.warning(f"Failed to disconnect container {container_id[:12]}: {e}")
            except APIError as e:
                alog.warning(f"Could not inspect network {name} for containers: {e}")
                
            # Now remove the network
            network.remove()
            alog.info(f"Successfully removed network {name}")
            return True
        except APIError as e:
            alog.warning(f"Failed to remove network {name}: {e}")
            return False
            
    def ensure_network(self, name: str, force_remove: bool = False) -> bool:
        """
        Ensure a Docker network exists with the correct configuration.
        
        Args:
            name: Network name
            force_remove: Whether to force remove the network if it exists
            
        Returns:
            bool: True if network is ready, False otherwise
        """
        # Remove network if requested
        if force_remove:
            alog.info(f"Force removing network {name}")
            self.remove_network(name)
            
        # First check if network exists in the listing
        try:
            networks = self.client.networks.list(names=[name])
            network_exists = len(networks) > 0
        except APIError as e:
            # Even if listing fails, continue with deployment
            alog.warning(f"Could not list networks: {e}")
            alog.warning("Continuing with deployment despite network listing failure")
            network_exists = False
        
        # If network exists in list, try to validate it
        if network_exists:
            network = networks[0]
            alog.info(f"Network {name} already exists")
            
            try:
                # Validate network configuration
                network_data = network.attrs
                driver = network_data.get("Driver", "")
                
                # Different behavior based on driver
                if driver == "overlay":
                    attachable = network_data.get("Attachable", False)
                    if not attachable:
                        alog.warning(f"Network {name} is not attachable, which may cause connectivity issues")
                        # Don't fail - just warn and continue
                        
                    alog.info(f"Network {name} is properly configured (overlay, attachable={attachable})")
                    return True
                elif driver == "bridge":
                    alog.info(f"Network {name} is using bridge driver (suitable for single-node deployments)")
                    return True
                else:
                    alog.warning(f"Network {name} is using an uncommon driver: {driver}")
                    return True
            except APIError as e:
                # More permissive approach - if network exists but we can't inspect it fully
                # (could be permissions), still try to use it
                alog.warning(f"Could not inspect network {name} details: {e}")
                
                try:
                    # Just do a basic listing to see if it's there
                    all_networks = self.client.networks.list()
                    network_names = [n.name for n in all_networks]
                    
                    if name in network_names:
                        alog.info(f"✅ Network '{name}' exists but cannot be inspected in detail")
                        alog.info(f"✅ Will use existing {name} network (limited inspection capability)")
                        return True
                except Exception as e2:
                    alog.warning(f"Error when trying to list all networks: {e2}")
            
        # Network doesn't exist or couldn't be validated, create it
        alog.info(f"Network {name} does not exist or needs recreation, creating...")
        
        # Remove any existing network with this name first (recovery mechanism)
        try:
            self.client.networks.prune()
            alog.info("Pruned unused networks")
        except Exception as e:
            alog.warning(f"Could not prune networks: {e}")
            
        # Create with first overlay driver, then fallback to bridge if that fails
        for attempt in range(2):  # Two attempts: first overlay, then bridge
            try:
                driver = "overlay" if attempt == 0 else "bridge"
                alog.info(f"Attempting to create network {name} with {driver} driver")
                
                # Create the network
                if driver == "overlay":
                    network = self.create_network(name, driver="overlay", attachable=True)
                else:
                    network = self.create_network(name, driver="bridge")
                    
                if network:
                    alog.info(f"✅ Successfully created network {name} with {driver} driver")
                    return True
            except Exception as e:
                alog.warning(f"Failed to create network with {driver} driver: {e}")
                # Continue to the next attempt
        
        # After all attempts, do one final check if it exists
        try:
            networks = self.client.networks.list(names=[name])
            if networks:
                alog.info(f"✅ Network {name} now exists after creation attempts")
                return True
        except Exception as e:
            alog.warning(f"Final network existence check failed: {e}")
            
        # We'll still consider this a success even if we couldn't verify or create
        # the network. Docker Swarm may handle this internally.        
        alog.warning(f"⚠️ Could not definitively create or verify network {name}")
        alog.warning("Proceeding with deployment anyway - Docker Swarm may handle this internally")
        return True
                
    def print_services_summary(self) -> None:
        """
        Print a summary of all Docker Swarm services currently running.
        """
        try:
            print("\n=== DOCKER SWARM SERVICES SUMMARY ===")
            services = self.client.services.list()
            
            # Sort services by name for consistent display
            services.sort(key=lambda s: s.name)
            
            # Print header
            print(f"{'SERVICE NAME':<30} {'ID':<12} {'TASKS':<8} {'STARTED':<15}")
            print(f"{'-'*30:<30} {'-'*12:<12} {'-'*8:<8} {'-'*15:<15}")
            
            from datetime import datetime, timezone
            now = datetime.now(timezone.utc)
            
            # Create API client for low-level API access
            api_client = self.client.api
            
            for service in services:
                # Get service tasks to count those that are actually running
                # Use the API client instead of direct tasks access
                tasks = api_client.tasks(filters={'service': service.id})
                # Tasks don't need explicit sorting for our use case
                running_tasks = len([t for t in tasks if t['Status']['State'] == 'running'])
                
                # Get the most recent task start time instead of service creation time
                time_ago = 'unknown'
                if tasks:
                    # Find the most recently started running task
                    running_tasks_list = [t for t in tasks if t['Status']['State'] == 'running']
                    if running_tasks_list:
                        # Sort tasks by start time (most recent first)
                        running_tasks_list.sort(key=lambda t: t['Status'].get('Timestamp', ''), reverse=True)
                        started_at = running_tasks_list[0]['Status'].get('Timestamp', '')
                        
                        if started_at:
                            try:
                                # Parse ISO format timestamp
                                started_time = datetime.fromisoformat(started_at.replace('Z', '+00:00'))
                                delta = now - started_time
                                # Format with more precision (minutes)
                                if delta.days > 30:
                                    time_ago = f"{delta.days} days ago"
                                elif delta.days > 0:
                                    time_ago = f"{delta.days} days, {delta.seconds//3600} hrs ago"
                                elif delta.seconds >= 3600:
                                    hours = delta.seconds//3600
                                    minutes = (delta.seconds % 3600)//60
                                    time_ago = f"{hours} hrs, {minutes} mins ago"
                                else:
                                    time_ago = f"{delta.seconds//60} mins ago"
                            except (ValueError, TypeError):
                                time_ago = 'unknown'
                
                # Print service name, short ID, number of running tasks, and creation time
                print(f"{service.name:<30} {service.short_id:<12} {running_tasks:<8} {time_ago:<15}")
            
            print("=== END OF SERVICES SUMMARY ===")
        except DockerException as e:
            print(f"Failed to list services: {e}")
            
    def get_service(self, name: str) -> Optional[Service]:
        """
        Get service by name.
        
        Args:
            name: Service name
            
        Returns:
            Service or None: The service if found, None otherwise
        """
        try:
            services = self.client.services.list(filters={"name": name})
            return services[0] if services else None
        except DockerException as e:
            alog.error(f"Failed to get service {name}: {e}")
            return None
            

            
    def pull_image(self, image_name: str) -> bool:
        """
        Pull a Docker image.
        
        Args:
            image_name: Image name to pull
            
        Returns:
            bool: True if image was successfully pulled, False otherwise
        """
        try:
            if self.verbose:
                alog.info(f"Pulling Docker image: {image_name}")
            
            # For Google Artifact Registry images, try to ensure authentication is set up
            if any(registry in image_name for registry in ["gcr.io", "us-docker.pkg.dev"]):
                # Configure Google Artifact Registry authentication
                self._configure_artifact_registry_auth()
            
            # The Docker SDK will use the standard authentication methods
            self.client.images.pull(image_name)
            return True
        except docker.errors.ImageNotFound:
            alog.error(f"Image not found: {image_name}")
            return False
        except docker.errors.APIError as e:
            alog.error(f"Error pulling image {image_name}: {e}")
            
            # If this is an authentication error with Google Artifact Registry
            # and we have direct CLI access, try using the CLI as a fallback
            if ("unauthorized" in str(e).lower() or "denied" in str(e).lower()) and \
               any(registry in image_name for registry in ["gcr.io", "us-docker.pkg.dev"]):
                
                alog.info(f"Trying CLI fallback for pulling {image_name}...")
                from . import docker_command
                
                success, _ = docker_command.run_docker_command(
                    host=self.docker_host,
                    command_args=["pull", image_name],
                    verbose=self.verbose,
                    skip_tls_verify=(not self.tls_verify)
                )
                
                return success
            return False
            
    def update_service(self, service_id: str, **options) -> bool:
        """
        Update a Docker service with new parameters.
        
        Args:
            service_id: ID of the service to update
            **options: Service update options (e.g., image="new_image:tag")
            
        Returns:
            bool: True if update was successful, False otherwise
        """
        if not options:
            alog.warning("No update options provided, skipping service update")
            return True
        
        # Get the service by ID
        service = self.client.services.get(service_id)
        
        if self.verbose:
            alog.info(f"Updating service {service.name} ({service_id[:12]}) with options: {options}")
            
        # If we have an image, handle it directly
        if 'image' in options:
            # Get the current image for logging
            try:
                current_image = service.attrs.get('Spec', {}).get('TaskTemplate', {}).get('ContainerSpec', {}).get('Image', 'unknown')
                alog.info(f"Changing image from {current_image} to {options['image']}")
            except (KeyError, AttributeError):
                pass
        
        # Update the service with the image parameter and force the update
        print(f"Changing image from {current_image} to {options.get('image')}")
        # Add force_update=True to force the service to restart with the new image
        # even if the image tag hasn't changed
        service.update(image=options.get('image'), force_update=True)
        
        # Log success
        alog.info(f"Service {service.name} updated successfully")
        return True
            
    def remove_service(self, name: str) -> bool:
        """
        Remove a Docker service.
        
        Args:
            name: Service name
            
        Returns:
            bool: True if removal was successful, False otherwise
        """
        service = self.get_service(name)
        if not service:
            alog.info(f"Service {name} does not exist, no need to remove")
            return True
            
        try:
            service.remove()
            alog.info(f"Successfully removed service {name}")
            
            # Wait a moment for the service to be fully removed
            import time
            time.sleep(5)
            return True
        except APIError as e:
            alog.error(f"Failed to remove service {name}: {e}")
            return False
            
    def remove_stack(self, stack_name: str) -> bool:
        """
        Remove a Docker stack.
        
        Args:
            stack_name: Stack name to remove
            
        Returns:
            bool: True if removal was successful, False otherwise
        """
        try:
            # Import our specialized Docker command helper
            from . import docker_command
            
            # Use our specialized Docker command helper which properly handles certificates
            return docker_command.remove_stack(
                host=self.docker_host,
                stack_name=stack_name,
                verbose=self.verbose,
                skip_tls_verify=(not self.tls_verify)  # Pass the inverse of tls_verify
            )
        except Exception as e:
            alog.error(f"Error during stack removal: {e}")
            return False
            
    def _ensure_cert_files_from_env(self):
        """
        Create temporary certificate files from environment variables if they don't exist yet.
        This helps CLI commands access the certificates.
        
        Returns:
            tuple: (ca_path, cert_path, key_path) if certificates were created, None otherwise
        """
        # If we already have created temp cert files, just return them
        if self.temp_cert_dir:
            ca_path = os.path.join(self.temp_cert_dir, "ca.pem")
            cert_path = os.path.join(self.temp_cert_dir, "cert.pem")
            key_path = os.path.join(self.temp_cert_dir, "key.pem")
            
            if all(os.path.isfile(p) for p in [ca_path, cert_path, key_path]):
                return (ca_path, cert_path, key_path)
        
        # Check if environment variables are set
        docker_ca_cert = os.environ.get("DOCKER_CA_CERT")
        docker_cert = os.environ.get("DOCKER_CERT")
        docker_key = os.environ.get("DOCKER_KEY")
        
        if docker_ca_cert and docker_cert and docker_key:
            # Create temporary directory for certificates
            self.temp_cert_dir = tempfile.mkdtemp(prefix="docker-certs-")
            ca_path = os.path.join(self.temp_cert_dir, "ca.pem")
            cert_path = os.path.join(self.temp_cert_dir, "cert.pem")
            key_path = os.path.join(self.temp_cert_dir, "key.pem")
            
            try:
                # Write certificate contents to files
                with open(ca_path, "w") as f:
                    f.write(docker_ca_cert)
                with open(cert_path, "w") as f:
                    f.write(docker_cert)
                with open(key_path, "w") as f:
                    f.write(docker_key)
                
                # Register cleanup to remove temp files on exit
                # Use a local reference to avoid closure problems
                temp_dir = self.temp_cert_dir
                atexit.register(lambda td=temp_dir: shutil.rmtree(td, ignore_errors=True))
                
                if self.verbose:
                    alog.info(f"Created temporary certificate files in {self.temp_cert_dir}")
                
                return (ca_path, cert_path, key_path)
            except Exception as e:
                alog.error(f"Failed to create temporary certificate files: {e}")
                if self.temp_cert_dir:
                    shutil.rmtree(self.temp_cert_dir, ignore_errors=True)
                    self.temp_cert_dir = None
                
        return None
        
    def _ensure_cert_files_from_env(self):
        """
        Create temporary files from Docker certificate environment variables if they exist.
        
        Returns:
            tuple: (ca_path, cert_path, key_path) if created, None otherwise
        """
        # If we already have created temp cert files, just return them
        if self.temp_cert_dir:
            ca_path = os.path.join(self.temp_cert_dir, "ca.pem")
            cert_path = os.path.join(self.temp_cert_dir, "cert.pem")
            key_path = os.path.join(self.temp_cert_dir, "key.pem")
            
            if all(os.path.isfile(p) for p in [ca_path, cert_path, key_path]):
                return (ca_path, cert_path, key_path)
        
        # Check for Docker certificate environment variables
        docker_ca_cert = os.environ.get("DOCKER_CA_CERT")
        docker_cert = os.environ.get("DOCKER_CERT")
        docker_key = os.environ.get("DOCKER_KEY")
        
        if docker_ca_cert and docker_cert and docker_key:
            # Create temporary directory for certificates
            self.temp_cert_dir = tempfile.mkdtemp(prefix="docker-certs-")
            ca_path = os.path.join(self.temp_cert_dir, "ca.pem")
            cert_path = os.path.join(self.temp_cert_dir, "cert.pem")
            key_path = os.path.join(self.temp_cert_dir, "key.pem")
            
            try:
                # Write certificate contents to files
                with open(ca_path, "w") as f:
                    f.write(docker_ca_cert)
                with open(cert_path, "w") as f:
                    f.write(docker_cert)
                with open(key_path, "w") as f:
                    f.write(docker_key)
                
                # Set up cleanup to remove temp files when the process exits
                # Lambda function captures 'self.temp_cert_dir' variable directly
                # rather than trying to access 'shutil' in the closure
                temp_dir = self.temp_cert_dir  # Create a local reference for the lambda
                atexit.register(lambda _dir=temp_dir: shutil.rmtree(_dir, ignore_errors=True))
                
                if self.verbose:
                    alog.info("Successfully created temporary certificate files from environment variables")
                
                return (ca_path, cert_path, key_path)
            except Exception as e:
                alog.error(f"Error creating temporary certificate files: {e}")
                if self.temp_cert_dir:
                    shutil.rmtree(self.temp_cert_dir)
                    self.temp_cert_dir = None
                
        return None
        
    def _configure_artifact_registry_auth(self):
        """
        Configure authentication for Google Artifact Registry in the current environment.
        This allows Docker to pull images from private Google Artifact Registry repositories.
        
        Returns:
            bool: True if configuration was successful, False otherwise
        """
        try:
            import os
            import subprocess
            import json
            import tempfile
            import base64
            
            # Before pulling images, let's get an access token for Google Artifact Registry
            alog.info("Obtaining access token for Google Artifact Registry")
            
            # First, try to use the gcloud CLI which is usually available in GitHub Actions
            try:
                # Check if we're already authenticated with gcloud
                token_process = subprocess.run(
                    ["gcloud", "auth", "print-access-token"],
                    capture_output=True,
                    text=True,
                    check=False
                )
                
                # If successful, we can use this token
                if token_process.returncode == 0:
                    access_token = token_process.stdout.strip()
                    alog.info("✅ Successfully obtained Google access token from gcloud")
                    
                    # CHANGED: Using environment variable approach as primary method
                    # This works better in CI/CD environments, especially with remote Docker hosts
                    alog.info("Configuring direct access token for registry operations via environment variable")
                    os.environ["REGISTRY_ACCESS_TOKEN"] = access_token
                    
                    # Also configure authentication locally for potential local operations
                    # This doesn't affect remote Docker operations but helps with debugging
                    try:
                        # Run gcloud auth configure-docker to setup local credentials
                        configure_process = subprocess.run(
                            ["gcloud", "auth", "configure-docker", "us-docker.pkg.dev", "--quiet"],
                            capture_output=True,
                            text=True,
                            check=False
                        )
                        
                        if configure_process.returncode == 0:
                            alog.info("Configured local Docker credential helper for Google Artifact Registry")
                    except Exception as e:
                        alog.debug(f"Failed to configure local Docker auth (non-critical): {e}")
                        
                    # Only try Docker login if we're connecting to a remote Docker host
                    # This is a secondary authentication method and may not work in CI/CD
                    if self.docker_host and self.verbose:
                        alog.info(f"Using REGISTRY_ACCESS_TOKEN environment variable for authentication")
                        alog.info(f"Remote Docker host will use the token for direct API authentication")
                    
                    return True
            except Exception as e:
                alog.warning(f"Couldn't authenticate with gcloud: {e}")
                
            # If we get here, we couldn't authenticate using gcloud
            alog.warning("Failed to authenticate with Google Artifact Registry. Images may fail to pull.")
            return False
        except Exception as e:
            alog.error(f"Error configuring Google Artifact Registry authentication: {e}")
            return False
    
    def _prepare_docker_command(self):
        """
        Prepare the Docker command with proper TLS options.
        This handles both Docker host and certificate configuration.
        
        Returns:
            list: Docker command with appropriate host and TLS options
        """
        # Basic command
        cmd = ["docker"]
        
        # Add Docker host if specified
        if self.docker_host:
            cmd.extend(["--host", self.docker_host])
        
        # Add TLS options if needed (for TCP connections)
        if self.docker_host and "tcp://" in self.docker_host:
            # Get cert paths - first from temp dir (env vars), then from docker_cert_path
            ca_path = None
            cert_path = None
            key_path = None
            
            # Check for temporary certificates first (from env vars)
            if self.temp_cert_dir:
                ca_path = os.path.join(self.temp_cert_dir, "ca.pem")
                cert_path = os.path.join(self.temp_cert_dir, "cert.pem")
                key_path = os.path.join(self.temp_cert_dir, "key.pem")
                
                if not all(os.path.isfile(p) for p in [ca_path, cert_path, key_path]):
                    # Reset if any are missing
                    ca_path = None
                    cert_path = None
                    key_path = None
            
            # If no temp certs, check docker_cert_path
            if ca_path is None and self.docker_cert_path:
                cert_dir = os.path.expanduser(self.docker_cert_path)
                ca_path = os.path.join(cert_dir, "ca.pem")
                cert_path = os.path.join(cert_dir, "cert.pem")
                key_path = os.path.join(cert_dir, "key.pem")
                
                if not all(os.path.isfile(p) for p in [ca_path, cert_path, key_path]):
                    # Reset if any are missing
                    ca_path = None
                    cert_path = None
                    key_path = None
            
            # If we found valid certificate files, add TLS options
            if ca_path and cert_path and key_path:
                if not self.tls_verify:
                    cmd.append("--tls")
                    cmd.append("--tlsverify=false")
                else:
                    cmd.append("--tlsverify")
                    
                cmd.extend([
                    f"--tlscacert={ca_path}",
                    f"--tlscert={cert_path}",
                    f"--tlskey={key_path}"
                ])
        
        # No valid certificates found, warn and return base command
        alog.warning("No valid Docker certificate files found for TLS connection")
        return cmd
            
    def deploy_stack(self, stack_name: str, compose_file: str, env: dict = None, force: bool = False) -> bool:
        """
        Deploy a Docker stack.
        
        Args:
            stack_name: Stack name
            compose_file: Path to Docker Compose file
            env: Environment variables for the deployment
            
        Returns:
            bool: True if deployment was successful, False otherwise
        """
        import os
        
        try:
            # Import our specialized Docker command helper
            from . import docker_command
            
            # Expand env vars in compose file path
            compose_file = os.path.expanduser(compose_file)
            
            if not os.path.isfile(compose_file):
                alog.error(f"Compose file {compose_file} does not exist")
                return False
                
            # Configure Google Artifact Registry authentication if we're using a remote host
            if self.host_ip:
                self._configure_artifact_registry_auth()
                
            # Use our specialized Docker command helper which properly handles certificates
            return docker_command.deploy_stack(
                host=self.docker_host,
                stack_name=stack_name,
                compose_file=compose_file,
                env=env,
                verbose=self.verbose,
                skip_tls_verify=(not self.tls_verify)  # Pass the inverse of tls_verify
            )
                
        except Exception as e:
            alog.error(f"Error during stack deployment: {e}")
            return False
