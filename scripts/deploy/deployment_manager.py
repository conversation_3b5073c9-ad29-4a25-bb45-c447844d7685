#!/usr/bin/env python3
"""
Deployment Manager for GetAddie deployment tool.
Manages the deployment of Celery and web services.
"""

import os
import subprocess
import yaml
from typing import List, Set, Dict

import alog
from addie import settings
from scripts.deploy.base_deployer import (
    Ce<PERSON>yDeployer,
    Ad<PERSON>WebDeployer,
    StudentWebDeployer,
    CounsellorWebDeployer,
    ApiWebDeployer,
    WebServiceUpdater,
)
from scripts.deploy.docker_client import DockerClient


class DeploymentManager:
    """Manages deployment of all services."""

    def __init__(
        self,
        env,
        docker_host=None,
        docker_cert_path=None,
        tls_verify=True,
        dry_run=False,
        verbose=False,
        force_remove_network=False,
    ):
        """Initialize deployment manager with common settings for all deployers."""
        if verbose:
            alog.info(f"TLS verification: {tls_verify}")

        # Create a shared Docker client for all deployers
        self.docker_client = DockerClient(
            docker_host=docker_host,
            docker_cert_path=docker_cert_path,
            tls_verify=tls_verify,
            verbose=verbose,
        )

        # Initialize deployers with shared Docker client
        self.celery_deployer = CeleryDeployer(
            env=env,
            docker_client=self.docker_client,
            force_remove_network=force_remove_network,
            dry_run=dry_run,
            verbose=verbose,
        )

        # Web service deployers
        self.web_deployers = {
            "admin": AdminWebDeployer(
                env=env,
                docker_client=self.docker_client,
                dry_run=dry_run,
                verbose=verbose,
            ),
            "student": StudentWebDeployer(
                env=env,
                docker_client=self.docker_client,
                dry_run=dry_run,
                verbose=verbose,
            ),
            "counsellor": CounsellorWebDeployer(
                env=env,
                docker_client=self.docker_client,
                dry_run=dry_run,
                verbose=verbose,
            ),
            "api": ApiWebDeployer(
                env=env,
                docker_client=self.docker_client,
                dry_run=dry_run,
                verbose=verbose,
            ),
        }

        self.dry_run = dry_run
        self.verbose = verbose

    def validate_connection(self) -> bool:
        """Validate connection to Docker daemon."""
        return self.docker_client.test_connection()

    def detect_changes(self, base_commit: str = None, branch: str = None) -> Set[str]:
        """
        Detect which services have changes since the specified commit.

        Args:
            base_commit: Base commit to compare against
            branch: Branch name

        Returns:
            Set of service names that have changes
        """
        changes = set()

        if self.dry_run:
            alog.info("Dry run, would detect changes here")
            return {"celery", "admin", "student", "counsellor", "api"}

        if base_commit:
            try:
                # Get list of changed files since base_commit
                cmd = ["git", "diff", "--name-only", base_commit]

                if branch:
                    cmd.append(branch)

                process = subprocess.run(
                    cmd,
                    check=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    cwd=settings.PROJECT_ROOT,
                )

                changed_files = process.stdout.strip().split("\n")

                # Determine which services changed
                for file in changed_files:
                    if file.startswith("addie/"):
                        if "api" not in changes:
                            changes.add("api")
                        if "celery" not in changes:
                            changes.add("celery")
                    elif file.startswith("web/admin/") and "admin" not in changes:
                        changes.add("admin")
                    elif file.startswith("web/student/") and "student" not in changes:
                        changes.add("student")
                    elif (
                        file.startswith("web/counsellor/")
                        and "counsellor" not in changes
                    ):
                        changes.add("counsellor")
                    elif file.startswith("web/nginx-secure/"):
                        # Nginx changes affect all web services
                        changes.update(["admin", "student", "counsellor"])
            except subprocess.SubprocessError as e:
                alog.error(f"Error detecting changes: {e}")
                # In case of error, deploy all services
                return {"celery", "admin", "student", "counsellor", "api"}
        else:
            # No base commit specified, deploy all services
            changes = {"celery", "admin", "student", "counsellor", "api"}

        return changes

    def print_services(self):
        """Print Docker services summary."""
        self.docker_client.print_services_summary()

    def deploy_all(self, env: str, force: bool = False) -> bool:
        """
        Deploy all services to the specified environment.

        Args:
            env: Deployment environment
            force: Whether to force deployment

        Returns:
            bool: True if all deployments succeeded, False otherwise
        """
        alog.info(f"Deploying all services to {env} environment")

        # First deploy Celery (as other services may depend on it)
        celery_success = self.celery_deployer.deploy(force)

        if not celery_success and not force:
            alog.error("Celery deployment failed, not deploying web services")
            return False

        # Then deploy web services using deploy_services
        # Get all web service names from the web_deployers dictionary
        web_services = list(self.web_deployers.keys())
        web_success = self.deploy_services(services=web_services, env=env, force=force)

        success = celery_success and web_success

        alog.info(
            f"Deployment {'successful' if success else 'failed'}: "
            + f"Celery {'✅' if celery_success else '❌'}, "
            + f"Web {'✅' if web_success else '❌'}"
        )
        
        # Run Prisma migrations after services are deployed
        if success or force:
            self._run_prisma_migrations(env)

        # Print services summary
        self.print_services()

        return success

    def _run_prisma_migrations(self, env: str) -> bool:
        """
        Run Prisma database migrations after service deployment.
        
        Args:
            env: Deployment environment
            
        Returns:
            bool: True if migration succeeded, False otherwise
        """
        alog.info("Running Prisma database migrations...")
        
        if self.dry_run:
            alog.info("[DRY RUN] Would run 'prisma migrate deploy' command")
            return True
        
        try:
            # Set the correct environment variables based on the deployment environment
            migration_env = os.environ.copy()
            
            # Determine the database URL based on environment
            if env == "prod":
                # Production database URL
                db_url = os.environ.get("DATABASE_URL")
                if not db_url:
                    alog.warning("No DATABASE_URL found in environment, migrations may fail")
            else:
                # Development database URL
                db_url = os.environ.get("DATABASE_URL")
                if not db_url:
                    alog.warning("No DATABASE_URL found in environment, migrations may fail")
            
            # Set DATABASE_URL if we have it
            if db_url:
                migration_env["DATABASE_URL"] = db_url
            
            # Run the prisma migrate deploy command
            process = subprocess.run(
                "prisma migrate deploy",
                shell=True,
                cwd=settings.PROJECT_ROOT,
                env=migration_env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Check if the command was successful
            if process.returncode == 0:
                alog.info("✅ Prisma database migrations completed successfully")
                alog.debug(process.stdout)
                return True
            else:
                alog.error(f"❌ Failed to run Prisma migrations: {process.stderr}")
                alog.debug(process.stdout)
                return False
                
        except Exception as e:
            alog.error(f"❌ Error running Prisma migrations: {str(e)}")
            return False
            
    def _get_service_images(self) -> Dict[str, str]:
        """
        Extract service images from the nginx-secure Docker Compose files.
        
        Returns:
            Dict[str, str]: Dictionary mapping service names to image names
        """
        images = {}
        compose_files = [
            os.path.join(settings.PROJECT_ROOT, "web/nginx-secure/docker-compose.yml"),
            os.path.join(settings.PROJECT_ROOT, "counsellor_web/nginx-secure/docker-compose.yml")
        ]
        
        for compose_file in compose_files:
            if not os.path.isfile(compose_file):
                alog.warning(f"Compose file not found: {compose_file}")
                continue
                
            try:
                with open(compose_file, 'r') as f:
                    compose_data = yaml.safe_load(f)
                    
                if not compose_data or 'services' not in compose_data:
                    alog.warning(f"No services found in {compose_file}")
                    continue
                    
                for service_name, service_config in compose_data['services'].items():
                    # Skip proxy and letsencrypt services
                    if service_name in ['proxy', 'letsencrypt']:
                        continue
                        
                    if 'image' in service_config:
                        # Extract the base service name (admin, student, counsellor, etc.)
                        base_name = service_name
                        if service_name.startswith('dev-'):
                            base_name = service_name[4:]  # Remove 'dev-' prefix
                        if service_name.startswith('qa-'):
                            base_name = service_name[3:]  # Remove 'qa-' prefix
                            
                        images[base_name] = service_config['image']
                        alog.info(f"Found image for {base_name}: {service_config['image']}")
            except Exception as e:
                alog.error(f"Error reading compose file {compose_file}: {e}")
                
        return images

    def deploy_services(
        self, services: List[str], env: str, force: bool = False
    ) -> bool:
        """
        Update Docker images for the specified services instead of deploying.

        Args:
            services: List of service names to update
            env: Deployment environment
            force: Whether to force update

        Returns:
            bool: True if all specified updates succeeded, False otherwise
        """
        alog.info(f"Updating images for services {', '.join(services)} in {env} environment")
        
        # Initialize success variable
        success = True
        
        # Deploy Celery if requested (Celery deployment is unchanged)
        if "celery" in services:
            alog.info("Deploying Celery service")
            if not self.celery_deployer.deploy(force):
                success = False
                alog.error("Celery deployment failed")
        
        # For web services, we need to get the images from the compose file
        service_images = self._get_service_images()
        
        if not service_images:
            alog.error("No service images found in nginx-secure docker-compose files")
            return False

        # Update web services if requested
        for name in [s for s in services if s != "celery"]:
            if name in self.web_deployers:
                # Use existing deployer instances
                deployer = self.web_deployers[name]
                
                if name in service_images:
                    # Update the service with the image from the compose file
                    base_image = service_images[name]
                    
                    # Adjust image tag based on environment
                    # For prod environment, use the base image without a tag suffix
                    # For dev environment, ensure we're using the :dev tag
                    if env == "dev":
                        # If the image doesn't have a tag, add :dev tag
                        if ":" not in base_image:
                            image = f"{base_image}:dev"
                        else:
                            image = base_image
                    else:  # prod environment
                        # Remove any tag to use the base image as is
                        if ":" in base_image:
                            image = base_image.split(":")[0]
                        else:
                            image = base_image
                    
                    alog.info(f"Updating {name} service with image: {image}")
                    
                    # Pull the image before updating to ensure we're using the latest version
                    # if not self.dry_run:
                    alog.info(f"Pulling image: {image}")
                    if not self.docker_client.pull_image(image):
                        alog.warning(f"Failed to pull image: {image}, continuing with update anyway")
                
                    # If we're in dev environment, we need to adjust the service name to include 'dev-' prefix
                    if env == "dev":
                        # Create a custom service name for dev environment
                        service_name = f"dev-{name}"
                        alog.info(f"Using dev service name: {service_name} for {name}")
                        # Update service with the pulled image
                        if not deployer.update(service_image=image, service_name=service_name, force=force):
                            success = False
                            alog.error(f"{name} service image update failed")
                    else:
                        # Use standard service name for non-dev environments
                        # Update service with the pulled image
                        if not deployer.update(service_image=image, force=force):
                            success = False
                            alog.error(f"{name} service image update failed")
                else:
                    alog.error(f"No image found for service: {name}")
                    success = False
            else:
                alog.error(f"Unknown service: {name}")
                success = False

        alog.info(f"Image updates {'succeeded' if success else 'failed'}")

        # Print services summary
        self.print_services()

        return success
