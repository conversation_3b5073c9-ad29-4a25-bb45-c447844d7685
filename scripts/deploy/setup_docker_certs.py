#!/usr/bin/env python3
"""
Setup Docker certificates from environment variables.
This ensures Docker CLI commands can use the certificates properly.
"""
import os
import shutil
import sys
import tempfile

def setup_docker_certs():
    """
    Create Docker certificate files from environment variables if they exist.
    Returns the directory containing the certificates or None if not created.
    """
    # Check if environment variables are set
    docker_ca_cert = os.environ.get("DOCKER_CA_CERT")
    docker_cert = os.environ.get("DOCKER_CERT")
    docker_key = os.environ.get("DOCKER_KEY")
    
    if not (docker_ca_cert and docker_cert and docker_key):
        print("❌ Docker certificate environment variables not found")
        return None
        
    # Create a temporary directory for certificates
    temp_dir = tempfile.mkdtemp(prefix="docker-certs-")
    
    try:
        # Write certificate contents to files
        ca_path = os.path.join(temp_dir, "ca.pem")
        cert_path = os.path.join(temp_dir, "cert.pem")
        key_path = os.path.join(temp_dir, "key.pem")
        
        with open(ca_path, "w") as f:
            f.write(docker_ca_cert)
        with open(cert_path, "w") as f:
            f.write(docker_cert)
        with open(key_path, "w") as f:
            f.write(docker_key)
            
        print(f"✅ Created Docker certificate files in {temp_dir}")
        
        # Set DOCKER_CERT_PATH environment variable to point to the directory
        os.environ["DOCKER_CERT_PATH"] = temp_dir
        print(f"✅ Set DOCKER_CERT_PATH={temp_dir}")
        
        return temp_dir
    except Exception as e:
        print(f"❌ Error creating certificate files: {e}")
        shutil.rmtree(temp_dir, ignore_errors=True)
        return None

if __name__ == "__main__":
    print("🔐 Setting up Docker certificates from environment variables...")
    cert_dir = setup_docker_certs()
    
    if cert_dir:
        print(f"✅ Certificate setup complete. Files are in {cert_dir}")
        # Print export command for shell usage
        print(f"To use these certificates in a shell, run:")
        print(f"export DOCKER_CERT_PATH={cert_dir}")
        sys.exit(0)
    else:
        print("❌ Certificate setup failed")
        sys.exit(1)
