#!/usr/bin/env python3
"""
Utility script to verify Docker certificate handling.
This helps debug issues with TLS certificates in the Docker client.
"""
import os
import sys
import subprocess
import tempfile

def verify_docker_certs():
    """
    Check if Docker certificates are available and usable.
    Creates temporary files from environment variables if needed.
    """
    print("🔍 Verifying Docker certificate configuration...")
    
    # Check for certificate environment variables
    docker_ca_cert = os.environ.get("DOCKER_CA_CERT")
    docker_cert = os.environ.get("DOCKER_CERT")
    docker_key = os.environ.get("DOCKER_KEY")
    
    if docker_ca_cert and docker_cert and docker_key:
        print("✅ Found Docker certificate environment variables")
        
        # Create temporary directory for certificates
        temp_dir = tempfile.mkdtemp(prefix="docker-certs-")
        print(f"📁 Created temporary directory: {temp_dir}")
        
        try:
            # Write certificate contents to files
            ca_path = os.path.join(temp_dir, "ca.pem")
            cert_path = os.path.join(temp_dir, "cert.pem")
            key_path = os.path.join(temp_dir, "key.pem")
            
            with open(ca_path, "w") as f:
                f.write(docker_ca_cert)
            with open(cert_path, "w") as f:
                f.write(docker_cert)
            with open(key_path, "w") as f:
                f.write(docker_key)
                
            print("✅ Written certificate contents to temporary files")
            
            # Test Docker command with the certificates
            docker_host = os.environ.get("DOCKER_HOST", "")
            
            if not docker_host:
                print("⚠️ DOCKER_HOST environment variable not set")
                # Check if we can find a host in related environment variables
                if os.environ.get("HOST_IP"):
                    docker_host = f"tcp://{os.environ.get('HOST_IP')}:2376"
                    print(f"🔄 Using HOST_IP to create Docker host: {docker_host}")
                else:
                    print("❌ Unable to determine Docker host")
                    return False
                
            cmd = ["docker", "--host", docker_host,
                   "--tlsverify",
                   f"--tlscacert={ca_path}",
                   f"--tlscert={cert_path}",
                   f"--tlskey={key_path}",
                   "info"]
                   
            print(f"🧪 Testing Docker connection with command: {' '.join(cmd)}")
            
            # Run the command
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            stdout, stderr = process.communicate()
            
            if process.returncode == 0:
                print("✅ Docker connection successful using certificate files")
                print("\nDocker info output:")
                print("-" * 40)
                print(stdout.decode('utf-8').strip())
                print("-" * 40)
                return True
            else:
                print("❌ Docker connection failed with certificate files")
                print("\nError output:")
                print("-" * 40)
                print(stderr.decode('utf-8').strip())
                print("-" * 40)
                return False
                
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
        finally:
            # Cleanup
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
            print(f"🧹 Cleaned up temporary directory: {temp_dir}")
    else:
        print("❌ Docker certificate environment variables not found")
        missing = []
        if not docker_ca_cert:
            missing.append("DOCKER_CA_CERT")
        if not docker_cert:
            missing.append("DOCKER_CERT")
        if not docker_key:
            missing.append("DOCKER_KEY")
        print(f"⚠️ Missing variables: {', '.join(missing)}")
        return False

if __name__ == "__main__":
    print("🐳 Docker Certificate Verifier")
    print("=" * 40)
    
    success = verify_docker_certs()
    
    if success:
        print("\n✅ Verification completed successfully")
        sys.exit(0)
    else:
        print("\n❌ Verification failed")
        sys.exit(1)
