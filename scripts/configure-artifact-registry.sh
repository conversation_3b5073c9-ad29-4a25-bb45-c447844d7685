#!/bin/bash
# Configure Docker to use Google Artifact Registry credentials
# This script creates a Docker configuration that can authenticate with
# Google Artifact Registry using the gcloud credential helper.

set -e

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "Error: gcloud command not found, please install Google Cloud SDK"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Error: docker command not found, please install Docker"
    exit 1
fi

# Directories
DOCKER_CONFIG_DIR="$HOME/.docker"
REMOTE_HOST=${1:-""}

# Create Docker config directory if it doesn't exist
mkdir -p "$DOCKER_CONFIG_DIR"

# Check if we have an existing config.json
CONFIG_FILE="$DOCKER_CONFIG_DIR/config.json"
if [ -f "$CONFIG_FILE" ]; then
    echo "Found existing Docker config at $CONFIG_FILE, making a backup..."
    cp "$CONFIG_FILE" "$CONFIG_FILE.bak"
    echo "Backup created at $CONFIG_FILE.bak"
fi

# Get credentials from gcloud
echo "Obtaining access token from gcloud..."
ACCESS_TOKEN=$(gcloud auth print-access-token)

if [ -z "$ACCESS_TOKEN" ]; then
    echo "Error: Failed to get access token from gcloud. Please run 'gcloud auth login' first."
    exit 1
fi

echo "Access token obtained successfully."

# Create a new config with Google Artifact Registry credentials
echo "Creating Docker config with Google Artifact Registry credentials..."

# Base64 encode the credentials (username:password format)
ENCODED_TOKEN=$(echo -n "oauth2accesstoken:$ACCESS_TOKEN" | base64)

# Create or update config.json
CONFIG_CONTENT='{
  "auths": {
    "us-docker.pkg.dev": {
      "auth": "'$ENCODED_TOKEN'"
    },
    "gcr.io": {
      "auth": "'$ENCODED_TOKEN'"
    },
    "us.gcr.io": {
      "auth": "'$ENCODED_TOKEN'"
    }
  }
}'

echo "$CONFIG_CONTENT" > "$CONFIG_FILE"
echo "Docker configuration created successfully at $CONFIG_FILE"

# If a remote host is specified, try to copy the config there
if [ -n "$REMOTE_HOST" ]; then
    echo "Attempting to copy configuration to remote host $REMOTE_HOST..."
    
    # Try to create the directory on the remote host
    ssh -o StrictHostKeyChecking=no root@$REMOTE_HOST "mkdir -p /root/.docker" || {
        echo "Error: Could not create .docker directory on remote host."
        echo "Make sure you have SSH access to the remote host."
        exit 1
    }
    
    # Copy the config file to the remote host
    scp -o StrictHostKeyChecking=no "$CONFIG_FILE" root@$REMOTE_HOST:/root/.docker/config.json || {
        echo "Error: Could not copy config.json to remote host."
        exit 1
    }
    
    echo "Configuration successfully copied to remote host $REMOTE_HOST"
fi

echo "Google Artifact Registry authentication is now configured for Docker."
echo "Note: The access token is short-lived (typically 1 hour). You may need to run this script again if authentication fails."
