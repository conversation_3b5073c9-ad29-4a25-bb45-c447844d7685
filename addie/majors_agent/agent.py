#!/usr/bin/env python
"""
Majors Agent - AI-powered academic major recommendations

This module provides an AI agent that uses LLM to analyze student OCEAN personality scores
and provide intelligent, nuanced major recommendations with detailed reasoning.
"""

import alog
from typing import List, Dict, Any
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from addie.majors_agent.tools import MAJOR_MATCHING_TOOLS
from addie import settings



def parse_llm_response(response_content: str) -> Dict[str, any]:
    """
    Parse the LLM response to extract both summary and major recommendations.

    Args:
        response_content: Raw response content from the LLM

    Returns:
        Dictionary with 'summary' and 'recommendations' keys

    Raises:
        Exception: If parsing fails
    """
    try:
        alog.debug(f"Parsing LLM response: {response_content[:200]}...")

        import json
        import re

        # Extract summary from <summary> tags
        summary_match = re.search(r'<summary>(.*?)</summary>', response_content, re.DOTALL)
        summary = summary_match.group(1).strip() if summary_match else None

        # Extract recommendations from <recommendations> tags
        recommendations_match = re.search(r'<recommendations>(.*?)</recommendations>', response_content, re.DOTALL)

        if recommendations_match:
            recommendations_content = recommendations_match.group(1).strip()
            # Look for JSON array in the recommendations content
            json_match = re.search(r'\[[\s\S]*\]', recommendations_content)

            if json_match:
                json_str = json_match.group(0)
                recommendations = json.loads(json_str)

                # Validate the structure
                validated_recommendations = []
                for rec in recommendations:
                    if isinstance(rec, dict) and 'major' in rec and 'match_percent' in rec:
                        validated_recommendations.append({
                            'major': str(rec['major']),
                            'match_percent': float(rec['match_percent'])
                        })

                if len(validated_recommendations) > 0:
                    # Sort by match percentage (highest first)
                    validated_recommendations.sort(key=lambda x: x['match_percent'], reverse=True)
                    alog.info(f"Successfully parsed {len(validated_recommendations)} recommendations and summary from LLM")
                    return {
                        'summary': summary,
                        'recommendations': validated_recommendations
                    }

        # If structured parsing fails, try to extract recommendations manually
        alog.warning("Structured parsing failed, attempting manual extraction")
        manual_recommendations = extract_recommendations_manually(response_content)

        # Try to extract summary from the general content if no structured tags
        if not summary:
            # Look for summary-like content at the beginning
            lines = response_content.split('\n')
            summary_lines = []
            for line in lines[:10]:  # Check first 10 lines
                line = line.strip()
                if line and not line.startswith('[') and not line.startswith('{') and len(line) > 20:
                    summary_lines.append(line)
                    if len(summary_lines) >= 2:  # Take first 2 meaningful lines
                        break
            summary = ' '.join(summary_lines) if summary_lines else "AI-generated major recommendations based on OCEAN personality analysis."

        return {
            'summary': summary,
            'recommendations': manual_recommendations
        }

    except Exception as e:
        alog.error(f"Error parsing LLM response: {str(e)}")
        raise Exception(f"Failed to parse LLM response: {str(e)}")


def extract_recommendations_manually(response_content: str) -> List[Dict[str, any]]:
    """
    Manually extract recommendations if JSON parsing fails.

    Args:
        response_content: Raw response content from the LLM

    Returns:
        List of dictionaries with major recommendations
    """
    import re

    recommendations = []

    # Define the 7 majors we expect
    expected_majors = [
        "Medicine", "Psychology", "Law", "Economics",
        "Political Science", "Science", "Arts"
    ]

    # Try to find major names and percentages in the text
    for major in expected_majors:
        # Look for patterns like "Computer Science: 85.5%" or "Computer Science - 85.5%"
        pattern = rf"{re.escape(major)}[\s\-:]*(\d+\.?\d*)%?"
        match = re.search(pattern, response_content, re.IGNORECASE)

        if match:
            percentage = float(match.group(1))
            recommendations.append({
                'major': major,
                'match_percent': percentage
            })

    # If we found some recommendations, sort and return
    if recommendations:
        recommendations.sort(key=lambda x: x['match_percent'], reverse=True)
        alog.info(f"Manually extracted {len(recommendations)} recommendations")
        return recommendations

    # If all else fails, return a fallback response
    alog.error("Could not extract any recommendations from LLM response")
    raise Exception("Failed to extract recommendations from LLM response")


def majors_agent(student_id: str) -> Dict[str, Any]:
    """
    AI-powered major recommendations agent using research-based tools.

    This agent uses LangChain tools to analyze OCEAN personality scores
    and generate evidence-based major recommendations.

    Args:
        student_id: The student's unique identifier

    Returns:
        Dictionary containing recommendations and summary

    Raises:
        Exception: If unable to generate recommendations
    """
    try:
        alog.info(f"Starting tool-enabled major recommendations for student {student_id}")

        # System prompt that instructs the AI to use tools
        system_prompt = """You are an expert academic advisor specializing in personality-based major recommendations.

Your task is to analyze a student's OCEAN (Big Five) personality scores and provide personalized major recommendations.

You have access to these research-based tools:
1. ocean_score_analysis - Get detailed OCEAN personality analysis for the student
2. research_based_major_matching - Calculate major matches using empirical research data
3. major_correlation_lookup - Look up specific correlation data for any major

INSTRUCTIONS:
1. ALWAYS start by using ocean_score_analysis to understand the student's personality profile
2. ALWAYS use research_based_major_matching with their OCEAN scores to get evidence-based recommendations
3. Optionally use major_correlation_lookup for any majors you want to understand better
4. Synthesize all information to provide comprehensive recommendations

IMPORTANT: You MUST use both ocean_score_analysis AND research_based_major_matching tools. Do not skip either tool.

Provide your final response in this exact format:

<summary>
[Write a 2-3 sentence summary explaining the student's personality strengths and how they align with their top recommended majors, incorporating both research data and psychological insights]
</summary>

<recommendations>
[JSON array with 7 major recommendations sorted by match percentage (highest to lowest), format: {{"major": "Major Name", "match_percent": 75.5}}]
</recommendations>"""

        # Create the prompt template
        prompt_template = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            ("human", f"Please analyze student {student_id} and provide major recommendations using the available tools."),
        ])

        # Initialize the LLM with tool binding
        llm = ChatOpenAI(
            model=settings.MODEL,
            temperature=0.3
        )

        # Create the chain with tools
        chain = prompt_template | llm.bind_tools(MAJOR_MATCHING_TOOLS)

        alog.info(f"Executing tool-enabled chain for student {student_id}")

        # Execute the chain
        response = chain.invoke({})

        # Handle tool calls if present
        if response.tool_calls:
            alog.info(f"AI agent made {len(response.tool_calls)} tool calls")

            # Execute tool calls
            tool_results = []
            for tool_call in response.tool_calls:
                tool_name = tool_call["name"]
                tool_args = tool_call["args"]

                alog.info(f"Executing tool: {tool_name} with args: {tool_args}")

                # Find and execute the tool
                for tool in MAJOR_MATCHING_TOOLS:
                    if tool.name == tool_name:
                        result = tool._run(**tool_args)
                        tool_results.append(f"Tool {tool_name} result:\n{result}")
                        break

            # Create follow-up prompt with tool results
            follow_up_prompt = f"""Based on the tool results below, provide your final recommendations:

{chr(10).join(tool_results)}

Provide your response in this exact format:

<summary>
[Write a 2-3 sentence summary explaining the student's personality strengths and how they align with their top recommended majors, incorporating both research data and psychological insights]
</summary>

<recommendations>
[JSON array with 7 major recommendations sorted by match percentage (highest to lowest), format: {{"major": "Major Name", "match_percent": 75.5}}]
</recommendations>"""

            # Get final response
            final_response = llm.invoke([HumanMessage(content=follow_up_prompt)])
            agent_output = final_response.content
        else:
            agent_output = response.content

        # Parse the agent's response
        parsed_response = parse_llm_response(agent_output)

        recommendations = parsed_response['recommendations']
        summary = parsed_response['summary']

        alog.info(f"Tool-enabled agent generated {len(recommendations)} major recommendations for student {student_id}")
        alog.info(f"Top 3 recommendations: {recommendations[:3]}")
        alog.info(f"Generated summary: {summary}")

        # Return the results
        return {
            'recommendations': recommendations,
            'summary': summary,
            'method': 'tool_enabled_chain',
            'agent_output': agent_output
        }

    except Exception as e:
        alog.error(f"Unexpected error in majors_agent for student {student_id}: {str(e)}")
        raise Exception(f"Failed to generate major recommendations: {str(e)}")
