"""
Major Recommendations Agent

This module provides major recommendations based on Table 3 means calculations
using Euclidean distance between student OCEAN scores and major mean profiles.
"""

import alog
from typing import List, Dict, Any
from langchain_openai import ChatOpenAI
from addie.lib import prisma_client
from addie import settings
from addie.majors_agent.matcher import MajorsMatcher
from addie.data_model.student_context import StudentContext


async def generate_ai_summaries(student_id: str, student_scores: Dict[str, float], matches: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Generate AI summaries for each major recommendation.

    Args:
        student_id: Student's unique identifier
        student_scores: Student's OCEAN scores
        matches: List of major matches with distances and percentages

    Returns:
        List of matches with AI-generated summaries added
    """
    try:
        alog.info("Generating AI summaries for major recommendations")

        # Get comprehensive student context
        prisma = prisma_client()
        user = prisma.student.find_unique_or_raise(
            where={"id": student_id},
            include={"users": True}
        )

        if not user.users:
            raise Exception(f"No user found for student {student_id}")

        user_id = user.users[0].id

        # Create and initialize student context
        student_context = StudentContext(user_id=user_id, id=student_id)
        await student_context.async_init()

        # Initialize the LLM
        llm = ChatOpenAI(
            model=settings.MODEL,
            temperature=0.3
        )

        # Prepare student profile with OCEAN scores
        student_profile = f"""
Student OCEAN Scores:
- Openness to Experience: {student_scores['openness_to_experience']:.1f}/50
- Conscientiousness: {student_scores['conscientiousness']:.1f}/50
- Extroversion: {student_scores['extroversion']:.1f}/50
- Agreeableness: {student_scores['agreeableness']:.1f}/50
- Neuroticism: {student_scores['neuroticism']:.1f}/50
"""

        # Add comprehensive student context
        student_context_info = f"""
Student Information:
- Name: {student_context.user.get('first_name', '') if student_context.user else ''} {student_context.user.get('last_name', '') if student_context.user else ''}
- Grade: {getattr(student_context, 'grade', 'N/A')}
"""

        # Add academic achievements if available
        achievements = getattr(student_context, 'academic_achievements', [])
        if achievements:
            achievements_text = "\n".join([f"- {achievement.get('title', 'N/A')}: {achievement.get('description', 'N/A')}"
                                         for achievement in achievements])
            student_context_info += f"""
Academic Achievements:
{achievements_text}
"""

        # Add growth opportunities if available
        growth_opportunities = getattr(student_context, 'growth_opportunities', [])
        if growth_opportunities:
            growth_text = "\n".join([f"- {opp.get('title', 'N/A')}: {opp.get('description', 'N/A')}"
                                   for opp in growth_opportunities])
            student_context_info += f"""
Growth Opportunities:
{growth_text}
"""

        # Add onboarding summary if available
        onboarding_summary = getattr(student_context, 'onboarding_summary', None)
        if onboarding_summary:
            student_context_info += f"""
Onboarding Summary:
{onboarding_summary.get('content', 'N/A') if isinstance(onboarding_summary, dict) else str(onboarding_summary)}
"""

        # Add teacher comments summary if available
        teacher_comments_summary = getattr(student_context, 'teacher_comments_summary', None)
        if teacher_comments_summary:
            student_context_info += f"""
Teacher Comments Summary:
{teacher_comments_summary.get('content', 'N/A') if isinstance(teacher_comments_summary, dict) else str(teacher_comments_summary)}
"""

        matches_data = ""
        for match in matches:
            matches_data += f"""
Major: {match['major']}
Distance: {match['distance']:.2f}
Match Percentage: {match['match_percentage']:.2f}%
"""

        # Create prompt for AI summary generation
        prompt = f"""You are an expert academic advisor. Based on the student's OCEAN personality scores, comprehensive student context, and the calculated major matches, generate individual summaries for each major.

{student_profile}

{student_context_info}

Major Matches (calculated using Euclidean distance to mean profiles):
{matches_data}

For each major, write a 2-3 sentence summary directly addressing the student using "you" and "your". Consider both their personality profile (OCEAN scores) and their academic background, achievements, and interests when explaining why this major fits or doesn't fit them. Base your recommendations on the match percentage, distance calculations, and the student's demonstrated interests and achievements.

Provide your response in this exact format:

<summaries>
{{"Medicine": "Your summary for Medicine here...",
"Psychology": "Your summary for Psychology here...",
"Law": "Your summary for Law here...",
"Economics": "Your summary for Economics here...",
"Political Science": "Your summary for Political Science here...",
"Science": "Your summary for Science here...",
"Arts": "Your summary for Arts here..."}}
</summaries>"""

        # Get AI response
        response = llm.invoke(prompt)
        ai_content = response.content

        # Parse AI summaries
        summaries = parse_ai_summaries(ai_content)

        # Add summaries to matches
        for match in matches:
            major = match['major']
            match['summary'] = summaries.get(major, f"This major has a {match['match_percentage']:.2f}% match with your personality profile.")

        alog.info(f"Successfully generated AI summaries for {len(matches)} majors")
        return matches

    except Exception as e:
        alog.error(f"Error generating AI summaries: {str(e)}")
        # Fallback to default summaries
        for match in matches:
            match['summary'] = f"This major has a {match['match_percentage']:.2f}% match with your personality profile based on Euclidean distance calculations."
        return matches


def parse_ai_summaries(ai_content: str) -> Dict[str, str]:
    """Parse AI-generated summaries from the response."""
    import re
    import json

    try:
        # Extract summaries from <summaries> tags
        summaries_match = re.search(r'<summaries>(.*?)</summaries>', ai_content, re.DOTALL)

        if summaries_match:
            summaries_content = summaries_match.group(1).strip()
            # Parse JSON
            summaries = json.loads(summaries_content)
            return summaries
        else:
            alog.warning("Could not find <summaries> tags in AI response")
            return {}

    except Exception as e:
        alog.error(f"Error parsing AI summaries: {str(e)}")
        return {}


async def majors_agent(student_id: str) -> Dict[str, Any]:
    """
    Major recommendations using Table 3 means calculations with AI-generated summaries.

    This function:
    1. Calculates major recommendations using Euclidean distance
    2. Generates AI summaries for each major
    3. Saves results to database (StudentMajors and StudentMajorMatches)

    Args:
        student_id: The student's unique identifier

    Returns:
        Dictionary containing recommendations list

    Raises:
        Exception: If calculation fails
    """
    try:
        alog.info(f"Starting Table 3 major recommendations for student {student_id}")

        # Get student's OCEAN scores from database
        prisma = prisma_client()
        ocean_scores = prisma.oceanscores.find_first(where={'student_id': student_id})

        if not ocean_scores:
            raise Exception(f"No OCEAN scores found for student {student_id}")

        # Convert to dictionary for calculations
        student_scores = {
            'openness_to_experience': ocean_scores.openness_to_experience,
            'conscientiousness': ocean_scores.conscientiousness,
            'extroversion': ocean_scores.extroversion,
            'agreeableness': ocean_scores.agreeableness,
            'neuroticism': ocean_scores.neuroticism
        }

        alog.info(f"Retrieved OCEAN scores for student {student_id}: {student_scores}")

        # Calculate major matches using Table 3 means
        matcher = MajorsMatcher()
        matches = matcher.calculate_all_matches(student_scores)

        alog.info(f"Calculated {len(matches)} major matches using Euclidean distance")

        # Generate AI summaries for each major
        matches_with_summaries = await generate_ai_summaries(student_id, student_scores, matches)

        # Save to database
        save_to_database(student_id, ocean_scores.id, matches_with_summaries, prisma)

        # Format response
        recommendations = []
        for match in matches_with_summaries:
            recommendations.append({
                'major': match['major'],
                'match_percent': match['match_percentage'],  # Already rounded to 2 decimal places in MajorsMatcher
                'distance': match['distance'],  # Already rounded to 2 decimal places in MajorsMatcher
                'summary': match['summary']
            })

        alog.info(f"Successfully generated {len(recommendations)} major recommendations for student {student_id}")

        return {
            'recommendations': recommendations,
            'method': 'table3_euclidean_distance_with_ai_summaries'
        }

    except Exception as e:
        alog.error(f"Error in majors_agent for student {student_id}: {str(e)}")
        raise Exception(f"Failed to generate major recommendations: {str(e)}")


def save_to_database(student_id: str, ocean_score_id: str, matches: List[Dict[str, Any]], prisma) -> None:
    """
    Save major recommendations to database.

    Args:
        student_id: Student's ID
        ocean_score_id: OCEAN scores ID
        matches: List of major matches with summaries
        prisma: Prisma client instance
    """
    try:
        alog.info(f"Saving major recommendations to database for student {student_id}")

        # Check if session already exists
        existing_session = prisma.studentmajors.find_first(
            where={'ocean_score_id': ocean_score_id},
            include={'matches': True}
        )

        if existing_session:
            alog.info(f"Major recommendations session already exists for ocean_score_id {ocean_score_id}. Updating existing entries.")
            student_majors_session = existing_session

            # Update existing StudentMajorMatches records
            for match in matches:
                # Find existing match by major name
                existing_match = None
                for existing in existing_session.matches:
                    if existing.major_name == match['major']:
                        existing_match = existing
                        break

                if existing_match:
                    # Update existing match, preserving user preferences and metadata
                    prisma.studentmajormatches.update(
                        where={'id': existing_match.id},
                        data={
                            'match_percentage': match['match_percentage'],
                            'summary': match['summary']
                            # Note: liked, disliked, source, deleted, and deleted_by are preserved from existing record
                        }
                    )
                    alog.info(f"Updated existing match for {match['major']}: {match['match_percentage']:.2f}% (preserved user preferences and metadata)")
                else:
                    # Create new match for this major (shouldn't happen normally, but handles edge cases)
                    prisma.studentmajormatches.create(
                        data={
                            'student_major_id': student_majors_session.id,
                            'major_name': match['major'],
                            'match_percentage': match['match_percentage'],
                            'summary': match['summary'],
                            'source': 'ADDIE',
                            'liked': None,
                            'disliked': None,
                            'deleted': False,
                            'deleted_by': None
                        }
                    )
                    alog.info(f"Created new match for {match['major']}: {match['match_percentage']:.2f}%")

            alog.info(f"Successfully updated {len(matches)} major matches in database")
        else:
            # Create new StudentMajors session
            student_majors_session = prisma.studentmajors.create(
                data={
                    'student_id': student_id,
                    'ocean_score_id': ocean_score_id
                }
            )

            alog.info(f"Created StudentMajors session: {student_majors_session.id}")

            # Create StudentMajorMatches records
            match_data = []
            for match in matches:
                match_data.append({
                    'student_major_id': student_majors_session.id,
                    'major_name': match['major'],
                    'match_percentage': match['match_percentage'],
                    'summary': match['summary'],
                    'source': 'ADDIE',
                    'liked': None,
                    'disliked': None,
                    'deleted': False,
                    'deleted_by': None
                })

            # Batch create all matches
            prisma.studentmajormatches.create_many(data=match_data)

            alog.info(f"Successfully saved {len(match_data)} major matches to database")

        # Log analytics for the newly created recommendations
        try:
            from addie.services.major_analytics import MajorAnalyticsService
            analytics_service = MajorAnalyticsService()
            analytics_service.log_major_recommendation_creation(student_id, matches)
        except Exception as analytics_error:
            alog.warning(f"Failed to log analytics for student {student_id}: {str(analytics_error)}")
            # Don't fail the main operation if analytics logging fails

    except Exception as e:
        alog.error(f"Error saving to database: {str(e)}")
        raise Exception(f"Failed to save recommendations to database: {str(e)}")