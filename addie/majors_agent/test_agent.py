#!/usr/bin/env python
"""
Test script for the tool-enabled majors_agent

This script provides functionality to test the majors_agent with sample data
and verify that the tool-enabled major matching logic works correctly.
"""

import sys
import os

# Add the parent directory to the path so we can import addie modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from addie.majors_agent.agent import majors_agent
from addie.services.major_recommendations import MajorRecommendationService


def test_with_real_student():
    """Test the majors_agent with a real student ID (if available)."""
    print("Testing majors_agent with real student data...")
    
    # You would need to provide a real student ID that has OCEAN scores
    real_student_id = "cmdpua1oj00037006fbztn051"  # Example student ID
    
    try:
        print(f"Testing with real student ID: {real_student_id}")
        results = majors_agent(real_student_id)
        
        print("✓ Successfully generated recommendations")
        print(f"Method used: {results.get('method', 'unknown')}")
        
        recommendations = results.get('recommendations', [])
        print(f"Generated {len(recommendations)} recommendations")
        
        if recommendations:
            print("\nTop 5 recommendations:")
            for i, rec in enumerate(recommendations[:5], 1):
                print(f"  {i}. {rec['major']}: {rec['match_percent']:.1f}%")
        
        if results.get('summary'):
            print(f"\nAI Summary: {results['summary']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {str(e)}")
        print("Note: This test requires a real student ID with OCEAN scores in the database")
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("TOOL-ENABLED MAJORS AGENT TEST")
    print("=" * 60)
    test_with_real_student()
