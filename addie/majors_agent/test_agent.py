#!/usr/bin/env python
"""
Test script for the tool-enabled majors_agent

This script provides functionality to test the majors_agent with sample data
and verify that the tool-enabled major matching logic works correctly.
"""

import sys
import os
import argparse
import alog
import asyncio

# Add the parent directory to the path so we can import addie modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from addie.majors_agent.agent import majors_agent
from addie.services.major_recommendations import MajorRecommendationService
from addie.services.ocean_scores import OceanScoresService
from addie.lib import prisma_client


def ensure_ocean_scores(student_id: str) -> bool:
    """
    Ensure OCEAN scores exist for the student, calculate them if they don't.
    This function will find the latest COMPLETED BFI survey for the student.

    Args:
        student_id: The student's ID

    Returns:
        True if OCEAN scores exist or were successfully calculated, False otherwise
    """
    try:
        prisma = prisma_client()

        # First, verify the student exists
        student = prisma.student.find_unique(where={'id': student_id})
        if not student:
            print(f"✗ Student with ID '{student_id}' does not exist in the database")
            return False

        print(f"✓ Student found: ID {student_id}")

        # Check if OCEAN scores already exist
        existing_scores = prisma.oceanscores.find_unique(where={'student_id': student_id})

        if existing_scores:
            print(f"✓ OCEAN scores already exist for student {student_id}")
            print(f"   Scores: O={existing_scores.openness_to_experience}, C={existing_scores.conscientiousness}, E={existing_scores.extroversion}, A={existing_scores.agreeableness}, N={existing_scores.neuroticism}")
            return True

        print(f"⚠ No OCEAN scores found for student {student_id}")

        # Check if student has any completed BFI workflows
        ocean_service = OceanScoresService()
        latest_bfi_workflow = ocean_service.find_latest_bfi_workflow(student_id)

        if not latest_bfi_workflow:
            print(f"✗ No completed BFI (Big Five Personality Test) workflow found for student {student_id}")
            print("   The student must complete a BFI survey before OCEAN scores can be calculated")
            return False

        print(f"✓ Found latest completed BFI workflow: {latest_bfi_workflow}")
        print(f"   Attempting to calculate OCEAN scores...")

        # Try to calculate OCEAN scores
        success = ocean_service.process_student_ocean_scores(student_id)

        if success:
            print(f"✓ Successfully calculated OCEAN scores for student {student_id}")
            # Verify the scores were saved
            new_scores = prisma.oceanscores.find_unique(where={'student_id': student_id})
            if new_scores:
                print(f"   Calculated scores: O={new_scores.openness_to_experience}, C={new_scores.conscientiousness}, E={new_scores.extroversion}, A={new_scores.agreeableness}, N={new_scores.neuroticism}")
            return True
        else:
            print(f"✗ Failed to calculate OCEAN scores for student {student_id}")
            print("   This could mean insufficient BFI responses or data quality issues")
            return False

    except Exception as e:
        print(f"✗ Error checking/calculating OCEAN scores: {str(e)}")
        return False


async def test_with_student(student_id: str):
    """Test the majors_agent with a specific student ID."""
    print("Testing majors_agent with real student data...")

    try:
        print(f"Testing with student ID: {student_id}")

        # Validate student ID format
        if not student_id or not isinstance(student_id, str) or len(student_id.strip()) == 0:
            print("✗ Invalid student ID provided")
            return False

        # First ensure OCEAN scores exist
        print("\n=== STEP 1: Ensuring OCEAN Scores ===")
        if not ensure_ocean_scores(student_id):
            print("✗ Cannot proceed without OCEAN scores")
            return False

        # Now test the majors agent
        print("\n=== STEP 2: Generating Major Recommendations ===")
        results = await majors_agent(student_id)

        print("✓ Successfully generated recommendations")
        print(f"Method used: {results.get('method', 'unknown')}")

        recommendations = results.get('recommendations', [])
        print(f"Generated {len(recommendations)} recommendations")

        if recommendations:
            print("\n=== STEP 3: Recommendation Results ===")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec['major']}: {rec['match_percent']:.1f}% (distance: {rec.get('distance', 'N/A')})")
                if rec.get('summary'):
                    print(f"     Summary: {rec['summary'][:100]}...")
        else:
            print("⚠ No recommendations were generated")
            return False

        return True

    except Exception as e:
        print(f"✗ Test failed with exception: {str(e)}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return False


def main():
    parser = argparse.ArgumentParser(description='Test the majors agent with a specific student')
    parser.add_argument('--student-id', type=str, help='Student ID to test with')

    args = parser.parse_args()

    print("=" * 60)
    print("TOOL-ENABLED MAJORS AGENT TEST")
    print("=" * 60)

    if args.student_id:
        success = asyncio.run(test_with_student(args.student_id))
    else:
        # Default student ID for testing
        default_student_id = "cm5n03f6p0000sfz92g87b20f"
        print(f"No student ID provided, using default: {default_student_id}")
        success = asyncio.run(test_with_student(default_student_id))

    if success:
        print("\n✓ Test completed successfully!")
    else:
        print("\n✗ Test failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
