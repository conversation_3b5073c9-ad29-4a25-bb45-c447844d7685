"""
Research-Based Major Matching Tool

Based on empirical data from:
<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, D<PERSON>, & <PERSON>, L<PERSON> (2015). Personality, academic majors and performance:
Revealing complex patterns. Personality and Individual Differences, 85, 69-76.

This tool uses actual correlation coefficients and predictive validity data from the research
to calculate major matches based on OCEAN personality scores.
"""

import alog
from typing import Dict, List, Tuple, Any
import math


class ResearchBasedMajorMatcher:
    """
    Research-based major matching using empirical correlation data from <PERSON><PERSON><PERSON> et al. (2015).

    Uses actual correlation coefficients between Big Five traits and academic performance
    for different majors to calculate evidence-based match percentages.
    """

    def __init__(self):
        # Correlation coefficients from <PERSON><PERSON><PERSON> et al. (2015) Table 4
        # These show how each OCEAN trait correlates with GPA for each major
        self.major_correlations = {
            'Medicine': {
                'openness_to_experience': -0.03,
                'conscientiousness': 0.18,
                'extroversion': 0.03,
                'agreeableness': 0.04,
                'neuroticism': 0.11
            },
            'Psychology': {
                'openness_to_experience': -0.08,
                'conscientiousness': 0.20,
                'extroversion': -0.28,
                'agreeableness': 0.07,
                'neuroticism': 0.11
            },
            'Law': {
                'openness_to_experience': -0.13,
                'conscientiousness': 0.21,
                'extroversion': 0.07,
                'agreeableness': 0.03,
                'neuroticism': -0.12
            },
            'Economics': {
                'openness_to_experience': 0.12,
                'conscientiousness': 0.09,
                'extroversion': 0.11,
                'agreeableness': 0.04,
                'neuroticism': -0.13
            },
            'Political Science': {
                'openness_to_experience': 0.23,
                'conscientiousness': 0.15,
                'extroversion': 0.05,
                'agreeableness': 0.12,
                'neuroticism': -0.01
            },
            'Science': {
                'openness_to_experience': 0.08,
                'conscientiousness': 0.20,
                'extroversion': 0.03,
                'agreeableness': -0.02,
                'neuroticism': -0.04
            },
            'Arts': {
                'openness_to_experience': 0.07,
                'conscientiousness': 0.19,
                'extroversion': 0.08,
                'agreeableness': 0.03,
                'neuroticism': 0.08
            }
        }

        # Predictive validity (R²) for each major from Table 5
        self.major_predictive_validity = {
            'Medicine': 0.06,
            'Psychology': 0.15,
            'Law': 0.07,
            'Economics': 0.05,
            'Political Science': 0.07,
            'Science': 0.05,
            'Arts': 0.06
        }

    def normalize_ocean_score(self, score: float, trait: str) -> float:
        """
        Normalize OCEAN score to standard scale for correlation calculations.

        Args:
            score: Raw OCEAN score (0-50 range)
            trait: OCEAN trait name

        Returns:
            Normalized score (-2 to +2 range)
        """
        # Convert 0-50 scale to -2 to +2 scale for correlation calculations
        # Assuming mean around 25 and std around 8 for OCEAN scores
        normalized = (score - 25) / 8
        return max(-2, min(2, normalized))

    def calculate_major_match(self, ocean_scores: Dict[str, float], major: str) -> Tuple[float, str]:
        """
        Calculate match percentage for a specific major using research correlations.

        Args:
            ocean_scores: Dictionary with OCEAN trait scores
            major: Major name

        Returns:
            Tuple of (match_percentage, explanation)
        """
        if major not in self.major_correlations:
            return 0.0, f"No research data available for {major}"

        correlations = self.major_correlations[major]

        # Calculate correlation-based score
        total_correlation_score = 0.0
        explanations = []

        for trait, correlation in correlations.items():
            if trait in ocean_scores:
                normalized_score = self.normalize_ocean_score(ocean_scores[trait], trait)

                # Calculate contribution: correlation * normalized_score
                contribution = correlation * normalized_score
                total_correlation_score += contribution

                # Generate explanation for significant correlations
                if abs(correlation) >= 0.10:
                    trait_name = trait.replace('_', ' ').title()
                    raw_score = ocean_scores[trait]

                    if correlation > 0 and normalized_score > 0:
                        explanations.append(f"High {trait_name} ({raw_score:.1f}) is advantageous for {major}")
                    elif correlation > 0 and normalized_score < 0:
                        explanations.append(f"Low {trait_name} ({raw_score:.1f}) may be challenging for {major}")
                    elif correlation < 0 and normalized_score < 0:
                        explanations.append(f"Low {trait_name} ({raw_score:.1f}) aligns well with {major}")
                    elif correlation < 0 and normalized_score > 0:
                        explanations.append(f"High {trait_name} ({raw_score:.1f}) may not align with {major}")

        # Apply predictive validity weighting
        validity_weight = self.major_predictive_validity[major]
        weighted_score = total_correlation_score * (1 + validity_weight)

        # Convert to 0-100 percentage using sigmoid transformation
        # This maps correlation scores to realistic percentage ranges
        match_percentage = 50 + (25 * math.tanh(weighted_score))
        match_percentage = max(0, min(100, match_percentage))

        # Create explanation
        explanation = ". ".join(explanations) if explanations else f"Moderate alignment with {major} based on personality profile"
        explanation += f". Research reliability: {validity_weight:.2f}"

        return round(match_percentage, 1), explanation

    def generate_all_matches(self, ocean_scores: Dict[str, float]) -> List[Dict[str, Any]]:
        """
        Generate research-based match percentages for all majors.

        Args:
            ocean_scores: Dictionary with OCEAN trait scores

        Returns:
            List of major matches sorted by percentage (highest first)
        """
        matches = []

        alog.info(f"Calculating research-based major matches for OCEAN scores: {ocean_scores}")

        for major in self.major_correlations.keys():
            match_percentage, explanation = self.calculate_major_match(ocean_scores, major)

            matches.append({
                'major': major,
                'match_percent': match_percentage,
                'explanation': explanation,
                'research_validity': self.major_predictive_validity[major],
                'method': 'research_based'
            })

        # Sort by match percentage (highest first)
        matches.sort(key=lambda x: x['match_percent'], reverse=True)

        alog.info(f"Research-based matches calculated. Top 3: {[(m['major'], m['match_percent']) for m in matches[:3]]}")

        return matches

    def get_trait_insights(self, ocean_scores: Dict[str, float]) -> Dict[str, str]:
        """
        Generate insights about how each OCEAN trait affects major selection.

        Args:
            ocean_scores: Dictionary with OCEAN trait scores

        Returns:
            Dictionary with trait insights
        """
        insights = {}

        for trait, score in ocean_scores.items():
            normalized = self.normalize_ocean_score(score, trait)
            trait_name = trait.replace('_', ' ').title()

            if normalized > 1:
                level = "Very High"
            elif normalized > 0.5:
                level = "High"
            elif normalized > -0.5:
                level = "Moderate"
            elif normalized > -1:
                level = "Low"
            else:
                level = "Very Low"

            # Find majors where this trait is most/least important
            best_majors = []
            worst_majors = []

            for major, correlations in self.major_correlations.items():
                correlation = correlations.get(trait, 0)
                if correlation > 0.15:
                    best_majors.append(major)
                elif correlation < -0.15:
                    worst_majors.append(major)

            insight = f"{level} {trait_name} ({score:.1f}/50)"
            if best_majors:
                insight += f". Advantageous for: {', '.join(best_majors)}"
            if worst_majors:
                insight += f". May be challenging for: {', '.join(worst_majors)}"

            insights[trait] = insight

        return insights


def research_based_major_matching(ocean_scores: Dict[str, float]) -> Dict[str, Any]:
    """
    Main function to generate research-based major recommendations.

    Args:
        ocean_scores: Dictionary with OCEAN trait scores

    Returns:
        Dictionary with recommendations and insights
    """
    matcher = ResearchBasedMajorMatcher()

    # Generate matches
    matches = matcher.generate_all_matches(ocean_scores)

    # Generate trait insights
    insights = matcher.get_trait_insights(ocean_scores)

    # Create summary
    top_3 = matches[:3]
    summary = f"Based on empirical research, your personality profile best aligns with {top_3[0]['major']} ({top_3[0]['match_percent']:.1f}%), followed by {top_3[1]['major']} ({top_3[1]['match_percent']:.1f}%) and {top_3[2]['major']} ({top_3[2]['match_percent']:.1f}%). These recommendations are based on correlation patterns between personality traits and academic performance from a study of university students."

    return {
        'recommendations': [{'major': m['major'], 'match_percent': m['match_percent']} for m in matches],
        'detailed_matches': matches,
        'trait_insights': insights,
        'summary': summary,
        'method': 'research_based',
        'source': 'Vedel et al. (2015) - Personality, academic majors and performance'
    }
