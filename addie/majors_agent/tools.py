"""
<PERSON><PERSON><PERSON><PERSON> for Major Matching

These tools provide the AI agent with access to research-based major matching
capabilities and OCEAN personality analysis functions.
"""

import alog
from langchain.tools import BaseTool
from pydantic import BaseModel, Field
from addie.majors_agent.research_based_matcher import ResearchBasedMajorMatcher


class ResearchBasedMajorMatchingInput(BaseModel):
    """Input schema for research-based major matching tool."""
    extroversion: float = Field(description="Extroversion score (0-50)")
    agreeableness: float = Field(description="Agreeableness score (0-50)")
    conscientiousness: float = Field(description="Conscientiousness score (0-50)")
    neuroticism: float = Field(description="Neuroticism score (0-50)")
    openness_to_experience: float = Field(description="Openness to Experience score (0-50)")


class ResearchBasedMajorMatchingTool(BaseTool):
    """
    Tool that provides research-based major matching using empirical correlation data.
    
    Based on <PERSON><PERSON><PERSON> et al. (2015) study of personality traits and academic performance.
    Uses actual correlation coefficients between Big Five traits and GPA for different majors.
    """
    
    name: str = "research_based_major_matching"
    description: str = """
    Calculate major recommendations based on empirical research data from academic studies.
    
    This tool uses actual correlation coefficients between Big Five personality traits 
    and academic performance (GPA) from a study of university students across 7 majors:
    Medicine, Psychology, Law, Economics, Political Science, Science, and Arts.
    
    Input: OCEAN personality scores (0-50 scale)
    Output: Research-based match percentages with explanations and predictive validity scores
    
    Use this tool to get evidence-based major recommendations grounded in actual student performance data.
    """
    args_schema: type[BaseModel] = ResearchBasedMajorMatchingInput
    
    def __init__(self):
        super().__init__()
    
    def _run(self, extroversion: float, agreeableness: float, conscientiousness: float, 
             neuroticism: float, openness_to_experience: float) -> str:
        """Execute the research-based major matching."""
        try:
            ocean_scores = {
                'extroversion': extroversion,
                'agreeableness': agreeableness,
                'conscientiousness': conscientiousness,
                'neuroticism': neuroticism,
                'openness_to_experience': openness_to_experience
            }
            
            alog.info(f"Research tool called with OCEAN scores: {ocean_scores}")
            
            # Create matcher instance
            matcher = ResearchBasedMajorMatcher()

            # Generate matches using research data
            matches = matcher.generate_all_matches(ocean_scores)

            # Format results for AI agent
            result = "RESEARCH-BASED MAJOR RECOMMENDATIONS:\n\n"

            for i, match in enumerate(matches, 1):
                result += f"{i}. {match['major']}: {match['match_percent']:.1f}%\n"
                result += f"   Explanation: {match['explanation']}\n"
                result += f"   Research Validity: {match['research_validity']:.2f}\n\n"

            # Add trait insights
            insights = matcher.get_trait_insights(ocean_scores)
            result += "PERSONALITY TRAIT INSIGHTS:\n\n"

            for _, insight in insights.items():
                result += f"• {insight}\n"
            
            result += f"\nSource: Vedel et al. (2015) - Personality, academic majors and performance"
            
            alog.info(f"Research tool returning {len(matches)} recommendations")
            return result
            
        except Exception as e:
            alog.error(f"Error in research-based major matching tool: {str(e)}")
            return f"Error calculating research-based recommendations: {str(e)}"


class OceanScoreAnalysisInput(BaseModel):
    """Input schema for OCEAN score analysis tool."""
    student_id: str = Field(description="Student ID to analyze OCEAN scores for")


class OceanScoreAnalysisTool(BaseTool):
    """
    Tool that retrieves and analyzes a student's OCEAN personality scores.
    
    Provides detailed analysis of each personality trait and how it relates to academic majors.
    """
    
    name: str = "ocean_score_analysis"
    description: str = """
    Retrieve and analyze a student's OCEAN (Big Five) personality scores.
    
    This tool fetches the student's personality assessment results and provides:
    - Individual trait scores (Openness, Conscientiousness, Extroversion, Agreeableness, Neuroticism)
    - Trait level interpretations (Very Low, Low, Moderate, High, Very High)
    - Academic implications for each trait
    - Overall personality profile summary
    
    Input: Student ID
    Output: Detailed OCEAN score analysis with academic implications
    
    Use this tool to understand a student's personality profile before making major recommendations.
    """
    args_schema: type[BaseModel] = OceanScoreAnalysisInput
    
    def __init__(self):
        super().__init__()
    
    def _run(self, student_id: str) -> str:
        """Execute OCEAN score analysis for a student."""
        try:
            alog.info(f"OCEAN analysis tool called for student {student_id}")
            
            # Get OCEAN scores
            from addie.services.ocean_scores import OceanScoresService
            ocean_service = OceanScoresService()
            ocean_data = ocean_service.get_ocean_scores(student_id)
            
            if not ocean_data:
                return f"No OCEAN scores found for student {student_id}. Please complete the Big Five personality assessment first."
            
            # Extract scores
            scores = {
                'openness_to_experience': ocean_data['openness_to_experience'],
                'conscientiousness': ocean_data['conscientiousness'],
                'extroversion': ocean_data['extroversion'],
                'agreeableness': ocean_data['agreeableness'],
                'neuroticism': ocean_data['neuroticism']
            }
            
            # Analyze each trait
            result = f"OCEAN PERSONALITY ANALYSIS FOR STUDENT {student_id}:\n\n"
            
            trait_descriptions = {
                'openness_to_experience': 'Openness to Experience - creativity, curiosity, intellectual interests',
                'conscientiousness': 'Conscientiousness - organization, discipline, goal-directed behavior',
                'extroversion': 'Extroversion - sociability, assertiveness, positive emotions',
                'agreeableness': 'Agreeableness - cooperation, trust, empathy',
                'neuroticism': 'Neuroticism - emotional instability, anxiety, stress sensitivity'
            }
            
            for trait, score in scores.items():
                # Determine level
                if score >= 35:
                    level = "Very High"
                elif score >= 28:
                    level = "High"
                elif score >= 22:
                    level = "Moderate"
                elif score >= 15:
                    level = "Low"
                else:
                    level = "Very Low"
                
                trait_name = trait.replace('_', ' ').title()
                description = trait_descriptions[trait]
                
                result += f"{trait_name}: {score:.1f}/50 ({level})\n"
                result += f"  {description}\n"
                
                # Add academic implications
                if trait == 'conscientiousness':
                    if score >= 25:
                        result += "  Academic Advantage: Strong organization and study habits benefit most majors\n"
                    else:
                        result += "  Academic Challenge: May need extra structure and time management support\n"
                elif trait == 'openness_to_experience':
                    if score >= 25:
                        result += "  Academic Advantage: Benefits creative and theoretical majors like Arts, Political Science\n"
                    else:
                        result += "  Academic Advantage: May prefer structured, practical majors like Law, Medicine\n"
                elif trait == 'extroversion':
                    if score >= 25:
                        result += "  Academic Advantage: Benefits collaborative and people-oriented majors\n"
                    else:
                        result += "  Academic Advantage: May prefer independent study and research-focused majors\n"
                elif trait == 'neuroticism':
                    if score >= 25:
                        result += "  Academic Challenge: May need stress management support in high-pressure majors\n"
                    else:
                        result += "  Academic Advantage: Good emotional stability for demanding academic programs\n"
                elif trait == 'agreeableness':
                    if score >= 25:
                        result += "  Academic Advantage: Benefits helping professions and collaborative environments\n"
                    else:
                        result += "  Academic Advantage: May excel in competitive or analytical fields\n"
                
                result += "\n"
            
            # Overall profile summary
            high_traits = [trait.replace('_', ' ').title() for trait, score in scores.items() if score >= 28]
            low_traits = [trait.replace('_', ' ').title() for trait, score in scores.items() if score <= 22]
            
            result += "OVERALL PERSONALITY PROFILE:\n"
            if high_traits:
                result += f"Strengths: High {', '.join(high_traits)}\n"
            if low_traits:
                result += f"Considerations: Low {', '.join(low_traits)}\n"
            
            result += f"\nAssessment Date: {ocean_data['created_at']}"
            
            alog.info(f"OCEAN analysis completed for student {student_id}")
            return result
            
        except Exception as e:
            alog.error(f"Error in OCEAN score analysis tool: {str(e)}")
            return f"Error analyzing OCEAN scores: {str(e)}"


class MajorCorrelationLookupInput(BaseModel):
    """Input schema for major correlation lookup tool."""
    major: str = Field(description="Major name to look up correlations for")


class MajorCorrelationLookupTool(BaseTool):
    """
    Tool that provides detailed correlation data for a specific major.
    
    Shows how each OCEAN trait correlates with academic performance in that major.
    """
    
    name: str = "major_correlation_lookup"
    description: str = """
    Look up detailed correlation data between OCEAN traits and academic performance for a specific major.
    
    This tool provides:
    - Correlation coefficients for each OCEAN trait with GPA in the specified major
    - Statistical significance levels
    - Predictive validity (R²) for the major
    - Interpretation of what each correlation means for student success
    
    Available majors: Medicine, Psychology, Law, Economics, Political Science, Science, Arts
    
    Input: Major name
    Output: Detailed correlation analysis for that major
    
    Use this tool to understand which personality traits predict success in specific academic fields.
    """
    args_schema: type[BaseModel] = MajorCorrelationLookupInput
    
    def __init__(self):
        super().__init__()
    
    def _run(self, major: str) -> str:
        """Look up correlation data for a specific major."""
        try:
            alog.info(f"Major correlation lookup for: {major}")
            
            matcher = ResearchBasedMajorMatcher()

            if major not in matcher.major_correlations:
                available_majors = list(matcher.major_correlations.keys())
                return f"Major '{major}' not found. Available majors: {', '.join(available_majors)}"

            correlations = matcher.major_correlations[major]
            validity = matcher.major_predictive_validity[major]
            
            result = f"CORRELATION ANALYSIS FOR {major.upper()}:\n\n"
            result += f"Predictive Validity (R²): {validity:.3f}\n"
            result += f"(Higher R² = more reliable predictions)\n\n"
            
            result += "OCEAN TRAIT CORRELATIONS WITH GPA:\n\n"
            
            # Sort by absolute correlation strength
            sorted_traits = sorted(correlations.items(), key=lambda x: abs(x[1]), reverse=True)
            
            for trait, correlation in sorted_traits:
                trait_name = trait.replace('_', ' ').title()
                
                # Determine significance and interpretation
                if abs(correlation) >= 0.20:
                    strength = "Strong"
                elif abs(correlation) >= 0.10:
                    strength = "Moderate"
                else:
                    strength = "Weak"
                
                direction = "Positive" if correlation > 0 else "Negative"
                
                result += f"{trait_name}: {correlation:+.3f} ({strength} {direction})\n"
                
                # Interpretation
                if correlation > 0.15:
                    result += f"  → High {trait_name} strongly predicts better performance in {major}\n"
                elif correlation > 0.05:
                    result += f"  → High {trait_name} somewhat predicts better performance in {major}\n"
                elif correlation < -0.15:
                    result += f"  → Low {trait_name} strongly predicts better performance in {major}\n"
                elif correlation < -0.05:
                    result += f"  → Low {trait_name} somewhat predicts better performance in {major}\n"
                else:
                    result += f"  → {trait_name} has minimal impact on performance in {major}\n"
                
                result += "\n"
            
            result += f"Source: Vedel et al. (2015) empirical study of university students"
            
            return result
            
        except Exception as e:
            alog.error(f"Error in major correlation lookup tool: {str(e)}")
            return f"Error looking up major correlations: {str(e)}"


# Tool instances for use by the AI agent
research_based_major_matching_tool = ResearchBasedMajorMatchingTool()
ocean_score_analysis_tool = OceanScoreAnalysisTool()
major_correlation_lookup_tool = MajorCorrelationLookupTool()

# List of all tools for easy import
MAJOR_MATCHING_TOOLS = [
    research_based_major_matching_tool,
    ocean_score_analysis_tool,
    major_correlation_lookup_tool
]
