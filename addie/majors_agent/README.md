# Majors Agent - AI-Powered Academic Major Recommendations

The Majors Agent is a true AI agent that uses Large Language Models (ChatGPT/OpenAI) to analyze students' OCEAN (Big Five) personality scores and provide intelligent, nuanced academic major recommendations with detailed psychological reasoning.

## Overview

The AI agent analyzes a student's personality profile using advanced LLM capabilities, considering psychological research about personality-career fit, and provides thoughtful recommendations for 7 key academic majors with match percentages and reasoning.

## Features

- **True AI Agent**: Uses ChatGPT/OpenAI LLM for intelligent personality analysis
- **Psychological Reasoning**: Provides evidence-based recommendations with detailed reasoning
- **Holistic Analysis**: Considers all OCEAN traits together, not just mathematical similarity
- **Adaptive Intelligence**: LLM can consider nuanced personality combinations
- **Automatic Pipeline**: Automatically generates recommendations when OCEAN scores are calculated
- **JSON Response Parsing**: Robust parsing of LLM responses with fallback mechanisms
- **REST API**: FastAPI endpoints for integration with web applications
- **Comprehensive Testing**: Unit tests and CLI tools for validation

## Architecture

### Core Components

1. **`agent.py`** - Main majors_agent function and matching logic
2. **`MajorRecommendationService`** - Database operations and business logic
3. **API Routes** - REST endpoints for web integration
4. **Celery Tasks** - Background processing support
5. **Database Model** - MajorRecommendation table for persistence

### Major Focus Areas

The system provides AI-powered analysis for 7 key academic majors:

- **Medicine** - Healthcare, medical practice, patient care
- **Psychology** - Human behavior, mental health, research
- **Law** - Legal practice, advocacy, justice system
- **Economics** - Financial systems, market analysis, policy
- **Political Science** - Government, public policy, international relations
- **Science** - Natural sciences, research, STEM fields
- **Arts** - Creative fields, visual arts, performing arts, design

The AI agent considers personality traits, career satisfaction factors, academic requirements, and comprehensive student context including academic achievements, growth opportunities, conversation summaries, and teacher comments for each field.

## Usage

### Direct Function Call

```python
import asyncio
from addie.majors_agent import majors_agent

# Generate recommendations for a student (async function)
async def get_recommendations():
    recommendations = await majors_agent("student_id_here")
    return recommendations

# Or use asyncio.run for synchronous contexts
recommendations = asyncio.run(majors_agent("student_id_here"))

# Returns:
# {
#   "recommendations": [
#     {"major": "Computer Science", "match_percent": 92.1, "summary": "..."},
#     {"major": "Psychology", "match_percent": 85.4, "summary": "..."},
#     ...
#   ],
#   "method": "table3_euclidean_distance_with_ai_summaries"
# }
```

### Service Class

```python
import asyncio
from addie.services.major_recommendations import MajorRecommendationService

service = MajorRecommendationService()

# Generate and save to database (async method)
success = await service.generate_and_save_recommendations("student_id")
# Or use asyncio.run for synchronous contexts
success = asyncio.run(service.generate_and_save_recommendations("student_id"))

# Retrieve stored recommendations (synchronous)
recommendations = service.get_recommendations("student_id", limit=5)

# Get summary statistics (synchronous)
summary = service.get_recommendations_summary("student_id")

# Refresh recommendations (async method)
success = await service.refresh_recommendations("student_id")
```

### Automatic Pipeline

Major recommendations are automatically generated when OCEAN scores are calculated:

```python
from addie.tasks import calculate_ocean_scores_task

# This will calculate OCEAN scores AND generate major recommendations
task = calculate_ocean_scores_task.delay(student_id="student_id")
```

### REST API

```bash
# Generate recommendations (for manual regeneration)
POST /major-recommendations/generate
{
  "student_id": "student_id_here"
}

# Get stored recommendations
GET /major-recommendations/student/{student_id}

# Get top N recommendations
GET /major-recommendations/student/{student_id}/top/5

# Refresh recommendations
POST /major-recommendations/student/{student_id}/refresh

# Delete recommendations
DELETE /major-recommendations/student/{student_id}
```

**Note**: Major recommendations are automatically generated when OCEAN scores are calculated via the BFI workflow completion. If recommendations already exist, they are updated with new scores and fresh AI summaries while preserving user preferences (liked/disliked status) and metadata (source, deleted, deleted_by). The API endpoints are primarily for manual regeneration and retrieval.

## Prerequisites

1. **OCEAN Scores**: Student must have OCEAN personality scores calculated
2. **Database**: MajorRecommendation table must be created via migration
3. **Dependencies**: Prisma, FastAPI, Celery (for background processing)

## Installation & Setup

1. **Database Migration**:
   ```bash
   # Apply the migration to create MajorRecommendation table
   npx prisma migrate deploy
   ```

2. **Celery Worker** (for background processing):
   ```bash
   # Start Celery worker
   celery -A addie.tasks worker --loglevel=info
   ```

3. **API Server**:
   ```bash
   # Start FastAPI server
   uvicorn addie.api.app:router --reload
   ```

## Testing

### Unit Tests
```bash
# Run comprehensive test suite
python addie/majors_agent/test_agent.py
```

### CLI Testing
```bash
# List students with OCEAN scores
python addie/scripts/test_majors_agent.py --list-students

# Generate recommendations for specific student
python addie/scripts/test_majors_agent.py --student-id <student_id>

# Generate and save to database
python addie/scripts/test_majors_agent.py --student-id <student_id> --save-to-db
```

## AI Agent Details

### Analysis Process

1. **Retrieve OCEAN Scores**: Get student's personality scores from database
2. **Gather Student Context**: Collect comprehensive student information including:
   - Academic achievements and growth opportunities
   - Onboarding conversation summaries
   - Teacher comments summaries
   - Student profile information (name, grade, etc.)
3. **Create AI Prompt**: Generate detailed prompt with student's personality profile and context
4. **LLM Analysis**: ChatGPT analyzes personality traits, academic background, and career fit
5. **Parse Response**: Extract structured recommendations from AI response
6. **Rank Results**: Sort majors by AI-determined match percentage (highest first)

### AI Prompt Structure

The AI agent receives a comprehensive prompt including:
- Student's detailed OCEAN personality profile with explanations
- Comprehensive student context:
  - Student name and grade level
  - Academic achievements and accomplishments
  - Growth opportunities and areas for development
  - Onboarding conversation summaries
  - Teacher comments and feedback summaries
- List of 7 available academic majors
- Instructions for psychological analysis considering both personality and academic background
- Request for structured response with match percentages and personalized summaries

### Response Parsing

The system includes robust parsing mechanisms:
- Primary: JSON extraction from LLM response
- Fallback: Manual text parsing for major names and percentages
- Validation: Ensures all 12 majors are included with valid percentages

## Database Schema

```sql
CREATE TABLE "MajorRecommendation" (
    "id" TEXT NOT NULL,
    "student_id" TEXT NOT NULL,
    "major" TEXT NOT NULL,
    "match_percent" DOUBLE PRECISION NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    
    CONSTRAINT "MajorRecommendation_pkey" PRIMARY KEY ("id")
);

-- Indexes for performance
CREATE INDEX "MajorRecommendation_student_id_idx" ON "MajorRecommendation"("student_id");
CREATE INDEX "MajorRecommendation_match_percent_idx" ON "MajorRecommendation"("match_percent");
CREATE UNIQUE INDEX "MajorRecommendation_student_id_major_key" ON "MajorRecommendation"("student_id", "major");
```

## Error Handling

The system includes comprehensive error handling for:

- Missing OCEAN scores
- Invalid student IDs
- Database connection issues
- Calculation errors
- API validation errors

## Performance Considerations

- **Caching**: Consider caching recommendations for frequently accessed students
- **Batch Processing**: Use Celery for processing multiple students
- **Database Indexing**: Indexes on student_id and match_percent for fast queries
- **Rate Limiting**: API endpoints should include rate limiting for production use

## Future Enhancements

- **Additional Majors**: Expand beyond 12 majors with more research data
- **Weighted Matching**: Allow different weights for OCEAN dimensions
- **Confidence Intervals**: Include confidence scores with recommendations
- **Personalization**: Factor in student interests, grades, and other data
- **Machine Learning**: Train models on actual student outcomes
