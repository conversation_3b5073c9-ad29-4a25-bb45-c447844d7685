import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Environment
ENV: str = os.environ.get('ENV', 'development')  # Default to development if not set

DATABASE_URL: str = os.environ.get('DATABASE_URL') or ''
PGVECTOR_URL: str = os.environ.get('PGVECTOR_URL') or ''

POSTGRES_USER: str = os.environ.get('POSTGRES_USER')
POSTGRES_DB: str = os.environ.get('POSTGRES_DB')
POSTGRES_HOST: str = os.environ.get('POSTGRES_HOST')
POSTGRES_PASSWORD: str = os.environ.get('POSTGRES_PASSWORD')
DATABASE_PORT: str = os.environ.get('DATABASE_PORT')

MODEL = 'gpt-4.1'

TEMP = 0.7
PROVIDER = 'openai'

DATA_DIR = os.environ.get('DATA_DIR')

API_HOST = os.environ.get('ADDIE_API_HOST', 'dev-api.getaddie.com')
PROTOCOL = "http://" if "localhost" in API_HOST else "https://"
API_URL = PROTOCOL + API_HOST
QDRANT_URL = os.environ.get('QDRANT_URL')
QDRANT__SERVICE__API_KEY = os.environ.get('QDRANT__SERVICE__API_KEY')
REDIS_CONN = os.environ.get('REDIS_CONN')
REDIS_HOST = os.environ.get('REDIS_HOST')
REDIS_PORT = os.environ.get('REDIS_PORT')
REDIS_PASS = os.environ.get('REDIS_PASS')

RUN_MODE=os.environ.get('RUN_MODE')

# Twilio configuration
TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN')
TWILIO_VERIFY_SERVICE_SID = os.environ.get('TWILIO_VERIFY_SERVICE_SID')
TWILIO_PHONE_NUMBER = os.environ.get('TWILIO_PHONE_NUMBER')

# Voice calling configuration
VOICE_WEBSOCKET_URL = "ws://" + API_HOST + "/voice-stream"
OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
OPENAI_REALTIME_MODEL = os.environ.get('OPENAI_REALTIME_MODEL', 'gpt-4o-realtime-preview-2024-10-01')

LOG_LEVEL=os.environ.get('LOG_LEVEL', 'INFO')

if LOG_LEVEL == '':
    LOG_LEVEL='INFO'

# API Key for internal service authentication
ADDIE_API_KEY = os.environ.get('ADDIE_API_KEY')

PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
