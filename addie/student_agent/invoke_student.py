from addie.lib import dict_to_json
from addie.student_agent.state import StudentAgentState
from langchain_core.messages import SystemMessage, AIMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.utils.function_calling import convert_to_openai_tool
from langchain_openai import ChatOpenAI
from pydantic import validate_call

from addie.tools.goal_completion_tool import GoalCompletionTool

tools = [GoalCompletionTool()]


@validate_call
def gen_student_system_prompt(state: dict) -> str:
    student_context = state['student_context']
    student_agent = state['student_agent']
    student_context = dict_to_json(student_context)
    prompt = state['student_agent'].prompt.content

    system_prompt = f"""
    You are a student. 
    When communicating with the counselor please consider the following context 
    The context describes your highschool career so far. :
    You will also follow the role described in the role below
    while being careful not to violate the instructions already given:
    student agent role: 
    {prompt}
    Then this context:
    {student_agent}
    student context:
    {student_context}
    """

    return system_prompt


def invoke_student(state: StudentAgentState):
    assert state['student_agent'].id

    # alog.info(state['student_agent'])
    # alog.info(f'### student agent id: {state["student_agent"].id} ###')

    messages = state['messages']
    model = state['model']
    student_context = state['student_context']

    if len(messages) > 0:

        last_msg = messages[-1]
        session_id = state['user_id']

        if last_msg.type == 'human':
            prompt = ChatPromptTemplate.from_messages(
                [
                    SystemMessage(content=gen_student_system_prompt(state)),
                    MessagesPlaceholder("message_history", optional=True, n_history=2),
                    MessagesPlaceholder("question", optional=True),
                ]
            )

            chain = ChatOpenAI(model=model)

            chain = chain.bind(tools=[convert_to_openai_tool(tool) for tool in tools])

            chain = prompt | chain

            # chain_with_history = RunnableWithMessageHistory(
            #     chain,
            #     get_history,
            #     input_messages_key="question",
            #     history_messages_key="message_history",
            #     name=session_id
            # )

            config = {"configurable": {"session_id": session_id}}
            max_msgs = 10
            if len(messages) < max_msgs:
                max_msgs = len(messages)
            last_msgs = messages[-max_msgs:]

            # alog.info(alog.pformat(last_msgs))

            response = chain.invoke(dict(
                question=last_msgs,
            ), config)

            # history = get_history(session_id)
            # messages = history.get_messages()
            # alog.info(messages[-1])

            messages.append(AIMessage(content=response.content))

            state['messages'] = messages

            return state

    return state
