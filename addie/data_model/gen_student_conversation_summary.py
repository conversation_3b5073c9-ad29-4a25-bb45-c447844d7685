from addie import settings
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from pydantic.dataclasses import dataclass

import alog
import hashlib
import json

from addie.lib import prisma_client, omit, dict_to_json, pick


@dataclass
class GeneratedStudentConversationSummary:
    student_id: str
    result: str = None

    def __post_init__(self):
        self.prisma = prisma_client()
        self.model = ChatOpenAI(
            model=settings.MODEL,
            temperature=settings.TEMP
        )

        self.generate()

        self.save()

    @property
    def context(self):
        alog.info('## get context ##')

        student = self.prisma.student.find_unique_or_raise(
            where=dict(
                id=self.student_id
            ),
            include=dict(
                users=True,
                question_response=True
            ),
        )

        assert len(student.users) == 1
        user = student.users[0]

        responses = [pick(['response'], qr.dict())
                     for qr in student.question_response]

        user_data = pick(['first_name', 'last_name'], user.dict())

        assert len(responses) > 0

        data = dict(responses=responses, **user_data)

        return json.dumps(data)

    def generate(self):
        prompt = (
            '''
## Prompt: Summarizing Student Questionnaire Responses

### Objective
Summarize a student’s questionnaire responses into a concise, actionable report that highlights key insights and recommendations for college counselors. Focus on the most important details that will help the counselor understand the student’s profile, strengths, challenges, and aspirations.

### Input
You are analyzing a student questionnaire. The responses provide information about the student’s background, values, academic profile, extracurriculars, aspirations, and challenges. Use this input to create a detailed yet concise summary.

### Instructions
Generate a structured summary using the following format:

---

### **Student Summary**
**Student**: [Name]  
**Pronouns**: [Pronouns]  
**Race/Ethnicity**: [Optional, if shared]  

### **In Their Own Words**

Use the student's responses to craft a concise and engaging narrative that captures the essence of who they are, written in their own voice. This section should provide a summary of the student’s core values, passions, and aspirations, as they relate to their college journey. The tone should reflect their personality and use their own phrasing wherever possible. Focus on creating a vivid and personal snapshot to give the counselor a quick and meaningful understanding of the student.

**Output Format**:
**Student**: [Name]  
[Engaging and personalized narrative based on the student's own words.]



---

### **Background**
- **Family Context**: [Parents’ educational background and professions, any information on affordability concerns or financial aid.]  
- **Key Relationships**: [Trusted adults, close friends, or social support network.]  

---

### **Key Personality and Values**
- **Core Values**: [Identify personal values or guiding principles.]  
- **Role Model or Motto**: [Summarize role model or motto and why it’s meaningful.]  
- **Personal Tone**: [Describe the student’s tone and personality based on responses.]  

---

### **Academic Profile**
- **Strengths**: [Top 2-3 academic areas where the student excels.]  
- **Challenges**: [Specific academic areas where the student struggles, with examples.]  
- **Competencies/Mindsets**: [School-defined competency or mindset the student identifies with, with anecdotal evidence.]  

---

### **Extracurricular Involvement**
- **Top Activities**: [Most meaningful 2-3 activities, including leadership roles and volunteer work.]  
- **Key Achievements**: [Notable awards or accomplishments tied to extracurriculars.]  
- **Unique Experiences**: [Examples of research, internships, or distinctive projects.]  

---

### **Aspirations and College Preferences**
- **Future Goals**: [Desired field(s) of study and career aspirations.]  
- **College Fit**: [What the student hopes to find in a college and any schools they feel attached to.]  

---

### **Challenges and Support Needs**
- **Social-Emotional Concerns**: [Explicit worries or anxieties about the college process or personal challenges.]  
- **Support Network**: [Trusted individuals (friends, family, or faculty) they rely on.]  
- **Barriers**: [Financial, logistical, or other challenges they face.]  

---

### **Counselor Action Items**
- **Key Recommendations**: [2-3 actionable steps the counselor can take to support the student.]  
- **Focus Areas**: [Specific strengths, challenges, or aspirations to address during the meeting.]  
- **Application Insights**: [Themes for personal statements or application enhancements based on the student’s values and profile.]  

---

### Requirements
1. Use **direct quotes** from the student’s responses when applicable to support claims.
2. Ensure the summary is clear, concise, and actionable.
3. Organize the output in a professional, readable format.

### Output in Markup
Create your output in markup
            '''
            "IMPORTANT: if there is not information to complete a section of the summarize, please omit that section."
            "Do not wrap response in code block."
            "Given context: {context}"

        )
        prompt = ChatPromptTemplate.from_template(prompt)

        output_parser = StrOutputParser()
        chain = prompt | self.model | output_parser

        result = chain.invoke({
            "context": self.context,
        })

        self.result = result

    def save(self):
        assert len(self.result) > 0

        summary = self.prisma.generatedstudentconversationsummary.create(
            data=dict(
                student_id=self.student_id,
                content=self.result
            )
        )

        alog.info(summary.content)

        assert summary.id
