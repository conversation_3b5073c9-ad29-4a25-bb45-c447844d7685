import json

import alog
from pydantic.dataclasses import dataclass

from addie.lib import prisma_client, omit


@dataclass
class Prompt:
    content: str = None
    id: str = None
    exclude_keys = []

    def __post_init__(self):
        if self.id:
            self.load()
        else:
            self.save()

    def save(self):
        prisma = prisma_client()
        save_keys = ['content']
        data = {k: v for k, v in self.__dict__.items() if k in save_keys}

        prompt = prisma.prompt.create(
            data=data
        )

        self.id = prompt.id
        self.apply_values(prompt)

    def load(self):
        value = prisma_client().prompt.find_unique_or_raise(
            where=dict(id=self.id)
        )
        alog.info(value)
        self.apply_values(value)

    def apply_values(self, obj, exclude_keys=None):
        data = {k: v for k, v in obj.__dict__.items()}
        data = omit(self.exclude_keys, data)

        for k, v in data.items():
            setattr(self, k, v)
