import asyncio
import json
from dataclasses import field, asdict
from typing import Any, List, Optional
import alog
import prisma.models
import redis
from celery import Celery
from prisma import Prisma
from prisma.enums import WorkflowStatus, StudentWorkflowStatus, WorkflowType
from pydantic import validate_call_decorator, ConfigDict
from pydantic.dataclasses import dataclass

from addie import celery_config, settings
from addie.lib import prisma_client, omit, pick, dict_to_json, search_collection


app = Celery("addie")
conf = app.config_from_object(celery_config)

search_chat_history_task = app.signature(
    "addie.tasks.search_chat_history_task",
)
search_teacher_comments_task = app.signature(
    "addie.tasks.search_teacher_comments_task",
)
# Initialize Redis client with proper defaults and error handling
def get_redis_client():
    """Get Redis client with proper configuration and error handling."""
    try:
        # Use REDIS_CONN if available (preferred method)
        if settings.REDIS_CONN:
            return redis.from_url(settings.REDIS_CONN)

        # Fallback to individual parameters with defaults
        host = settings.REDIS_HOST or '0.0.0.0'
        port = int(settings.REDIS_PORT) if settings.REDIS_PORT else 6379
        password = settings.REDIS_PASS or 'addie!=='

        return redis.Redis(host=host, port=port, password=password)
    except Exception as e:
        alog.error(f"Failed to initialize Redis client: {e}")
        # Return a mock client that doesn't break the application
        return None

redis_client = get_redis_client()


# @validate_call_decorator
def cache_json_for_user(user_id: str, key: str, data: any):
    """Cache JSON data for a user in Redis with error handling."""
    try:
        if redis_client is None:
            alog.warning(f"Redis client not available, cannot cache data for user:{user_id}:{key}")
            return

        json_str = dict_to_json(data)
        redis_client.set(f"user:{user_id}:{key}", json_str)
    except Exception as e:
        alog.error(f"Error caching data to Redis for user:{user_id}:{key} - {e}")

    # stored_json = r.json().get('user:1', '$')


def get_json_for_user(user_id: str, key: str):
    """Get JSON data for a user from Redis cache with error handling."""
    try:
        if redis_client is None:
            alog.warning(f"Redis client not available, cannot get data for user:{user_id}:{key}")
            return None

        json_str = redis_client.get(f"user:{user_id}:{key}")

        if json_str:
            return json.loads(json_str)
    except Exception as e:
        alog.error(f"Error getting data from Redis for user:{user_id}:{key} - {e}")

    return None




@dataclass
class StudentContext:
    user_id: str
    id: Optional[str] = None
    grade: int = 11
    academic_achievements: list = field(default_factory=list)
    growth_opportunities: list = field(default_factory=list)
    pending_workflows: list = field(default_factory=list)
    pending_workflow_steps: list = field(default_factory=list)
    current_workflow_name: Optional[str] = None
    messages: list = field(default_factory=lambda: [])
    user_message_history: List[dict] = field(default_factory=lambda: [])
    teacher_comments: List[str] = field(default_factory=list)
    alternate_user_id: Optional[str] = None
    user: Optional[dict] = None
    current_question: Optional[dict] = None
    next_question: Optional[dict] = None
    teacher_comments_summary: List[dict] = field(default_factory=lambda: [])
    onboarding_summary: List[dict] = field(default_factory=lambda: [])
    _student_workflow_id: Optional[str] = None
    mode: str = "Production"  # Possible values: "Production" or "Test"
    _workflow_id: Optional[str] = None

    model_config = ConfigDict(arbitrary_types_allowed=True)

    @property
    def student_workflow_id(self) -> Optional[str]:
        return self._student_workflow_id

    @student_workflow_id.setter
    def student_workflow_id(self, value: Optional[str]):
        self._student_workflow_id = value

    def __post_init__(self):
        pass

    async def async_init(self):
        self.prisma = prisma_client()

        assert self.id

        # If we have a workflow_id but no student_workflow_id yet, try to find or create the associated student workflow
        if self.workflow_id and not self.student_workflow_id:
            # Find the workflow by ID with retry logic for parallel test environments
            workflow = None
            max_retries = 3
            retry_count = 0
            
            while retry_count < max_retries:
                try:
                    workflow = self.prisma.workflow.find_unique(
                        where=dict(id=self.workflow_id), include=dict(steps=True)
                    )
                    if workflow:
                        break
                    else:
                        alog.warning(f"Workflow {self.workflow_id} not found, retry {retry_count + 1}/{max_retries}")
                        retry_count += 1
                        if retry_count < max_retries:
                            import time
                            time.sleep(0.1)  # Small delay for database consistency
                except Exception as e:
                    alog.warning(f"Error finding workflow {self.workflow_id}, retry {retry_count + 1}/{max_retries}: {e}")
                    retry_count += 1
                    if retry_count < max_retries:
                        import time
                        time.sleep(0.1)
            
            if not workflow:
                alog.error(f"Failed to find workflow {self.workflow_id} after {max_retries} retries")
                # Continue without setting student_workflow_id to allow graceful degradation
                return self
                
            # Create or find the student_workflow for this workflow
            student_wf = self.upsert_student_wf(workflow)
            self.student_workflow_id = student_wf.id
            alog.debug(
                f"Set student_workflow_id to {self.student_workflow_id} from workflow_id {self.workflow_id}"
            )

        # Fetch workflow_id if we have a student_workflow_id but no workflow_id yet
        if self.student_workflow_id and not self.workflow_id:
            alog.info(f'## student_workflow_id: {self.student_workflow_id}')

            try:
                student_workflow = self.prisma.studentworkflow.find_first(
                    where=dict(id=self.student_workflow_id), include=dict(workflow=True)
                )
                if student_workflow and student_workflow.workflow:
                    self.workflow_id = student_workflow.workflow.id
                    alog.debug(
                        f"Set workflow_id to {self.workflow_id} from student_workflow_id {self.student_workflow_id}"
                    )
                else:
                    alog.warning(f"StudentWorkflow {self.student_workflow_id} not found or has no workflow")
            except Exception as e:
                alog.warning(f"Error finding student workflow {self.student_workflow_id}: {e}")

        tasks = []

        if len(self.messages) > 0:
            last_msg_content = self.messages[-1]["content"]

            # search_chat_history_task.apply_async(
            #     kwargs=dict(query=last_msg_content, user_id=self.user_id)
            # )
            # search_teacher_comments_task.apply_async(
            #     kwargs=dict(
            #         query=last_msg_content,
            #         user_id=self.user_id,
            #         alternate_user_id=self.alternate_user_id,
            #     )
            # )

        tasks += [
            # self.get_chat_history(),
            # self.get_relevant_teacher_comments(),
            self.student_enrichments(),
            self.get_pending_workflow_steps(),
        ]

        await asyncio.gather(*tasks)

        # Allow both workflow_id and student_workflow_id to be None for general chat mode
        # This enables dynamic workflow selection in SMS agents
        if not self.workflow_id or not self.student_workflow_id:
            alog.info("Operating in general chat mode - no specific workflow assigned")
            return self

        return self

    async def get_chat_history(self):
        self.user_message_history = (
            get_json_for_user(self.user_id, "user_message_history") or []
        )

        if len(self.user_message_history) > 1:
            self.user_message_history = self.user_message_history[1:]

    async def get_relevant_teacher_comments(self):
        self.teacher_comments = get_json_for_user(self.user_id, "teacher_comments")
        # alog.info('## user-teacher-comments ##')
        # alog.info(alog.pformat(self.teacher_comments))

    async def student_enrichments(self):
        user = self.prisma.user.find_unique_or_raise(
            where={"id": self.user_id},
            include={
                "students": {
                    "include": {
                        "growth_opportunities": True,
                        "academic_achievements": True,
                    }
                },
            },
        )
        self.user = pick(["id", "first_name", "last_name"], user.model_dump())

        student = user.students[0]
        self.id = student.id
        assert student

        excluded_keys = ["createdAt", "updatedAt", "id", "role"]
        student = pick(
            [
                "grade",
                "growth_opportunities",
                "academic_achievements",
            ],
            student.model_dump(),
        )

        growth_opps = []

        for growth_opp in student["growth_opportunities"]:
            growth_opp = omit(
                ["data", "student_agent", "studentId", "id", "createdAt", "updatedAt"],
                growth_opp,
            )

            growth_opps.append(growth_opp)

        student["growth_opportunities"] = growth_opps

        achievements = []
        for academic_achievement in student["academic_achievements"]:
            academic_achievement = omit(
                ["data", "student_agent", "studentId", "id", "createdAt", "updatedAt"],
                growth_opp,
            )
            achievements.append(academic_achievement)

        student["academic_achievements"] = achievements

        onboarding_summary = self.prisma.generatedstudentconversationsummary.find_first(
            where=dict(student_id=self.id)
        )

        if onboarding_summary:
            onboarding_summary = onboarding_summary.model_dump()

        student["onboarding_summary"] = onboarding_summary

        teacher_comments_summary = (
            self.prisma.generatedteachercommentssummary.find_first(
                where=dict(student_id=self.id)
            )
        )
        if teacher_comments_summary:
            teacher_comments_summary = teacher_comments_summary.model_dump()

        student["teacher_comments_summary"] = teacher_comments_summary

        for k, v in student.items():
            setattr(self, k, v)

    @property
    def workflow_id(self) -> Optional[str]:
        return self._workflow_id

    @workflow_id.setter
    def workflow_id(self, value: Optional[str]):
        self._workflow_id = value

    def ensure_student_workflows(self):
        top_tier_workflows = self.prisma.workflow.find_many(
            # not INACTIVE
            where=dict(
                parent_step=None,
                status=WorkflowStatus.PUBLISHED,
                workflow_type=WorkflowType.UNSTRUCTURED,
            ),
            include=dict(
                steps=dict(include=dict(child_workflows=dict(include=dict(steps=True))))
            ),
        )

        for workflow in top_tier_workflows:
            student_wf = self.upsert_student_wf(workflow)
            if not student_wf:
                continue  # Skip this workflow if upsert failed

            for step in workflow.steps:
                self.upsert_student_step(step, student_wf)

                for wf in step.child_workflows:
                    child_student_wf = self.upsert_student_wf(wf)
                    if child_student_wf:
                        for child_wf_step in wf.steps:
                            self.upsert_student_step(child_wf_step, child_student_wf)

                        self.upsert_student_wf(wf, True)

                self.upsert_student_wf(workflow, True)

            try:
                student_wf = self.prisma.studentworkflow.find_first(
                    where=dict(
                        student_id=self.id,
                        workflow_id=workflow.id,
                    ),
                    include=dict(steps=True),
                )
                
                if student_wf and len(student_wf.steps) != len(workflow.steps):
                    alog.warning(f"Step count mismatch for workflow {workflow.id}: expected {len(workflow.steps)}, got {len(student_wf.steps)}")
            except Exception as e:
                alog.warning(f"Error verifying student workflow steps for workflow {workflow.id}: {e}")

    def upsert_student_wf(self, workflow, init_steps=False):
        # First verify that the workflow still exists before attempting to connect
        try:
            existing_workflow = self.prisma.workflow.find_unique(
                where=dict(id=workflow.id)
            )
            if not existing_workflow:
                alog.warning(f"Workflow {workflow.id} not found, skipping student workflow creation")
                return None
        except Exception as e:
            alog.warning(f"Error checking workflow {workflow.id}: {e}, skipping student workflow creation")
            return None
            
        data = dict(
            student=dict(connect=dict(id=self.id)),
            workflow=dict(connect=dict(id=workflow.id)),
        )

        if init_steps:
            data["init_steps"] = init_steps

        # alog.info(alog.pformat(data))
        # alog.info(f"{self.id} {workflow.id}")

        try:
            student_wf = self.prisma.studentworkflow.upsert(
                where=dict(
                    student_id_workflow_id=dict(student_id=self.id, workflow_id=workflow.id)
                ),
                data=dict(create=data, update=data),
            )
            return student_wf
        except Exception as e:
            alog.warning(f"Error upserting student workflow for workflow {workflow.id}: {e}")
            return None

    def upsert_student_step(self, step, student_wf):
        if not student_wf:
            alog.warning(f"Cannot create student step for step {step.id}: student_wf is None")
            return None
            
        try:
            data = dict(
                student=dict(connect=dict(id=self.id)),
                step=dict(connect=dict(id=step.id)),
                data=dict_to_json(step.data),
                student_workflow=dict(connect=dict(id=student_wf.id)),
            )

            student_step = self.prisma.studentworkflowstep.upsert(
                where=dict(student_id_step_id=dict(student_id=self.id, step_id=step.id)),
                data=dict(create=data, update=data),
            )
            return student_step
        except Exception as e:
            alog.warning(f"Error upserting student step for step {step.id}: {e}")
            return None

    @property
    def workflows(self):
        wfs = self.prisma.studentworkflow.find_many(where=dict(student_id=self.id))
        return wfs

    def get_pending_workflows(self):
        # If a specific workflow_id is provided, ensure only one student workflow is in pending workflows
        if self.workflow_id:
            # First, check if we already have a pending workflow for this workflow_id
            try:
                existing_wf = self.prisma.studentworkflow.find_first(
                    where=dict(student_id=self.id, workflow=dict(id=self.workflow_id)),
                    include=dict(workflow=True),
                )
                if existing_wf and existing_wf.workflow:
                    self.pending_workflows = [
                        dict(name=existing_wf.workflow.name, id=existing_wf.id)
                    ]
                else:
                    alog.warning(f"No student workflow found for workflow_id {self.workflow_id} and student {self.id}")
                    self.pending_workflows = []
            except Exception as e:
                alog.warning(f"Error finding student workflow for workflow_id {self.workflow_id}: {e}")
                self.pending_workflows = []
        else:
            try:
                wfs = self.prisma.studentworkflow.find_many(
                    where=dict(
                        student_id=self.id,
                        workflow=dict(workflow_type="UNSTRUCTURED"),
                        OR=[
                            dict(status=StudentWorkflowStatus.NOT_STARTED),
                            dict(status=StudentWorkflowStatus.IN_PROGRESS),
                        ],
                    ),
                    include=dict(steps=dict(include=dict(step=True)), workflow=True),
                )

                pending_wfs = []
                for wf in wfs:
                    # Check if workflow is properly loaded
                    if not wf.workflow:
                        alog.warning(f"StudentWorkflow {wf.id} has no associated workflow, skipping")
                        continue
                        
                    for step in wf.steps:
                        # Check if step is properly loaded (handle null step field)
                        if not step or not hasattr(step, 'step') or not step.step:
                            alog.warning(f"StudentWorkflowStep {step.id if step else 'unknown'} has no associated step, skipping")
                            continue
                            
                        if not step.completed:
                            incomplete_wf = dict(name=wf.workflow.name, id=wf.id)
                            pending_wfs.append(incomplete_wf)
                            break

                self.pending_workflows = pending_wfs
            except Exception as e:
                alog.warning(f"Error getting pending workflows for student {self.id}: {e}")
                self.pending_workflows = []

            num_wfs = len(self.pending_workflows)
            alog.info(f"## num pending workflows: {num_wfs} ##")

    async def get_pending_workflow_steps(self):
        if not self.student_workflow_id:
            alog.info("No student_workflow_id, ensuring student workflows")
            self.ensure_student_workflows()

        alog.info(f"Getting pending workflows for student ID: {self.id}")
        self.get_pending_workflows()
        self.pending_workflow_steps = []

        alog.info(f"Workflow ID: {self.workflow_id}")

        # Allow workflow_id to be None for general chat mode when no workflows are found
        # This enables dynamic workflow selection in SMS agents
        if not self.workflow_id:
            alog.info("No workflow_id found - operating in general chat mode")
            return

        # If a specific student_workflow_id is provided, prioritize that workflow
        if self.student_workflow_id:
            alog.info(f"Looking up specific student workflow: {self.student_workflow_id}")
            try:
                wf = self.prisma.studentworkflow.find_first(
                    where=dict(id=self.student_workflow_id),
                    include=dict(
                        steps=dict(include=dict(step=True)),
                        workflow=dict(
                            include=dict(steps=dict(include=dict(child_workflows=True)))
                        ),
                    ),
                )
                if wf:
                    alog.info(f"Found student workflow with ID: {wf.id}")
                    # Process this specific workflow
                    self._process_workflow_steps(wf)

                    if self.pending_workflow_steps:
                        return
                else:
                    alog.warning(f"Student workflow {self.student_workflow_id} not found")
            except Exception as e:
                alog.warning(f"Error finding student workflow {self.student_workflow_id}: {e}")
                # Continue to try other workflows

        # Only process other workflows if we didn't specify a workflow_id
        if not self.workflow_id:
            # Process regular pending workflows if no specific workflow was found
            for pending_wf in self.pending_workflows:
                wf_id = pending_wf["id"]

                try:
                    wf = self.prisma.studentworkflow.find_first(
                        where=dict(id=wf_id),
                        include=dict(
                            steps=dict(include=dict(step=True)),
                            workflow=dict(
                                include=dict(steps=dict(include=dict(child_workflows=True)))
                            ),
                        ),
                    )
                    
                    if not wf:
                        alog.warning(f"Pending workflow {wf_id} not found, skipping")
                        continue

                    # Process this workflow's steps
                    if self._process_workflow_steps(wf):
                        # If we found steps, break the loop
                        self.student_workflow_id = wf_id
                        break
                except Exception as e:
                    alog.warning(f"Error processing pending workflow {wf_id}: {e}")
                    continue

    @property
    def workflow_name(self):
        """Get the current workflow name

        Returns:
            str: The name of the current workflow
        """
        if self.workflow_id:
            # Fetch the workflow name from the database if workflow_id is set
            try:
                workflow = self.prisma.workflow.find_first(
                    where=dict(id=self.workflow_id)
                )
                if workflow:
                    return workflow.name
                else:
                    alog.warning(f"Workflow {self.workflow_id} not found for name lookup")
            except Exception as e:
                alog.warning(f"Error getting workflow name for {self.workflow_id}: {e}")
        
        # Fall back to current_workflow_name if workflow_id is not set or workflow not found
        return self.current_workflow_name

    @property
    def is_sms_mode(self) -> bool:
        """Check if the current workflow is in SMS mode.

        Returns:
            bool: True if the current workflow is in SMS mode, False otherwise
        """
        if self.student_workflow_id:
            student_workflow = self.prisma.studentworkflow.find_first(
                where={"id": self.student_workflow_id}
            )
            if student_workflow and hasattr(student_workflow, "mode"):
                return student_workflow.mode == "sms"
        return False

    def set_workflow_mode(self, mode: str = "web") -> bool:
        """Set the mode of the current workflow.

        Args:
            mode: The mode to set, can be 'web', 'sms', or 'voice'

        Returns:
            bool: True if the mode was successfully set, False otherwise
        """
        if not self.student_workflow_id:
            return False

        try:
            # Update the workflow mode
            self.prisma.studentworkflow.update(
                where={"id": self.student_workflow_id},
                data={"mode": mode}
            )
            return True
        except Exception as e:
            alog.error(f"Error setting workflow mode: {str(e)}")
            return False

    def _process_workflow_steps(self, wf):
        """Process a workflow's steps and add incomplete steps to pending_workflow_steps

        Args:
            wf: The workflow to process

        Returns:
            bool: True if steps were found and added, False otherwise
        """
        steps = wf.steps
        alog.info(f"Found {len(steps)} steps in workflow {wf.id}")
        alog.info(f"Workflow name: {wf.workflow.name if hasattr(wf, 'workflow') else 'N/A'}")

        # If no student workflow steps exist, create them
        if len(steps) == 0:
            alog.info(f"No steps found for workflow {wf.id}. Attempting to create steps from original workflow.")
            self._create_steps_for_student_workflow(wf)

            # Refresh the workflow to get the newly created steps
            wf = self.prisma.studentworkflow.find_first_or_raise(
                where=dict(id=wf.id),
                include=dict(
                    steps=dict(include=dict(step=True)),
                    workflow=dict(
                        include=dict(steps=dict(include=dict(child_workflows=True)))
                    ),
                ),
            )
            steps = wf.steps
            alog.info(f"After creating steps, found {len(steps)} steps in workflow {wf.id}")

        if len(steps) > 0:
            steps.sort(key=lambda x: x.step.index)

        # Look for unstructured-goals steps
        for i, step in enumerate(steps):
            alog.info(f"Step {i+1}/{len(steps)}: id={step.id}, completed={step.completed}")
            # alog.info(f"Step data: {step.data}")

            if not step.completed:
                # Found a matching unstructured-goals step that's not completed
                alog.info(f"Processing incomplete step: {step.id}")
                step_dict = step.dict()
                step_dict["step_id"] = step_dict["id"]
                del step_dict["id"]
                del step_dict["step"]
                del step_dict["student_workflow"]
                del step_dict["student_workflow_id"]
                del step_dict["student_id"]

                self.pending_workflow_steps.append(step_dict)
                alog.info(f"Added step to pending_workflow_steps. Current count: {len(self.pending_workflow_steps)}")

                self.current_workflow_name = self.workflow_name

        self.init_questions()

        # Return True if we found steps
        return len(self.pending_workflow_steps) > 0



    def _create_steps_for_student_workflow(self, student_wf):
        """Create StudentWorkflowStep entries for a StudentWorkflow based on the original WorkflowStep entries

        Args:
            student_wf: The StudentWorkflow for which to create steps

        Returns:
            bool: True if steps were created, False otherwise
        """
        if not hasattr(student_wf, 'workflow') or not student_wf.workflow:
            alog.warning(f"StudentWorkflow {student_wf.id} has no associated Workflow")
            return False

        # Get the original workflow with its steps
        original_workflow = self.prisma.workflow.find_unique_or_raise(
            where=dict(id=student_wf.workflow_id),
            include=dict(steps=True)
        )

        if not original_workflow.steps or len(original_workflow.steps) == 0:
            alog.warning(f"Workflow {original_workflow.id} has no steps to copy")
            return False

        alog.info(f"Creating {len(original_workflow.steps)} steps for StudentWorkflow {student_wf.id}")

        # Create a StudentWorkflowStep for each WorkflowStep
        created_steps = []
        for workflow_step in original_workflow.steps:
            # Create the StudentWorkflowStep
            student_step_data = {
                "student": {"connect": {"id": self.id}},
                "step": {"connect": {"id": workflow_step.id}},
                "student_workflow": {"connect": {"id": student_wf.id}},
                "data": dict_to_json(workflow_step.data)
            }

            student_step = self.prisma.studentworkflowstep.create(data=student_step_data)
            created_steps.append(student_step)
            alog.info(f"Created StudentWorkflowStep {student_step.id} for step {workflow_step.name}")

        return len(created_steps) > 0

    def init_questions(self):
        alog.info(
            f"### num pending workflow steps: {len(self.pending_workflow_steps)} ###"
        )

        # We're only queuing one step now
        if self.pending_workflow_steps:
            alog.info("Found pending workflow steps, setting current_question")
            self.current_question = self.pending_workflow_steps[0]

            # alog.info(f"Set current_question: {self.current_question}")

            # Handle next question if available
            if len(self.pending_workflow_steps) > 1:
                alog.info("Found additional pending steps, setting next_question")
                self.next_question = self.pending_workflow_steps[1]
                alog.info(f"Set next_question: {self.next_question}")
        else:
            alog.info("No pending workflow steps found, setting current_question and next_question to None")
            self.current_question = None
            self.next_question = None
            alog.info(f"Workflow ID: {self.workflow_id}, Student workflow ID: {self.student_workflow_id}")

    def mark_current_step_complete(self):
        """Mark the current workflow step as complete

        Raises:
            ValueError: If there is no current step to complete

        Returns:
            bool: True if a step was successfully marked as complete
        """
        if not self.pending_workflow_steps or len(self.pending_workflow_steps) == 0:
            raise ValueError("No current workflow step to complete")

        next_step = self.pending_workflow_steps[0]

        # Verify the step exists
        self.prisma.studentworkflowstep.find_first_or_raise(
            where=dict(id=next_step["step_id"]),
        )

        # Update the step to mark it as completed
        self.prisma.studentworkflowstep.update(
            where=dict(id=next_step["step_id"]), data=dict(completed=True)
        )

        return True

    @property
    def completed_steps(self) -> int:
        """Get the number of completed workflow steps for this student.

        Returns:
            int: The count of completed workflow steps
        """
        completed_count = self.prisma.studentworkflowstep.count(
            where=dict(student_id=self.id, completed=True)
        )
        return completed_count

    def is_last_step_completed(self) -> bool:
        """
        Check if the last student workflow step is completed.

        Returns:
            bool: True if the last step is completed, False otherwise.
        """
        assert self.student_workflow_id

        steps = self.prisma.studentworkflowstep.find_many(
            where={"student_workflow_id": self.student_workflow_id},
            include=dict(step=True),
        )

        steps.sort(key=lambda x: x.step.index)
        last_step = steps[-1]

        return last_step.completed

    @staticmethod
    def search_chat_history(query: str, user_id: str):
        # Function disabled
        alog.info("search_chat_history function has been disabled")
        return

    @staticmethod
    def search_teacher_comments(user_id: str, alternate_user_id: str, query: str):
        alog.info(
            alog.pformat(
                dict(user_id=user_id, alternate_user_id=alternate_user_id, query=query)
            )
        )

        if alternate_user_id:
            user_id = alternate_user_id

        user = prisma_client().user.find_unique_or_raise(where=dict(id=user_id))

        if not user:
            return

        results = search_collection("teacher_comments", query, user_id)
        data = []

        for doc in results:
            assert doc.metadata["user_id"] == user_id
            # doc_obj = json.loads(doc.page_content)
            data.append(doc.page_content)

        cache_json_for_user(user_id, "teacher_comments", data)

        alog.info(data)

    def dump(self):
        data = asdict(self)
        data = omit(["messages", "pending_workflows", "pending_workflow_steps"], data)

        assert "messages" not in data
        # assert 'user_message_history' in data

        # Remove sensitive user IDs
        if "user" in data and data["user"] and "id" in data["user"]:
            del data["user"]["id"]
        if "user_id" in data:
            del data["user_id"]
        # if "id" in data:
        #     del data["id"]
        if "alternate_user_id" in data:
            del data["alternate_user_id"]

        # remove date fields nested and in arrays
        for k, v in data.items():
            if isinstance(v, dict):
                data[k] = omit(["created_at", "updated_at"], v)
            elif isinstance(v, list):
                for item in v:
                    if isinstance(item, dict):
                        data[k] = omit(["created_at", "updated_at"], item)

        # Clean up question data
        questions = []
        if data.get("current_question"):
            questions.append(data["current_question"])
        if data.get("next_question"):
            questions.append(data["next_question"])

        for q in questions:
            if not q:
                continue

            # Remove unnecessary fields from questions
            if "child_workflows" in q:
                del q["child_workflows"]
            if "data" in q and isinstance(q["data"], dict):
                if "created_at" in q["data"]:
                    del q["data"]["created_at"]
                if "index" in q["data"]:
                    del q["data"]["index"]
                if "question_hash" in q["data"]:
                    del q["data"]["question_hash"]
                if "question_id" in q["data"]:
                    del q["data"]["question_id"]
                if "questionnaire_id" in q["data"]:
                    del q["data"]["questionnaire_id"]
                if "updated_at" in q["data"]:
                    del q["data"]["updated_at"]

            if "index" in q:
                del q["index"]
            if "name" in q:
                del q["name"]
            if "parent_workflow" in q:
                del q["parent_workflow"]
            if "parent_workflow_id" in q:
                del q["parent_workflow_id"]
            if "student_steps" in q:
                del q["student_steps"]

        return data

    @staticmethod
    def save(message_id: int, data: dict):
        context_data = dict_to_json(data)

        # Convert integer message_id to string to match the database schema
        data = dict(data=context_data, message_id=str(message_id))

        context = prisma_client().studentcontext.create(data)
