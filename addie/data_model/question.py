import hashlib
import alog
from addie.lib import prisma_client, omit
from typing import Optional
from pydantic.dataclasses import dataclass
from dataclasses import asdict, is_dataclass, field
import traceback


@dataclass
class Question:
    question_hash: Optional[str] = field(init=False, default=None)
    can_skip: bool = None
    id: str = None
    index: int = None
    question: str = None
    questionnaire_id: str = None
    slug: str = None
    workflow_id: str = None
    question_type: str = "Question"

    def __post_init__(self):
        self.prisma = prisma_client()

        if self.can_skip is None and not self.id:
            raise ValueError("can_skip is required")

        if self.question:
            clean_question = self.question.strip()
            clean_question = clean_question.replace("\n", " ")
            clean_question = clean_question.replace("  ", " ")
            self.question = clean_question

        # Load if we have an ID but missing data
        if self.id and (not self.question or not self.workflow_id):
            self.load()
        # Save if we have enough data to create/update
        elif self.question and self.question_type:
            self.save()

    def _compute_question_hash(self):
        if not (self.question and self.question_type):
            alog.info(
                f"question: {self.question} question_type: {self.question_type}"
            )

            raise ValueError(
                "All necessary values are required to compute question hash"
            )

        self.question_hash = hashlib.sha256(
            f"{self.question}{self.workflow_id}{self.question_type}".encode()
        ).hexdigest()

    def save(self):
        prisma = prisma_client()
        self._compute_question_hash()

        data = {k: v for k, v in self.__dict__.items()}
        exclude_keys = [
            "prisma",
            "workflow_id",
            "question_type",
            "questionnaire",
            "response",
        ]

        if not self.id:
            exclude_keys.append("id")
        data = omit(exclude_keys, data)
        data["question_hash"] = self.question_hash

        alog.info(alog.pformat(data))

        question = prisma.question.upsert(
            where=dict(
                questionnaire_id_question_hash=dict(
                    questionnaire_id=self.questionnaire_id,
                    question_hash=self.question_hash,
                )
            ),
            data=dict(create=data, update=data),
        )

        self.apply_values(question)

    def load(self):
        question = prisma_client().question.find_unique(where=dict(id=self.id))
        self.apply_values(question)

    def delete(self):
        prisma_client().question.delete(where=dict(id=self.id))

    def apply_values(self, question):
        data = {k: v for k, v in question.__dict__.items()}
        exclude_keys = []
        data = omit(exclude_keys, data)

        for k, v in data.items():
            setattr(self, k, v)
