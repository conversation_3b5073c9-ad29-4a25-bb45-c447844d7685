from addie.data_model.question import Question
from dataclasses import field
from pydantic.dataclasses import dataclass
import alog
from addie.lib import prisma_client, omit


@dataclass
class BinaryQuestion(Question):
    """A question that can only have binary (yes/no) responses."""
    # add workflow_id
    workflow_id: str = None
    question_type: str = "BinaryQuestion"
    can_skip: bool = field(default=False)

    def __post_init__(self):
        super().__post_init__()
        self._compute_question_hash()

    def delete(self):
        prisma_client().binaryquestion.delete(where=dict(id=self.id, workflow_id=self.workflow_id))

    def load(self):
        question = prisma_client().binaryquestion.find_unique_or_raise(
            where=dict(id=self.id)
        )
        self.apply_values(question)

    def save(self):
        self._compute_question_hash()
        data = {k: v for k, v in self.__dict__.items()}
        exclude_keys = [
            "prisma",
            "workflow_id",
            "question_type",
            "questionnaire",
            "responses"
        ]

        if not self.id:
            exclude_keys.append("id")
        data = omit(exclude_keys, data)
        data["question_hash"] = self.question_hash

        if 'slug' not in data:
            raise ValueError("slug is required")

        alog.info(alog.pformat(data))

        question = self.prisma.binaryquestion.upsert(
            where=dict(
                questionnaire_id_question_hash=dict(
                    questionnaire_id=self.questionnaire_id,
                    question_hash=self.question_hash,
                )
            ),
            data=dict(create=data, update=data),
        )

        self.apply_values(question)
