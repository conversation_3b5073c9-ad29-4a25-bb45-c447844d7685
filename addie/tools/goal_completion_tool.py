from celery import Celery

from addie import celery_config
from addie.lib import prisma_client, pick
from pydantic import BaseModel, ConfigDict
import traceback
from langchain_core.tools import BaseTool
from typing import Type
import alog
from prisma.enums import WorkflowType

app = Celery("addie")
conf = app.config_from_object(celery_config)

gen_student_conversation_summary_task = app.signature(
    "addie.tasks.gen_student_conversation_summary_task"
)

gen_unstructured_conversation_summary_task = app.signature(
    "addie.tasks.gen_unstructured_conversation_summary_task"
)

calculate_ocean_scores_task = app.signature(
    "addie.tasks.calculate_ocean_scores_task"
)


def check_and_complete_workflow(student_workflow_id: str, student_id: str) -> bool:
    """
    Check if all steps in a workflow are completed and mark the workflow as completed if so.
    Also triggers appropriate summary generation based on workflow type.

    Args:
        student_workflow_id: The ID of the student workflow to check
        student_id: The ID of the student (for summary generation)

    Returns:
        bool: True if the workflow was completed, False otherwise
    """
    try:
        prisma = prisma_client()

        # Get the workflow and all its steps to check if this was the last one
        workflow_steps = prisma.studentworkflowstep.find_many(
            where=dict(  # type: ignore
                student_workflow_id=student_workflow_id,
            )
        )

        # Check if all steps are completed
        all_steps_completed = all(s.completed for s in workflow_steps)
        if all_steps_completed:
            # Get the student workflow to check its type
            student_workflow = prisma.studentworkflow.find_unique(
                where=dict(id=student_workflow_id),  # type: ignore
                include=dict(  # type: ignore
                    workflow=True
                )
            )

            # Mark the workflow as completed
            prisma.studentworkflow.update(
                where=dict(id=student_workflow_id),  # type: ignore
                data=dict(  # type: ignore
                    status="COMPLETED"
                )
            )

            alog.info(f"✅ Marked workflow {student_workflow_id} as COMPLETED for student {student_id}")

            # Generate appropriate summary based on workflow type
            if student_workflow and student_workflow.workflow and student_workflow.workflow.workflow_type == WorkflowType.UNSTRUCTURED:
                # Generate summary for unstructured conversation
                gen_unstructured_conversation_summary_task.apply_async(kwargs=dict(
                    student_id=student_id,
                    workflow_id=student_workflow.workflow.id
                ))
                alog.info(f"Triggered unstructured conversation summary for workflow {student_workflow.workflow.id}")
            else:
                # Generate summary for structured conversation
                # gen_student_conversation_summary_task.apply_async(kwargs=dict(
                #     student_id=student_id
                # ))
                alog.info(f"Triggered structured conversation summary for student {student_id}")

            # Check if this is a Big Five Personality Test workflow and trigger OCEAN calculation
            if student_workflow and student_workflow.workflow:
                workflow_tags = student_workflow.workflow.tags or []
                workflow_name = student_workflow.workflow.name or ""

                # Check if this workflow is tagged as BFI
                is_bfi_workflow = "type:The Big Five Personality Test (BFPT)" in workflow_tags

                if is_bfi_workflow:
                    alog.info(f"Detected BFI workflow completion for student {student_id} (workflow: {workflow_name}), triggering OCEAN scores calculation")
                    calculate_ocean_scores_task.apply_async(kwargs=dict(
                        student_id=student_id
                    ))
                    alog.info(f"Triggered OCEAN scores calculation for student {student_id}")

            return True

        return False

    except Exception as e:
        alog.error(f"Error checking and completing workflow: {str(e)}")
        return False


class GoalCompletionToolSchema(BaseModel):
    student_id: str
    step_id: str
    model_config = ConfigDict(arbitrary_types_allowed=True)


class GoalCompletionTool(BaseTool):
    """
    Use this tool to mark the current goal as complete.
    """

    name: str = "goal_completion_tool"
    description: str = """
    Use this tool to mark the current goal as complete.
    Be careful to use the `student_id` parameter to identify the student you want to complete the goal for.
    Be careful to use the `step_id` parameter to identify the goal you want to complete.
    """
    args_schema: Type[GoalCompletionToolSchema] = GoalCompletionToolSchema

    def _run(self, student_id: str, step_id: str) -> str:
        """Use the tool."""
        data = dict()
        args_data = dict(student_id=student_id, step_id=step_id)

        print('#' * 40)
        alog.info(args_data)
        print('#' * 40)

        try:
            prisma = prisma_client()

            print("#" * 40)
            print("Goal Completion Tool")
            print("#" * 40)
            print(alog.pformat(args_data))

            # Mark the current step as completed
            step = prisma.studentworkflowstep.update(
                where=dict(id=step_id),  # type: ignore
                data=dict(  # type: ignore
                    completed=True,
                )
            )

            alog.debug(step)

            # Check if workflow is complete and handle completion if so
            if step and step.student_workflow_id:
                workflow_completed = check_and_complete_workflow(step.student_workflow_id, student_id)
                if workflow_completed:
                    alog.info(f"Workflow completed for student {student_id}")

            print("#" * 40)
            print("Done Goal Completion Tool")
            print("#" * 40)

            if step:
                data = step.model_dump()
                return str(pick(["id", "completed"], data))
            else:
                return "Step not found"

        except Exception as err:
            alog.info(alog.pformat(data))
            alog.info(alog.pformat(args_data))
            alog.error(err)

            traceback.print_exception(type(err), err, err.__traceback__)

            raise err
            # return str(err)
