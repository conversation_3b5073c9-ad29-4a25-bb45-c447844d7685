#!/usr/bin/env python
"""
SMS Agent for Addie

This module provides SMS agents that handle both structured and unstructured conversations via text messages.
It processes incoming SMS messages, identifies the current workflow for the student,
validates and saves responses for structured workflows, or passes messages to the unstructured agent,
and returns appropriate SMS responses.
"""

import alog
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime
import re

from addie.lib import prisma_client
from addie.sms_agent.response_handlers import save_response
from addie import settings
from addie.agent import agent
from addie.agent.state.state import AgentState
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage


def log_sms_message_to_history(
    session_id: str, message_text: str, message_type: str = "human", source: str = "sms"
) -> None:
    """
    Log an SMS message to the messages table that LangChain uses.

    Args:
        session_id: The session ID (format: workflowId-userId)
        message_text: The content of the message
        message_type: Type of message ("human" for incoming, "ai" for outgoing)
        source: Source of the message (default "sms")
    """
    try:
        from langchain_core.messages import HumanMessage, AIMessage
        from addie.history import get_history

        # Create the appropriate LangChain message object with source metadata
        additional_kwargs = {"source": source, "timestamp": datetime.now().isoformat()}

        if message_type == "human":
            message = HumanMessage(
                content=message_text, additional_kwargs=additional_kwargs
            )
        elif message_type == "ai":
            message = AIMessage(
                content=message_text, additional_kwargs=additional_kwargs
            )
        else:
            # Default to HumanMessage for unknown types
            message = HumanMessage(
                content=message_text, additional_kwargs=additional_kwargs
            )

        # Use LangChain's history system to properly serialize and save the message
        history = get_history(session_id)
        history.add_message(message)

        alog.info(f"Logged {message_type} SMS message to session {session_id}")

    except Exception as e:
        alog.error(f"Error logging SMS message to history: {str(e)}")


def find_student_sms_ready_workflows(student_id: str) -> List[Any]:
    """
    Find workflows that are ready for SMS conversations, with proper priority handling.
    
    This function addresses the three SMS mode priority issues:
    1. Finds workflows already in SMS mode (user-initiated) with highest priority
    2. Supports both STRUCTURED and UNSTRUCTURED workflow types
    3. Prioritizes workflows that users manually set to SMS mode
    
    Priority order:
    1. Workflows already in SMS mode (highest priority)
    2. Workflows that can be switched to SMS mode (fallback)
    
    Args:
        student_id: The ID of the student
        
    Returns:
        List of StudentWorkflow objects ready for SMS, ordered by priority
    """
    try:
        prisma = prisma_client()
        
        # Priority 1: Look for workflows already in SMS mode (highest priority)
        # This includes both STRUCTURED and UNSTRUCTURED workflows
        sms_mode_workflows = prisma.studentworkflow.find_many(
            where={
                "student_id": student_id,
                "mode": "sms",
                "status": {"in": ["IN_PROGRESS", "NOT_STARTED"]},
                "workflow": {"status": "PUBLISHED"}
            },
            include={"workflow": True},
            order={"updated_at": "desc"}
        )
        
        if sms_mode_workflows:
            alog.info(f"Found {len(sms_mode_workflows)} workflows already in SMS mode for student {student_id}")
            return sms_mode_workflows
        
        # Priority 2: Look for workflows that can be switched to SMS mode
        # This is fallback behavior when no workflows are already in SMS mode
        switchable_workflows = prisma.studentworkflow.find_many(
            where={
                "student_id": student_id,
                "status": {"in": ["IN_PROGRESS", "NOT_STARTED"]},
                "workflow": {"status": "PUBLISHED"}
            },
            include={"workflow": True},
            order={"updated_at": "desc"}
        )
        
        alog.info(f"Found {len(switchable_workflows)} switchable workflows for student {student_id}")
        return switchable_workflows
        
    except Exception as e:
        alog.error(f"Error finding SMS-ready workflows: {str(e)}")
        return []


def get_priority_sms_workflow(student_id: str, workflow_id: Optional[str] = None) -> Tuple[Optional[Any], Optional[str]]:
    """
    Get the highest priority SMS workflow for a student, supporting both structured and unstructured workflows.
    
    This function replaces the old find_student_sms_workflow logic with proper priority handling:
    1. Prioritizes workflows already in SMS mode (user-initiated)
    2. Supports both STRUCTURED and UNSTRUCTURED workflow types
    3. Falls back to switchable workflows if no SMS mode workflows exist
    
    Args:
        student_id: The ID of the student
        workflow_id: Optional specific workflow ID to look for
        
    Returns:
        Tuple of (student_workflow, user_id) where student_workflow is the
        StudentWorkflow object and user_id is the associated user ID.
        Returns (None, None) if no workflow or user is found.
    """
    try:
        prisma = prisma_client()
        
        # Find the user associated with this student
        student = prisma.student.find_unique(
            where={"id": student_id}, include={"users": True}
        )

        if not student or not student.users or len(student.users) == 0:
            alog.error(f"No users found for student {student_id}")
            return None, None

        user_id = student.users[0].id

        # If a specific workflow_id is provided, look for that specific workflow
        if workflow_id:
            student_workflow = prisma.studentworkflow.find_first(
                where={
                    "student_id": student_id,
                    "workflow_id": workflow_id,
                    "status": {"in": ["IN_PROGRESS", "NOT_STARTED"]},
                    "workflow": {"status": "PUBLISHED"}
                },
                include={"workflow": True},
                order={"updated_at": "desc"}
            )
            
            if student_workflow:
                # Switch to SMS mode if not already
                if student_workflow.mode != "sms":
                    alog.info(f"Switching specific workflow {student_workflow.id} to SMS mode")
                    student_workflow = prisma.studentworkflow.update(
                        where={"id": student_workflow.id},
                        data={"mode": "sms", "status": "IN_PROGRESS"},
                        include={"workflow": True}
                    )
                return student_workflow, user_id
            else:
                alog.warning(f"Specific workflow {workflow_id} not found for student {student_id}")
                return None, user_id
        
        # Use the new priority-based workflow discovery
        sms_ready_workflows = find_student_sms_ready_workflows(student_id)
        
        if not sms_ready_workflows:
            alog.warning(f"No SMS-ready workflows found for student {student_id}")
            return None, user_id
        
        # Get the highest priority workflow (first in the list)
        student_workflow = sms_ready_workflows[0]
        
        # Activate NOT_STARTED workflows
        if student_workflow.status == "NOT_STARTED":
            alog.info(f"Activating NOT_STARTED workflow {student_workflow.id}")
            student_workflow = prisma.studentworkflow.update(
                where={"id": student_workflow.id},
                data={"status": "IN_PROGRESS"},
                include={"workflow": True}
            )
        
        # Ensure the workflow is in SMS mode
        if student_workflow.mode != "sms":
            alog.info(f"Switching workflow {student_workflow.id} to SMS mode")
            student_workflow = prisma.studentworkflow.update(
                where={"id": student_workflow.id},
                data={"mode": "sms"},
                include={"workflow": True}
            )
        
        return student_workflow, user_id
        
    except Exception as e:
        alog.error(f"Error getting priority SMS workflow: {str(e)}")
        return None, None


def find_student_sms_workflow(student_id: str, workflow_id: Optional[str] = None) -> Tuple[Optional[Any], Optional[str]]:
    """
    Find and potentially reactivate an SMS workflow for a student.
    
    This function is specifically designed for unstructured workflows and will only
    return workflows that are:
    - UNSTRUCTURED type 
    - PUBLISHED status
    - In SMS mode
    
    It encapsulates the logic for finding active SMS workflows,
    reactivating completed ones, and extracting user information.
    
    Args:
        student_id: The ID of the student
        workflow_id: Optional specific workflow ID to look for
        
    Returns:
        Tuple of (student_workflow, user_id) where student_workflow is the 
        StudentWorkflow object and user_id is the associated user ID.
        Returns (None, None) if no workflow or user is found.
    """
    try:
        prisma = prisma_client()
        
        # Find the user associated with this student
        student = prisma.student.find_unique(
            where={"id": student_id}, include={"users": True}
        )

        if not student or not student.users or len(student.users) == 0:
            alog.error(f"No users found for student {student_id}")
            return None, None

        user_id = student.users[0].id

        # Build the base query conditions for unstructured, published workflows
        # Note: We don't filter by mode here - we'll switch the workflow to SMS mode when found
        where_conditions = {
            "student_id": student_id,
            "workflow": {
                "workflow_type": "UNSTRUCTURED",
                "status": "PUBLISHED"
            }
        }
        
        # Add workflow_id filter if specified
        if workflow_id:
            where_conditions["workflow_id"] = workflow_id

        # Try to find an active workflow (IN_PROGRESS or NOT_STARTED only)
        alog.info(f"Searching for unstructured workflows with conditions: {where_conditions}")
        student_workflow = prisma.studentworkflow.find_first(
            where={**where_conditions, "status": {"in": ["IN_PROGRESS", "NOT_STARTED"]}},
            order={"updated_at": "desc"},
            include={"workflow": True},
        )
        alog.info(f"Found student workflow: {student_workflow.id if student_workflow else 'None'}")

        # If no active workflow is found, don't reactivate completed ones
        if not student_workflow:
            alog.warning(f"No active unstructured SMS workflow (IN_PROGRESS or NOT_STARTED) found for student {student_id}")
            return None, user_id
        
        # If we found a NOT_STARTED workflow, activate it by setting status to IN_PROGRESS
        # Only activate new workflows if this is the first message or explicitly requested
        if student_workflow.status == "NOT_STARTED":
            alog.info(f"Found NOT_STARTED unstructured workflow {student_workflow.id}, activating it")
            student_workflow = prisma.studentworkflow.update(
                where={"id": student_workflow.id},
                data={"status": "IN_PROGRESS"},
                include={"workflow": True},
            )
        
        # Ensure the workflow is in SMS mode (switch from web mode if needed)
        if student_workflow.mode != "sms":
            alog.info(f"Switching workflow {student_workflow.id} to SMS mode")
            student_workflow = prisma.studentworkflow.update(
                where={"id": student_workflow.id},
                data={"mode": "sms"},
                include={"workflow": True},
            )
            
        return student_workflow, user_id
        
    except Exception as e:
        alog.error(f"Error finding student SMS workflow: {str(e)}")
        return None, None


def get_session_id_for_sms(student_id: str, workflow_id: str) -> str:
    """
    Construct session ID for SMS conversations using the standard format.

    Args:
        student_id: The student ID
        workflow_id: The workflow ID

    Returns:
        Session ID in format: workflowId-userId
    """
    try:
        prisma = prisma_client()

        # Get the user_id from the student (many-to-many relationship)
        student = prisma.student.find_unique(
            where={"id": student_id}, include={"users": True}
        )

        if student and student.users and len(student.users) > 0:
            # Use the first user associated with this student
            user_id = student.users[0].id
            return f"{workflow_id}-{user_id}"
        else:
            alog.warning(
                f"Could not find user for student {student_id}, using student_id as fallback"
            )
            return f"{workflow_id}-{student_id}"

    except Exception as e:
        alog.error(f"Error constructing session ID: {str(e)}")
        return f"{workflow_id}-{student_id}"


async def sms_unstructured_agent(
    student_id: str,
    message_text: str,
    phone_number: Optional[str] = None,
    workflow_id: Optional[str] = None,
    student_workflow_id: Optional[str] = None,
) -> dict:
    """
    Process an incoming SMS message from a student for an unstructured workflow.
    This agent uses the LangChain-based student agent to handle conversations.
    
    By default, the agent will dynamically find the active SMS workflow for the student.
    For testing purposes, you can specify a specific workflow_id or student_workflow_id
    to override the dynamic selection.

    Args:
        student_id: The ID of the student sending the message
        message_text: The text content of the SMS message
        phone_number: Optional phone number of the student
        workflow_id: Optional ID of the workflow to use (for testing purposes)
        student_workflow_id: Optional ID of the student workflow to use (for testing purposes)

    Returns:
        A dict containing:
            - response: The SMS response text to send back to the student
            - student_workflow: The StudentWorkflow object
            - workflow_id: The workflow ID
            - session_id: The conversation session ID
            - message_count: Number of messages in the conversation
            - workflow_steps: Information about workflow steps and completion
            - user_id: The user ID
            - student_id: The student ID
    """
    try:
        alog.info(
            f"Processing unstructured SMS message from student {student_id}: {message_text}"
        )

        # Handle different workflow selection modes
        if student_workflow_id:
            # Testing mode: Use specific student workflow ID
            alog.info(f"Using specific student_workflow_id for testing: {student_workflow_id}")
            prisma = prisma_client()
            student_workflow = prisma.studentworkflow.find_unique(
                where={"id": student_workflow_id},
                include={"workflow": True}
            )
            if not student_workflow:
                return {
                    "response": "Sorry, the specified conversation could not be found.",
                    "student_workflow": None,
                    "workflow_id": workflow_id,
                    "session_id": None,
                    "message_count": 0,
                    "workflow_steps": {"error": "StudentWorkflow not found"},
                    "user_id": None,
                    "student_id": student_id
                }
            
            # Get user_id from the student
            student = prisma.student.find_unique(
                where={"id": student_id}, include={"users": True}
            )
            user_id = student.users[0].id if student and student.users else None
            
        elif workflow_id:
            # Testing mode: Use specific workflow_id
            alog.info(f"Using specific workflow_id for testing: {workflow_id}")
            student_workflow, user_id = get_priority_sms_workflow(student_id, workflow_id)
        else:
            # Production mode: Dynamic workflow selection (default behavior)
            alog.info(f"Using dynamic workflow selection for student {student_id}")
            student_workflow, user_id = get_priority_sms_workflow(student_id)
        
        if not student_workflow:
            if not user_id:
                error_msg = "Sorry, I couldn't find your account. Please contact your counselor for assistance."
                return {
                    "response": error_msg,
                    "student_workflow": None,
                    "workflow_id": workflow_id,
                    "session_id": None,
                    "message_count": 0,
                    "workflow_steps": {"error": "No account found"},
                    "user_id": user_id,
                    "student_id": student_id
                }
            
            # No active workflow found, but student exists - allow general chat
            alog.info(f"No active workflow found for student {student_id}, enabling general chat mode")
            
            # Create a general chat session ID using student_id
            general_chat_session_id = f"general-chat-{student_id}-{user_id}"
            
            # Initialize agent state for general chat (without workflow context)
            state = await AgentState.init(
                session_id=general_chat_session_id,
                user_id=user_id,
                student_id=student_id,
                student_workflow_id=None,  # No specific workflow
                agent_type="sms",
                log_student_context=True
            )

            # Add the human message to the state with SMS source information
            state["messages"] += [HumanMessage(
                content=message_text,
                additional_kwargs={"source": "sms", "timestamp": datetime.now().isoformat()}
            )]

            # Invoke the agent with the updated state
            state = agent().invoke(state)

            # Extract the AI's response
            messages = state["messages"]
            response_text = "Hi! I'm here to help you with any questions about college or life. What would you like to talk about?"
            
            # Look backwards through messages to find the last AIMessage
            for message in reversed(messages):
                if isinstance(message, AIMessage) and message.content:
                    response_text = message.content
                    break
                elif isinstance(message, ToolMessage):
                    alog.debug("Skipping ToolMessage in SMS response")
                    continue

            return {
                "response": response_text,
                "student_workflow": None,
                "workflow_id": None,
                "session_id": general_chat_session_id,
                "message_count": len(messages),
                "workflow_steps": {"mode": "sms", "total_steps": 0, "completed_steps": 0, "general_chat": True},
                "user_id": user_id,
                "student_id": student_id
            }

        workflow_id = student_workflow.workflow_id
        if 'prisma' not in locals():
            prisma = prisma_client()

        # Construct the session ID for this conversation
        session_id = f"{workflow_id}-{user_id}"
        alog.info(f"Using session ID: {session_id} for unstructured workflow")
        alog.info(
            alog.pformat(
                dict(
                    session_id=session_id,
                    user_id=user_id,
                    student_id=student_id,
                    student_workflow_id=student_workflow.id,
                )
            )
        )

        # Initialize agent state
        state = await AgentState.init(
            session_id=session_id,
            user_id=user_id,
            student_id=student_id,
            student_workflow_id=student_workflow.id,
            agent_type="sms",
            log_student_context=True
        )

        # Add the human message to the state with SMS source information
        state["messages"] += [HumanMessage(
            content=message_text,
            additional_kwargs={"source": "sms", "timestamp": datetime.now().isoformat()}
        )]

        # Invoke the agent with the updated state
        state = agent().invoke(state)

        # Extract the AI's response (last message that's not a tool message)
        messages = state["messages"]
        response_text = "I'm sorry, I couldn't process your message."
        
        # Look backwards through messages to find the last AIMessage
        # Skip ToolMessage objects which contain tool execution results
        for message in reversed(messages):
            if isinstance(message, AIMessage) and message.content:
                response_text = message.content
                break
            elif isinstance(message, ToolMessage):
                alog.debug("Skipping ToolMessage in SMS response")
                continue

        # Always collect and return metadata for internal purposes
        try:
            # Get workflow steps information
            workflow_steps = prisma.studentworkflowstep.find_many(
                where={
                    "student_id": student_id,
                    "student_workflow_id": student_workflow.id
                },
                include={"step": True}
            )
            
            completed_steps = [step for step in workflow_steps if step.completed]
            remaining_steps = [step for step in workflow_steps if not step.completed]
            
            # Get message count from conversation history
            message_count = len(messages)
            
            # Prepare workflow steps metadata
            workflow_steps_info = {
                "workflow_name": student_workflow.workflow.name if student_workflow.workflow else "Unknown",
                "total_steps": len(workflow_steps),
                "completed_steps": len(completed_steps),
                "remaining_steps": len(remaining_steps),
                "completion_percentage": (len(completed_steps) / len(workflow_steps) * 100) if workflow_steps else 0,
                "all_steps_completed": len(remaining_steps) == 0
            }
            
            # Return enhanced response with metadata
            return {
                "response": response_text,
                "student_workflow": student_workflow,
                "workflow_id": workflow_id,
                "session_id": session_id,
                "message_count": message_count,
                "workflow_steps": workflow_steps_info,
                "user_id": user_id,
                "student_id": student_id
            }
            
        except Exception as metadata_error:
            alog.warning(f"Error collecting metadata: {str(metadata_error)}")
            # Return basic metadata if detailed collection fails
            return {
                "response": response_text,
                "student_workflow": student_workflow,
                "workflow_id": workflow_id,
                "session_id": session_id,
                "message_count": len(messages),
                "workflow_steps": {"error": "Could not collect steps information"},
                "user_id": user_id,  
                "student_id": student_id
            }

    except Exception as e:
        # log stack trace of error
        alog.error(f"Error processing unstructured SMS message: {str(e)}")
        
        # Return a proper error response dictionary instead of raising
        error_response = "Sorry, I encountered an error processing your message. Please try again later."
        return {
            "response": error_response,
            "student_workflow": None,
            "workflow_id": None,
            "session_id": None,
            "message_count": 0,
            "workflow_steps": {"error": "Exception occurred"},
            "user_id": None,
            "student_id": student_id
        }


def process_sms_message(
    student_id: str,
    message_text: str,
    phone_number: Optional[str] = None,
    workflow_id: Optional[str] = None,
) -> str:
    """
    Process an incoming SMS message from a student.

    Args:
        student_id: The ID of the student sending the message
        message_text: The text content of the SMS message
        phone_number: Optional phone number of the student
        workflow_id: Optional ID of the workflow to use (for active SMS conversations)

    Returns:
        A string containing the SMS response to send back to the student
    """
    try:
        alog.info(f"Processing SMS message from student {student_id}: {message_text}")

        # Get the current workflow and question for the student
        workflow_info = get_current_workflow_info(student_id, workflow_id)

        if not workflow_info:
            return "Sorry, I couldn't find an active conversation for you. Please contact your counselor for assistance."

        current_workflow, current_question = workflow_info
        actual_workflow_id = workflow_id or current_workflow.get("workflow_id")

        # Log the incoming SMS message to the messages table
        if actual_workflow_id:
            session_id = get_session_id_for_sms(student_id, actual_workflow_id)
            log_sms_message_to_history(session_id, message_text, "human", "sms")

        if not current_question:
            # If there's no current question, the workflow might be complete
            if is_workflow_complete(student_id, workflow_id):
                return "You've completed all questions in this conversation. Thank you!"
            else:
                # Start the workflow by sending the first question
                return send_next_question(student_id, workflow_id)

        # Process the student's response to the current question
        result = process_response(current_question, message_text)

        if result["status"] == "success":
            # If the response was valid, save it and send the next question
            save_response(student_id, current_question, result["normalized_value"])
            response_text = send_next_question(student_id, actual_workflow_id)
        else:
            # If the response was invalid, send an error message
            response_text = result["message"]

        # Log the AI response to the messages table
        if actual_workflow_id:
            session_id = get_session_id_for_sms(student_id, actual_workflow_id)
            log_sms_message_to_history(session_id, response_text, "ai", "sms")

        return response_text

    except Exception as e:
        alog.error(f"Error processing SMS message: {str(e)}")
        error_response = "Sorry, I encountered an error processing your message. Please try again later or contact your counselor for assistance."

        # Try to log the error response if we have workflow info
        try:
            if "actual_workflow_id" in locals() and actual_workflow_id:
                session_id = get_session_id_for_sms(student_id, actual_workflow_id)
                log_sms_message_to_history(session_id, error_response, "ai", "sms")
        except:
            pass  # Don't let logging errors prevent the error response

        return error_response


def get_current_workflow_info(
    student_id: str, workflow_id: Optional[str] = None
) -> Optional[Tuple[str, Dict[str, Any]]]:
    """
    Get the current workflow and question for a student.

    Args:
        student_id: The ID of the student
        workflow_id: Optional ID of the workflow to use

    Returns:
        A tuple containing the workflow ID and current question data, or None if not found
    """
    prisma = prisma_client()

    try:
        # If workflow_id is provided, use it
        if workflow_id:
            student_workflow = prisma.studentworkflow.find_first(
                where={"student_id": student_id, "workflow_id": workflow_id},
                include={"workflow": {"include": {"steps": True}}},
            )

            if not student_workflow:
                alog.info(f"Workflow {workflow_id} not found for student {student_id}")
                return None
        else:
            # First, find in-progress workflows for the student
            student_workflows = prisma.studentworkflow.find_many(
                where={"student_id": student_id, "status": "IN_PROGRESS"},
                include={"workflow": {"include": {"steps": True}}},
            )
            
            # If no in-progress workflows, look for completed ones
            if not student_workflows:
                alog.info(f"No in-progress workflows found for student {student_id}, checking for completed ones")
                
                # Find the most recent completed workflow
                most_recent_workflow = prisma.studentworkflow.find_first(
                    where={"student_id": student_id, "status": "COMPLETED"},
                    order={"updated_at": "desc"},
                    include={"workflow": {"include": {"steps": True}}},
                )
                
                # If we found a completed workflow, reactivate it
                if most_recent_workflow:
                    alog.info(f"Reactivating most recent completed workflow {most_recent_workflow.id} for student {student_id}")
                    prisma.studentworkflow.update(
                        where={"id": most_recent_workflow.id},
                        data={"status": "IN_PROGRESS"},
                    )
                    
                    # Add the reactivated workflow to the student_workflows list
                    student_workflows = [most_recent_workflow]

            # Sort by updated_at in descending order (most recent first)
            student_workflows.sort(
                key=lambda wf: wf.updated_at if hasattr(wf, "updated_at") else None,
                reverse=True,
            )

            if not student_workflows:
                alog.info(f"No in-progress workflows found for student {student_id}")
                return None

            # Use the most recently updated workflow
            student_workflow = student_workflows[0]
            workflow_id = student_workflow.workflow_id
    except Exception as e:
        alog.error(f"Error getting workflow information: {str(e)}")
        return None

    # Get the student workflow steps to find the current question
    student_steps = prisma.studentworkflowstep.find_many(
        where={"student_id": student_id, "student_workflow_id": student_workflow.id},
        include={"step": True},
    )

    # Sort steps by the step index
    student_steps.sort(
        key=lambda step: (
            step.step.index if step.step and hasattr(step.step, "index") else 0
        )
    )

    # Find the first uncompleted step
    current_step = next((step for step in student_steps if not step.completed), None)

    if not current_step:
        alog.info(
            f"No uncompleted steps found for student {student_id} in workflow {workflow_id}"
        )
        return workflow_id, None

    # Extract question data from the step
    step_data = current_step.step.data

    # Determine question type based on the table field
    question_type = "short-answer"  # Default
    if "table" in step_data:
        if step_data["table"] == "MultipleChoiceQuestion":
            question_type = "multiple-choice"
        elif step_data["table"] == "BinaryQuestion":
            question_type = "binary"
        elif step_data["table"] == "LikertQuestion":
            question_type = "likert"

    # Build the question context
    question_context = {
        "question_id": step_data.get("questionId", ""),
        "question_text": step_data.get("question", ""),
        "question_type": question_type,
        "step_id": current_step.step_id,
        "can_skip": step_data.get("canSkip", False),
    }

    # Add options for multiple-choice and likert questions
    if question_type in ["multiple-choice", "likert"] and "options" in step_data:
        question_context["options"] = step_data["options"]

    # Add character limits for text questions
    if (
        question_type in ["short-answer", "long-answer"]
        and "characterLimit" in step_data
    ):
        question_context["character_limit"] = step_data["characterLimit"]

    return workflow_id, question_context


def is_workflow_complete(student_id: str, workflow_id: str) -> bool:
    """
    Check if a workflow is complete for a student.

    Args:
        student_id: The ID of the student
        workflow_id: The ID of the workflow

    Returns:
        True if the workflow is complete, False otherwise
    """
    prisma = prisma_client()

    # Get the student workflow
    student_workflow = prisma.studentworkflow.find_first(
        where={"student_id": student_id, "workflow_id": workflow_id}
    )

    if not student_workflow:
        return False

    # Check if all steps are completed
    student_steps = prisma.studentworkflowstep.find_many(
        where={"student_id": student_id, "student_workflow_id": student_workflow.id}
    )

    return all(step.completed for step in student_steps)


def process_response(question: Dict[str, Any], response_text: str) -> Dict[str, Any]:
    """
    Process and validate a student's response to a question.

    Args:
        question: The question data
        response_text: The student's response text

    Returns:
        A dictionary with status, message, and normalized value if successful
    """
    question_type = question.get("question_type", "short-answer")

    # Check for skip request
    if response_text.lower() == "skip":
        if question.get("can_skip", False):
            return {
                "status": "success",
                "message": "Question skipped.",
                "normalized_value": "SKIPPED",
            }
        else:
            return {
                "status": "error",
                "message": "This question cannot be skipped. Please provide an answer.",
            }

    # Validate based on question type
    if question_type == "multiple-choice":
        return validate_multiple_choice(question, response_text)
    elif question_type == "binary":
        return validate_binary(response_text)
    elif question_type == "likert":
        question_id = question.get("question_id")
        options = question.get("options", [])
        return validate_likert(response_text, question_id, options)
    else:  # short-answer or long-answer
        return validate_text_answer(question, response_text)


def validate_multiple_choice(
    question: Dict[str, Any], response_text: str
) -> Dict[str, Any]:
    """Validate a multiple-choice response with fuzzy matching."""
    from addie.sms_agent.prompts import get_error_response
    from addie.sms_agent.fuzzy_logic import normalize_response

    options = question.get("options", [])

    # Use fuzzy logic to normalize the response
    result = normalize_response(response_text, "multiple-choice", options)

    if result["status"] == "error":
        result["message"] = get_error_response("invalid_choice")

    return result


def validate_binary(response_text: str) -> Dict[str, Any]:
    """Validate a binary (yes/no) response with fuzzy matching."""
    from addie.sms_agent.prompts import get_error_response
    from addie.sms_agent.fuzzy_logic import normalize_response

    # Use fuzzy logic to normalize the response
    result = normalize_response(response_text, "binary")

    if result["status"] == "error":
        result["message"] = get_error_response("invalid_binary")

    return result


def validate_likert(response_text: str, question_id: str = None, options: list = None) -> Dict[str, Any]:
    """Validate a Likert scale response with fuzzy matching."""
    from addie.sms_agent.prompts import get_error_response
    from addie.sms_agent.fuzzy_logic import normalize_response

    # Use fuzzy logic to normalize the response, passing both options and question_id
    result = normalize_response(response_text, "likert", options=options, question_id=question_id)

    if result["status"] == "error":
        result["message"] = get_error_response("invalid_likert")

    return result


def validate_text_answer(
    question: Dict[str, Any], response_text: str
) -> Dict[str, Any]:
    """
    Validate a text answer (short or long).
    No fuzzy logic is applied to text answers.
    """
    from addie.sms_agent.prompts import get_error_response

    # Check for skip responses
    if response_text.lower().strip() in ["skip", "pass", "next"]:
        return {"status": "success", "normalized_value": "SKIPPED", "skipped": True}

    char_limit = question.get("character_limit", {})
    min_chars = char_limit.get("min", 0)
    max_chars = char_limit.get("max", 10000)

    if len(response_text) < min_chars:
        return {
            "status": "error",
            "message": get_error_response("too_short")
            + f" Please provide at least {min_chars} characters.",
        }

    if len(response_text) > max_chars:
        return {
            "status": "error",
            "message": get_error_response("too_long")
            + f" Please limit your response to {max_chars} characters.",
        }

    # Return the original text without any fuzzy logic processing
    return {"status": "success", "normalized_value": response_text}


def send_next_question(student_id: str, workflow_id: str) -> str:
    """
    Get the next question to send to the student.

    Args:
        student_id: The ID of the student
        workflow_id: The ID of the workflow

    Returns:
        A formatted SMS message with the next question
    """
    # Get the current workflow info again to find the next question
    workflow_info = get_current_workflow_info(student_id, workflow_id)

    if not workflow_info:
        return "Sorry, I couldn't find an active conversation for you. Please contact your counselor for assistance."

    _, current_question = workflow_info

    if not current_question:
        # If there's no current question, the workflow might be complete
        if is_workflow_complete(student_id, workflow_id):
            # Mark the workflow as completed
            prisma = prisma_client()
            prisma.studentworkflow.update_many(
                where={"student_id": student_id, "workflow_id": workflow_id},
                data={"status": "COMPLETED"},
            )
            return "You've completed all questions in this conversation. Thank you!"
        else:
            return "Sorry, I couldn't find the next question. Please contact your counselor for assistance."

    # Format the question
    question_message = format_question(current_question)

    full_message = question_message

    # Log the welcome message to the messages table
    try:
        session_id = get_session_id_for_sms(student_id, workflow_id)
        log_sms_message_to_history(session_id, full_message, "ai", "sms")
    except Exception as log_error:
        alog.error(f"Error logging welcome message: {str(log_error)}")

    return full_message


def format_question(question: Dict[str, Any]) -> str:
    """
    Format a question for SMS delivery.

    Args:
        question: The question data

    Returns:
        A formatted SMS message
    """
    from addie.sms_agent.prompts import (
        format_multiple_choice_for_sms,
        format_binary_for_sms,
        format_likert_for_sms,
        format_text_for_sms,
    )

    question_type = question.get("question_type", "short-answer")
    question_text = question.get("question_text", "")
    can_skip = question.get("can_skip", False)

    if question_type == "multiple-choice":
        options = question.get("options", [])
        message = format_multiple_choice_for_sms(question_text, options)

    elif question_type == "binary":
        message = format_binary_for_sms(question_text)

    elif question_type == "likert":
        # First check if options are provided in the question context (from workflow step data)
        likert_options = question.get("options", [])
        
        # If no options in context, try to get them from the database
        if not likert_options:
            question_id = question.get("question_id")
            if question_id:
                from addie.sms_agent.fuzzy_logic import get_likert_options
                likert_options = get_likert_options(question_id)
        
        message = format_likert_for_sms(question_text, likert_options)

    else:  # short-answer or long-answer
        char_limit = question.get("character_limit", {})
        min_chars = char_limit.get("min", 0)
        max_chars = char_limit.get("max", 10000)
        message = format_text_for_sms(question_text, min_chars, max_chars)

    # Add skip instruction if applicable
    if can_skip:
        message += "\n\nType 'skip' to skip this question."

    return message


def initiate_conversation(student_id: str, workflow_id: Optional[str] = None) -> str:
    """
    Initiate an SMS conversation with a student.

    This function sends a welcome message and the first question of a workflow.
    If workflow_id is not provided, it will use the most recently updated in-progress workflow.

    Args:
        student_id: The ID of the student
        workflow_id: Optional ID of the workflow to use

    Returns:
        A formatted SMS message with the welcome message and first question
    """
    try:
        prisma = prisma_client()

        # Get student information
        try:
            # Try to get the student's name from the user record
            student_name = "there"  # Default greeting

            try:
                # Get student with associated users (many-to-many relationship)
                student = prisma.student.find_unique(
                    where={"id": student_id}, include={"users": True}
                )

                if student and student.users and len(student.users) > 0:
                    user = student.users[0]
                    if hasattr(user, "first_name") and user.first_name:
                        student_name = user.first_name
            except Exception as e:
                # If that fails, try a simpler query
                alog.warning(f"Error getting student with users: {str(e)}")
                student = prisma.student.find_unique(where={"id": student_id})

            if not student:
                alog.error(f"Student with ID {student_id} not found")
                return "Error: Student not found."

        except Exception as e:
            alog.error(f"Error getting student information: {str(e)}")
            # Continue with default greeting

        # Find the workflow to use
        if workflow_id:
            # Use the specified workflow
            student_workflow = prisma.studentworkflow.find_first(
                where={"student_id": student_id, "workflow_id": workflow_id},
                include={"workflow": True},
            )

            if not student_workflow:
                alog.error(f"Workflow {workflow_id} not found for student {student_id}")
                error_message = f"Error: Workflow not found for student."
                # Note: We can't log this message because we don't have a valid workflow to create a session_id
                return error_message
        else:
            # Find in-progress workflows for the student
            student_workflows = prisma.studentworkflow.find_many(
                where={"student_id": student_id, "status": "IN_PROGRESS"},
                include={"workflow": True},
            )

            if not student_workflows:
                alog.info(f"No in-progress workflows found for student {student_id}")
                no_conversation_message = "Hi there! You don't have any active conversations at the moment. Please contact your counselor for assistance."
                # Note: We can't log this message because we don't have a workflow_id to create a session_id
                return no_conversation_message

            # Sort by updated_at in descending order (most recent first)
            student_workflows.sort(
                key=lambda wf: wf.updated_at if hasattr(wf, "updated_at") else None,
                reverse=True,
            )

            # Use the most recently updated workflow
            student_workflow = student_workflows[0]

        workflow_name = (
            student_workflow.workflow.name
            if hasattr(student_workflow.workflow, "name")
            else "conversation"
        )

        # Clean up workflow name
        prefixes_to_remove = ["Unstructured Conversation: ", "Conversation: ", "Assignment: "]
        for prefix in prefixes_to_remove:
            if workflow_name.startswith(prefix):
                workflow_name = workflow_name[len(prefix):]
                break

        # Create welcome message
        welcome_message = f"Hi {student_name}!\n\n"

        # Find the conversation intro from workflow.data.description
        conversation_intro = None

        try:
            if hasattr(student_workflow.workflow, "data"):
                conversation_data = student_workflow.workflow.data
                if hasattr(conversation_data, "description"):
                    conversation_intro = conversation_data.description
                elif isinstance(conversation_data, dict):
                    conversation_intro = conversation_data.get("description")
        except Exception as e:
            pass

        # If there's a conversation intro, add it to the welcome message
        if conversation_intro:
            alog.info(f"Conversation intro: {conversation_intro}")
            welcome_message += f"{conversation_intro}\n\n"
        else:
            alog.info("No conversation intro found, using default welcome message.")
            welcome_message += f"Welcome to your {workflow_name} conversation. I'll be asking you a series of questions. Let's get started!\n\n"


        # Get the first question
        workflow_info = get_current_workflow_info(
            student_id, student_workflow.workflow_id
        )

        if not workflow_info:
            error_message = (
                welcome_message
                + "Sorry, I couldn't find any questions for you. Please contact your counselor for assistance."
            )
            # Log the error message
            try:
                session_id = get_session_id_for_sms(
                    student_id, student_workflow.workflow_id
                )
                log_sms_message_to_history(session_id, error_message, "ai", "sms")
            except Exception as log_error:
                alog.error(f"Error logging error message: {str(log_error)}")
            return error_message

        _, current_question = workflow_info

        if not current_question:
            # If there's no current question, the workflow might be complete
            if is_workflow_complete(student_id, student_workflow.workflow_id):
                complete_message = (
                    welcome_message
                    + "You've already completed all questions in this conversation. Thank you!"
                )
            else:
                complete_message = (
                    welcome_message
                    + "Sorry, I couldn't find the next question. Please contact your counselor for assistance."
                )

            # Log the completion/error message
            try:
                session_id = get_session_id_for_sms(
                    student_id, student_workflow.workflow_id
                )
                log_sms_message_to_history(session_id, complete_message, "ai", "sms")
            except Exception as log_error:
                alog.error(f"Error logging completion message: {str(log_error)}")

            return complete_message

        # Format the question
        question_message = format_question(current_question)

        full_message = welcome_message + question_message

        # Log the welcome message to the messages table
        try:
            session_id = get_session_id_for_sms(
                student_id, student_workflow.workflow_id
            )
            log_sms_message_to_history(session_id, full_message, "ai", "sms")
        except Exception as log_error:
            alog.error(f"Error logging welcome message: {str(log_error)}")

        return full_message

    except Exception as e:
        alog.error(f"Error initiating conversation: {str(e)}")
        error_response = "Sorry, I encountered an error starting your conversation. Please try again later or contact your counselor for assistance."

        # Try to log the error response if we have workflow info
        try:
            if "student_workflow" in locals() and student_workflow:
                session_id = get_session_id_for_sms(
                    student_id, student_workflow.workflow_id
                )
                log_sms_message_to_history(session_id, error_response, "ai", "sms")
        except:
            pass  # Don't let logging errors prevent the error response

        return error_response


def get_student_phone_number(student_id: str) -> Optional[str]:
    """
    Get the phone number for a student.

    Args:
        student_id: The ID of the student

    Returns:
        The phone number if found, None otherwise
    """
    try:
        prisma = prisma_client()

        # Try to find the student and associated users (many-to-many relationship)
        try:
            student = prisma.student.find_unique(
                where={"id": student_id}, include={"users": True}
            )

            if student and student.users and len(student.users) > 0:
                user = student.users[0]
                # Check if user is enabled before returning phone number
                if hasattr(user, "enabled") and user.enabled == False:
                    alog.warning(f"User {user.id} is disabled, not returning phone number for student {student_id}")
                    return None
                if hasattr(user, "phone_number") and user.phone_number:
                    return user.phone_number
        except Exception as e:
            alog.warning(f"Error getting student with users: {str(e)}")

        alog.error(f"No phone number found for student {student_id}")
        return None

    except Exception as e:
        alog.error(f"Error getting student phone number: {str(e)}")
        return None


def send_sms_via_twilio(phone_number: str, message: str) -> bool:
    """
    Send an SMS message via Twilio.

    Args:
        phone_number: The phone number to send the message to
        message: The message to send

    Returns:
        True if the message was sent successfully, False otherwise
    """
    try:
        # Check if Twilio credentials are configured
        if not all([settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN]):
            alog.warning("Twilio credentials not configured. Cannot send SMS.")
            return False

        # Import Twilio client
        from twilio.rest import Client

        # Initialize Twilio client
        client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)

        # Get the Twilio phone number from settings or use a default
        twilio_phone_number = settings.TWILIO_PHONE_NUMBER
        if not twilio_phone_number:
            alog.warning("TWILIO_PHONE_NUMBER not set. Using default number.")
            twilio_phone_number = "+***********"  # Default Twilio number

        # Send the message
        message = client.messages.create(
            body=message, from_=twilio_phone_number, to=phone_number
        )

        alog.info(f"Sent SMS to {phone_number} with SID: {message.sid}")
        return True

    except Exception as e:
        alog.error(f"Error sending SMS: {str(e)}")
        return False


def send_conversation_initiation_sms(student_conversation_id: str, send_sms: bool = True) -> dict:
    """
    Send an SMS to initiate a conversation with a student.

    Args:
        student_conversation_id: The ID of the student conversation (StudentWorkflow.id)

    Returns:
        A dictionary with the result of the operation
    """
    try:
        # Get the student workflow
        prisma = prisma_client()
        student_workflow = prisma.studentworkflow.find_unique(
            where={"id": student_conversation_id},
            include={"student": True, "workflow": True},
        )

        if not student_workflow:
            alog.error(
                f"Student conversation with ID {student_conversation_id} not found"
            )
            return {"error": "Student conversation not found"}

        # Get the student ID and workflow ID
        student_id = student_workflow.student_id
        workflow_id = student_workflow.workflow_id

        alog.info(
            f"Found student ID {student_id} and workflow ID {workflow_id} for conversation {student_conversation_id}"
        )

        # Set the mode of other SMS workflows for this student to "web"
        try:
            # Only update workflows that currently have mode="sms" to "web" mode
            # This prevents updating timestamps for conversations that are already in "web" mode
            prisma.studentworkflow.update_many(
                where={
                    "student_id": student_id,
                    "id": {"not": student_conversation_id},
                    "mode": "sms",
                },
                data={"mode": "web"},
            )

            # Set the mode of this workflow to "sms" and status to IN_PROGRESS
            prisma.studentworkflow.update(
                where={"id": student_conversation_id},
                data={"mode": "sms", "status": "IN_PROGRESS"},
            )

            alog.info(
                f"Updated student conversation {student_conversation_id} to mode 'sms' and status 'IN_PROGRESS', and any other SMS conversations for this student to mode 'web'"
            )
        except Exception as e:
            alog.error(f"Error updating workflow modes: {str(e)}")
            # Continue with the conversation even if mode update fails

        # Get the student's phone number
        phone_number = get_student_phone_number(student_id)

        if not phone_number:
            alog.error(f"No phone number found for student {student_id}")
            return {"error": "Student has no phone number"}

        # Generate the welcome message
        welcome_message = initiate_conversation(student_id, workflow_id)

        # Only send SMS if requested (for direct API calls)
        if send_sms and all([settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN]):
            if send_sms_via_twilio(phone_number, welcome_message):
                alog.info(f"Sent SMS to {phone_number}")
                return {
                    "success": True,
                    "message": "Conversation initiated",
                    "welcome_message": welcome_message,
                }
            else:
                alog.error(f"Failed to send SMS to {phone_number}")
                return {
                    "error": "Failed to send SMS",
                    "welcome_message": welcome_message,
                }
        else:
            alog.warning("Twilio credentials not configured. Skipping SMS send.")
            return {
                "success": True,
                "message": "Conversation initiated (mock)",
                "welcome_message": welcome_message,
            }

    except Exception as e:
        alog.error(f"Error initiating SMS conversation: {str(e)}")
        return {"error": f"Error initiating conversation: {str(e)}"}



def send_reminder_sms (reminder_id:str):
    """
    Send an SMS reminder to a student, content of the reminder is based on the reminder record, student information is also stored in the reminder record

    Args:
        reminder_id: The ID of the reminder (Reminder record in prisma)

    Returns:
        A dictionary with the result of the operation
    """

    try:
        prisma = prisma_client()
        reminder = prisma.reminder.find_first(
            where = {
                "id": reminder_id,
            }
        )

        if not reminder:
            alog.error(f"Reminder {reminder_id} not found")
            return {"error": f"Reminder {reminder_id} not found"}

        alog.info(f"Found reminder with ID: {reminder_id}")

        student_phone_number = get_student_phone_number(reminder.student_id)
        if not student_phone_number:
            alog.error(f"Student with id {reminder.student_id} has no phone number")
            return {"error": f"Student with id {reminder.student_id} has no phone number"}

        alog.info(f"Student ID for this reminder is: {reminder.student_id}")
        alog.info(f"Student phone number for this reminder is: {student_phone_number}")

        # Get the reminder's final_message and we will be sending this message to student
        reminder_final_message = reminder.final_message
        if not reminder_final_message or reminder_final_message.strip() =="":
            alog.error(f"Reminder with id {reminder_id} has no final message")
            return {"error": f"Reminder with id {reminder_id} has no final message"}

        alog.info(f"Final message for reminder ==> {reminder_final_message}")


        # If Twilio credentials are configured, send the message
        if all([settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN]):
            if send_sms_via_twilio(student_phone_number, reminder_final_message):
                alog.info(f"Sent SMS to {student_phone_number}")
                return {"success": True, "message": "reminder sent", "reminder_final_message": reminder_final_message}
            else:
                alog.error(f"Failed to send SMS to {student_phone_number}")
                return {"error": "Failed to send SMS", "reminder_final_message": reminder_final_message }
        else:
            alog.warning("Twilio credentials not configured. Skipping SMS send.")
            return {"success": True, "message": "Reminder sent (mock)", "reminder_final_message": reminder_final_message}

    except Exception as e:
        alog.error(f"Error sending reminder to student: {str(e)}")
        return {"error": f"Error sending reminder to student: {str(e)}"}



def send_conversation_notification_sms(student_conversation_id: str) -> dict:
    """
    Send a notification SMS to inform student about a new conversation without changing conversation status or mode.
    
    Args:
        student_conversation_id: The ID of the student conversation (StudentWorkflow.id)

    Returns:
        A dictionary with the result of the operation

    Note: Will not send notification if student is already engaged in an active SMS conversation
    """
    try:
        from addie.sms_agent.utils import has_sms_conversation_started
        
        prisma = prisma_client()
        
        # Get the student workflow
        student_workflow = prisma.studentworkflow.find_unique(
            where={"id": student_conversation_id},
            include={
                "student": {
                    "include": {"users": True}
                },
                "workflow": True
            }
        )
        
        if not student_workflow:
            alog.error(f"Student conversation with ID {student_conversation_id} not found")
            return {"error": "Student conversation not found"}
            
        # Get the student ID and workflow ID
        student_id = student_workflow.student_id
        workflow_id = student_workflow.workflow_id
        
        alog.info(f"Found student ID {student_id} and workflow ID {workflow_id} for conversation {student_conversation_id}")

        # Check if student is already engaged in an SMS conversation
        active_sms_conversation = prisma.studentworkflow.find_first(
            where={
                "student_id": student_id,
                "mode": "sms",
                "status": "IN_PROGRESS"
            }
        )

        if active_sms_conversation:
            alog.info(f"Student {student_id} is already engaged in SMS conversation {active_sms_conversation.id}. Skipping notification.")
            return {
                "success": False,
                "message": "Notification skipped - student already engaged in SMS conversation",
                "active_conversation_id": active_sms_conversation.id
            }
            
        # Check if a notification has already been sent for this conversation
        if has_sms_conversation_started(student_id, workflow_id):
            alog.info(f"SMS conversation already started for student {student_id} and workflow {workflow_id}. Skipping notification.")
            return {
                "success": False,
                "message": "Notification skipped - SMS messages already exist for this conversation",
                "conversation_id": student_conversation_id
            }

        # Get student information
        student_name = "there"  # Default greeting
        if (student_workflow.student and 
            student_workflow.student.users and 
            len(student_workflow.student.users) > 0):
            user = student_workflow.student.users[0]
            if hasattr(user, 'first_name') and user.first_name:
                student_name = user.first_name
        
        # Get the student's phone number
        phone_number = get_student_phone_number(student_id)
        if not phone_number:
            alog.error(f"No phone number found for student {student_id}")
            return {"error": "Student has no phone number"}
            
        # Create notification message with prompt for conversation mode preference

        # Get the conversation name
        workflow_name = student_workflow.workflow.name if student_workflow.workflow and hasattr(student_workflow.workflow, 'name') else "a new conversation"

        # Clean up workflow name
        prefixes_to_remove = ["Unstructured Conversation: ", "Conversation: ", "Assignment: "]
        for prefix in prefixes_to_remove:
            if workflow_name.startswith(prefix):
                workflow_name = workflow_name[len(prefix):]
                break

        # Create the notification message
        notification_message = f"Hi {student_name}!\n\n"

        # Find the conversation intro from workflow.data.description
        conversation_intro = None

        try:
            if hasattr(student_workflow.workflow, "data"):
                conversation_data = student_workflow.workflow.data
                if hasattr(conversation_data, "description"):
                    conversation_intro = conversation_data.description
                elif isinstance(conversation_data, dict):
                    conversation_intro = conversation_data.get("description")
        except Exception as e:
            pass

        # Check the workflow type to determine if we should offer call option
        workflow_type = None
        try:
            if hasattr(student_workflow.workflow, "workflow_type"):
                workflow_type = student_workflow.workflow.workflow_type
        except Exception as e:
            alog.warning(f"Error getting workflow type: {str(e)}")
            pass

        # If there's a conversation intro, add it to the welcome message
        if conversation_intro:
            alog.info(f"Conversation intro: {conversation_intro}")
            notification_message += f"{conversation_intro}\n\n"
            
            # Only offer call option for UNSTRUCTURED conversations
            if workflow_type != "STRUCTURED":
                notification_message += "Would you like to chat over text or would you prefer a call? Reply with 'text' or 'call'."
            else:
                notification_message += "Reply to this message to begin the conversation."
        else:
            alog.info("No conversation intro found, using default notification message.")
            # Only offer call option for UNSTRUCTURED conversations
            if workflow_type != "STRUCTURED":
                notification_message = f"Hey {student_name}! Addie has a new conversation for you - \"{workflow_name}\". Would you like to chat over text or would you prefer a call? Reply with 'text' or 'call'."
            else:
                notification_message = f"Hey {student_name}! Addie has a new conversation for you - \"{workflow_name}\". Reply to this message to begin the conversation."
        
        # Log the notification message to the messages table
        try:
            session_id = get_session_id_for_sms(student_id, workflow_id)
            log_sms_message_to_history(session_id, notification_message, "ai", "sms")
        except Exception as log_error:
            alog.warning(f"Error logging notification message: {str(log_error)}")
            # Continue with sending SMS even if logging fails

        # Send the SMS if Twilio credentials are configured
        if all([settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN]):
            if send_sms_via_twilio(phone_number, notification_message):
                alog.info(f"Sent notification SMS to {phone_number} for conversation {student_conversation_id}")
                return {"success": True, "message": "Notification sent", "notification_message": notification_message}
            else:
                alog.error(f"Failed to send notification SMS to {phone_number}")
                return {"error": "Failed to send SMS", "notification_message": notification_message}
        else:
            alog.warning("Twilio credentials not configured. Skipping SMS send.")
            return {"success": True, "message": "Notification initiated (mock)", "notification_message": notification_message}

    except Exception as e:
        alog.error(f"Error sending notification SMS: {str(e)}")
        return {"error": f"Error sending notification: {str(e)}"}


def detect_conversation_mode_preference(message_text: str) -> str:
    """
    Detect the preferred conversation mode from a student's message.
    
    This function analyzes the student's message for keywords that indicate
    their preference for SMS or voice conversation. Uses strict word boundary
    matching to prevent false positives during normal conversation.
    
    Args:
        message_text: The text message from the student
        
    Returns:
        A string indicating the preferred mode: 'sms', 'voice', or None for default
        
    Updated by Dempsey (2025-06-10): Added strict word boundary matching using regex
    to prevent false positives during normal conversation.
    """
    # Convert to lowercase for case-insensitive matching
    text = message_text.lower().strip()
    
    # Define keywords for each mode
    sms_keywords = ["text", "sms", "dm", "message", "chat"]
    voice_keywords = ["call", "voice", "talk", "speak", "phone"]
    
    # More strict matching for initial mode selection
    # Check for exact matches or matches at word boundaries
    for keyword in sms_keywords:
        # Check for exact match or word boundary
        if keyword == text or re.search(r'\b' + re.escape(keyword) + r'\b', text):
            alog.info(f"Detected SMS preference from keyword: {keyword}")
            return "sms"
    
    # Check for voice keywords with the same strict matching
    for keyword in voice_keywords:
        if keyword == text or re.search(r'\b' + re.escape(keyword) + r'\b', text):
            alog.info(f"Detected voice preference from keyword: {keyword}")
            return "voice"
    
    # Return None if no clear preference is detected
    alog.info("No clear preference detected in message")
    return None


def handle_conversation_mode_response(student_id: str, workflow_id: str, message_text: str) -> str:
    """
    Handle a student's response to the conversation mode selection prompt.
    
    This function processes a student's reply to the initial message asking if they
    prefer SMS or voice conversation. It detects their preference, updates the
    conversation mode, and initiates the appropriate conversation flow.
    
    Args:
        student_id: The ID of the student
        workflow_id: The ID of the workflow/conversation
        message_text: The student's message text
        
    Returns:
        A response message to send back to the student
    """
    prisma = prisma_client()
    
    try:
        # Find the StudentWorkflow record
        student_workflow = prisma.studentworkflow.find_first(
            where={
                "student_id": student_id,
                "workflow_id": workflow_id
            }
        )
        
        if not student_workflow:
            alog.error(f"StudentWorkflow not found for student {student_id} and workflow {workflow_id}")
            return "Sorry, I couldn't find your conversation. Please contact your counselor for assistance."
        
        # Detect the preferred mode from the message
        mode = detect_conversation_mode_preference(message_text)
        
        if mode == "sms":
            # Use the existing function to handle SMS initiation without sending SMS, as that is handled via TwiML.
            result = send_conversation_initiation_sms(student_workflow.id, send_sms=False)

            if "error" in result:
                alog.error(f"Error initiating SMS conversation: {result['error']}")
                return "Sorry, I couldn't start the SMS conversation. Please try again later."

            return result.get("welcome_message", "Starting your conversation now...")
            
        elif mode == "voice":
            # Voice functionality is not yet implemented
            # Just inform the user and default to SMS for now
            alog.info(f"Student {student_id} requested voice mode, which is not yet implemented")

            # TODO: implement voice functionality
            # Update the workflow mode to SMS since voice is not available
            result = send_conversation_initiation_sms(student_workflow.id, send_sms=False)

            if "error" in result:
                return "Sorry, I encountered an error. Let's continue over text for now."

            return "Voice conversations are coming soon! In the meantime, I've started a text conversation for you.\n\n" + result.get("welcome_message", "")

        else:
            # Default to SMS for ambiguous responses
            alog.info(f"No clear preference detected, defaulting to SMS for student {student_id}")
            result = send_conversation_initiation_sms(student_workflow.id, send_sms=False)

            if "error" in result:
                return "I'll continue our conversation over text. " + result["error"]

            return "I'll continue our conversation over text.\n\n" + result.get("welcome_message", "")
            
    except Exception as e:
        alog.error(f"Error handling conversation mode response: {str(e)}")
        return "Sorry, I encountered an error. Let's continue over text for now."
