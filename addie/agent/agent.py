#!/usr/bin/env python
from langgraph.graph.graph import CompiledGraph
from addie.agent.call_model import call_model
from addie.agent.call_tool import call_tool
from addie.agent.agent_conditional import agent_conditional_edge
from addie.agent.state.state import AgentState
from langgraph.graph import StateGraph

import click
import alog



def agent() -> CompiledGraph:
    workflow = StateGraph(state_schema=AgentState)

    def entry_point(state: AgentState):
        alog.info('### entry_point ###')
        value = "end"
        msgs = state["messages"]

        if len(msgs) > 0:
            last_message = state["messages"][-1]

            if last_message.type == "human":
                value = "agent"

            # if is_tool_call(last_message):
            #     value = "call_tool"
        else:
            value = "agent"

        # alog.info('### entry_point: ' + value)

        return value

    workflow.add_node("agent", call_model)

    workflow.add_conditional_edges("agent", agent_conditional_edge)

    workflow.add_node("call_tool", call_tool)

    def call_tool_edge(state: AgentState):
        alog.info('### call_tool_edge ###')
        value = "end"

        last_msgs = state["messages"][-2:]

        alog.info(alog.pformat(last_msgs))

        raise Exception()

        for msg in last_msgs:
            if hasattr(msg, "type"):
                if msg.type == "human":
                    value = "agent"

            if hasattr(msg, "tool_calls"):
                value = "call_tool"
                break

            if hasattr(msg, "role"):
                if msg.role == "tool":
                    value = "call_tool"
                    break

        print("#" * 80)
        print(f"call_tool_edge: {value}")
        print("#" * 80)

        return value

    # workflow.add_conditional_edges("call_tool", call_tool_edge)

    # workflow.add_conditional_edges(
    #     "call_tool",
    #     lambda state: "end")

    workflow.set_conditional_entry_point(entry_point)

    # Compile without checkpointer to avoid KeyError: '__start__' in versions_seen
    # This is a workaround for a bug in LangGraph 0.0.69
    workflow_compile = workflow.compile(checkpointer=None)

    return workflow_compile



@click.command()
def main(**kwargs):
    agent()


if __name__ == "__main__":
    main()
