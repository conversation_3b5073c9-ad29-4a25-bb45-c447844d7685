from addie.agent.state.state import Agent<PERSON>tate
from addie.history import get_history
from addie.lib import dict_to_json, prisma_client
from addie.tools.goal_completion_tool import GoalCompletionTool
from addie.tools.voice_call_tool import <PERSON><PERSON>allTool
from langgraph.prebuilt import Too<PERSON><PERSON>xecutor
from langgraph.prebuilt import ToolIn<PERSON>
from typing import List, Any
from langchain_core.messages import AIMessage, ToolMessage
import alog

prisma = prisma_client()

tools = [GoalCompletionTool(), VoiceCallTool()]

tool_executor = ToolExecutor(tools)


def call_tool(state: AgentState):
    # alog.info(f'''
    # ###
    # {dict(
    #     name='call_tool'
    # )}
    # ###
    # ''')
    result = dict()
    messages = state["messages"]
    alog.info(alog.pformat(messages[-2:]))

    # Get tool calls from the last message
    tool_calls = (
        messages[-1].tool_calls
        if hasattr(messages[-1], "tool_calls") and messages[-1].tool_calls
        else []
    )

    alog.info(f"Found {len(tool_calls)} tool calls")
    alog.info(alog.pformat(tool_calls))

    # Process tool calls if any exist
    new_tool_messages = []

    if tool_calls:
        # Create tool invocations for each tool call
        tool_invocations = []
        tool_metadata = []

        for tool_call in tool_calls:
            try:
                # Extract tool name and arguments
                tool_name = tool_call.get("name")
                tool_id = tool_call.get("id")
                tool_args = tool_call.get("args", {})

                # Create a tool invocation
                invocation = ToolInvocation(tool=tool_name, tool_input=tool_args)

                tool_invocations.append(invocation)
                tool_metadata.append({"name": tool_name, "id": tool_id})

                alog.info(
                    f"Created tool invocation for: {tool_name} with args: {tool_args}"
                )
            except Exception as e:
                alog.error(f"Error creating tool invocation from tool call: {str(e)}")

        # Execute tool invocations if we have any
        if tool_invocations:
            alog.info(f"Executing {len(tool_invocations)} tool invocations")
            responses = tool_executor.batch(tool_invocations, return_exceptions=True)

            # Process responses
            for metadata, response in zip(tool_metadata, responses):
                if isinstance(response, Exception):
                    alog.error(f"Tool execution error: {str(response)}")
                    raise response

                # Create a new tool message with the response
                new_tool_msg = ToolMessage(
                    content=dict_to_json(response),
                    name=metadata["name"],
                    tool_call_id=metadata["id"],
                )

                alog.info(new_tool_msg)
                alog.info(alog.pformat(new_tool_msg.dict()))

                new_tool_messages.append(new_tool_msg)
    else:
        # No tool calls found
        alog.info("No tool calls found to process")

    # Add the new tool messages to the result if we have any
    if new_tool_messages:
        alog.info(
            f"Adding {len(new_tool_messages)} new tool messages to the conversation"
        )
        # Get history and add messages
        history = get_history(state["session_id"])
        history.add_messages(new_tool_messages)

        # Update result with combined messages
        result["messages"] = messages = messages + new_tool_messages

        # Get fresh history after adding messages to verify they're there
        fresh_history = get_history(state["session_id"])
        fresh_messages = fresh_history.get_messages()

        # Log the messages for debugging
        alog.info(f"Verifying {len(new_tool_messages)} tool messages are in history")

        # Check each message is saved in the fresh messages by comparing tool_call_ids
        for msg in new_tool_messages:
            msg_tool_call_id = getattr(msg, 'tool_call_id', None)

            # Skip check if no tool_call_id
            if not msg_tool_call_id:
                alog.warning(f"Message has no tool_call_id to check: {alog.pformat(msg)}")
                continue

            # Find matching message by tool_call_id
            found = False
            for fresh_msg in fresh_messages:
                if getattr(fresh_msg, 'tool_call_id', None) == msg_tool_call_id:
                    found = True
                    alog.info(f"Found matching message with tool_call_id: {msg_tool_call_id}")
                    break

            if not found:
                # Log detailed debugging information
                alog.error(f"Message with tool_call_id {msg_tool_call_id} not found in history")
                alog.error(f"Original message: {alog.pformat(msg)}")

                # Show all tool_call_ids in history for comparison
                tool_call_ids = [getattr(m, 'tool_call_id', None) for m in fresh_messages]
                alog.error(f"Tool call IDs in history: {tool_call_ids}")

                # Don't assert - we'll just continue with what we have
                alog.warning("Continuing despite missing message in history")
            else:
                alog.info("✓ Message verified in history by tool_call_id")

    else:
        fresh_history = get_history(state["session_id"])
        fresh_messages = fresh_history.get_messages()
        result["messages"] = fresh_messages

    # Log the final messages (use fresh_messages to ensure we're seeing the latest state)
    if fresh_messages and len(fresh_messages) >= 2:
        alog.info("Final messages (last 2):")
        alog.info(alog.pformat(fresh_messages[-2:]))
    elif fresh_messages:
        alog.info("Final message:")
        alog.info(alog.pformat(fresh_messages[-1:]))

    return result
