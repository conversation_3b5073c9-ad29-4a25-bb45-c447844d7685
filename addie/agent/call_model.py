from copy import copy, deepcopy
import asyncio
import threading

import alog

from addie.agent.call_tool import tools
from addie.history import get_history
from addie.agent.state.state import AgentState
from addie.data_model.student_context import StudentContext
from addie.data_model.system_prompt_storage import SystemPromptStorage
from addie.lib import dict_to_j<PERSON>, prisma_client
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_core.utils.function_calling import convert_to_openai_tool
from langchain_openai import ChatOpenAI as _ChatOpenAI

def create_chat_openai(**kwargs):
    """Create ChatOpenAI instance with proxies parameter filtered out.
    
    This resolves the compatibility issue described in:
    https://community.openai.com/t/error-with-openai-1-56-0-client-init-got-an-unexpected-keyword-argument-proxies/1040332/2
    """
    # Filter out any proxy-related parameters that cause compatibility issues
    filtered_kwargs = {k: v for k, v in kwargs.items() if k not in ['proxies', 'proxy']}
    return _ChatOpenAI(**filtered_kwargs)
from pydantic import validate_call


# When Answering questions keep in mind that the next question `slug`
# is: {slug}.
# When Answering questions keep in mind that the next `step_id`
# is: {step_id}.
# As a critical step of the instructions above, you must
# ONLY CALL ONE TOOL AT A TIME AND EACH TOOL ONCE per message.
# In your tool calling responses you must ensure you only include a
# maximum of 1 tool call.


@validate_call()
def gen_system_prompt(student_context: dict) -> str:
    # Use a deep copy to avoid modifying the original context
    _student_context = deepcopy(student_context)

    current_question = _student_context.get("current_question", None)
    mode = _student_context.get("mode")
    workflow_id = _student_context.get("workflow_id")

    # Check if system prompt should be disabled for this workflow
    disable_system_prompt = False
    if workflow_id:
        workflow = prisma_client().workflow.find_unique(
            where={"id": workflow_id}
        )
        if workflow and workflow.disable_system_prompt:
            disable_system_prompt = True

    student_context = dict_to_json(_student_context)
    addie_config = prisma_client().addieconfig.find_first(
        include=dict(prompt=True, system_prompt=True)
    )

    prompt = addie_config.prompt.content

    # If system prompt is disabled, return minimal prompt without system/counselor prompts
    if disable_system_prompt:
        # Only include basic context without system or counselor prompts
        minimal_prompt = f"""You are currently running in `{mode}` mode.

## context start ##
{student_context}
## context end ##
"""

        if current_question:
            minimal_prompt += f"""
Also, note that the `id` field is the student id which can be used for tool calling.
Be very careful to use only this step_id to identify the current question:
step_id: {current_question["step_id"]}
"""

        return minimal_prompt

    # Normal system prompt with both system and session prompts
    raw_system_prompt = addie_config.system_prompt.content

    system_prompt = f"""
    {raw_system_prompt}

    You are currently running in `{mode}` mode.
    When communicating with the student please consider the following context:

    ## counselor prompt begin
    {prompt}
    ## counselor prompt end

    ## context start ##
    {student_context}
    ## context end ##
    """

    if current_question:
        system_prompt += f"""
        Also, note that the `id` field is the student id which can be used for tool calling.
        Be very careful to use only this step_id to identify the current question:
        step_id: {current_question["step_id"]}
        """

    return system_prompt


def call_model(state: AgentState):
    alog.info("### call_model ###")

    messages = state["messages"]
    model = state["model"]
    log_student_context = state["log_student_context"]
    student_context = state["student_context"]
    alternate_user_id = state.get("alternate_user_id", None)
    user_id = state["user_id"]
    session_id = state["session_id"]
    agent_type = state.get("agent_type", "counselor")  # Get agent type from state

    if len(messages) > 0:
        last_msg = [messages[-1]]
    else:
        last_msg = [HumanMessage(content="")]

    last_msg_type = None
    actual_system_prompt_used = None  # Initialize to track the actual system prompt used

    if hasattr(last_msg, "type"):
        last_msg_type = last_msg.type

    if last_msg_type != "ai" or len(messages) <= 1:
        system_prompt = gen_system_prompt(student_context)
        # Store the system prompt for later use when saving to database
        actual_system_prompt_used = system_prompt

        prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessage(content=system_prompt),
                MessagesPlaceholder("message_history", optional=True, n_history=2),
                MessagesPlaceholder("question", optional=True),
            ]
        )

        # Initialize ChatOpenAI - use wrapper function to filter proxies parameter
        chain = create_chat_openai(
            model=model,
            temperature=0,
        )

        chain = chain.bind(tools=[convert_to_openai_tool(tool) for tool in tools])

        chain = prompt | chain

        chain_with_history = RunnableWithMessageHistory(
            chain,
            get_history,
            input_messages_key="question",
            history_messages_key="message_history",
            name=session_id,
        )

        config = {"configurable": {"session_id": session_id}}

        history = get_history(session_id)
        # get last up to 2 messages
        last_messages = history.get_messages()[-2:]
        alog.info(alog.pformat(last_messages))

        alog.info(last_msg)

        response = chain_with_history.invoke(
            dict(
                question=last_msg,
            ),
            config,
        )

        # Check for tool_calls in the response
        if hasattr(response, "tool_calls") and response.tool_calls:
            print("#" * 80)
            print(f"Response contains tool_calls: {len(response.tool_calls)}")

            for tool_call in response.tool_calls:
                alog.info(alog.pformat(tool_call))

            print("#" * 80)

            # if len(response.tool_calls) > 0:
            #     raise Exception()

        # # Log the full response for debugging
        # alog.info("Full response:")
        # alog.info(alog.pformat(response))

        response = response.dict()

        # Get updated history after the response is processed
        history = get_history(session_id)
        messages = history.get_messages()
        
        # Find the message that matches our response
        matching_message = None
        for message in messages:
            # Check if the message content matches our response
            if hasattr(message, 'content') and message.content == response["content"]:
                matching_message = message
                break

        alog.info(alog.pformat(response))
        print('### matching message ###')
        alog.info(alog.pformat(matching_message))

        # print last 2 messages from history
        if len(messages) >= 2:
            print('### Last two messages ###')
            alog.info(alog.pformat(messages[-2:]))

        # Log the result of the history check
        if matching_message:
            alog.info(f"Response successfully saved to history with ID: {getattr(matching_message, 'id', 'unknown')}")
        else:
            # Log issue but don't fail
            alog.warning(f"Could not find matching message in history for response with content: {response['content'][:50]}...")
            alog.info(f"Last 2 messages in history: {alog.pformat(messages[-2:] if len(messages) >= 2 else messages)}")
            alog.info(f"Response: {alog.pformat(response)}")
        
        # No assertions - more robust to continue even if history has issues

        if log_student_context:
            # Get the database message ID by querying the database for the most recent AI message
            # This works because the RunnableWithMessageHistory just added the response to the database
            db_message_id = None
            try:
                # Query the database directly for the most recent AI message in this session
                with history._connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT id FROM messages 
                        WHERE session_id = %s 
                        AND message->>'type' = 'ai'
                        ORDER BY id DESC 
                        LIMIT 1
                    """, (session_id,))
                    result = cursor.fetchone()
                    if result:
                        db_message_id = result[0]
                        alog.info(f"Found most recent AI message with database ID: {db_message_id}")
                    else:
                        alog.warning("No AI message found in database for this session")
            except Exception as e:
                alog.error(f"Error querying for recent AI message: {str(e)}")
                
            openai_response_id = response.get('id', None)

            alog.info(f'\n## db_message_id: {db_message_id}, openai_response_id: {openai_response_id} ##')

            if db_message_id:
                StudentContext.save(db_message_id, student_context)
                
                # Also save the system prompt used for this message (async fire-and-forget)
                try:
                    # Use the exact system prompt that was used to generate the response
                    if actual_system_prompt_used is not None:
                        def run_async_save():
                            """Run async save in a separate thread to avoid blocking."""
                            try:
                                # Create new event loop for this thread
                                loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(loop)
                                loop.run_until_complete(
                                    SystemPromptStorage.save_with_message_async(
                                        message_id=db_message_id,
                                        system_prompt=actual_system_prompt_used,
                                        student_context=student_context,
                                        agent_type=agent_type
                                    )
                                )
                                loop.close()
                            except Exception as e:
                                alog.error(f"Error in background async save for message {db_message_id}: {str(e)}")
                        
                        # Run in background thread (fire-and-forget)
                        thread = threading.Thread(target=run_async_save, daemon=True)
                        thread.start()
                        alog.info(f"Initiated background async save of system prompt for message {db_message_id}")
                    else:
                        alog.warning(f"No system prompt available to save for message {db_message_id}")
                except Exception as e:
                    alog.error(f"Error initiating async save of system prompt for message {db_message_id}: {str(e)}")
            else:
                alog.warning("Could not save student context or system prompt: no valid database message ID found")
                # Don't raise exception - this is not critical enough to fail the entire conversation

        if len(messages) > 1:
            alog.info(alog.pformat(messages[-2:]))
        else:
            alog.info(alog.pformat(messages[-1:]))
            
        # Verify that tool calls are included in the messages
        if hasattr(response, 'tool_calls') and response.get('tool_calls'):
            print('### Checking for tool calls in final messages ###')
            has_tool_calls = False
            
            # Check the last two messages to see if tool calls are present
            last_msgs = messages[-2:] if len(messages) >= 2 else messages
            for msg in last_msgs:
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    has_tool_calls = True
                    alog.info(f"Found tool calls in message with ID: {getattr(msg, 'id', 'unknown')}")
                    break
                    
            if not has_tool_calls:
                alog.error("Tool calls found in response but not in the final messages list")
                alog.error(f"Response: {alog.pformat(response)}")
                alog.error(f"Last messages: {alog.pformat(last_msgs)}")
                raise ValueError("Tool calls missing from final messages")
                
            alog.info("✓ Tool calls verified in final messages list")

        # Final verification: ensure response is in messages list
        response_in_messages = False
        response_id = response.get('id')
        
        if response_id:
            for msg in messages:
                if hasattr(msg, 'id') and msg.id == response_id:
                    response_in_messages = True
                    break
                    
            if not response_in_messages:
                alog.warning(f"Response with ID {response_id} not found in messages list, adding it")
                # Convert dict response back to message object if needed
                from langchain_core.messages import AIMessage
                
                # Create AI message from response
                ai_message = AIMessage(
                    content=response.get('content', ''),
                    additional_kwargs={}
                )
                
                # Add tool calls if present
                if response.get('tool_calls'):
                    ai_message.tool_calls = response.get('tool_calls')
                    
                # Add message ID
                if response_id:
                    ai_message.id = response_id
                    
                # Add to messages
                messages.append(ai_message)
                alog.info("Added response to messages list")
        
        return dict(messages=messages)
    else:
        raise Exception()
