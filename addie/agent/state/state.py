from addie import settings
from addie.history import get_history
from addie.data_model.student_context import StudentContext
from langchain_core.messages import BaseMessage
from typing import Optional, TypedDict, List
import alog


class AgentState(TypedDict):
    log_student_context: bool
    student_context: dict
    first_model_ran: bool
    messages: List[BaseMessage]
    model: Optional[str]
    session_id: str
    temperature: int
    history_len: int
    user_id: str
    student_id: str
    alternate_user_id: str = None
    student_workflow_id: Optional[str] = None
    mode: str = "Production"  # Possible values: "Production" or "Test"
    agent_type: str = "counselor"  # Agent type for system prompt storage

    @staticmethod
    async def init(
            session_id: str,
            user_id: str,
            student_id: str,
            alternate_user_id: str = None,
            log_student_context: bool = False,
            mode: str = "Production",  # Possible values: "Production" or "Test"
            student_workflow_id: Optional[str] = None,
            agent_type: str = "counselor",  # Agent type for system prompt storage
    ) -> 'AgentState':
        model = settings.MODEL
        messages = get_history(session_id).get_messages()
        student_context = await StudentContext(
            user_id=user_id,
            id=student_id,
            alternate_user_id=alternate_user_id,
            messages=[msg.dict() for msg in messages],
            mode=mode,
            _student_workflow_id=student_workflow_id
        ).async_init()

        return AgentState(
            alternate_user_id=alternate_user_id,
            log_student_context=log_student_context,
            first_model_ran=False,
            messages=messages,
            model=model,
            session_id=session_id,
            temperature=0,
            user_id=user_id,
            student_id=student_id,
            student_workflow_id=student_workflow_id,
            student_context=student_context.dump(),
            mode=mode,
            agent_type=agent_type
        )
