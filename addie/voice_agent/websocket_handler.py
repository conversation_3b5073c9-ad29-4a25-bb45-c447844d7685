"""
WebSocket handler for voice agent functionality.

This module handles the bidirectional audio streams between Twilio Media Streams
and OpenAI's Realtime API.
"""

import json
import asyncio
import alog
import websockets
from datetime import datetime
from websockets.protocol import State
from fastapi import WebSocket
from fastapi.websockets import WebSocketDisconnect
from typing import Optional, Dict, Any
from twilio.rest import Client
from addie.tools.voice_tools import goal_completion_tool
from .models import create_session
from addie import settings

from .config import get_voice_config
from .models import (
    CallSession, CallRecord, CallStatus,
    get_session, update_session
)
from addie.lib import prisma_client
from addie.tools.voice_tools import tools
from addie.data_model.system_prompt_storage import SystemPromptStorage
from addie.data_model.student_context import StudentContext


def get_voice_engagement_context(session: CallSession) -> Optional[Dict[str, str]]:
    """
    Extract engagement tracking context from voice session.
    
    Args:
        session: The voice call session
        
    Returns:
        Dictionary with user_id, student_id, workflow_id, session_id for engagement tracking
        or None if context cannot be extracted
    """
    try:
        prisma = prisma_client()
        
        # Get student and user info
        student = prisma.student.find_unique(
            where={'id': session.student_id},
            include={'users': True}
        )
        
        if not student or not student.users:
            alog.warning(f"No user found for student ID: {session.student_id}")
            return None
            
        # Get workflow_id from student_workflow_id  
        workflow_id = None
        if session.student_workflow_id:
            student_workflow = prisma.studentworkflow.find_unique(
                where={'id': session.student_workflow_id},
                include={'workflow': True}
            )
            if student_workflow and student_workflow.workflow:
                workflow_id = student_workflow.workflow.id
        
        user_id = student.users[0].id
        session_id = f"{workflow_id}-{user_id}" if workflow_id else f"voice-{session.student_id}"
        
        return {
            'user_id': user_id,
            'student_id': session.student_id,
            'workflow_id': workflow_id,
            'session_id': session_id
        }
    except Exception as e:
        alog.error(f"Failed to get voice engagement context: {e}")
        return None


def track_voice_session_event(session: CallSession, event_type: str, metadata: Dict[str, Any] = None) -> None:
    """
    Track voice session engagement events.
    
    Args:
        session: The voice call session
        event_type: Either 'session_started' or 'session_ended'
        metadata: Additional metadata for the event
    """
    try:
        from addie.services.engagement_tracker import EngagementTracker
        from prisma.enums import EngagementChannel, EngagementEventType
        
        context = get_voice_engagement_context(session)
        if not context:
            return
            
        tracker = EngagementTracker(prisma_client())
        
        # Convert string to enum
        if event_type == 'session_started':
            event = EngagementEventType.session_started
        elif event_type == 'session_ended':
            event = EngagementEventType.session_ended
        else:
            alog.warning(f"Unknown session event type: {event_type}")
            return
            
        tracker.track_message_event(
            user_id=context['user_id'],
            student_id=context['student_id'],
            session_id=context['session_id'],
            channel=EngagementChannel.voice,
            event_type=event,
            workflow_id=context['workflow_id'],
            metadata=metadata or {}
        )
        
        alog.info(f"Tracked voice session event: {event_type} for student {context['student_id']}")
        
    except Exception as e:
        alog.error(f"Failed to track voice session event: {e}")


def log_voice_message_to_history(
    session_id: str, message_text: str, message_type: str = "human", source: str = "voice", session: Optional[CallSession] = None
) -> Optional[int]:
    """
    Log a voice message to the messages table that LangChain uses and track engagement.

    Args:
        session_id: The session ID (format: workflowId-userId)
        message_text: The content of the message
        message_type: Type of message ("human" for user speech, "ai" for assistant response)
        source: Source of the message (default "voice")
        session: The voice call session for engagement tracking (optional)
        
    Returns:
        Message ID if successful, None otherwise
    """
    try:
        from langchain_core.messages import HumanMessage, AIMessage
        from addie.history import get_history

        # Create the appropriate LangChain message object with source metadata
        additional_kwargs = {"source": source, "timestamp": datetime.now().isoformat()}

        if message_type == "human":
            message = HumanMessage(
                content=message_text, additional_kwargs=additional_kwargs
            )
        elif message_type == "ai":
            message = AIMessage(
                content=message_text, additional_kwargs=additional_kwargs
            )
        else:
            # Default to HumanMessage for unknown types
            message = HumanMessage(
                content=message_text, additional_kwargs=additional_kwargs
            )

        # Use LangChain's history system to properly serialize and save the message
        history = get_history(session_id)
        history.add_message(message)

        # Try to get the message ID from the most recent message in the database
        try:
            prisma = prisma_client()
            recent_message = prisma.messages.find_first(
                where={"session_id": session_id},
                order={"id": "desc"}
            )
            message_id = recent_message.id if recent_message else None
            
            alog.debug(f"Logged {message_type} voice message to session {session_id}, message_id: {message_id}")
            
            # Track engagement event if session is provided
            if session:
                try:
                    from addie.services.engagement_tracker import EngagementTracker
                    from prisma.enums import EngagementChannel, EngagementEventType
                    
                    context = get_voice_engagement_context(session)
                    if context:
                        tracker = EngagementTracker(prisma)
                        
                        event_type = (EngagementEventType.message_received if message_type == "human" 
                                     else EngagementEventType.message_sent)
                        
                        tracker.track_message_event(
                            user_id=context['user_id'],
                            student_id=context['student_id'],
                            session_id=context['session_id'],
                            channel=EngagementChannel.voice,
                            event_type=event_type,
                            message_id=message_id,
                            workflow_id=context['workflow_id'],
                            metadata={'transcript_length': len(message_text), 'source': source}
                        )
                        
                        alog.info(f"Tracked voice engagement event: {event_type} for student {context['student_id']}")
                        
                except Exception as e:
                    alog.error(f"Failed to track voice engagement: {e}")
                    # Don't let engagement tracking failures break message logging
            
            return message_id
            
        except Exception as e:
            alog.error(f"Error getting message ID after logging: {str(e)}")
            return None

    except Exception as e:
        alog.error(f"Error logging voice message to history: {str(e)}")
        return None


class VoiceWebSocketHandler:
    """Handles WebSocket connections for voice calls."""

    def __init__(self):
        self.config = get_voice_config()
        self.openai_ws_url = f'wss://api.openai.com/v1/realtime?model={self.config.openai_model}'

    async def handle_media_stream(self, websocket: WebSocket, call_sid: str):
        """
        Handle WebSocket connections between Twilio and OpenAI.

        Args:
            websocket: The Twilio WebSocket connection (already accepted)
            call_sid: The Twilio call SID
        """
        alog.info(f"Voice client connected for call SID: {call_sid}")
        # Note: WebSocket is already accepted in the route handler

        # Get or create session
        session = get_session(call_sid)
        if not session:
            alog.error(f"No session found for call SID: {call_sid}")
            await websocket.close(code=1000, reason="No session found")
            return

        # Update session status
        update_session(call_sid, twilio_connected=True)

        # Update call record to mark call as started
        call_record = CallRecord.from_db(call_sid)
        if call_record:
            call_record.status = CallStatus.IN_PROGRESS
            if not call_record.started_at:
                call_record.started_at = datetime.now()
            call_record.save_to_db()
            alog.info(f"Call started: {call_sid}")

            # Ensure workflow mode is set to 'voice' when call starts
            call_record._update_studentworkflow()
        else:
            alog.warning(f"No call record found for SID: {call_sid}")

        # Initialize transcript collection (clear any existing parts)
        session.transcript_parts.clear()

        try:
            # Connect to OpenAI Realtime API with improved connection settings
            async with websockets.connect(
                self.openai_ws_url,
                extra_headers=self.config.get_openai_headers(),
                ping_interval=20,  # Send ping every 20 seconds to keep connection alive
                ping_timeout=10,   # Wait 10 seconds for pong response
                close_timeout=10,  # Wait 10 seconds for close handshake
                max_size=2**20,    # 1MB max message size for audio data
                compression=None   # Disable compression for real-time audio
            ) as openai_ws:

                # Send session update to OpenAI
                await self._send_session_update(openai_ws, session)
                update_session(call_sid, openai_session_active=True)
                
                # Track session started event
                track_voice_session_event(session, 'session_started', {
                    'call_sid': call_sid,
                    'started_at': datetime.now().isoformat()
                })

                # Send initial greeting to start the conversation
                await self._send_initial_greeting(openai_ws, session)

                # Handle bidirectional communication with improved error handling
                tasks = [
                    self._receive_from_twilio(websocket, openai_ws, session),
                    self._send_to_twilio(websocket, openai_ws, session),
                ]

                # Run tasks and handle exceptions gracefully
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # Log any exceptions that occurred
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        task_name = ["receive_from_twilio", "send_to_twilio"][i]
                        alog.error(f"Exception in {task_name}: {str(result)}")

        except Exception as e:
            alog.error(f"Error in media stream handler: {str(e)}")
            # Update call record with error
            call_record = CallRecord.from_db(call_sid)
            if call_record:
                # Save transcript if available before marking as failed
                transcript = session.get_full_transcript() if session and session.transcript_parts else None
                call_record.mark_as_failed(str(e))
                if transcript:
                    call_record.transcript = transcript
                    call_record.save_to_db()
                alog.debug(f"Updated call record with error for SID: {call_sid}")
                
                # Track session ended event for failed calls
                if session:
                    track_voice_session_event(session, 'session_ended', {
                        'call_sid': call_sid,
                        'ended_at': datetime.now().isoformat(),
                        'completion_status': 'failed',
                        'error_message': str(e),
                        'transcript_length': len(transcript) if transcript else 0,
                        'total_messages': len(session.transcript_parts) if session.transcript_parts else 0
                    })

        finally:
            # Update call record with completion
            call_record = CallRecord.from_db(call_sid)
            if call_record and call_record.status not in [CallStatus.FAILED, CallStatus.COMPLETED]:
                # Get session again to ensure we have the latest data
                current_session = get_session(call_sid)
                transcript = None
                if current_session and current_session.transcript_parts:
                    transcript = current_session.get_full_transcript()

                call_record.mark_as_completed(transcript)
                alog.info(f"Call completed: {call_sid}")
                
                # Track session ended event
                if current_session:
                    track_voice_session_event(current_session, 'session_ended', {
                        'call_sid': call_sid,
                        'ended_at': datetime.now().isoformat(),
                        'completion_status': 'completed',
                        'transcript_length': len(transcript) if transcript else 0,
                        'total_messages': len(current_session.transcript_parts) if current_session.transcript_parts else 0
                    })

            # Clean up session
            update_session(call_sid, twilio_connected=False, openai_session_active=False)

    async def _receive_from_twilio(
        self,
        twilio_ws: WebSocket,
        openai_ws: Any,  # websockets connection
        session: CallSession
    ):
        """
        Receive audio data from Twilio and send it to OpenAI Realtime API.

        Args:
            twilio_ws: Twilio WebSocket connection
            openai_ws: OpenAI WebSocket connection
            session: Current call session
        """
        try:
            async for message in twilio_ws.iter_text():
                data = json.loads(message)

                if data['event'] == 'media' and openai_ws.state == State.OPEN:
                    # Forward audio to OpenAI for processing and transcription
                    audio_payload = data['media']['payload']

                    audio_append = {
                        "type": "input_audio_buffer.append",
                        "audio": audio_payload
                    }
                    await openai_ws.send(json.dumps(audio_append))
                    session.update_activity()

                elif data['event'] == 'start':
                    stream_sid = data['start']['streamSid']
                    update_session(session.call_sid, stream_sid=stream_sid)
                    alog.debug(f"Stream started: {stream_sid}")

                elif data['event'] == 'stop':
                    alog.info(f"Stream stopped: {session.call_sid}")
                    # Trigger call completion when stream stops
                    call_record = CallRecord.from_db(session.call_sid)
                    if call_record and call_record.status == CallStatus.IN_PROGRESS:
                        transcript = session.get_full_transcript() if session.transcript_parts else None
                        call_record.mark_as_completed(transcript)
                        
                        # Track session ended event
                        track_voice_session_event(session, 'session_ended', {
                            'call_sid': session.call_sid,
                            'ended_at': datetime.now().isoformat(),
                            'completion_status': 'completed',
                            'transcript_length': len(transcript) if transcript else 0,
                            'total_messages': len(session.transcript_parts) if session.transcript_parts else 0
                        })
                    break

        except WebSocketDisconnect:
            alog.debug(f"Twilio client disconnected: {session.call_sid}")
            # Handle call completion on disconnect
            call_record = CallRecord.from_db(session.call_sid)
            if call_record and call_record.status == CallStatus.IN_PROGRESS:
                transcript = session.get_full_transcript() if session.transcript_parts else None
                call_record.mark_as_completed(transcript)
                
                # Track session ended event for disconnect
                track_voice_session_event(session, 'session_ended', {
                    'call_sid': session.call_sid,
                    'ended_at': datetime.now().isoformat(),
                    'completion_status': 'completed',
                    'disconnect_reason': 'client_disconnect',
                    'transcript_length': len(transcript) if transcript else 0,
                    'total_messages': len(session.transcript_parts) if session.transcript_parts else 0
                })
        except Exception as e:
            alog.error(f"Error receiving from Twilio: {str(e)}")
        finally:
            if openai_ws.state == State.OPEN:
                await openai_ws.close()

    async def _send_to_twilio(
        self,
        twilio_ws: WebSocket,
        openai_ws: Any,  # websockets connection
        session: CallSession
    ):
        """
        Receive events from OpenAI Realtime API and send audio back to Twilio.

        Args:
            twilio_ws: Twilio WebSocket connection
            openai_ws: OpenAI WebSocket connection
            session: Current call session
        """
        # Initialize audio buffer for managing audio chunks
        audio_buffer = []
        buffer_size_limit = 50  # Limit buffer size to prevent memory issues
        audio_chunk_count = 0  # Track number of audio chunks sent
        last_audio_chunk_time = None  # Track timing of last audio chunk

        # Track response state to prevent invalid cancellations
        active_response = False
        response_in_progress = False

        try:
            async for openai_message in openai_ws:
                response = json.loads(openai_message)

                # Log only critical events
                if response['type'] == 'error':
                    alog.error(f"OpenAI error: {response}")
                elif response['type'] == 'session.updated':
                    alog.debug("OpenAI session updated")

                if response['type'] == 'response.audio.delta' and response.get('delta'):
                    # Mark that we have an active response generating audio
                    active_response = True
                    response_in_progress = True

                    # Forward audio from OpenAI to Twilio with improved error handling
                    try:
                        if not session.stream_sid:
                            alog.warning(f"No stream SID available for call {session.call_sid}, buffering audio")
                            # Buffer audio chunks if stream SID is not yet available
                            if len(audio_buffer) < buffer_size_limit:
                                audio_buffer.append(response['delta'])
                            continue

                        # Send any buffered audio first
                        if audio_buffer:
                            alog.debug(f"Sending {len(audio_buffer)} buffered audio chunks")
                            for buffered_audio in audio_buffer:
                                await self._send_audio_chunk_to_twilio(twilio_ws, session, buffered_audio)
                            audio_buffer.clear()

                        # Send current audio chunk
                        await self._send_audio_chunk_to_twilio(twilio_ws, session, response['delta'])
                        session.update_activity()
                        audio_chunk_count += 1
                        last_audio_chunk_time = asyncio.get_event_loop().time()

                    except Exception as e:
                        alog.error(f"Error processing audio data: {str(e)}")
                        # Don't break the loop, continue processing other events

                # Collect transcript from OpenAI responses
                if response['type'] == 'response.audio_transcript.delta':
                    transcript_delta = response.get('delta', '')
                    if transcript_delta:
                        # Accumulate assistant transcript deltas instead of saving each one
                        session._current_assistant_transcript += transcript_delta

                elif response['type'] == 'response.audio_transcript.done':
                    # Assistant transcript is complete, save the full sentence
                    if session._current_assistant_transcript.strip():
                        assistant_text = session._current_assistant_transcript.strip()
                        session.add_transcript_part(f"Assistant: {assistant_text}")
                        alog.info(f"Assistant: {assistant_text}")

                        # Log assistant message to conversation history
                        session_id = session.get_session_id_for_conversation()
                        if session_id:
                            message_id = log_voice_message_to_history(session_id, assistant_text, "ai", session=session)
                            
                            # Save system prompt with this AI message
                            if message_id and hasattr(session, 'student_id') and hasattr(session, 'student_workflow_id'):
                                await self._save_system_prompt_for_ai_message(
                                    session, 
                                    message_id, 
                                    async_mode=self.config.system_prompt_async_mode
                                )

                        session._current_assistant_transcript = ""



                elif response['type'] == 'conversation.item.input_audio_transcription.completed':
                    # User speech transcript from OpenAI
                    transcript = response.get('transcript', '')
                    if transcript and transcript.strip():
                        session.add_transcript_part(f"User: {transcript.strip()}")
                        alog.info(f"User: {transcript.strip()}")

                        # Log user message to conversation history
                        session_id = session.get_session_id_for_conversation()
                        if session_id:
                            log_voice_message_to_history(session_id, transcript.strip(), "human", session=session)
                    else:
                        alog.debug(f"Empty transcript in transcription event")

                elif response['type'] == 'conversation.item.input_audio_transcription.failed':
                    # Log transcription failures
                    error = response.get('error', {})
                    alog.warning(f"OpenAI user audio transcription failed: {error}")

                elif response['type'] == 'input_audio_buffer.speech_started':
                    alog.debug("User started speaking")

                    if session.stream_sid and self.config.enable_twilio_buffer_clear:
                        clear_twilio = {
                            "streamSid": session.stream_sid,
                            "event": "clear"
                        }
                        await twilio_ws.send_json(clear_twilio)

                    # Only attempt to cancel if there's actually an active response
                    if active_response and response_in_progress:
                        interrupt_message = {
                            "type": "response.cancel"
                        }
                        await openai_ws.send(json.dumps(interrupt_message))
                        alog.debug("Cancelled active OpenAI response")
                        # Mark response as no longer active
                        active_response = False
                        response_in_progress = False

                elif response['type'] == 'input_audio_buffer.speech_stopped':
                    alog.debug("User stopped speaking")

                elif response['type'] == 'response.audio.done':
                    alog.debug(f"Audio response completed - {audio_chunk_count} chunks sent")

                    # CRITICAL FIX: Send any remaining buffered audio chunks
                    if audio_buffer:
                        alog.debug(f"Flushing {len(audio_buffer)} remaining buffered audio chunks")
                        for buffered_audio in audio_buffer:
                            try:
                                await self._send_audio_chunk_to_twilio(twilio_ws, session, buffered_audio)
                                audio_chunk_count += 1
                            except Exception as e:
                                alog.error(f"Error sending buffered audio chunk: {e}")
                        audio_buffer.clear()

                    # Add a longer delay to ensure all audio chunks reach Twilio and are processed
                    await asyncio.sleep(0.15)  # 150ms delay to ensure complete delivery

                    # Mark response as no longer active only after ensuring delivery
                    active_response = False

                    # Check if we have any pending transcript that wasn't sent
                    if session._current_assistant_transcript.strip():
                        assistant_text = session._current_assistant_transcript.strip()
                        alog.warning(f"Audio completed but transcript still pending: {assistant_text}")
                        session.add_transcript_part(f"Assistant: {assistant_text}")

                        # Log assistant message to conversation history (fallback)
                        session_id = session.get_session_id_for_conversation()
                        if session_id:
                            message_id = log_voice_message_to_history(session_id, assistant_text, "ai", session=session)
                            
                            # Save system prompt with this AI message
                            if message_id and hasattr(session, 'student_id') and hasattr(session, 'student_workflow_id'):
                                await self._save_system_prompt_for_ai_message(
                                    session, 
                                    message_id, 
                                    async_mode=self.config.system_prompt_async_mode
                                )

                        session._current_assistant_transcript = ""

                    # Verify audio stream integrity
                    if audio_chunk_count == 0:
                        alog.warning("Audio response completed but no audio chunks were sent")
                    elif last_audio_chunk_time:
                        time_since_last_chunk = asyncio.get_event_loop().time() - last_audio_chunk_time
                        if time_since_last_chunk > 5.0:  # More than 5 seconds since last chunk
                            alog.warning(f"Large gap ({time_since_last_chunk:.1f}s) since last audio chunk")
                    # This indicates the AI has finished speaking

                elif response['type'] == 'response.cancelled':
                    alog.debug("AI response was cancelled")
                    # Mark response as no longer active since it was cancelled
                    active_response = False
                    response_in_progress = False

                elif response['type'] == 'response.function_call_arguments.delta':
                    # Handle streaming function call arguments
                    call_id = response.get('call_id')
                    delta = response.get('delta', '')

                    if call_id:
                        # Accumulate function arguments
                        if not hasattr(self, '_function_calls'):
                            self._function_calls = {}
                        if call_id not in self._function_calls:
                            self._function_calls[call_id] = {
                                'name': response.get('name', ''),
                                'arguments': ''
                            }
                        self._function_calls[call_id]['arguments'] += delta

                elif response['type'] == 'response.function_call_arguments.done':
                    # Handle completed function call arguments
                    call_id = response.get('call_id')
                    name = response.get('name')
                    arguments = response.get('arguments', '')

                    alog.info(f"Function call: {name}")

                    # Execute the function call
                    await self._execute_function_call(openai_ws, call_id, name, arguments)

                elif response['type'] == 'response.done':
                    alog.debug(f"Response completed - {audio_chunk_count} audio chunks")

                    # Mark response as completely done
                    active_response = False
                    response_in_progress = False

                    # BACKUP FIX: Ensure any remaining buffered audio is sent (fallback safety)
                    if audio_buffer:
                        alog.debug(f"Backup flush: {len(audio_buffer)} remaining chunks")
                        for buffered_audio in audio_buffer:
                            try:
                                await self._send_audio_chunk_to_twilio(twilio_ws, session, buffered_audio)
                                audio_chunk_count += 1
                            except Exception as e:
                                alog.error(f"Error sending backup buffered audio chunk: {e}")
                        audio_buffer.clear()
                        # Add extra delay for backup flush
                        await asyncio.sleep(0.1)  # 100ms delay for backup flush

                    # Fallback: if we have accumulated assistant transcript but didn't get the done event
                    if session._current_assistant_transcript.strip():
                        assistant_text = session._current_assistant_transcript.strip()
                        session.add_transcript_part(f"Assistant: {assistant_text}")
                        alog.warning(f"Added incomplete assistant transcript: {assistant_text}")

                        # Log assistant message to conversation history (fallback)
                        session_id = session.get_session_id_for_conversation()
                        if session_id:
                            message_id = log_voice_message_to_history(session_id, assistant_text, "ai", session=session)
                            
                            # Save system prompt with this AI message
                            if message_id and hasattr(session, 'student_id') and hasattr(session, 'student_workflow_id'):
                                await self._save_system_prompt_for_ai_message(
                                    session, 
                                    message_id, 
                                    async_mode=self.config.system_prompt_async_mode
                                )

                        session._current_assistant_transcript = ""

                    # Reset audio chunk counter for next response
                    audio_chunk_count = 0

                elif response['type'] == 'error':
                    error_details = response.get('error', {})
                    alog.error(f"OpenAI Realtime API error: {error_details}")

                    # Handle specific error types that might cause audio interruptions
                    error_type = error_details.get('type', '')
                    if error_type in ['connection_error', 'rate_limit_error']:
                        alog.warning(f"Recoverable error detected: {error_type}")
                        # Could implement retry logic here if needed

                # Minimal debug logging for development
                if response['type'] in ['error', 'rate_limits.updated']:
                    alog.debug(f"OpenAI event: {response['type']}")

        except Exception as e:
            alog.error(f"Error sending to Twilio: {str(e)}")

    async def _send_audio_chunk_to_twilio(self, twilio_ws: WebSocket, session: CallSession, audio_payload: str):
        """
        Send a single audio chunk to Twilio with proper error handling and validation.

        Args:
            twilio_ws: Twilio WebSocket connection
            session: Current call session
            audio_payload: Base64 encoded audio data
        """
        try:
            # Validate audio payload
            if not audio_payload or not isinstance(audio_payload, str):
                alog.warning(f"Invalid audio payload for call {session.call_sid}")
                return

            # Check WebSocket connection state
            if hasattr(twilio_ws, 'client_state') and twilio_ws.client_state.name != 'CONNECTED':
                alog.warning(f"Twilio WebSocket not connected for call {session.call_sid}, state: {twilio_ws.client_state.name}")
                return

            audio_delta = {
                "event": "media",
                "streamSid": session.stream_sid,
                "media": {
                    "payload": audio_payload
                }
            }

            await twilio_ws.send_json(audio_delta)

            # Add a small delay to prevent overwhelming the WebSocket connection
            # This helps prevent audio chunks from being dropped due to network congestion
            await asyncio.sleep(0.001)  # 1ms delay between chunks

        except Exception as e:
            alog.error(f"Error sending audio chunk to Twilio: {str(e)}")
            # Don't re-raise to avoid breaking the audio stream

    async def _save_system_prompt_for_ai_message(
        self, 
        session: CallSession, 
        message_id: int,
        async_mode: bool = True  # Default to async for production
    ) -> None:
        """Save system prompt for AI voice messages only."""
        try:
            # Get the actual user_id from student record
            prisma = prisma_client()
            student = prisma.student.find_unique(
                where={"id": session.student_id},
                include={"users": True}
            )
            
            if not student or not student.users:
                alog.warning(f"No user found for student ID: {session.student_id}")
                return
                
            user_id = student.users[0].id
            
            # Get system prompt
            system_prompt = await self.config.get_voice_system_prompt(
                session.student_id, session.student_workflow_id
            )
            
            # Get student context with correct user_id
            student_context = await StudentContext(
                user_id=user_id,  # ✅ CORRECT - actual User table ID
                id=session.student_id,  # Student record ID
                _student_workflow_id=session.student_workflow_id
            ).async_init()
            
            # Save with configurable async/sync mode
            if async_mode:
                # Production: fire-and-forget async
                asyncio.create_task(
                    SystemPromptStorage.save_with_message_configurable(
                        message_id=message_id,
                        system_prompt=system_prompt,
                        student_context=student_context.dump() if hasattr(student_context, 'dump') else {},
                        agent_type='voice',
                        async_mode=True
                    )
                )
                alog.info(f"Initiated async SystemPrompt storage for AI voice message {message_id}")
            else:
                # Testing: synchronous execution for reliable verification
                result = await SystemPromptStorage.save_with_message_configurable(
                    message_id=message_id,
                    system_prompt=system_prompt,
                    student_context=student_context.dump() if hasattr(student_context, 'dump') else {},
                    agent_type='voice',
                    async_mode=False
                )
                alog.info(f"Completed sync SystemPrompt storage for AI voice message {message_id}: {result is not None}")
            
        except Exception as e:
            alog.error(f"Error saving voice system prompt for message {message_id}: {str(e)}")

    async def _execute_function_call(self, openai_ws: Any, call_id: str, function_name: str, arguments_str: str):
        """
        Execute a function call and send the result back to OpenAI.

        Args:
            openai_ws: OpenAI WebSocket connection
            call_id: The function call ID from OpenAI
            function_name: Name of the function to call
            arguments_str: JSON string of function arguments
        """
        try:
            # Parse arguments
            arguments = json.loads(arguments_str) if arguments_str else {}
            alog.debug(f"Executing function {function_name}")

            # Find and execute the function
            function_result = None
            for tool in tools:
                if tool.get('name') == function_name:
                    # Import the function dynamically

                    if function_name == 'goal_completion_tool':
                        student_id = arguments.get('student_id', '')
                        step_id = arguments.get('step_id', '')
                        function_result = await goal_completion_tool(student_id, step_id)
                    # Add more function handlers here as needed
                    break

            if function_result is None:
                function_result = f"Error: Function {function_name} not found or not implemented"
                alog.error(function_result)

            alog.debug(f"Function {function_name} completed")

            # Send the function result back to OpenAI
            function_call_output = {
                "type": "conversation.item.create",
                "item": {
                    "type": "function_call_output",
                    "call_id": call_id,
                    "output": str(function_result)
                }
            }

            await openai_ws.send(json.dumps(function_call_output))

            # Trigger the model to continue the conversation
            response_create = {
                "type": "response.create"
            }
            await openai_ws.send(json.dumps(response_create))

        except Exception as e:
            alog.error(f"Error executing function call {function_name}: {str(e)}")

            # Send error result back to OpenAI
            error_output = {
                "type": "conversation.item.create",
                "item": {
                    "type": "function_call_output",
                    "call_id": call_id,
                    "output": f"Error executing function: {str(e)}"
                }
            }

            try:
                await openai_ws.send(json.dumps(error_output))
                # Trigger the model to continue even after error
                response_create = {"type": "response.create"}
                await openai_ws.send(json.dumps(response_create))
            except Exception as send_error:
                alog.error(f"Error sending function call error result: {str(send_error)}")

    async def _send_session_update(self, openai_ws: Any, session: Optional[CallSession] = None):
        """
        Send session update to OpenAI WebSocket.

        Args:
            openai_ws: OpenAI WebSocket connection
            session: Call session containing student and workflow data
        """
        # Get student_id and student_workflow_id from session or instance attributes
        student_id = session.student_id if session else getattr(self, 'student_id', None)
        student_workflow_id = session.student_workflow_id if session else getattr(self, 'student_workflow_id', None)

        if not student_id or not student_workflow_id:
            alog.error(f"Missing required data for session update: student_id={student_id}, student_workflow_id={student_workflow_id}")
            return

        session_update = await self.config.get_openai_session_config(student_id, student_workflow_id)
        alog.debug('Sending session update to OpenAI')
        await openai_ws.send(json.dumps(session_update))

    async def _send_initial_greeting(self, openai_ws: Any, session: Optional[CallSession] = None):
        """
        Send an initial greeting to trigger OpenAI to generate an audio response.

        Args:
            openai_ws: OpenAI WebSocket connection
            session: Call session containing student and workflow data
        """
        # Get student_id and student_workflow_id from session or instance attributes
        student_id = session.student_id if session else getattr(self, 'student_id', None)
        student_workflow_id = session.student_workflow_id if session else getattr(self, 'student_workflow_id', None)

        if not student_id or not student_workflow_id:
            alog.error(f"Missing required data for initial greeting: student_id={student_id}, student_workflow_id={student_workflow_id}")
            return

        # Create a system message to start the conversation
        greeting_item = {
            "type": "conversation.item.create",
            "item": {
                "type": "message",
                "role": "user",
                "content": [
                    {
                        "type": "input_text",
                        "text": "Hello, this is the start of our conversation."
                    }
                ]
            }
        }
        await openai_ws.send(json.dumps(greeting_item))

        # Create a response to trigger audio generation
        response_create = {
            "type": "response.create"
        }
        alog.debug("Sending initial greeting trigger to OpenAI")
        await openai_ws.send(json.dumps(response_create))

    async def initiate_call(
        self,
        to_number: str,
        student_id: Optional[str] = None,
        workflow_id: Optional[str] = None,
        student_workflow_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Initiate an outbound voice call.

        Args:
            to_number: Phone number to call
            student_id: Optional student ID
            workflow_id: Optional workflow ID
            student_workflow_id: Optional student workflow ID

        Returns:
            Dictionary with call information
        """
        self.student_id = student_id
        self.workflow_id = workflow_id
        self.student_workflow_id = student_workflow_id
        try:

            if student_id and not student_workflow_id:
                try:
                    prisma = prisma_client()

                    if workflow_id:
                        print(f"Workflow ID: {workflow_id}")
                        student_workflow = prisma.studentworkflow.find_first(
                            where={
                                "student_id": student_id,
                                "workflow_id": workflow_id,
                            }  # type: ignore
                        )
                        print(f"Student Workflow: {student_workflow}")
                    else:
                        student_workflow = prisma.studentworkflow.find_first(
                            where={"student_id": student_id, "status": "NOT_STARTED"}  # type: ignore
                        )
                    if student_workflow:
                        student_workflow_id = student_workflow.id
                        workflow_id = workflow_id or student_workflow.workflow_id
                except Exception as e:
                    alog.error(f"Error finding student workflow: {str(e)}")

            client = Client(self.config.twilio_account_sid, self.config.twilio_auth_token)

            # Create the call with status callback
            call = client.calls.create(
                to=to_number,
                from_=self.config.twilio_phone_number,
                url=f"{self._get_base_url()}/voice/incoming-call",
                status_callback=f"{self._get_base_url()}/voice/call-status",
                status_callback_event=['initiated', 'ringing', 'answered', 'completed'],
                status_callback_method='POST',
                machine_detection="Enable",
                timeout=15
            )

            # Only create call record if we have required data
            if student_id and student_workflow_id:
                call_record = CallRecord(
                    call_sid=call.sid,
                    student_id=student_id,
                    student_workflow_id=student_workflow_id,
                    status=CallStatus.INITIATED
                )
                call_record.save_to_db()
            else:
                alog.warning(f"Cannot create call record - missing required data: student_id={student_id}, student_workflow_id={student_workflow_id}")

            # Create session
            session = get_session(call.sid)
            if not session:
                create_session(
                    call.sid,
                    student_id=student_id,
                    workflow_id=workflow_id,
                    student_workflow_id=student_workflow_id
                )

            alog.info(f"Call initiated: {call.sid}")
            return {
                "success": True,
                "call_sid": call.sid,
                "message": "Call initiated successfully"
            }

        except Exception as e:
            alog.error(f"Error initiating call: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }



    def _get_base_url(self) -> str:
        """Get the base URL for webhooks."""
        # This should be configured based on your deployment
        # For development, you might use ngrok
        # For production, use your actual domain
        return settings.API_URL


# Global handler instance
_handler: Optional[VoiceWebSocketHandler] = None


def get_voice_handler() -> VoiceWebSocketHandler:
    """Get the global voice WebSocket handler."""
    global _handler
    if _handler is None:
        _handler = VoiceWebSocketHandler()
    return _handler
