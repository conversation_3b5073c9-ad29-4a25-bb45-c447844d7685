"""
Data models for voice agent functionality.

This module contains models for call records, session management,
and voice conversation tracking.
"""

import alog
import re
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from enum import Enum
from dataclasses import dataclass, field
from addie.lib import prisma_client
from prisma.enums import StudentWorkflowMode, StudentWorkflowStatus


class CallStatus(str, Enum):
    """Enumeration of call statuses."""
    INITIATED = "INITIATED"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


@dataclass
class CallRecord:
    """Model for tracking voice call records."""

    call_sid: str
    student_id: str
    student_workflow_id: str  # Required in database
    status: CallStatus = CallStatus.INITIATED
    started_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None
    duration_seconds: Optional[int] = None
    transcript: Optional[str] = None
    error_message: Optional[str] = None
    retry_count: int = 0

    def save_to_db(self) -> None:
        """Save the call record to the database."""
        try:
            prisma = prisma_client()

            # Check if record already exists
            existing_record = prisma.callrecord.find_unique(
                where={"twilio_call_sid": self.call_sid}
            )

            if existing_record:
                # Update existing record with explicit field assignments
                update_kwargs: Dict[str, Any] = {}
                if self.status:
                    update_kwargs["status"] = self.status.value
                if self.started_at:
                    update_kwargs["start_time"] = self.started_at
                if self.ended_at:
                    update_kwargs["end_time"] = self.ended_at
                if self.duration_seconds is not None:
                    update_kwargs["duration"] = self.duration_seconds
                if self.transcript:
                    update_kwargs["transcript"] = self.transcript
                if self.error_message:
                    update_kwargs["failure_reason"] = self.error_message
                if self.retry_count is not None:
                    update_kwargs["retry_count"] = self.retry_count

                prisma.callrecord.update(
                    where={"twilio_call_sid": self.call_sid},
                    data=update_kwargs  # type: ignore
                )
                alog.info(f"Updated call record for SID: {self.call_sid}")
                # Note: Don't update workflow mode for existing records - only for new ones
            else:
                # Create new record with explicit field assignments
                create_kwargs: Dict[str, Any] = {
                    "twilio_call_sid": self.call_sid,
                    "student_id": self.student_id,
                    "student_workflow_id": self.student_workflow_id,
                }

                # Add optional fields
                if self.status:
                    create_kwargs["status"] = self.status.value
                if self.started_at:
                    create_kwargs["start_time"] = self.started_at
                if self.ended_at:
                    create_kwargs["end_time"] = self.ended_at
                if self.duration_seconds is not None:
                    create_kwargs["duration"] = self.duration_seconds
                if self.transcript:
                    create_kwargs["transcript"] = self.transcript
                if self.error_message:
                    create_kwargs["failure_reason"] = self.error_message
                if self.retry_count is not None:
                    create_kwargs["retry_count"] = self.retry_count

                prisma.callrecord.create(data=create_kwargs)  # type: ignore
                alog.info(f"Created call record for SID: {self.call_sid}")

                # Update workflow mode to 'voice' when call record is created
                alog.info(f"Updating workflow mode to 'voice' for new call record: {self.call_sid}")
                self._update_studentworkflow()

        except Exception as e:
            alog.error(f"Error saving call record: {str(e)}")

    def _update_studentworkflow(self) -> None:
        """Update the associated workflow mode to 'voice'."""
        try:
            prisma = prisma_client()

            # Update the student workflow mode to 'voice'
            prisma.studentworkflow.update(
                where={"id": self.student_workflow_id},
                data={
                    "mode": StudentWorkflowMode.voice,
                    "status": StudentWorkflowStatus.IN_PROGRESS,
                }
            )
            alog.info(f"Updated workflow {self.student_workflow_id} mode to 'voice' for call {self.call_sid} and status to {StudentWorkflowStatus.IN_PROGRESS.value}")

        except Exception as e:
            alog.error(f"Error updating workflow mode to voice for call {self.call_sid}: {str(e)}")

    def increment_retry_count(self) -> None:
        """Increment the retry count for this call record."""
        self.retry_count += 1
        alog.info(f"Incremented retry count to {self.retry_count} for call SID: {self.call_sid}")

    def mark_as_failed(self, error_message: str) -> None:
        """Mark the call as failed with an error message."""
        self.status = CallStatus.FAILED
        self.error_message = error_message
        self.ended_at = datetime.now()
        if self.started_at:
            # Handle timezone-aware vs naive datetime comparison
            started_at_naive = self.started_at.replace(tzinfo=None) if self.started_at.tzinfo else self.started_at
            duration = self.ended_at - started_at_naive
            self.duration_seconds = int(duration.total_seconds())
        self.save_to_db()

    def mark_as_completed(self, transcript: Optional[str] = None) -> None:
        """Mark the call as completed."""
        self.status = CallStatus.COMPLETED
        self.ended_at = datetime.now()
        if self.started_at:
            # Handle timezone-aware vs naive datetime comparison
            started_at_naive = self.started_at.replace(tzinfo=None) if self.started_at.tzinfo else self.started_at
            duration = self.ended_at - started_at_naive
            self.duration_seconds = int(duration.total_seconds())
        if transcript:
            self.transcript = transcript
        self.save_to_db()

    @classmethod
    def cleanup_stale_calls(cls, max_duration_minutes: int = 60) -> int:
        """
        Clean up calls that have been in progress for too long.

        Args:
            max_duration_minutes: Maximum duration before marking as completed

        Returns:
            Number of calls cleaned up
        """
        try:
            prisma = prisma_client()

            # Get all IN_PROGRESS calls and check them manually to avoid timezone issues
            stale_calls = prisma.callrecord.find_many(
                where={"status": "IN_PROGRESS"} # type: ignore
            )

            # Filter by age manually
            cutoff_time = datetime.now() - timedelta(minutes=max_duration_minutes)
            filtered_calls = []

            for call in stale_calls:
                if call.start_time and call.start_time.replace(tzinfo=None) < cutoff_time:
                    filtered_calls.append(call)

            stale_calls = filtered_calls

            cleaned_count = 0
            for db_call in stale_calls:
                call_record = cls.from_db(db_call.twilio_call_sid)
                if call_record:
                    call_record.mark_as_completed("Call timed out - automatically completed")
                    alog.info(f"Auto-completed stale call: {call_record.call_sid}")
                    cleaned_count += 1

            return cleaned_count

        except Exception as e:
            alog.error(f"Error cleaning up stale calls: {str(e)}")
            return 0

    @classmethod
    def from_db(cls, call_sid: str) -> Optional["CallRecord"]:
        """Load a call record from the database."""
        try:
            prisma = prisma_client()
            record = prisma.callrecord.find_unique(
                where={"twilio_call_sid": call_sid}
            )

            if not record:
                return None

            return cls(
                call_sid=record.twilio_call_sid,
                student_id=record.student_id,
                student_workflow_id=record.student_workflow_id,
                status=CallStatus(record.status),
                started_at=record.start_time,
                ended_at=record.end_time,
                duration_seconds=record.duration,
                transcript=record.transcript,
                error_message=record.failure_reason,
                retry_count=record.retry_count or 0,
            )

        except Exception as e:
            alog.error(f"Error loading call record: {str(e)}")
            return None


@dataclass
class CallSession:
    """Model for managing active call sessions."""

    call_sid: str
    stream_sid: Optional[str] = None
    student_id: Optional[str] = None
    workflow_id: Optional[str] = None
    student_workflow_id: Optional[str] = None
    session_id: Optional[str] = None  # For conversation history
    call_record_id: Optional[str] = None  # Database call record ID
    openai_session_active: bool = False
    twilio_connected: bool = False
    created_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    transcript_parts: List[str] = field(default_factory=list)
    _current_assistant_transcript: str = field(default_factory=str)  # For accumulating assistant transcript deltas

    def update_activity(self) -> None:
        """Update the last activity timestamp."""
        self.last_activity = datetime.now()

    def add_transcript_part(self, text: str) -> None:
        """Add a transcript part to the collection with proper formatting."""
        if text and text.strip():
            # Add line break after each transcript entry for proper formatting
            formatted_text = text.strip() + "\n"
            self.transcript_parts.append(formatted_text)
            alog.info(f"Added transcript part: {text.strip()}")

    def is_active(self) -> bool:
        """Check if the session is considered active."""
        return self.openai_session_active and self.twilio_connected

    def get_session_id_for_conversation(self) -> Optional[str]:
        """Get the session ID for conversation history."""
        if self.workflow_id and self.student_id:
            # Get the user_id from the student
            try:
                prisma = prisma_client()
                student = prisma.student.find_unique(
                    where={"id": self.student_id},
                    include={"users": True}
                )

                if student and student.users and len(student.users) > 0:
                    user_id = student.users[0].id
                    return f"{self.workflow_id}-{user_id}"
                else:
                    alog.warning(f"Could not find user for student {self.student_id}")
                    return f"{self.workflow_id}-{self.student_id}"

            except Exception as e:
                alog.error(f"Error getting session ID: {str(e)}")
                return f"{self.workflow_id}-{self.student_id}"

        return None

    def get_full_transcript(self) -> str:
        """Get the full transcript from both transcript_parts and metadata."""
        # First, try the new transcript_parts field
        if self.transcript_parts:
            # Join with empty string since each part already has a newline
            return "".join(self.transcript_parts).strip()

        # Fallback to the old metadata format for backward compatibility
        transcript_entries = self.metadata.get('transcript', [])
        if not transcript_entries:
            return ""

        # Combine all transcript entries into a single string
        transcript_lines = []
        for entry in transcript_entries:
            if isinstance(entry, dict):
                speaker = entry.get('speaker', 'unknown')
                text = entry.get('text', '')
                timestamp = entry.get('timestamp', '')
                if text and speaker != 'assistant_delta':  # Skip delta entries
                    transcript_lines.append(f"[{timestamp}] {speaker}: {text}")
            elif isinstance(entry, str):
                transcript_lines.append(entry)

        return "\n".join(transcript_lines)


# In-memory session storage (consider using Redis for production)
_sessions: Dict[str, CallSession] = {}


def get_session(call_sid: str) -> Optional[CallSession]:
    """Get an active call session."""
    return _sessions.get(call_sid)


def create_session(call_sid: str, **kwargs) -> CallSession:
    """Create a new call session."""
    session = CallSession(call_sid=call_sid, **kwargs)
    _sessions[call_sid] = session
    alog.info(f"Created call session for SID: {call_sid}")
    return session


def update_session(call_sid: str, **kwargs) -> Optional[CallSession]:
    """Update an existing call session."""
    session = _sessions.get(call_sid)
    if session:
        for key, value in kwargs.items():
            if hasattr(session, key):
                setattr(session, key, value)
        session.update_activity()
        alog.info(f"Updated call session for SID: {call_sid}")
    return session


def remove_session(call_sid: str) -> None:
    """Remove a call session."""
    if call_sid in _sessions:
        del _sessions[call_sid]
        alog.info(f"Removed call session for SID: {call_sid}")


def get_all_sessions() -> Dict[str, CallSession]:
    """Get all active sessions."""
    return _sessions.copy()


def cleanup_inactive_sessions(max_age_minutes: int = 60) -> int:
    """Clean up inactive sessions older than max_age_minutes."""
    now = datetime.now()
    to_remove = []

    for call_sid, session in _sessions.items():
        age_minutes = (now - session.last_activity).total_seconds() / 60
        if age_minutes > max_age_minutes:
            to_remove.append(call_sid)

    for call_sid in to_remove:
        remove_session(call_sid)

    if to_remove:
        alog.info(f"Cleaned up {len(to_remove)} inactive sessions")

    return len(to_remove)


def find_session_by_stream_sid(stream_sid: str) -> Optional[CallSession]:
    """Find a session by stream SID."""
    for call_sid, session in _sessions.items():
        if session.stream_sid == stream_sid:
            return session
    return None


# Voice Call Request/Response Models
@dataclass
class VoiceCallRequest:
    """Request model for initiating voice calls."""
    phone_number: str
    student_id: str
    student_workflow_id: str
    workflow_id: Optional[str] = None
    message: Optional[str] = None
    webhook_url: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class VoiceCallResponse:
    """Response model for voice call operations."""
    success: bool
    message: str
    call_sid: Optional[str] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


# Phone number utility functions
def validate_phone_number(phone_number: str) -> bool:
    """
    Validate phone number format.

    Args:
        phone_number: Phone number to validate

    Returns:
        True if valid, False otherwise
    """
    if not phone_number:
        return False

    # Remove all non-digit characters except +
    cleaned = re.sub(r'[^\d+]', '', phone_number)

    # Check for valid formats:
    # +1XXXXXXXXXX (US/Canada with country code)
    # 1XXXXXXXXXX (US/Canada with 1 prefix, 11 digits)
    # XXXXXXXXXX (US/Canada without country code, 10 digits)
    if cleaned.startswith('+1'):
        return len(cleaned) == 12 and cleaned[2:].isdigit()
    elif len(cleaned) == 11 and cleaned.startswith('1'):
        return cleaned[1:].isdigit()
    elif len(cleaned) == 10:
        return cleaned.isdigit()
    else:
        return False


def format_phone_number(phone_number: str) -> str:
    """
    Format phone number to E.164 format (+1XXXXXXXXXX).

    Args:
        phone_number: Phone number to format

    Returns:
        Formatted phone number in E.164 format
    """
    if not phone_number:
        return ""

    # Remove all non-digit characters except +
    cleaned = re.sub(r'[^\d+]', '', phone_number)

    # Handle different input formats
    if cleaned.startswith('+1'):
        # Already in correct format
        return cleaned
    elif cleaned.startswith('1') and len(cleaned) == 11:
        # US/Canada number with country code but no +
        return '+' + cleaned
    elif len(cleaned) == 10:
        # US/Canada number without country code
        return '+1' + cleaned
    else:
        # Return as-is if we can't determine format
        return phone_number
