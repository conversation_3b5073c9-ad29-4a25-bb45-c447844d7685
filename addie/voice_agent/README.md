# Voice Agent

The Voice Agent module provides voice calling functionality for Addie using Twilio Media Streams and OpenAI's Realtime API.

## Features

- **Bidirectional Audio Streaming**: Real-time audio communication between Twilio and OpenAI
- **Call Management**: Track call records, sessions, and status
- **Student Integration**: Automatic student lookup and workflow association
- **WebSocket Handling**: Robust WebSocket connection management
- **Error Handling**: Comprehensive error handling and logging

## Architecture

The voice agent follows a clean modular architecture:

```
addie/voice_agent/
├── __init__.py          # Module exports
├── config.py            # Configuration management
├── models.py            # Data models for calls and sessions
├── websocket_handler.py # WebSocket communication logic
├── routes.py            # FastAPI routes
└── README.md           # This file
```

## Configuration

The voice agent requires the following environment variables:

```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Twilio Configuration  
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Optional Configuration
OPENAI_VOICE_MODEL=gpt-4o-realtime-preview-2024-10-01
OPENAI_VOICE=alloy
OPENAI_VOICE_TEMPERATURE=0.8
VOICE_WEBHOOK_BASE_URL=https://your-domain.com
```

## API Endpoints

### Voice Routes (prefix: `/voice`)

- `GET /voice/` - Get voice agent status
- `POST /voice/make-call` - Initiate an outbound call
- `POST /voice/incoming-call` - Handle incoming call webhook (Twilio)
- `WebSocket /voice/media-stream` - Media stream endpoint for audio
- `POST /voice/call-status` - Update call status (Twilio webhook)
- `GET /voice/sessions` - Get active call sessions

## Usage

### Making an Outbound Call

```python
from addie.voice_agent import get_voice_handler

handler = get_voice_handler()
result = await handler.initiate_call(
    to_number="+**********",
    student_id="student_123",
    workflow_id="workflow_456"
)
```

### Handling Incoming Calls

Incoming calls are automatically handled through the `/voice/incoming-call` webhook endpoint. The system:

1. Validates the Twilio request
2. Looks up the student by phone number
3. Finds an active workflow
4. Creates a call record and session
5. Returns TwiML to connect to the media stream

## WebSocket Communication

The voice agent manages bidirectional WebSocket connections:

- **Twilio → OpenAI**: Audio data from phone calls is forwarded to OpenAI Realtime API
- **OpenAI → Twilio**: AI-generated audio responses are sent back to the caller

## Call Lifecycle

1. **Initiation**: Call is initiated (inbound or outbound)
2. **Connection**: WebSocket connections established
3. **Session Setup**: OpenAI session configured with voice parameters
4. **Audio Streaming**: Bidirectional audio communication
5. **Completion**: Call ends, records updated, sessions cleaned up

## Error Handling

The voice agent includes comprehensive error handling:

- WebSocket connection failures
- OpenAI API errors
- Twilio webhook validation
- Database operation errors
- Session management errors

## Dependencies

- `websockets`: WebSocket client/server library
- `twilio`: Twilio SDK for voice calls
- `fastapi`: Web framework for API endpoints
- `pydantic`: Data validation and serialization

## Development

To test the voice agent locally:

1. Install dependencies: `pip install -r requirements.txt`
2. Set up environment variables
3. Use ngrok for webhook URLs: `ngrok http 8000`
4. Configure Twilio webhooks to point to your ngrok URL
5. Run the FastAPI server: `fastapi run addie/api/app.py`

## Production Considerations

- Use Redis for session storage in production
- Configure proper webhook URLs
- Set up monitoring and alerting
- Implement rate limiting
- Add authentication for API endpoints
- Configure proper logging levels
