"""
Configuration module for the Voice Agent.

This module contains configuration settings for Twilio Media Streams,
OpenAI Realtime API, and voice agent parameters.
"""

import os
import asyncio
from typing import List, Optional
from dataclasses import dataclass, asdict
import alog
from addie.lib import prisma_client
from addie.data_model.student_context import StudentContext
from addie.agent.call_model import gen_system_prompt
from addie.tools.voice_tools import tools
from addie.history import get_history


@dataclass
class VoiceAgentConfig:
    """Configuration class for the Voice Agent."""

    # Required Configuration (no defaults)
    openai_api_key: str
    twilio_account_sid: str
    twilio_auth_token: str
    twilio_phone_number: str

    # OpenAI Realtime API Configuration (with defaults)
    openai_model: str = "gpt-4o-realtime-preview-2024-10-01"
    openai_voice: str = "alloy"
    openai_temperature: float = 0.8

    # Audio Configuration
    input_audio_format: str = "g711_ulaw"
    output_audio_format: str = "g711_ulaw"

    # Voice Agent Behavior
    system_message: str = (
        "You are <PERSON><PERSON>, a helpful and empathetic AI counselor assistant who specializes in "
        "supporting students with their academic and personal growth. You have access to the "
        "student's conversation history and context. Always maintain a warm, supportive tone "
        "and provide thoughtful guidance. Keep responses conversational and appropriate for "
        "voice interaction - avoid overly long responses that would be difficult to follow "
        "in a phone conversation."
    )

    # Session Configuration
    turn_detection_type: str = "server_vad"
    modalities: Optional[List[str]] = None

    # Interruption Configuration (based on OpenAI community solution)
    enable_twilio_buffer_clear: bool = True  # Clear Twilio buffer on interruption (critical for proper interruption)

    # Logging Configuration
    log_event_types: Optional[List[str]] = None
    
    # SystemPrompt storage configuration
    system_prompt_async_mode: bool = True  # Default to async for production

    def __post_init__(self):
        """Set default values for list fields."""
        if self.modalities is None:
            self.modalities = ["text", "audio"]

        if self.log_event_types is None:
            self.log_event_types = [
                'response.content.done',
                'rate_limits.updated',
                'response.done',
                'input_audio_buffer.committed',
                'input_audio_buffer.speech_stopped',
                'input_audio_buffer.speech_started',
                'session.created',
                'session.updated',
                'error'
            ]

    @classmethod
    def from_env(cls) -> "VoiceAgentConfig":
        """Create configuration from environment variables."""
        openai_api_key = os.getenv('OPENAI_API_KEY')
        if not openai_api_key:
            raise ValueError('Missing OPENAI_API_KEY environment variable')

        twilio_account_sid = os.getenv('TWILIO_ACCOUNT_SID')
        if not twilio_account_sid:
            raise ValueError('Missing TWILIO_ACCOUNT_SID environment variable')

        twilio_auth_token = os.getenv('TWILIO_AUTH_TOKEN')
        if not twilio_auth_token:
            raise ValueError('Missing TWILIO_AUTH_TOKEN environment variable')

        twilio_phone_number = os.getenv('TWILIO_PHONE_NUMBER')
        if not twilio_phone_number:
            raise ValueError('Missing TWILIO_PHONE_NUMBER environment variable')

        return cls(
            openai_api_key=openai_api_key,
            openai_model=os.getenv('OPENAI_VOICE_MODEL', "gpt-4o-realtime-preview-2024-10-01"),
            openai_voice=os.getenv('OPENAI_VOICE', "alloy"),
            openai_temperature=float(os.getenv('OPENAI_VOICE_TEMPERATURE', "0.8")),
            twilio_account_sid=twilio_account_sid,
            twilio_auth_token=twilio_auth_token,
            twilio_phone_number=twilio_phone_number,
            input_audio_format=os.getenv('VOICE_INPUT_AUDIO_FORMAT', "g711_ulaw"),
            output_audio_format=os.getenv('VOICE_OUTPUT_AUDIO_FORMAT', "g711_ulaw"),
            system_message=os.getenv('VOICE_SYSTEM_MESSAGE', cls.__dataclass_fields__['system_message'].default),
            turn_detection_type=os.getenv('VOICE_TURN_DETECTION', "server_vad"),
            enable_twilio_buffer_clear=os.getenv('VOICE_TWILIO_BUFFER_CLEAR', "true").lower() == "true",
            system_prompt_async_mode=os.getenv('VOICE_SYSTEM_PROMPT_ASYNC', "true").lower() == "true",
        )

    async def get_openai_session_config(self, student_id, student_workflow_id) -> dict:
        """Get the session configuration for OpenAI Realtime API with comprehensive student and workflow context."""

        # Get base instructions
        instructions = await self.get_voice_system_prompt(student_id, student_workflow_id)

        return {
            "type": "session.update",
            "session": {
                "turn_detection": {
                    "type": self.turn_detection_type,
                    "threshold": 0.8,  # Higher threshold to prevent false speech detection cutoffs
                    "prefix_padding_ms": 800,  # More padding to capture complete speech
                    "silence_duration_ms": 2500  # Longer silence to ensure sentences complete
                },
                "input_audio_format": self.input_audio_format,
                "output_audio_format": self.output_audio_format,
                "input_audio_transcription": {
                    "model": "whisper-1"
                },
                "voice": self.openai_voice,
                "instructions": instructions,
                "modalities": self.modalities,
                "temperature": self.openai_temperature,
                "tools": tools,
                "tool_choice": "auto"
            }
        }

    def _load_comprehensive_context_instructions(self, student_id, student_workflow_id):
        # Add comprehensive student and workflow context
        instructions = self.get_default_system_prompt()
        try:
            # Check if we're in an async context
            try:
                # Try to get the current running loop
                loop = asyncio.get_running_loop()
                # If we're in an async context, we need to use a different approach
                alog.warning("Cannot load StudentContext in sync method from async context. Using fallback context.")

                # Use the simpler context loading as fallback
                student_context_dict = self.get_student_context(student_id)
                workflow_context_dict = self.get_workflow_context(student_workflow_id)

                if student_context_dict or workflow_context_dict:
                    context_instructions = self._build_simple_context_instructions(student_context_dict, workflow_context_dict)
                    instructions += context_instructions
                    alog.info(f"Added simple student and workflow context to voice instructions ({len(context_instructions)} chars)")

            except RuntimeError:
                # No running loop, we can create one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    # Load comprehensive student context
                    student_context = loop.run_until_complete(
                        self._load_comprehensive_student_context(student_id, student_workflow_id)
                    )

                    if student_context:
                        context_instructions = self._build_context_instructions(student_context)
                        instructions += context_instructions
                        alog.info(f"Added comprehensive student context to voice instructions ({len(context_instructions)} chars)")
                    else:
                        alog.warning("No student context loaded for voice session")
                finally:
                    loop.close()

        except Exception as e:
            alog.error(f"Error loading student context for voice session: {str(e)}")
            # Continue with base instructions if context loading fails

        return instructions

    def get_openai_headers(self) -> dict:
        """Get headers for OpenAI Realtime API connection."""
        return {
            "Authorization": f"Bearer {self.openai_api_key}",
            "OpenAI-Beta": "realtime=v1"
        }

    def get_default_system_prompt(self) -> str:
        """Get the default system prompt for the voice agent."""
        prisma = prisma_client()
        prompt = prisma.prompt.find_first(
            where={
                'id': "cmbznim5l0002nmnje4z9fbf5"
            }
        )

        if prompt:
            return prompt.content

        return self.system_message

    def get_student_context(self, student_id) -> dict:
        """Get the student context for the voice agent."""
        prisma = prisma_client()
        student = prisma.student.find_unique(
            where={"id": student_id},
            include={"users": True, "skills": True, "personal_experiences": True}
        )

        if not student:
            return {}

        user = student.users[0] if student.users else None

        # Extract interests from skills
        interests = []
        if student.skills:
            for skill in student.skills:
                if skill.category and skill.category.lower() == 'interest':
                    interests.append(skill.name)

        # Extract goals from personal experiences
        goals = []
        if student.personal_experiences:
            for experience in student.personal_experiences:
                if experience.title and 'goal' in experience.title.lower():
                    goals.append(experience.description or experience.title)

        context = {
            "name": f"{user.first_name} {user.last_name}" if user and user.first_name and user.last_name else None,
            "email": user.email if user else None,
            "grade": getattr(student, 'grade', None),
            "student_id": student.student_id,
            "interests": interests,
            "goals": goals
        }

        return context

    def get_workflow_context(self, student_workflow_id: str) -> dict:
        """Get the workflow context for the voice agent."""
        if not student_workflow_id:
            return {}

        try:
            prisma = prisma_client()
            student_workflow = prisma.studentworkflow.find_unique(
                where={"id": student_workflow_id},
                include={
                    "workflow": True,
                    "steps": {
                        "include": {
                            "step": True
                        }
                    }
                }
            )

            if not student_workflow:
                alog.warning(f"No student workflow found for ID: {student_workflow_id}")
                return {}

            workflow = student_workflow.workflow
            if not workflow:
                alog.warning(f"No workflow found for student workflow ID: {student_workflow_id}")
                return {}

            # Get current step information
            current_step = None
            total_steps = len(student_workflow.steps) if student_workflow.steps else 0
            completed_steps = 0

            if student_workflow.steps:
                for step in student_workflow.steps:
                    if step.completed:
                        completed_steps += 1
                    elif current_step is None:  # First incomplete step
                        current_step = step

            context = {
                "workflow_name": workflow.name,
                "workflow_description": getattr(workflow, 'description', None),
                "workflow_type": getattr(workflow, 'workflow_type', None),
                "status": student_workflow.status,
                "mode": getattr(student_workflow, 'mode', 'web'),
                "total_steps": total_steps,
                "completed_steps": completed_steps,
                "progress_percentage": (completed_steps / total_steps * 100) if total_steps > 0 else 0,
                "current_step": {
                    "name": current_step.step.name if current_step and current_step.step else None,
                    "data": current_step.data if current_step else None
                } if current_step else None
            }

            alog.info(f"Loaded workflow context: {workflow.name} ({completed_steps}/{total_steps} steps completed)")
            return context

        except Exception as e:
            alog.error(f"Error loading workflow context: {str(e)}")
            return {}

    async def _load_comprehensive_student_context(self, student_id: str, student_workflow_id: str) -> Optional[StudentContext]:
        """Load comprehensive student context using the StudentContext class"""
        try:
            if not student_id:
                alog.warning("No student ID provided for comprehensive context loading")
                return None

            alog.info(f"Loading comprehensive student context for student ID: {student_id}")

            # First, we need to get the user_id from the student record
            prisma = prisma_client()
            if not prisma:
                alog.warning("Prisma client not available")
                return None

            if not prisma.is_connected():
                prisma.connect()

            # Get the student record to find the associated user
            student = prisma.student.find_unique(
                where={"id": student_id},
                include={"users": True}
            )

            if not student or not student.users:
                alog.warning(f"No student or user found for student ID: {student_id}")
                return None

            user_id = student.users[0].id
            alog.info(f"Found user ID: {user_id} for student ID: {student_id}")

            # Create StudentContext instance with the correct user_id and student id
            student_context = StudentContext(
                user_id=user_id,
                id=student_id,  # Set the student record ID
                _student_workflow_id=student_workflow_id
            )

            # Initialize the context (this loads all the data)
            await student_context.async_init()

            alog.info(f"Successfully loaded comprehensive student context")
            alog.info(f"Student name: {student_context.user.get('first_name', 'N/A') if student_context.user else 'N/A'} {student_context.user.get('last_name', 'N/A') if student_context.user else 'N/A'}")
            alog.info(f"Grade: {student_context.grade}")
            alog.info(f"Current workflow: {student_context.current_workflow_name}")

            return student_context

        except Exception as e:
            alog.error(f"Error loading comprehensive student context: {str(e)}")
            import traceback
            alog.error(f"Traceback: {traceback.format_exc()}")
            return None

    def _build_context_instructions(self, student_context: StudentContext) -> str:
        """Build context instructions from StudentContext data"""
        try:
            context_instructions = "\n\nStudent Context:\n"

            # Basic student information
            if student_context.user:
                name = f"{student_context.user.get('first_name', '')} {student_context.user.get('last_name', '')}".strip()
                if name:
                    context_instructions += f"- Student name: {name}\n"

            if student_context.grade:
                context_instructions += f"- Grade level: {student_context.grade}\n"

            # Academic achievements
            if student_context.academic_achievements:
                achievements = [achievement.get('title', achievement.get('name', 'Achievement'))
                             for achievement in student_context.academic_achievements[:3]]  # Limit to top 3
                context_instructions += f"- Academic achievements: {', '.join(achievements)}\n"

            # Growth opportunities
            if student_context.growth_opportunities:
                opportunities = [opp.get('title', opp.get('name', 'Growth area'))
                               for opp in student_context.growth_opportunities[:3]]  # Limit to top 3
                context_instructions += f"- Growth areas: {', '.join(opportunities)}\n"

            # Current workflow information
            context_instructions += "\nWorkflow Context:\n"
            if student_context.current_workflow_name:
                context_instructions += f"- Current workflow: {student_context.current_workflow_name}\n"

            if student_context.current_question:
                question_data = student_context.current_question.get('data', {})
                if question_data.get('prompt'):
                    context_instructions += f"- Current focus: {question_data['prompt'][:100]}...\n"

            # Teacher comments summary
            if student_context.teacher_comments_summary:
                context_instructions += f"- Teacher feedback available: Yes\n"

            # Onboarding summary
            if student_context.onboarding_summary:
                context_instructions += f"- Previous conversations: Available\n"

            alog.info(f"Built context instructions: {len(context_instructions)} characters")
            return context_instructions

        except Exception as e:
            alog.error(f"Error building context instructions: {str(e)}")
            return "\n\nStudent Context: Error loading context\n"

    def _build_simple_context_instructions(self, student_context_dict: dict, workflow_context_dict: Optional[dict] = None) -> str:
        """Build simple context instructions from basic student and workflow context dicts"""
        try:
            context_instructions = ""

            # Student Context
            if student_context_dict:
                context_instructions += "\n\nStudent Context:\n"

                # Basic student information
                if student_context_dict.get('name'):
                    context_instructions += f"- Student name: {student_context_dict['name']}\n"

                if student_context_dict.get('grade'):
                    context_instructions += f"- Grade level: {student_context_dict['grade']}\n"

                if student_context_dict.get('interests'):
                    context_instructions += f"- Interests: {', '.join(student_context_dict['interests'])}\n"

                if student_context_dict.get('goals'):
                    context_instructions += f"- Goals: {', '.join(student_context_dict['goals'])}\n"

            # Workflow Context
            if workflow_context_dict:
                context_instructions += "\nWorkflow Context:\n"

                if workflow_context_dict.get('workflow_name'):
                    context_instructions += f"- Current workflow: {workflow_context_dict['workflow_name']}\n"

                if workflow_context_dict.get('workflow_description'):
                    context_instructions += f"- Workflow focus: {workflow_context_dict['workflow_description']}\n"

                if workflow_context_dict.get('progress_percentage') is not None:
                    progress = workflow_context_dict['progress_percentage']
                    context_instructions += f"- Progress: {progress:.0f}% complete ({workflow_context_dict.get('completed_steps', 0)}/{workflow_context_dict.get('total_steps', 0)} steps)\n"

                if workflow_context_dict.get('current_step') and workflow_context_dict['current_step'].get('name'):
                    context_instructions += f"- Current step: {workflow_context_dict['current_step']['name']}\n"

                if workflow_context_dict.get('mode'):
                    context_instructions += f"- Interaction mode: {workflow_context_dict['mode']}\n"

            alog.info(f"Built simple context instructions: {len(context_instructions)} characters")
            return context_instructions

        except Exception as e:
            alog.error(f"Error building simple context instructions: {str(e)}")
            return "\n\nStudent Context: Error loading context\n"

    async def get_voice_system_prompt(self, student_id: Optional[str], student_workflow_id: Optional[str]) -> str:
        """
        Generate system prompt for voice agent using the same approach as call_model.py.

        This method reuses the exact same logic as gen_system_prompt from addie.agent.call_model
        to ensure consistency across different agent types.

        Args:
            student_id: Student ID
            student_workflow_id: Student workflow ID

        Returns:
            Complete system prompt string
        """
        try:
            if not student_id:
                alog.warning("No student_id provided for voice system prompt")
                return self.get_default_system_prompt()

            # First check if addieconfig exists (required by gen_system_prompt)
            prisma = prisma_client()
            addie_config = prisma.addieconfig.find_first(
                include=dict(prompt=True, system_prompt=True)
            )

            if not addie_config or not addie_config.prompt or not addie_config.system_prompt:
                alog.warning("addieconfig not found or incomplete, using fallback system prompt")
                return self.get_default_system_prompt()

            # Get the user_id from the student record first
            student = prisma.student.find_unique(
                where={"id": student_id},
                include={"users": True}
            )

            if not student or not student.users:
                alog.warning(f"No student or user found for student ID: {student_id}")
                return self.get_default_system_prompt()

            user_id = student.users[0].id
            alog.info(f"Found user ID: {user_id} for student ID: {student_id}")

            # Load conversation history using the same method as AgentState.init()
            # Construct session_id the same way as other agents: workflow_id-user_id
            workflow_id = None
            if student_workflow_id:
                student_workflow = prisma.studentworkflow.find_unique(
                    where={"id": student_workflow_id},
                    include={"workflow": True}
                )
                if student_workflow and student_workflow.workflow:
                    workflow_id = student_workflow.workflow.id

            if workflow_id:
                session_id = f"{workflow_id}-{user_id}"
                messages = get_history(session_id).get_messages()
                conversation_messages = self._simplify_messages(messages)
            else:
                alog.warning("No workflow_id found, starting with empty conversation history")
                conversation_messages = []

            # Ensure StudentWorkflowStep records exist before creating StudentContext
            if student_workflow_id:
                self._ensure_student_workflow_steps(student_id, student_workflow_id)

            # Create StudentContext instance exactly like AgentState.init() does
            student_context = await StudentContext(
                user_id=user_id,  # Use the actual user_id from the User table
                id=student_id,   # This is the student record ID
                alternate_user_id=None,
                messages=conversation_messages,  # Include conversation history
                mode="voice",  # Set mode to 'voice' for voice calls
                _student_workflow_id=student_workflow_id
            ).async_init()

            # Convert to dict exactly like AgentState.init() does
            student_context_dict = asdict(student_context)

            # Use the exact same gen_system_prompt function from call_model.py
            system_prompt = gen_system_prompt(student_context_dict)

            alog.info(f"Generated voice system prompt using gen_system_prompt ({len(system_prompt)} chars)")
            return system_prompt

        except Exception as e:
            alog.error(f"Error generating voice system prompt: {str(e)}")
            # Fallback to enhanced prompt with available context
            return self.get_default_system_prompt()

    def _simplify_messages(self, messages: List) -> List[dict]:
        """
        Transform LangChain messages to simplified format with type, content, and timestamp.
        
        Args:
            messages: List of LangChain message objects
            
        Returns:
            List of simplified message dictionaries
        """
        simplified_messages = []
        
        for msg in messages:
            try:
                msg_dict = msg.dict() if hasattr(msg, 'dict') else msg
                
                # Extract type and content
                msg_type = msg_dict.get('type', '')
                content = msg_dict.get('content', '')
                
                # Only include human and ai messages
                if msg_type in ['human', 'ai'] and content:
                    simplified_msg = {
                        'type': msg_type,
                        'content': content
                    }
                    
                    # Try to extract timestamp from various possible locations
                    timestamp = None
                    if 'timestamp' in msg_dict:
                        timestamp = msg_dict['timestamp']
                    elif 'response_metadata' in msg_dict and isinstance(msg_dict['response_metadata'], dict):
                        timestamp = msg_dict['response_metadata'].get('timestamp')
                    elif 'additional_kwargs' in msg_dict and isinstance(msg_dict['additional_kwargs'], dict):
                        timestamp = msg_dict['additional_kwargs'].get('timestamp')
                    
                    if timestamp:
                        simplified_msg['timestamp'] = timestamp
                    
                    simplified_messages.append(simplified_msg)
                    
            except Exception as e:
                alog.warning(f"Error simplifying message: {e}")
                continue
        
        return simplified_messages

    def _load_conversation_history(self, student_workflow_id: str, user_id: str) -> List[dict]:
        """
        Load conversation history for a student workflow and user.
        Returns a list of message dictionaries in the format expected by voice agent tests.
        
        Args:
            student_workflow_id: Student workflow ID
            user_id: User ID
            
        Returns:
            List of message dictionaries with 'type' and 'content' keys
        """
        try:
            prisma = prisma_client()
            
            # Get the workflow_id from the student_workflow_id
            student_workflow = prisma.studentworkflow.find_unique(
                where={"id": student_workflow_id},
                include={"workflow": True}
            )
            
            if not student_workflow or not student_workflow.workflow:
                alog.warning(f"No workflow found for student workflow ID: {student_workflow_id}")
                return []
            
            workflow_id = student_workflow.workflow.id
            
            # Construct session_id the same way as other agents
            session_id = f"{workflow_id}-{user_id}"
            
            # Load messages from the database
            messages = prisma.messages.find_many(
                where={"session_id": session_id},
                order_by={"created_at": "asc"}
            )
            
            conversation_messages = []
            
            for msg in messages:
                try:
                    message_data = msg.message.get('data', {}) if msg.message else {}
                    message_type = message_data.get('type', '')
                    message_content = message_data.get('content', '')
                    
                    # Only include human and ai messages (filter out tool, function, etc.)
                    if message_type in ['human', 'ai'] and message_content:
                        conversation_messages.append({
                            'type': message_type,
                            'content': message_content
                        })
                
                except Exception as e:
                    alog.warning(f"Error processing message {msg.id}: {str(e)}")
                    continue
            
            alog.info(f"Loaded {len(conversation_messages)} conversation messages for session {session_id}")
            return conversation_messages
            
        except Exception as e:
            alog.error(f"Error loading conversation history: {str(e)}")
            return []

    def _ensure_student_workflow_steps(self, student_id: str, student_workflow_id: str) -> None:
        """
        Ensure that StudentWorkflowStep records exist for the given student workflow.
        This is critical for voice calls because StudentContext expects these records to exist.
        """
        try:
            prisma = prisma_client()

            # Get the student workflow with its steps
            student_workflow = prisma.studentworkflow.find_unique(
                where={"id": student_workflow_id},
                include={
                    "steps": True,
                    "workflow": {"include": {"steps": True}}
                }
            )

            if not student_workflow:
                alog.error(f"StudentWorkflow not found: {student_workflow_id}")
                return

            # Check if StudentWorkflowStep records already exist
            if len(student_workflow.steps) > 0:
                alog.info(f"StudentWorkflowStep records already exist for workflow {student_workflow_id}")
                return

            # Create StudentWorkflowStep records from the original workflow steps
            alog.info(f"Creating StudentWorkflowStep records for workflow {student_workflow_id}")
            workflow_steps = student_workflow.workflow.steps

            for step in workflow_steps:
                prisma.studentworkflowstep.create({
                    "data": {
                        "student_id": student_id,
                        "step_id": step.id,
                        "student_workflow_id": student_workflow_id,
                        "completed": False,
                        "data": step.data or {}
                    }
                })

            alog.info(f"Created {len(workflow_steps)} StudentWorkflowStep records for workflow {student_workflow_id}")

        except Exception as e:
            alog.error(f"Error ensuring student workflow steps: {str(e)}")
            # Don't raise the exception - let the voice call continue with fallback prompt

# Global configuration instance
_config: Optional[VoiceAgentConfig] = None


def get_voice_config() -> VoiceAgentConfig:
    """Get the global voice agent configuration."""
    global _config
    if _config is None:
        _config = VoiceAgentConfig.from_env()
    return _config


def set_voice_config(config: VoiceAgentConfig) -> None:
    """Set the global voice agent configuration."""
    global _config
    _config = config
