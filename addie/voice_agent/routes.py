"""
FastAPI routes for voice agent functionality.

This module provides HTTP and WebSocket endpoints for handling voice calls,
including incoming call webhooks, media stream connections, and call management.
"""

import alog
from datetime import datetime
from fastapi import APIRouter, Request, WebSocket, HTTPException
from fastapi.responses import Response
from pydantic import BaseModel
from typing import Dict, Any
from twilio.twiml.voice_response import VoiceResponse, Connect
from twilio.request_validator import RequestValidator

from .config import get_voice_config
from .websocket_handler import get_voice_handler, track_voice_session_event
from .models import (
    CallRecord, CallStatus,
    create_session, get_session, remove_session
)
from addie.lib import prisma_client
from addie import settings


# Create router
router = APIRouter(prefix="/voice", tags=["voice"])


class MakeCallRequest(BaseModel):
    """Request model for making outbound calls."""
    student_id: str
    student_workflow_id: str





def validate_twilio_request(request: Request, form_data: dict) -> bool:
    """
    Validate that the request is coming from Twilio.

    Args:
        request: FastAPI request object
        form_data: Form data from the request

    Returns:
        True if the request is valid, False otherwise
    """
    # Skip validation if Twilio credentials are not configured
    if not all([settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN]):
        alog.warning("Twilio credentials not configured. Skipping request validation.")
        return True

    # For testing, check for a special header to skip validation
    if request.headers.get("x-skip-twilio-validation") == "true":
        alog.warning("Skipping Twilio validation due to x-skip-twilio-validation header.")
        return True

    # For development/testing, you can disable validation based on environment
    if hasattr(settings, 'ENV') and settings.ENV in ["development", "test"]:
        alog.warning(f"{settings.ENV} environment detected. Skipping Twilio signature validation.")
        return True

    try:
        # Get the Twilio signature from the request headers
        twilio_signature = request.headers.get("x-twilio-signature", "")

        if not twilio_signature:
            alog.warning("No Twilio signature found in request headers")
            return False

        # Get the full URL of the request, accounting for proxies
        # When behind a proxy, we need to reconstruct the URL using the X-Forwarded headers
        if request.headers.get("x-forwarded-host") and request.headers.get("x-forwarded-proto"):
            # Use the forwarded host and protocol
            forwarded_host = request.headers.get("x-forwarded-host")
            forwarded_proto = request.headers.get("x-forwarded-proto")
            forwarded_uri = request.headers.get("x-original-uri", request.url.path)
            url = f"{forwarded_proto}://{forwarded_host}{forwarded_uri}"
            alog.info(f"Using forwarded URL for Twilio validation: {url}")
        else:
            # Fall back to the request URL
            url = str(request.url)
            alog.info(f"Using direct URL for Twilio validation: {url}")

        # Create a validator with our auth token
        validator = RequestValidator(settings.TWILIO_AUTH_TOKEN)

        # Validate the request
        return validator.validate(url, form_data, twilio_signature)

    except Exception as e:
        alog.error(f"Error validating Twilio request: {str(e)}")
        return False


@router.get("/", response_model=Dict[str, str])
async def voice_status():
    """Get voice agent status."""
    return {"message": "Twilio Voice Agent is running!"}


@router.post("/make-call", response_model=Dict[str, Any])
async def make_call(request: MakeCallRequest):
    """
    Initiate an outbound voice call.

    Args:
        request: Call request parameters (student_id and student_workflow_id)

    Returns:
        Dictionary with call information
    """
    try:
        # Retrieve the student and their associated user's phone number from the database
        prisma = prisma_client()
        student = prisma.student.find_unique(
            where={"id": request.student_id},
            include={"users": True}
        )

        if not student:
            raise HTTPException(status_code=404, detail=f"Student not found: {request.student_id}")

        if not student.users or len(student.users) == 0:
            raise HTTPException(status_code=400, detail=f"Student {request.student_id} has no associated user")

        # Get the first associated user (there should typically be only one)
        user = student.users[0]
        if not user.phone_number:
            raise HTTPException(status_code=400, detail=f"Student {request.student_id} has no phone number")

        alog.info(f"Making call to student {request.student_id} at {user.phone_number}")

        handler = get_voice_handler()
        result = await handler.initiate_call(
            to_number=user.phone_number,
            student_id=request.student_id,
            student_workflow_id=request.student_workflow_id
        )

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=500, detail=result["error"])

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        alog.error(f"Error making call: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.api_route("/incoming-call", methods=["GET", "POST"])
async def handle_incoming_call(request: Request):
    """
    Handle incoming call and return TwiML response to connect to Media Stream.

    Args:
        request: FastAPI request object

    Returns:
        TwiML response for Twilio
    """
    try:
        # Parse form data if it's a POST request
        if request.method == "POST":
            form_data = await request.form()

            # Validate the request is coming from Twilio
            if not validate_twilio_request(request, dict(form_data)):
                alog.error("Invalid Twilio request signature")
                return Response(
                    content="<?xml version='1.0' encoding='UTF-8'?><Response><Say>Unauthorized request</Say></Response>",
                    media_type="application/xml"
                )

            # Extract call information
            call_sid = str(form_data.get("CallSid", ""))
            from_number = str(form_data.get("From", ""))
            to_number = str(form_data.get("To", ""))
            answered_by = str(form_data.get("AnsweredBy", ""))

            alog.info(f"Incoming call from {from_number} to {to_number}, SID: {call_sid}")

            # Try to find student by phone number
            student_id = None
            workflow_id = None
            student_workflow_id = None

            try:
                prisma = prisma_client()

                # Check if this is an outbound call by looking for existing call record
                existing_call = CallRecord.from_db(call_sid)

                if existing_call:
                    if answered_by in ['machine_start', 'machine_end', 'fax']:
                        existing_call.mark_as_failed("Call answered by machine.")
                        # Detected voicemail or machine — hang up immediately
                        return Response(
                            content="<?xml version='1.0' encoding='UTF-8'?><Response><Hangup/></Response>",
                            media_type="application/xml"
                        )
                    # This is an outbound call - use existing call record data
                    student_id = existing_call.student_id
                    student_workflow_id = existing_call.student_workflow_id
                    # Get workflow_id from student_workflow if needed
                    if student_workflow_id:
                        sw = prisma.studentworkflow.find_unique(
                            where={"id": student_workflow_id},  # type: ignore
                            include={"workflow": True}
                        )
                        if sw and sw.workflow:
                            workflow_id = sw.workflow.id
                    alog.info(f"Found existing call record for outbound call: student_id={student_id}, student_workflow_id={student_workflow_id}")
                else:
                    # This is an inbound call - look up student by from_number using User->Student relationship
                    config = get_voice_config()
                    if from_number != config.twilio_phone_number:
                        alog.info(f"Processing inbound call from student: {from_number}")

                        # Find user by phone number (with normalization to handle invisible characters)
                        user = prisma.user.find_first(
                            where={"phone_number": {"contains": from_number.strip()}}  # type: ignore
                        )

                        if user:
                            # Find student associated with this user
                            user_with_students = prisma.user.find_unique(
                                where={"id": user.id},
                                include={"students": True}
                            )

                            if user_with_students and user_with_students.students and len(user_with_students.students) > 0:
                                student = user_with_students.students[0]
                                student_id = student.id
                                alog.info(f"Found student {student_id} for user {user.id} with phone number {from_number}")

                                # Find an active voice workflow for this student
                                student_workflow = prisma.studentworkflow.find_first(
                                    where={
                                        "student_id": student_id,
                                        "status": "IN_PROGRESS",  # type: ignore
                                        "mode": "voice",  # type: ignore
                                    },
                                    include={"workflow": True}
                                )

                                if student_workflow:
                                    workflow_id = student_workflow.workflow_id
                                    student_workflow_id = student_workflow.id
                                    alog.info(f"Found active voice workflow {workflow_id} (SW ID: {student_workflow_id}) for student {student_id}")
                                else:
                                    alog.warning(f"No active voice workflow found for student {student_id}")
                                    # Return error response and hangup
                                    response = VoiceResponse()
                                    response.pause(1)
                                    response.say("You don't have any assigned conversations. you <NAME_EMAIL>")
                                    response.hangup()
                                    return Response(content=str(response), media_type="application/xml")
                            else:
                                alog.warning(f"No students found for user {user.id}")
                                # Return error response and hangup
                                response = VoiceResponse()
                                response.pause(1)
                                response.say("You don't have any assigned conversations. you <NAME_EMAIL>")
                                response.hangup()
                                return Response(content=str(response), media_type="application/xml")
                        else:
                            alog.warning(f"No user found for phone number {from_number}")
                            # Return error response and hangup
                            response = VoiceResponse()
                            response.pause(1)
                            response.say("You don't have any assigned conversations. you <NAME_EMAIL>")
                            response.hangup()
                            return Response(content=str(response), media_type="application/xml")
                    else:
                        alog.warning(f"Unexpected call configuration: from={from_number}, to={to_number}")

            except Exception as e:
                alog.error(f"Error looking up student: {str(e)}")

            # Only proceed if we have the required student_workflow_id
            if student_id and student_workflow_id:
                # For inbound calls, create a new call record (outbound calls already have one)
                if not existing_call:
                    call_record = CallRecord(
                        call_sid=call_sid,
                        student_id=student_id,
                        student_workflow_id=student_workflow_id,
                        status=CallStatus.IN_PROGRESS,
                        started_at=datetime.now()
                    )
                    call_record.save_to_db()
                    alog.info(f"Created new call record for inbound call: {call_sid}")

                # Create session
                create_session(
                    call_sid,
                    student_id=student_id,
                    workflow_id=workflow_id,
                    student_workflow_id=student_workflow_id
                )

                # Create TwiML response for successful calls
                response = VoiceResponse()

                # Welcome message
                response.say(
                    "Please wait while we connect your call to Addie, your AI counselor assistant."
                )
                response.pause(length=1)
                response.say("Okay, you will hear Addie after a short pause!")

                # Connect to media stream
                host = request.url.hostname
                connect = Connect()
                # Use simpler URL without query params - call SID will be extracted from start event
                connect.stream(url=f'wss://{host}/voice/media-stream')
                response.append(connect)

                return Response(content=str(response), media_type="application/xml")
            else:
                alog.warning(f"Cannot proceed with call - missing required data: student_id={student_id}, student_workflow_id={student_workflow_id}")
                # Return error response
                response = VoiceResponse()
                response.say("Sorry, we're experiencing technical difficulties. Please try again later.")
                response.hangup()
                return Response(content=str(response), media_type="application/xml")

    except Exception as e:
        alog.error(f"Error handling incoming call: {str(e)}")
        # Return a simple error response
        response = VoiceResponse()
        response.say("Sorry, we're experiencing technical difficulties. Please try again later.")
        return Response(content=str(response), media_type="application/xml")


@router.websocket("/media-stream")
async def media_stream_websocket(websocket: WebSocket):
    """
    WebSocket endpoint for Twilio Media Streams.

    Args:
        websocket: WebSocket connection from Twilio
    """
    await websocket.accept()

    # Extract call SID from query parameters first
    call_sid = websocket.query_params.get("callSid")

    alog.info(f"WebSocket connection query params: {dict(websocket.query_params)}")
    alog.info(f"WebSocket connection headers: {dict(websocket.headers)}")
    alog.info(f"WebSocket connection path: {websocket.url.path}")

    if not call_sid:
        # Check for stream_sid in the connection parameters
        stream_sid = websocket.query_params.get("streamSid")
        if stream_sid:
            # Try to find the call_sid from the stream_sid
            from .models import find_session_by_stream_sid
            session = find_session_by_stream_sid(stream_sid)
            if session:
                call_sid = session.call_sid
                alog.info(f"Found call SID {call_sid} from stream SID {stream_sid}")

    if not call_sid:
        # If no call SID in query params, try to extract from Twilio Media Stream events
        alog.info("No call SID in query params, waiting for Twilio Media Stream events...")
        try:
            import json
            # Twilio sends "connected" event first, then "start" event
            # We need to wait for the "start" event to get the call SID
            while not call_sid:
                message = await websocket.receive_text()
                event_data = json.loads(message)
                event_type = event_data.get("event")

                alog.info(f"Received Twilio event: {event_type}")

                if event_type == "connected":
                    # Just acknowledge the connected event and continue waiting
                    alog.info("Twilio Media Stream connected, waiting for start event...")
                    continue
                elif event_type == "start":
                    start_data = event_data.get("start", {})
                    call_sid = start_data.get("callSid")
                    stream_sid = start_data.get("streamSid")
                    if call_sid:
                        alog.info(f"Extracted call SID from start event: {call_sid}")
                        if stream_sid:
                            alog.info(f"Extracted stream SID from start event: {stream_sid}")
                            # Update the session with the stream SID immediately
                            from .models import update_session
                            update_session(call_sid, stream_sid=stream_sid)
                        break
                    else:
                        alog.error("No call SID found in start event")
                        await websocket.close(code=1000, reason="No call SID in start event")
                        return
                else:
                    alog.warning(f"Unexpected event type before start: {event_type}")
                    # Continue waiting for start event
                    continue
        except Exception as e:
            alog.error(f"Error extracting call SID from events: {str(e)}")
            await websocket.close(code=1000, reason="Error processing events")
            return

    handler = get_voice_handler()
    # Note: We may have already consumed the "connected" and "start" events
    await handler.handle_media_stream(websocket, call_sid)


@router.get("/active-call-status", response_model=Dict[str, Any])
async def get_call_status(student_id: str, student_workflow_id: str):
    """
    Get call status for a student workflow.

    Args:
        student_id: Student ID
        student_workflow_id: Student workflow ID

    Returns:
        Call status information or 404 if no active call
    """
    try:
        prisma = prisma_client()

        # Find the most recent call record for this student workflow
        call_record = prisma.callrecord.find_first(  # type: ignore
            where={
                "student_id": student_id,
                "student_workflow_id": student_workflow_id,
                "status": {"in": [CallStatus.INITIATED.value, CallStatus.IN_PROGRESS.value]}  # Only active calls
            },
            order={"created_at": "desc"}
        )

        if not call_record:
            raise HTTPException(status_code=404, detail="No active call found")

        return {
            "call_sid": call_record.twilio_call_sid,
            "status": call_record.status,
            "student_id": call_record.student_id,
            "student_workflow_id": call_record.student_workflow_id,
            "start_time": call_record.start_time.isoformat() if call_record.start_time else None,
            "retry_count": call_record.retry_count or 0
        }

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        alog.error(f"Error getting call status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/call-status", response_model=Dict[str, str])
async def update_call_status(request: Request):
    """
    Update call status (webhook endpoint for Twilio).

    Args:
        request: FastAPI request object containing form data from Twilio

    Returns:
        Status confirmation
    """
    try:
        # Parse form data from Twilio webhook
        form_data = await request.form()

        # Validate the request is coming from Twilio
        if not validate_twilio_request(request, dict(form_data)):
            alog.error("Invalid Twilio request signature for call status update")
            raise HTTPException(status_code=403, detail="Invalid request signature")

        # Extract required fields from Twilio webhook
        call_sid = str(form_data.get('CallSid', ''))
        call_status = str(form_data.get('CallStatus', ''))

        if not call_sid or not call_status:
            alog.error(f"Missing required fields in call status webhook: CallSid={call_sid}, CallStatus={call_status}")
            raise HTTPException(status_code=400, detail="Missing required fields: CallSid and CallStatus")

        alog.info(f"Received call status update: CallSid={call_sid}, CallStatus={call_status}")

        call_record = CallRecord.from_db(call_sid)
        if call_record:
            # Map Twilio status to our status
            status_mapping = {
                "initiated": CallStatus.INITIATED,
                "ringing": CallStatus.INITIATED,
                "answered": CallStatus.IN_PROGRESS,
                "in-progress": CallStatus.IN_PROGRESS,
                "completed": CallStatus.COMPLETED,
                "busy": CallStatus.FAILED,
                "failed": CallStatus.FAILED,
                "no-answer": CallStatus.FAILED,
                "cancelled": CallStatus.FAILED,
            }

            new_status = status_mapping.get(call_status.lower(), CallStatus.FAILED)
            call_record.status = new_status

            if new_status in [CallStatus.COMPLETED, CallStatus.FAILED]:
                # Save transcript from session if available before marking as complete/failed
                session = get_session(call_sid)
                transcript = None
                if session and session.transcript_parts:
                    transcript = session.get_full_transcript()
                    alog.info(f"Collected transcript for call {call_sid}: {len(transcript)} characters")

                # Use the existing methods that handle timezone issues properly
                if new_status == CallStatus.COMPLETED:
                    call_record.mark_as_completed(transcript)
                    
                    # Track session ended event for webhook completion
                    if session:
                        track_voice_session_event(session, 'session_ended', {
                            'call_sid': call_sid,
                            'ended_at': datetime.now().isoformat(),
                            'completion_status': 'completed',
                            'source': 'twilio_webhook',
                            'transcript_length': len(transcript) if transcript else 0,
                            'total_messages': len(session.transcript_parts) if session.transcript_parts else 0
                        })
                        
                elif new_status == CallStatus.FAILED:
                    # Get failure reason from Twilio webhook data
                    failure_reason = f"Call {call_status}"
                    call_record.mark_as_failed(failure_reason)

                    # Track session ended event for webhook failure
                    if session:
                        track_voice_session_event(session, 'session_ended', {
                            'call_sid': call_sid,
                            'ended_at': datetime.now().isoformat(),
                            'completion_status': 'failed',
                            'source': 'twilio_webhook',
                            'failure_reason': failure_reason,
                            'transcript_length': len(transcript) if transcript else 0,
                            'total_messages': len(session.transcript_parts) if session.transcript_parts else 0
                        })

                    # Log failure details
                    alog.warning(f"Call failed for {call_sid}: {failure_reason} (retry #{call_record.retry_count})")

                # Override duration if Twilio provides it (more accurate than our calculation)
                call_duration_str = str(form_data.get('CallDuration', ''))
                if call_duration_str and call_duration_str.isdigit():
                    call_record.duration_seconds = int(call_duration_str)
                    alog.info(f"Updated call duration from Twilio: {call_duration_str} seconds")
                    call_record.save_to_db()  # Save the updated duration

                # Clean up session
                remove_session(call_sid)
            else:
                # For non-final statuses, just update the status
                call_record.status = new_status
                call_record.save_to_db()

            alog.info(f"Updated call status for {call_sid}: {new_status}")
        else:
            alog.warning(f"No call record found for CallSid: {call_sid}")

        return {"message": "Call status updated"}

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        alog.error(f"Error updating call status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cleanup-stale-calls", response_model=Dict[str, Any])
async def cleanup_stale_calls(max_duration_minutes: int = 60):
    """
    Clean up calls that have been stuck in IN_PROGRESS status.

    Args:
        max_duration_minutes: Maximum duration before marking as completed

    Returns:
        Cleanup results
    """
    try:
        cleaned_count = CallRecord.cleanup_stale_calls(max_duration_minutes)
        return {
            "message": f"Cleaned up {cleaned_count} stale calls",
            "cleaned_count": cleaned_count,
            "max_duration_minutes": max_duration_minutes
        }

    except Exception as e:
        alog.error(f"Error cleaning up stale calls: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sessions", response_model=Dict[str, Any])
async def get_active_sessions():
    """
    Get information about active call sessions.

    Returns:
        Dictionary with active session information
    """
    try:
        from .models import get_all_sessions
        sessions = get_all_sessions()

        session_info = {}
        for call_sid, session in sessions.items():
            session_info[call_sid] = {
                "call_sid": session.call_sid,
                "stream_sid": session.stream_sid,
                "student_id": session.student_id,
                "workflow_id": session.workflow_id,
                "is_active": session.is_active(),
                "created_at": session.created_at.isoformat(),
                "last_activity": session.last_activity.isoformat(),
            }

        return {
            "active_sessions": len(sessions),
            "sessions": session_info
        }

    except Exception as e:
        alog.error(f"Error getting active sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
