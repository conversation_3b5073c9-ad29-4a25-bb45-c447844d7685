#!/usr/bin/env python
"""
Twilio Voice Client

Handles Twilio voice calls and Media Streams for the voice agent.
"""

import alog
from typing import Optional, Dict, Any
from twilio.rest import Client
from twilio.twiml.voice_response import VoiceResponse
from addie import settings
from .models import VoiceCallRequest, VoiceCallResponse, format_phone_number, validate_phone_number


class TwilioVoiceClient:
    """Client for managing Twilio voice calls and Media Streams"""

    def __init__(self):
        """Initialize Twilio client"""
        self.account_sid = settings.TWILIO_ACCOUNT_SID
        self.auth_token = settings.TWILIO_AUTH_TOKEN
        self.phone_number = settings.TWILIO_PHONE_NUMBER

        if not all([self.account_sid, self.auth_token]):
            alog.warning("Twilio credentials not configured. Voice calls will not work.")
            self.client = None
        else:
            self.client = Client(self.account_sid, self.auth_token)
            alog.debug("Twilio voice client initialized")

    def initiate_call(self, request: VoiceCallRequest) -> VoiceCallResponse:
        """
        Initiate a voice call to a student

        Args:
            request: VoiceCallRequest with call details

        Returns:
            VoiceCallResponse with call status and details
        """
        try:
            if not self.client:
                return VoiceCallResponse(
                    success=False,
                    message="Twilio client not configured",
                    error="Missing Twilio credentials"
                )

            # Validate phone number
            if not validate_phone_number(request.phone_number):
                return VoiceCallResponse(
                    success=False,
                    message="Invalid phone number format",
                    error=f"Phone number {request.phone_number} is not valid"
                )

            # Format phone number
            to_number = format_phone_number(request.phone_number)
            from_number = self.phone_number or "+18449990354"  # Default Twilio number

            # Create TwiML for the call
            twiml_url = self._get_twiml_url(request)

            alog.debug(f"Initiating call from {from_number} to {to_number}")

            # Make the call
            call = self.client.calls.create(
                to=to_number,
                from_=from_number,
                url=twiml_url,
                method='POST',
                status_callback=request.webhook_url,
                status_callback_event=['initiated', 'ringing', 'answered', 'completed'],
                status_callback_method='POST'
            )

            alog.info(f"Call initiated: {call.sid}")

            return VoiceCallResponse(
                success=True,
                call_sid=call.sid,
                message="Call initiated successfully"
            )

        except Exception as e:
            alog.error(f"Error initiating call: {str(e)}")
            return VoiceCallResponse(
                success=False,
                message="Failed to initiate call",
                error=str(e)
            )

    def _get_twiml_url(self, request: VoiceCallRequest) -> str:
        """Get the TwiML URL for the call"""
        # This should point to your API endpoint that returns TwiML
        base_url = settings.API_URL or "http://localhost:8000"
        return f"{base_url}/api/voice/twiml?student_id={request.student_id}&student_workflow_id={request.student_workflow_id}"

    def generate_twiml_for_media_stream(
        self,
        student_id: str,
        student_workflow_id: str,
        websocket_url: Optional[str] = None
    ) -> str:
        """
        Generate TwiML to start a Media Stream

        Args:
            student_id: Student ID
            student_workflow_id: Student Workflow ID
            websocket_url: WebSocket URL for media streaming

        Returns:
            TwiML XML string
        """
        response = VoiceResponse()

        # Add a greeting
        response.say("Hello! I'm Addie, your AI counselor. Let's have a conversation about your goals and aspirations.")

        # Start Media Stream
        websocket_url = websocket_url or settings.VOICE_WEBSOCKET_URL

        # Add query parameters to WebSocket URL
        stream_url = f"{websocket_url}?student_id={student_id}&student_workflow_id={student_workflow_id}"

        start = response.start()
        start.stream(url=stream_url)

        # Keep the call alive while streaming
        response.pause(length=60)  # Pause for 60 seconds to allow conversation

        return str(response)

    def get_call_status(self, call_sid: str) -> Optional[Dict[str, Any]]:
        """
        Get the status of a call

        Args:
            call_sid: Twilio call SID

        Returns:
            Call status information or None if error
        """
        try:
            if not self.client:
                alog.warning("Twilio client not configured")
                return None

            call = self.client.calls(call_sid).fetch()

            return {
                'sid': call.sid,
                'status': call.status,
                'direction': call.direction,
                'from': call.from_,
                'to': call.to,
                'duration': call.duration,
                'start_time': call.start_time,
                'end_time': call.end_time,
                'price': call.price,
                'price_unit': call.price_unit
            }

        except Exception as e:
            alog.error(f"Error getting call status for {call_sid}: {str(e)}")
            return None

    def end_call(self, call_sid: str) -> bool:
        """
        End an active call

        Args:
            call_sid: Twilio call SID

        Returns:
            True if call was ended successfully, False otherwise
        """
        try:
            if not self.client:
                alog.warning("Twilio client not configured")
                return False

            self.client.calls(call_sid).update(status='completed')
            alog.debug(f"Call {call_sid} ended")
            return True

        except Exception as e:
            alog.error(f"Error ending call {call_sid}: {str(e)}")
            return False

    def validate_webhook_signature(self, url: str, params: Dict[str, str], signature: str) -> bool:
        """
        Validate Twilio webhook signature

        Args:
            url: The full URL of the webhook
            params: POST parameters from Twilio
            signature: X-Twilio-Signature header value

        Returns:
            True if signature is valid, False otherwise
        """
        try:
            from twilio.request_validator import RequestValidator

            if not self.auth_token:
                alog.warning("Cannot validate webhook signature: no auth token")
                return False

            validator = RequestValidator(self.auth_token)
            return validator.validate(url, params, signature)

        except Exception as e:
            alog.error(f"Error validating webhook signature: {str(e)}")
            return False
