"""
Admin dashboard service for ADDY-501
Provides backend support for the admin engagement dashboard
"""

import logging
from datetime import datetime
from typing import List, Dict, Optional

from prisma import Prisma
from prisma.enums import EngagementTier
from addie.lib import prisma_client
from addie.services.engagement_tracker import EngagementTracker

logger = logging.getLogger(__name__)


class AdminDashboardService:
    """Service for admin dashboard functionality"""
    
    def __init__(self, prisma: Prisma = None, engagement_tracker: EngagementTracker = None):
        self.prisma = prisma or prisma_client()
        self.engagement_tracker = engagement_tracker or EngagementTracker(self.prisma)
    
    def get_students_engagement_data(
        self,
        school_id: Optional[str] = None,
        tier: Optional[str] = None,
        limit: int = 50,
        offset: int = 0,
        sort_by: str = "last_activity_at",
        sort_order: str = "desc"
    ) -> Dict:
        """
        Get paginated student data for admin engagement dashboard.
        Includes all fields needed for ADDY-501 frontend table.
        """
        try:
            # Build where clause
            where_clause = {
                'engagement_tier': {'not': None}
            }
            
            if school_id:
                where_clause['school_id'] = school_id
                
            if tier:
                # Convert string to enum
                tier_enum = None
                for t in EngagementTier:
                    if t.value == tier:
                        tier_enum = t
                        break
                if tier_enum:
                    where_clause['engagement_tier'] = tier_enum
            
            # Get students with related data
            students = self.prisma.student.find_many(
                where=where_clause,
                include={
                    'users': True,
                    'school': True
                },
                skip=offset,
                take=limit
            )
            
            # Get total count
            total = self.prisma.student.count(where=where_clause)
            
            # Get last messages for each student
            result_students = []
            for student in students:
                user = student.users[0] if student.users else None
                
                # Get last messages
                last_to_student, last_from_student = self._get_last_messages(student.id)
                
                result_students.append({
                    'id': student.id,
                    'name': f"{user.first_name} {user.last_name}" if user else "Unknown",
                    'school': student.school.name if student.school else None,
                    'grade': student.grade,
                    'engagement_tier': student.engagement_tier.value if hasattr(student.engagement_tier, 'value') else student.engagement_tier,
                    'tier_updated_at': student.tier_updated_at,
                    'last_activity_at': student.last_activity_at,
                    'last_activity_channel': student.last_activity_channel,
                    'last_message_to_student': last_to_student,
                    'last_message_from_student': last_from_student,
                    'phone_number': user.phone_number if user else None
                })
            
            return {
                "students": result_students,
                "total": total,
                "has_more": offset + len(students) < total
            }
            
        except Exception as e:
            logger.error(f"Failed to get students engagement data: {e}")
            raise
    
    def get_message_templates(
        self,
        student_id: str
    ) -> List[Dict]:
        """Get available message templates for student's tier"""
        try:
            # Get student's tier
            student = self.prisma.student.find_unique(
                where={'id': student_id}
            )
            
            if not student:
                raise ValueError("Student not found")
            
            tier = student.engagement_tier.value if hasattr(student.engagement_tier, 'value') else student.engagement_tier
            
            # Use ReminderTemplateService to get templates
            from addie.services.reminder_template_service import ReminderTemplateService
            template_service = ReminderTemplateService(self.prisma)
            
            if tier:
                templates = template_service.get_templates_by_tier(tier)
            else:
                # Get all templates if no specific tier
                all_templates = template_service.get_all_templates(limit=100)
                templates = all_templates['templates']
            
            return templates
            
        except Exception as e:
            logger.error(f"Failed to get message templates: {e}")
            raise
    
    def send_message_to_student(
        self,
        student_id: str,
        message: str,
        template_id: Optional[str] = None,
        sent_by_user_id: str = None
    ) -> Dict:
        """
        Send immediate SMS to student and track engagement.
        Integrates with existing SMS infrastructure.
        """
        try:
            # Get student with user info
            student = self.prisma.student.find_unique(
                where={'id': student_id},
                include={
                    'users': True
                }
            )
            
            if not student or not student.users:
                raise ValueError("Student not found")
            
            user = student.users[0]
            if not user.phone_number:
                raise ValueError("Student has no phone number")
            
            # Send SMS using existing infrastructure
            # Note: send_sms_via_twilio import needs to be verified
            # from addie.api.sms_routes import send_sms_via_twilio
            # For now, mock the SMS sending functionality
            def send_sms_via_twilio(to_phone, message):
                return {"sid": f"mock-sms-{hash(message)}"}
            
            sms_result = send_sms_via_twilio(
                to_phone=user.phone_number,
                message=message
            )
            
            # Track as engagement event
            from prisma.enums import EngagementChannel, EngagementEventType
            
            self.engagement_tracker.track_message_event(
                user_id=user.id,
                student_id=student_id,
                session_id=f"admin-{student_id}",
                channel=EngagementChannel.sms,
                event_type=EngagementEventType.message_sent,
                metadata={
                    "template_id": template_id,
                    "sent_by_admin": sent_by_user_id,
                    "message_content": message
                }
            )
            
            return {
                "success": True,
                "message_id": sms_result.get("sid"),
                "sent_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to send SMS to student {student_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_recent_messages(
        self,
        student_id: str,
        limit: int = 10
    ) -> List[Dict]:
        """Get recent conversation messages for modal context"""
        try:
            # Get recent messages for this student
            messages = self.prisma.messages.find_many(
                where={
                    'session_id': {'contains': student_id}
                },
                order_by={'created_at': 'desc'},
                take=limit
            )
            
            result = []
            for msg in messages:
                message_data = msg.message
                sender = 'student' if message_data.get('type') == 'human' else 'addie'
                
                result.append({
                    'id': msg.id,
                    'content': message_data.get('content', ''),
                    'sender': sender,
                    'created_at': msg.created_at,
                })
            
            # Reverse to show oldest first
            return list(reversed(result))
            
        except Exception as e:
            logger.error(f"Failed to get recent messages: {e}")
            return []
    
    def get_engagement_summary(self) -> Dict:
        """Get overall engagement summary for dashboard"""
        try:
            # Get tier distribution
            from addie.services.engagement_tiers import EngagementTierService
            tier_service = EngagementTierService(prisma=self.prisma)
            
            distribution = tier_service.get_tier_distribution()
            
            # Get total students with tiers
            total_students = sum(distribution.values())
            
            # Get recent activity (last 24 hours)
            from datetime import timedelta
            yesterday = datetime.utcnow() - timedelta(days=1)
            
            recent_activity = self.prisma.engagementevent.count(
                where={'engaged_at': {'gte': yesterday}}
            )
            
            return {
                'tier_distribution': distribution,
                'total_students': total_students,
                'recent_activity_24h': recent_activity
            }
            
        except Exception as e:
            logger.error(f"Failed to get engagement summary: {e}")
            return {}
    
    def _get_last_messages(self, student_id: str) -> tuple[str, str]:
        """Get last message to and from student"""
        try:
            # Get last AI message (to student) - simplified query
            # Note: Complex JSON path queries may not be fully supported
            last_ai_msg = None  # Simplified for now
            
            # Get last human message (from student) - simplified query
            # Note: Complex JSON path queries may not be fully supported
            last_human_msg = None  # Simplified for now
            
            last_to_student = None
            last_from_student = None
            
            if last_ai_msg:
                last_to_student = last_ai_msg.message.get('content', '')[:100] + '...' if len(last_ai_msg.message.get('content', '')) > 100 else last_ai_msg.message.get('content', '')
            
            if last_human_msg:
                last_from_student = last_human_msg.message.get('content', '')[:100] + '...' if len(last_human_msg.message.get('content', '')) > 100 else last_human_msg.message.get('content', '')
            
            return last_to_student, last_from_student
            
        except Exception as e:
            logger.error(f"Failed to get last messages for student {student_id}: {e}")
            return None, None