#!/usr/bin/env python
"""
Major Recommendations Service

This service manages the storage and retrieval of AI-generated major recommendations
based on OCEAN personality scores using the StudentMajorMatches table.
"""

import alog
from typing import Dict, List, Optional, Any
from addie.lib import prisma_client
from addie.majors_agent.agent import majors_agent


class MajorRecommendationService:
    """Service for managing major recommendations based on OCEAN scores."""

    def __init__(self):
        self.prisma = prisma_client()

    def generate_and_save_recommendations(self, student_id: str) -> bool:
        """
        Generate major recommendations using the majors_agent and save them using the new StudentMajors schema.

        Phase 3 Implementation:
        - Check if a StudentMajors entry exists for the given ocean_score_id
        - If not, create one, then populate StudentMajorMatches records linked to it

        Args:
            student_id: The student's ID

        Returns:
            True if successful, False otherwise
        """
        try:
            alog.info(f"Generating and saving major recommendations for student {student_id}")

            # Get the student's OCEAN scores
            ocean_scores = self.prisma.oceanscores.find_unique(
                where={"student_id": student_id}
            )

            if not ocean_scores:
                alog.error(f"No OCEAN scores found for student {student_id}")
                return False

            # Check if a StudentMajors session already exists for this ocean_score_id
            existing_session = self.prisma.studentmajors.find_unique(
                where={"ocean_score_id": ocean_scores.id},
                include={"matches": True}
            )

            if existing_session:
                alog.info(f"Major recommendations session already exists for student {student_id} (ocean_score_id: {ocean_scores.id}). Skipping computation to avoid redundancy.")
                return True

            # Generate recommendations using the majors_agent
            agent_response = majors_agent(student_id)

            if not agent_response or not agent_response.get('recommendations'):
                alog.warning(f"No recommendations generated for student {student_id}")
                return False

            recommendations = agent_response['recommendations']
            summary = agent_response.get('summary', 'AI-generated major recommendations based on OCEAN personality analysis.')

            # Create the StudentMajors session record with AI-generated summary
            student_majors_session = self.prisma.studentmajors.create(
                data={
                    "student_id": student_id,
                    "ocean_score_id": ocean_scores.id,
                    "summary": summary
                }
            )

            # Save individual major matches linked to the session
            match_data = []
            for rec in recommendations:
                match_data.append({
                    "student_major_id": student_majors_session.id,
                    "major_name": rec["major"],
                    "match_percentage": rec["match_percent"],
                    "liked": None,  # Will be set by user interaction
                    "disliked": None  # Will be set by user interaction
                })

            # Batch create all matches
            self.prisma.studentmajormatches.create_many(
                data=match_data
            )

            alog.info(f"Successfully created major recommendations session {student_majors_session.id} with {len(recommendations)} matches and summary for student {student_id} (ocean_score_id: {ocean_scores.id})")
            return True

        except Exception as e:
            alog.error(f"Error generating and saving recommendations for student {student_id}: {str(e)}")
            return False

    def get_recommendations(self, student_id: str, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Retrieve stored major recommendations for a student using the new StudentMajors schema.

        Args:
            student_id: The student's ID
            limit: Optional limit on number of recommendations to return

        Returns:
            List of recommendation dictionaries sorted by match percentage (highest first)
        """
        try:
            # Get the latest StudentMajors session for this student
            student_majors_session = self.prisma.studentmajors.find_first(
                where={"student_id": student_id},
                order={"created_at": "desc"},
                include={
                    "matches": True,
                    "ocean_score": True
                }
            )

            if not student_majors_session:
                alog.info(f"No major recommendations session found for student {student_id}")
                return []

            # Sort matches by match percentage (highest first) and apply limit
            sorted_matches = sorted(student_majors_session.matches, key=lambda x: x.match_percentage, reverse=True)
            if limit:
                sorted_matches = sorted_matches[:limit]

            result = []
            for match in sorted_matches:
                result.append({
                    "id": match.id,
                    "major": match.major_name,
                    "match_percent": match.match_percentage,
                    "liked": match.liked,
                    "disliked": match.disliked,
                    "student_major_id": match.student_major_id,
                    "ocean_score_id": student_majors_session.ocean_score_id,
                    "session_summary": student_majors_session.summary,
                    "created_at": match.created_at,
                    "updated_at": match.updated_at
                })

            alog.info(f"Retrieved {len(result)} major recommendations for student {student_id} from session {student_majors_session.id}")
            return result

        except Exception as e:
            alog.error(f"Error retrieving recommendations for student {student_id}: {str(e)}")
            return []

    def get_top_recommendations(self, student_id: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get top N major recommendations for a student.
        
        Args:
            student_id: The student's ID
            limit: Number of top recommendations to return (default: 5)
            
        Returns:
            List of top recommendation dictionaries
        """
        return self.get_recommendations(student_id, limit=limit)

    def has_recommendations(self, student_id: str) -> bool:
        """
        Check if a student has any stored major recommendations using the new StudentMajors schema.

        Args:
            student_id: The student's ID

        Returns:
            True if recommendations exist, False otherwise
        """
        try:
            count = self.prisma.studentmajors.count(
                where={"student_id": student_id}
            )
            return count > 0
        except Exception as e:
            alog.error(f"Error checking recommendations for student {student_id}: {str(e)}")
            return False

    def delete_recommendations(self, student_id: str) -> bool:
        """
        Delete all major recommendations for a student using the new StudentMajors schema.
        This will cascade delete all associated StudentMajorMatches records.

        Args:
            student_id: The student's ID

        Returns:
            True if successful, False otherwise
        """
        try:
            result = self.prisma.studentmajors.delete_many(
                where={"student_id": student_id}
            )

            alog.info(f"Deleted {result.count} major recommendation sessions for student {student_id}")
            return True

        except Exception as e:
            alog.error(f"Error deleting recommendations for student {student_id}: {str(e)}")
            return False

    def refresh_recommendations(self, student_id: str) -> bool:
        """
        Refresh major recommendations by regenerating them from current OCEAN scores.
        
        Args:
            student_id: The student's ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            alog.info(f"Refreshing major recommendations for student {student_id}")
            
            # Delete existing recommendations and generate new ones
            return self.generate_and_save_recommendations(student_id)
            
        except Exception as e:
            alog.error(f"Error refreshing recommendations for student {student_id}: {str(e)}")
            return False

    def get_recommendations_summary(self, student_id: str) -> Dict[str, Any]:
        """
        Get a summary of major recommendations for a student.
        
        Args:
            student_id: The student's ID
            
        Returns:
            Dictionary with recommendation summary statistics
        """
        try:
            recommendations = self.get_recommendations(student_id)
            
            if not recommendations:
                return {
                    "student_id": student_id,
                    "total_recommendations": 0,
                    "top_major": None,
                    "top_match_percent": None,
                    "average_match_percent": None,
                    "last_updated": None
                }
            
            # Calculate summary statistics
            match_percentages = [rec["match_percent"] for rec in recommendations]
            average_match = sum(match_percentages) / len(match_percentages)
            
            return {
                "student_id": student_id,
                "total_recommendations": len(recommendations),
                "top_major": recommendations[0]["major"],
                "top_match_percent": recommendations[0]["match_percent"],
                "average_match_percent": round(average_match, 1),
                "last_updated": recommendations[0]["updated_at"]
            }
            
        except Exception as e:
            alog.error(f"Error getting recommendations summary for student {student_id}: {str(e)}")
            return {
                "student_id": student_id,
                "error": str(e)
            }

    def update_recommendation_feedback(self, student_id: str, major_name: str, liked: Optional[bool] = None, disliked: Optional[bool] = None) -> bool:
        """
        Update the liked/disliked status for a specific major recommendation using the new schema.

        Args:
            student_id: The student's ID
            major_name: The name of the major to update
            liked: True if user liked the recommendation, None to clear
            disliked: True if user disliked the recommendation, None to clear

        Returns:
            True if successful, False otherwise
        """
        try:
            # Find the latest StudentMajors session for this student
            student_majors_session = self.prisma.studentmajors.find_first(
                where={"student_id": student_id},
                order={"created_at": "desc"}
            )

            if not student_majors_session:
                alog.error(f"No major recommendations session found for student {student_id}")
                return False

            # Find the specific major match to update
            recommendation = self.prisma.studentmajormatches.find_first(
                where={
                    "student_major_id": student_majors_session.id,
                    "major_name": major_name
                }
            )

            if not recommendation:
                alog.error(f"No recommendation found for student {student_id} and major {major_name}")
                return False

            # Update the feedback
            self.prisma.studentmajormatches.update(
                where={"id": recommendation.id},
                data={
                    "liked": liked,
                    "disliked": disliked
                }
            )

            alog.info(f"Updated feedback for student {student_id}, major {major_name}: liked={liked}, disliked={disliked}")
            return True

        except Exception as e:
            alog.error(f"Error updating recommendation feedback for student {student_id}, major {major_name}: {str(e)}")
            return False

    def get_recommendations_by_ocean_score_id(self, ocean_score_id: str) -> List[Dict[str, Any]]:
        """
        Retrieve major recommendations by ocean_score_id using the new StudentMajors schema.

        Args:
            ocean_score_id: The OCEAN score ID

        Returns:
            List of recommendation dictionaries sorted by match percentage (highest first)
        """
        try:
            # Find the StudentMajors session for this ocean_score_id
            student_majors_session = self.prisma.studentmajors.find_unique(
                where={"ocean_score_id": ocean_score_id},
                include={
                    "matches": True,
                    "student": True,
                    "ocean_score": True
                }
            )

            if not student_majors_session:
                alog.info(f"No major recommendations session found for ocean_score_id {ocean_score_id}")
                return []

            # Sort matches by match percentage (highest first)
            sorted_matches = sorted(student_majors_session.matches, key=lambda x: x.match_percentage, reverse=True)

            result = []
            for match in sorted_matches:
                result.append({
                    "id": match.id,
                    "student_id": student_majors_session.student_id,
                    "ocean_score_id": ocean_score_id,
                    "student_major_id": match.student_major_id,
                    "major": match.major_name,
                    "match_percent": match.match_percentage,
                    "liked": match.liked,
                    "disliked": match.disliked,
                    "session_summary": student_majors_session.summary,
                    "created_at": match.created_at,
                    "updated_at": match.updated_at
                })

            alog.info(f"Retrieved {len(result)} major recommendations for ocean_score_id {ocean_score_id}")
            return result

        except Exception as e:
            alog.error(f"Error retrieving recommendations for ocean_score_id {ocean_score_id}: {str(e)}")
            return []

    def check_existing_recommendations_by_ocean_score(self, ocean_score_id: str) -> bool:
        """
        Check if recommendations already exist for a specific ocean_score_id using the new schema.

        Args:
            ocean_score_id: The OCEAN score ID

        Returns:
            True if recommendations exist, False otherwise
        """
        try:
            session = self.prisma.studentmajors.find_unique(
                where={"ocean_score_id": ocean_score_id}
            )
            return session is not None
        except Exception as e:
            alog.error(f"Error checking existing recommendations for ocean_score_id {ocean_score_id}: {str(e)}")
            return False

    def add_session_summary(self, student_id: str, summary: str) -> bool:
        """
        Add or update a summary for the latest major recommendations session.

        Args:
            student_id: The student's ID
            summary: AI-generated summary of the recommendations

        Returns:
            True if successful, False otherwise
        """
        try:
            # Find the latest session for this student
            session = self.prisma.studentmajors.find_first(
                where={"student_id": student_id},
                order={"created_at": "desc"}
            )

            if not session:
                alog.error(f"No major recommendations session found for student {student_id}")
                return False

            # Update the session with the summary
            self.prisma.studentmajors.update(
                where={"id": session.id},
                data={"summary": summary}
            )

            alog.info(f"Added summary to major recommendations session {session.id} for student {student_id}")
            return True

        except Exception as e:
            alog.error(f"Error adding session summary for student {student_id}: {str(e)}")
            return False
