"""
Engagement tier calculation service for ADDY-495
Automatically classifies students into Active/At-Risk/Dormant tiers
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List

from prisma import Prisma
from prisma.enums import EngagementTier
from addie.lib import prisma_client

logger = logging.getLogger(__name__)


class EngagementTierService:
    """Service for calculating and managing student engagement tiers"""
    
    def __init__(self, engagement_tracker=None, prisma: Prisma = None):
        self.prisma = prisma or prisma_client()
        self.engagement_tracker = engagement_tracker
    
    def calculate_tier(self, student_id: str) -> EngagementTier:
        """
        Calculate engagement tier based on latest activity across all channels.
        Priority: SMS > web_student > web_counselor > email > voice
        """
        now = datetime.utcnow()
        
        # Get latest engagement across all channels
        latest_engagement = self.get_latest_student_engagement(student_id)
        
        if not latest_engagement:
            # New students with assigned workflows default to Active
            if self.has_recent_workflow_assignment(student_id):
                return EngagementTier.Active
            return EngagementTier.Dormant
        
        # Handle timezone-aware vs naive datetime comparison
        if latest_engagement.tzinfo is not None:
            # Convert latest_engagement to naive UTC
            latest_engagement = latest_engagement.replace(tzinfo=None)
        time_since_last = now - latest_engagement
        
        if time_since_last <= timedelta(hours=24):
            return EngagementTier.Active
        elif time_since_last <= timedelta(days=2):
            return EngagementTier.At_Risk
        else:
            return EngagementTier.Dormant
    
    def update_student_tier(
        self, 
        student_id: str, 
        force_recalculate: bool = False
    ) -> str:
        """Update student's engagement tier and return new tier"""
        
        current_tier = self.calculate_tier(student_id)
        latest_activity = self.get_latest_student_engagement(student_id)
        latest_channel = self.get_latest_activity_channel(student_id)
        
        # Update student record
        self.update_student_record(
            student_id=student_id,
            tier=current_tier,
            last_activity_at=latest_activity,
            last_activity_channel=latest_channel
        )
        
        return current_tier.value
    
    def batch_update_all_tiers(self):
        """Nightly batch job to update all student tiers"""
        active_students = self.get_active_students()
        
        updated_count = 0
        failed_count = 0
        
        for student in active_students:
            try:
                self.update_student_tier(student.id)
                updated_count += 1
            except Exception as e:
                logger.error(f"Failed to update tier for student {student.id}: {e}")
                failed_count += 1
        
        logger.info(f"Batch tier update completed. Updated: {updated_count}, Failed: {failed_count}")
        return {'updated': updated_count, 'failed': failed_count}
    
    def get_tier_distribution(self) -> Dict[str, int]:
        """Get count of students in each tier"""
        try:
            # Get all students with engagement tiers
            students = self.prisma.student.find_many(
                where={'engagement_tier': {'not': None}}
            )
            
            distribution = {}
            for tier in EngagementTier:
                distribution[tier.value] = 0
            
            for student in students:
                if student.engagement_tier:
                    tier_value = student.engagement_tier.value if hasattr(student.engagement_tier, 'value') else student.engagement_tier
                    distribution[tier_value] = distribution.get(tier_value, 0) + 1
            
            return distribution
            
        except Exception as e:
            logger.error(f"Failed to get tier distribution: {e}")
            return {}
    
    def get_students_by_tier(self, tier: EngagementTier, limit: int = 100) -> List[Dict]:
        """Get students in a specific engagement tier"""
        try:
            students = self.prisma.student.find_many(
                where={'engagement_tier': tier},
                include={
                    'users': True,
                    'school': True
                },
                take=limit
            )
            
            result = []
            for student in students:
                user = student.users[0] if student.users else None
                result.append({
                    'id': student.id,
                    'name': f"{user.first_name} {user.last_name}" if user else "Unknown",
                    'school': student.school.name if student.school else None,
                    'grade': student.grade,
                    'tier': student.engagement_tier.value if hasattr(student.engagement_tier, 'value') else student.engagement_tier,
                    'last_activity_at': student.last_activity_at,
                    'last_activity_channel': student.last_activity_channel,
                    'phone_number': user.phone_number if user else None,
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get students by tier: {e}")
            return []
    
    def get_latest_student_engagement(self, student_id: str) -> datetime:
        """Get the latest engagement timestamp for a student"""
        try:
            events = self.prisma.engagementevent.find_many(
                where={'student_id': student_id}
            )
            
            if not events:
                return None
                
            # Find the latest event by comparing timestamps
            latest_event = max(events, key=lambda x: x.engaged_at)
            return latest_event.engaged_at
            
        except Exception as e:
            logger.error(f"Failed to get latest student engagement: {e}")
            return None
    
    def get_latest_activity_channel(self, student_id: str) -> str:
        """Get the channel of the student's latest activity"""
        try:
            events = self.prisma.engagementevent.find_many(
                where={'student_id': student_id}
            )
            
            if not events:
                return None
                
            # Find the latest event by comparing timestamps
            latest_event = max(events, key=lambda x: x.engaged_at)
            # Handle both enum objects and string values
            if hasattr(latest_event.channel, 'value'):
                return latest_event.channel.value
            else:
                return latest_event.channel
            
        except Exception as e:
            logger.error(f"Failed to get latest activity channel: {e}")
            return None
    
    def has_recent_workflow_assignment(self, student_id: str, hours: int = 24) -> bool:
        """Check if student has been assigned a workflow recently"""
        try:
            cutoff = datetime.utcnow() - timedelta(hours=hours)
            
            recent_workflow = self.prisma.studentworkflow.find_first(
                where={
                    'student_id': student_id,
                    'created_at': {'gte': cutoff}
                }
            )
            
            return recent_workflow is not None
            
        except Exception as e:
            logger.error(f"Failed to check recent workflow assignment: {e}")
            return False
    
    def get_active_students(self):
        """Get all active students (not deleted/suspended)"""
        try:
            # Get students who have associated users that are enabled
            students = self.prisma.student.find_many(
                where={
                    'users': {
                        'some': {
                            'enabled': True
                        }
                    }
                }
            )
            
            return students
            
        except Exception as e:
            logger.error(f"Failed to get active students: {e}")
            return []
    
    def update_student_record(
        self,
        student_id: str,
        tier: EngagementTier,
        last_activity_at: datetime,
        last_activity_channel: str
    ) -> None:
        """Update student's engagement tier and activity information"""
        try:
            self.prisma.student.update(
                where={'id': student_id},
                data={
                    'engagement_tier': tier,
                    'tier_updated_at': datetime.utcnow(),
                    'last_activity_at': last_activity_at,
                    'last_activity_channel': last_activity_channel
                }
            )
            
            logger.info(f"Updated student {student_id} tier to {tier.value}")
            
        except Exception as e:
            logger.error(f"Failed to update student record: {e}")
            raise
    
    def get_tier_trends(self, days: int = 30) -> Dict:
        """Get tier changes over time for analytics"""
        try:
            cutoff = datetime.utcnow() - timedelta(days=days)
            
            # This would require tracking tier changes over time
            # For now, return current distribution
            return self.get_tier_distribution()
            
        except Exception as e:
            logger.error(f"Failed to get tier trends: {e}")
            return {}