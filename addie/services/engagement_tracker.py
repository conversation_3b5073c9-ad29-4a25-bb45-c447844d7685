"""
Engagement tracking service for ADDY-499, ADDY-495, ADDY-501
Tracks all user interactions across channels (SMS, web, email, voice)
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

from prisma import Prisma
from prisma.enums import EngagementChannel, EngagementEventType, EngagementTier
from addie.lib import prisma_client, dict_to_json

logger = logging.getLogger(__name__)


class EngagementTracker:
    """Service for tracking user engagement events across all channels"""
    
    def __init__(self, prisma: Prisma = None):
        self.prisma = prisma or prisma_client()
    
    def track_message_event(
        self,
        user_id: str,
        student_id: str,
        session_id: str,
        channel: EngagementChannel,
        event_type: EngagementEventType,
        message_id: int = None,
        workflow_id: str = None,
        metadata: dict = None
    ) -> None:
        """Track any message-related engagement event"""
        try:
            self.prisma.engagementevent.create(
                data={
                    'user_id': user_id,
                    'student_id': student_id,
                    'session_id': session_id,
                    'channel': channel,
                    'event_type': event_type,
                    'message_id': message_id,
                    'workflow_id': workflow_id,
                    'metadata': dict_to_json(metadata) if metadata else None,
                    'engaged_at': datetime.utcnow(),
                }
            )
            
            # Update student's last activity if this is a student event
            if student_id and event_type in [EngagementEventType.message_sent, EngagementEventType.message_received]:
                self._update_student_last_activity(student_id, channel.value)
                
            logger.info(f"Tracked {event_type} event for user {user_id} on {channel}")
            
        except Exception as e:
            logger.error(f"Failed to track message event: {e}")
            raise
    
    def track_email_event(
        self,
        user_id: str,
        email_id: str,
        event_type: EngagementEventType,
        email_template_name: str,
        student_id: str = None,
        reminder_id: str = None,
        metadata: dict = None
    ) -> None:
        """Track email-specific engagement events"""
        try:
            self.prisma.engagementevent.create(
                data={
                    'user_id': user_id,
                    'student_id': student_id,
                    'channel': EngagementChannel.email,
                    'event_type': event_type,
                    'email_id': email_id,
                    'email_template_name': email_template_name,
                    'reminder_id': reminder_id,
                    'metadata': dict_to_json(metadata) if metadata else None,
                    'engaged_at': datetime.utcnow(),
                }
            )
            
            # Update student's last activity for email opens/clicks
            if student_id and event_type in [EngagementEventType.email_opened, EngagementEventType.email_clicked]:
                self._update_student_last_activity(student_id, 'email')
                
            logger.info(f"Tracked {event_type} email event for {email_template_name}")
            
        except Exception as e:
            logger.error(f"Failed to track email event: {e}")
            raise
    
    def track_reminder_sent(
        self,
        user_id: str,
        student_id: str,
        reminder_id: str,
        template_id: str,
        channel: EngagementChannel
    ) -> None:
        """Log when a reminder is sent"""
        try:
            self.prisma.engagementevent.create(
                data={
                    'user_id': user_id,
                    'student_id': student_id,
                    'channel': channel,
                    'event_type': EngagementEventType.reminder_sent,
                    'reminder_id': reminder_id,
                    'template_id': template_id,
                    'engaged_at': datetime.utcnow(),
                }
            )
            
            logger.info(f"Tracked reminder sent to student {student_id} via {channel}")
            
        except Exception as e:
            logger.error(f"Failed to track reminder sent: {e}")
            raise
    
    def track_reminder_engagement(
        self,
        reminder_id: str,
        channel: EngagementChannel,
        metadata: dict = None
    ) -> bool:
        """
        Track reminder engagement.
        Returns True if within 24h window, False otherwise.
        """
        try:
            # Get reminder sent timestamp
            reminder_sent = self.prisma.engagementevent.find_first(
                where={
                    'reminder_id': reminder_id,
                    'event_type': EngagementEventType.reminder_sent
                },
                order={'engaged_at': 'desc'}
            )
            
            if not reminder_sent:
                logger.warning(f"No reminder_sent event found for reminder {reminder_id}")
                return False
            
            now = datetime.utcnow()
            sent_at = reminder_sent.engaged_at
            # Handle timezone-aware vs naive datetime comparison
            if sent_at.tzinfo is not None:
                # Convert sent_at to naive UTC
                sent_at = sent_at.replace(tzinfo=None)
            within_24h = (now - sent_at) <= timedelta(hours=24)
            
            # Track the engagement event
            self.prisma.engagementevent.create(
                data={
                    'user_id': reminder_sent.user_id,
                    'student_id': reminder_sent.student_id,
                    'channel': channel,
                    'event_type': EngagementEventType.reminder_engaged,
                    'reminder_id': reminder_id,
                    'metadata': dict_to_json({**(metadata or {}), 'within_24h': within_24h}),
                    'engaged_at': now,
                }
            )
            
            # Update student's last activity
            if reminder_sent.student_id:
                self._update_student_last_activity(
                    reminder_sent.student_id, channel.value
                )
            
            logger.info(f"Tracked reminder engagement for {reminder_id}, within 24h: {within_24h}")
            return within_24h
            
        except Exception as e:
            logger.error(f"Failed to track reminder engagement: {e}")
            raise
    
    def get_last_engagement(
        self,
        user_id: str,
        channel: EngagementChannel = None
    ) -> Optional[datetime]:
        """Get timestamp of last engagement for a user"""
        try:
            where_clause = {'user_id': user_id}
            if channel:
                where_clause['channel'] = channel
                
            result = self.prisma.engagementevent.find_first(
                where=where_clause,
                order={'engaged_at': 'desc'}
            )
            
            return result.engaged_at if result else None
            
        except Exception as e:
            logger.error(f"Failed to get last engagement: {e}")
            return None
    
    def update_student_tier(
        self,
        student_id: str
    ) -> str:
        """
        Update student's engagement tier based on latest activity.
        Returns: 'Active', 'At-Risk', or 'Dormant'
        """
        try:
            from addie.services.engagement_tiers import EngagementTierService
            tier_service = EngagementTierService(self)
            return tier_service.update_student_tier(student_id)
        except Exception as e:
            logger.error(f"Failed to update student tier: {e}")
            raise
    
    def _update_student_last_activity(
        self,
        student_id: str,
        channel: str
    ) -> None:
        """Update student's last activity timestamp and channel"""
        try:
            self.prisma.student.update(
                where={'id': student_id},
                data={
                    'last_activity_at': datetime.utcnow(),
                    'last_activity_channel': channel
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to update student last activity: {e}")
    
    def get_student_engagement_stats(
        self,
        student_id: str,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get engagement statistics for a student over the last N days"""
        try:
            since_date = datetime.utcnow() - timedelta(days=days)
            
            events = self.prisma.engagementevent.find_many(
                where={
                    'student_id': student_id,
                    'engaged_at': {'gte': since_date}
                }
            )
            
            if not events:
                return {}
            
            engagement_types = {EngagementEventType.message_sent, EngagementEventType.email_opened, EngagementEventType.email_clicked}
            channels_used = set(event.channel for event in events)
            engagement_events = [event for event in events if event.event_type in engagement_types]
            
            return {
                'total_events': len(events),
                'channels_used': len(channels_used),
                'engagement_events': len(engagement_events),
                'last_activity': max(event.engaged_at for event in events),
                'first_activity': min(event.engaged_at for event in events),
            }
            
        except Exception as e:
            logger.error(f"Failed to get student engagement stats: {e}")
            return {}