#!/usr/bin/env python
"""
OCEAN Scores Calculation Service

This service calculates Big Five personality trait scores (OCEAN) from BFI survey responses.
The calculation follows the standard Big Five Inventory scoring methodology.

OCEAN stands for:
- O: Openness to Experience
- C: Conscientiousness  
- E: Extroversion
- A: Agreeableness
- N: Neuroticism
"""

import alog
from typing import Dict, List, Optional, Tuple
from addie.lib import prisma_client

class OceanScoresService:
    """Service for calculating and managing OCEAN personality scores."""
    
    # Question mapping for each personality trait
    # Based on the official Big Five Personality Test (BFPT) scoring formulas
    TRAIT_QUESTIONS = {
        'extroversion': {
            'positive': [1, 11, 21, 31, 41],  # Add these scores
            'negative': [6, 16, 26, 36, 46]   # Subtract these scores
        },
        'agreeableness': {
            'positive': [7, 17, 27, 37, 42, 47],  # Add these scores
            'negative': [2, 12, 22, 32]           # Subtract these scores
        },
        'conscientiousness': {
            'positive': [3, 13, 23, 33, 43, 48],  # Add these scores
            'negative': [8, 18, 28, 38]           # Subtract these scores
        },
        'neuroticism': {
            'positive': [9, 19],                  # Add these scores (reversed scoring)
            'negative': [4, 14, 24, 29, 34, 39, 44, 49]  # Subtract these scores (reversed scoring)
        },
        'openness_to_experience': {
            'positive': [5, 15, 25, 35, 40, 45, 50],  # Add these scores
            'negative': [10, 20, 30]                  # Subtract these scores
        }
    }
    
    # Base scores for each trait (from the scoring formulas)
    BASE_SCORES = {
        'extroversion': 20,
        'agreeableness': 14,
        'conscientiousness': 14,
        'neuroticism': 38,
        'openness_to_experience': 8
    }

    def __init__(self):
        self.prisma = prisma_client()

    def find_latest_bfi_workflow(self, student_id: str) -> Optional[str]:
        """
        Find the latest completed BFI workflow for a student.

        Args:
            student_id: The student's ID

        Returns:
            Workflow ID of the latest completed BFI workflow, or None if not found
        """
        try:
            # Get all completed workflows for this student, ordered by completion date
            student_workflows = self.prisma.studentworkflow.find_many(
                where={
                    "student_id": student_id,
                    "status": "COMPLETED"
                },
                include={
                    "workflow": True
                },
                order={"updated_at": "desc"}
            )

            # Find the latest BFI workflow
            for student_workflow in student_workflows:
                if student_workflow.workflow and student_workflow.workflow.tags:
                    workflow_tags = student_workflow.workflow.tags
                    # Check if this workflow is tagged as BFI
                    is_bfi_workflow = "type:The Big Five Personality Test (BFPT)" in workflow_tags

                    if is_bfi_workflow:
                        alog.info(f"Found latest BFI workflow for student {student_id}: {student_workflow.workflow.name} (ID: {student_workflow.workflow.id})")
                        return student_workflow.workflow.id

            alog.warning(f"No completed BFI workflow found for student {student_id}")
            return None

        except Exception as e:
            alog.error(f"Error finding BFI workflow for student {student_id}: {str(e)}")
            return None

    def get_bfi_responses(self, student_id: str, workflow_id: Optional[str] = None) -> Dict[int, int]:
        """
        Get BFI survey responses for a student from a specific workflow.

        Args:
            student_id: The student's ID
            workflow_id: The workflow ID for the BFI survey (optional, will find latest if not provided)

        Returns:
            Dictionary mapping question numbers (1-50) to response values (1-5)
        """
        try:
            # If no workflow_id provided, find the latest BFI workflow
            if not workflow_id:
                workflow_id = self.find_latest_bfi_workflow(student_id)
                if not workflow_id:
                    alog.warning(f"No BFI workflow found for student {student_id}")
                    return {}

            alog.info(f"Getting BFI responses for student {student_id} from workflow {workflow_id}")
            # First try to get responses by step_id (new method)
            step_ids = self._get_workflow_step_ids(workflow_id)
            responses = self.prisma.questionresponse.find_many(
                where={
                    "student_id": student_id,
                    "step_id": {
                        "in": step_ids
                    }
                },
                include={
                    "mc_question": {
                        "include": {
                            "choices": True
                        }
                    }
                }
            )

            # If no responses found by step_id, try alternative method
            if not responses:
                alog.info(f"No responses found by step_id, trying alternative method for student {student_id}")

                # Get questionnaire for this workflow
                questionnaire = self.prisma.questionnaire.find_first(
                    where={"workflow": {"some": {"id": workflow_id}}}
                )

                if questionnaire:
                    # Get responses by questionnaire multiple choice questions
                    mc_questions = self.prisma.multiplechoicequestion.find_many(
                        where={"questionnaire_id": questionnaire.id}
                    )
                    mc_question_ids = [q.id for q in mc_questions]

                    responses = self.prisma.questionresponse.find_many(
                        where={
                            "student_id": student_id,
                            "mc_question_id": {"in": mc_question_ids}
                        },
                        include={
                            "mc_question": {
                                "include": {
                                    "choices": True
                                }
                            }
                        }
                    )

            question_responses = {}

            for response in responses:
                if response.mc_question:
                    # Get the question index (1-50) from the question data
                    question_index = response.mc_question.index + 1  # Convert 0-based to 1-based

                    # Get response value - try response_data first, then response field
                    response_value = None
                    if response.response_data and isinstance(response.response_data, dict):
                        response_value = response.response_data.get('value')
                    elif response.response and response.response != "DEPRECATED: Use response_data.value instead":
                        response_value = response.response

                    if response_value:
                        # Convert response text to numeric value
                        numeric_value = self._convert_response_to_numeric(str(response_value))

                        if numeric_value is not None and 1 <= question_index <= 50:
                            question_responses[question_index] = numeric_value

            alog.info(f"Retrieved {len(question_responses)} BFI responses for student {student_id}")
            return question_responses

        except Exception as e:
            alog.error(f"Error retrieving BFI responses for student {student_id}: {str(e)}")
            return {}

    def _get_workflow_step_ids(self, workflow_id: str) -> List[str]:
        """Get all step IDs for a workflow."""
        try:
            workflow = self.prisma.workflow.find_unique(
                where={"id": workflow_id},
                include={"steps": True}
            )
            
            if workflow and workflow.steps:
                return [step.id for step in workflow.steps]
            return []
            
        except Exception as e:
            alog.error(f"Error getting workflow step IDs: {str(e)}")
            return []

    def _convert_response_to_numeric(self, response_text: str) -> Optional[int]:
        """
        Convert response text to numeric value (1-5).
        
        Handles both numeric responses and text responses:
        - "1" or "Strongly Disagree" -> 1
        - "2" or "Disagree" -> 2  
        - "3" or "Neutral" -> 3
        - "4" or "Agree" -> 4
        - "5" or "Strongly Agree" -> 5
        """
        response_text = response_text.strip().lower()
        
        # Direct numeric conversion
        if response_text.isdigit():
            value = int(response_text)
            if 1 <= value <= 5:
                return value
        
        # Text to numeric mapping
        text_mapping = {
            'strongly disagree': 1,
            'strongly disagreed': 1,
            'disagree': 2,
            'disagreed': 2,
            'neutral': 3,
            'agree': 4,
            'agreed': 4,
            'strongly agree': 5,
            'strongly agreed': 5
        }
        
        return text_mapping.get(response_text)

    def calculate_ocean_scores(self, student_id: str, workflow_id: Optional[str] = None) -> Optional[Dict[str, float]]:
        """
        Calculate OCEAN scores for a student based on their BFI responses.

        Args:
            student_id: The student's ID
            workflow_id: The workflow ID for the BFI survey (optional, will find latest if not provided)

        Returns:
            Dictionary with OCEAN scores or None if calculation fails
        """
        try:
            # Get BFI responses (will find latest workflow if workflow_id not provided)
            responses = self.get_bfi_responses(student_id, workflow_id)
            
            if len(responses) < 50:
                alog.warning(f"Insufficient responses for OCEAN calculation. Got {len(responses)}, need 50")
                return None
            
            # Calculate each trait score
            scores = {}
            
            for trait, questions in self.TRAIT_QUESTIONS.items():
                score = self.BASE_SCORES[trait]

                # Add positive questions
                for q_num in questions['positive']:
                    if q_num in responses:
                        score += responses[q_num]

                # Subtract negative questions
                for q_num in questions['negative']:
                    if q_num in responses:
                        score -= responses[q_num]

                scores[trait] = float(score)
            
            alog.info(f"Calculated OCEAN scores for student {student_id}: {scores}")
            return scores
            
        except Exception as e:
            alog.error(f"Error calculating OCEAN scores for student {student_id}: {str(e)}")
            return None

    def save_ocean_scores(self, student_id: str, scores: Dict[str, float]) -> bool:
        """
        Save or update OCEAN scores for a student.
        
        Args:
            student_id: The student's ID
            scores: Dictionary with OCEAN scores
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Use upsert to handle both create and update cases
            self.prisma.oceanscores.upsert(
                where={"student_id": student_id},
                data={
                    "create": {
                        "student_id": student_id,
                        "extroversion": scores['extroversion'],
                        "agreeableness": scores['agreeableness'],
                        "conscientiousness": scores['conscientiousness'],
                        "neuroticism": scores['neuroticism'],
                        "openness_to_experience": scores['openness_to_experience']
                    },
                    "update": {
                        "extroversion": scores['extroversion'],
                        "agreeableness": scores['agreeableness'],
                        "conscientiousness": scores['conscientiousness'],
                        "neuroticism": scores['neuroticism'],
                        "openness_to_experience": scores['openness_to_experience']
                    }
                }
            )
            
            alog.info(f"Successfully saved OCEAN scores for student {student_id}")
            return True
            
        except Exception as e:
            alog.error(f"Error saving OCEAN scores for student {student_id}: {str(e)}")
            return False

    def process_student_ocean_scores(self, student_id: str, workflow_id: Optional[str] = None) -> bool:
        """
        Complete process: calculate and save OCEAN scores for a student.

        Args:
            student_id: The student's ID
            workflow_id: The workflow ID for the BFI survey (optional, will find latest if not provided)

        Returns:
            True if successful, False otherwise
        """
        try:
            # Calculate scores (will find latest BFI workflow if workflow_id not provided)
            scores = self.calculate_ocean_scores(student_id, workflow_id)
            
            if scores is None:
                alog.error(f"Failed to calculate OCEAN scores for student {student_id}")
                return False
            
            # Save scores
            return self.save_ocean_scores(student_id, scores)
            
        except Exception as e:
            alog.error(f"Error processing OCEAN scores for student {student_id}: {str(e)}")
            return False

    def get_ocean_scores(self, student_id: str) -> Optional[Dict[str, any]]:
        """
        Retrieve OCEAN scores for a student.
        
        Args:
            student_id: The student's ID
            
        Returns:
            Dictionary with OCEAN scores and metadata or None if not found
        """
        try:
            ocean_scores = self.prisma.oceanscores.find_unique(
                where={"student_id": student_id}
            )
            
            if ocean_scores:
                return {
                    "student_id": ocean_scores.student_id,
                    "extroversion": ocean_scores.extroversion,
                    "agreeableness": ocean_scores.agreeableness,
                    "conscientiousness": ocean_scores.conscientiousness,
                    "neuroticism": ocean_scores.neuroticism,
                    "openness_to_experience": ocean_scores.openness_to_experience,
                    "created_at": ocean_scores.created_at,
                    "updated_at": ocean_scores.updated_at
                }
            
            return None
            
        except Exception as e:
            alog.error(f"Error retrieving OCEAN scores for student {student_id}: {str(e)}")
            return None
