"""
Phone Number Service with capability-aware selection and load balancing.

This service provides:
- Capability-aware phone number selection (SMS/Voice separate)
- Load balancing across multiple numbers per country
- Priority-based selection
- Usage tracking integration
- Regional fallback strategies
- Legacy compatibility methods for backwards compatibility
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from prisma import Prisma
from prisma.models import TwilioPhoneNumber

from addie.utils.environment import get_current_environment, is_environment_match

logger = logging.getLogger(__name__)


class PhoneNumberService:
    """Service with capability-aware phone number selection and load balancing."""
    
    # Country codes to country abbreviations mapping
    COUNTRY_CODES = {
        '+1': ['US', 'CA'],  # United States, Canada
        '+55': ['BR'],       # Brazil
        '+39': ['IT'],       # Italy
        '+44': ['GB'],       # United Kingdom
        '+49': ['DE'],       # Germany
        '+33': ['FR'],       # France
        '+34': ['ES'],       # Spain
        '+52': ['MX'],       # Mexico
    }
    
    # Enhanced country code mapping with regional preferences
    COUNTRY_PHONE_PREFERENCES = {
        # North America - prefer local numbers
        '+1': {
            'regions': ['US', 'CA'],
            'prefer_local': True,
            'fallback_regions': ['+44', '+1']  # UK English, then any US
        },
        
        # South America - prefer regional numbers
        '+55': {  # Brazil
            'regions': ['BR'],
            'prefer_local': True,
            'fallback_regions': ['+1', '+34']  # US English, Spanish
        },
        '+52': {  # Mexico  
            'regions': ['MX'],
            'prefer_local': True,
            'fallback_regions': ['+1', '+34']
        },
        
        # Europe - prefer regional numbers
        '+39': {  # Italy
            'regions': ['IT'],
            'prefer_local': True, 
            'fallback_regions': ['+44', '+1']  # UK English, US English
        },
        '+44': {  # UK
            'regions': ['GB'],
            'prefer_local': True,
            'fallback_regions': ['+1']
        },
        '+49': {  # Germany
            'regions': ['DE'], 
            'prefer_local': True,
            'fallback_regions': ['+44', '+1']
        },
        '+33': {  # France
            'regions': ['FR'],
            'prefer_local': True,
            'fallback_regions': ['+44', '+1']
        },
        '+34': {  # Spain
            'regions': ['ES'],
            'prefer_local': True,
            'fallback_regions': ['+1']
        },
    }
    
    def __init__(self, db: Optional[Prisma] = None):
        """Initialize the enhanced phone number service.
        
        Args:
            db: Optional Prisma database client. If not provided, a new one will be created.
        """
        self.db = db or Prisma()
        if not self.db.is_connected():
            self.db.connect()
        self.current_environment = get_current_environment()
        logger.info(f"PhoneNumberService initialized for {self.current_environment} environment")
    
    def extract_country_code(self, phone_number: str) -> str:
        """Extract country code from phone number with improved US detection."""
        if not phone_number:
            return '+1'  # Default fallback
            
        # Remove any non-digit characters except +
        clean_number = ''.join(c for c in phone_number if c.isdigit() or c == '+')
        
        # Handle invalid patterns like ++123456789
        if clean_number.count('+') > 1 or (clean_number.startswith('+') and len(clean_number.replace('+', '')) < 7):
            return '+1'  # Default fallback for invalid patterns
        
        if clean_number.startswith('+'):
            # Check known country codes first (longest first to avoid partial matches)
            known_codes = sorted(self.COUNTRY_PHONE_PREFERENCES.keys(), key=len, reverse=True)
            for code in known_codes:
                if clean_number.startswith(code):
                    return code
            
            # For unknown codes, try common patterns
            # Check for specific patterns like +86 (China), +82 (Korea), etc.
            common_codes = ['+86', '+82', '+81', '+91', '+7', '+61', '+81', '+20', '+27']
            for code in common_codes:
                if clean_number.startswith(code):
                    return code
            
            # For truly unknown codes, try to extract first 1-3 digits after +
            # But be more intelligent about it
            if len(clean_number) >= 4:  # At least +123
                for length in [3, 2, 1]:
                    potential_code = clean_number[:length + 1]  # +1 for the +
                    # Simple heuristic: if next character is digit and makes sense as phone number
                    if len(clean_number) > length + 1:
                        remaining = clean_number[length + 1:]
                        # Phone numbers should be at least 7 digits after country code
                        if len(remaining) >= 7:
                            return potential_code
                    
            return '+1'  # Default fallback
        else:
            # Try to infer if it's a US number without country code
            digits_only = ''.join(c for c in phone_number if c.isdigit())
            
            # US numbers: 10 digits, first digit 2-9
            if len(digits_only) == 10 and digits_only[0] in '23456789':
                return '+1'
            
            # US numbers with 1 prefix: 11 digits starting with 1
            if len(digits_only) == 11 and digits_only.startswith('1'):
                return '+1'
                
            # Check common US patterns like ************
            import re
            us_pattern = re.match(r'^[2-9]\d{2}[-.]?\d{3}[-.]?\d{4}$', phone_number.strip())
            if us_pattern:
                return '+1'
            
            return '+1'  # Default fallback
    
    def get_phone_number_for_sms(self, target_phone: str, prefer_local: bool = True) -> Optional[str]:
        """
        Get best phone number for SMS based on:
        1. Target country code
        2. SMS capability verified by Twilio
        3. Environment matching (dev/prod)
        4. Priority ranking
        5. Usage statistics (load balancing)
        """
        country_code = self.extract_country_code(target_phone)
        
        try:
            # Query for SMS-capable numbers for target country
            numbers = self.db.twiliophonenumber.find_many(
                where={
                    'country_code': country_code,
                    'is_active': True,
                    'twilio_verified': True,
                    'environment': self.current_environment,  # Filter by environment
                },
                order=[
                    {'priority': 'desc'},
                    {'created_at': 'asc'}  # Prefer older numbers for stability
                ]
            )
            
            # Filter for SMS capability
            sms_numbers = []
            for number in numbers:
                capabilities = json.loads(number.capabilities) if isinstance(number.capabilities, str) else number.capabilities
                if capabilities.get('sms', False):
                    sms_numbers.append(number)
            
            if sms_numbers:
                selected = self.select_best_number_by_usage(sms_numbers, 'sms')
                if selected:
                    logger.info(f"Selected SMS number {selected} for country code {country_code} in {self.current_environment} environment")
                    return selected
            
            # Try fallback without environment filter if no environment-matched numbers found
            logger.info(f"No {self.current_environment} SMS numbers found for {country_code}, trying without environment filter")
            
            # Query again without environment filter
            all_numbers = self.db.twiliophonenumber.find_many(
                where={
                    'country_code': country_code,
                    'is_active': True,
                    'twilio_verified': True,
                },
                order=[
                    {'priority': 'desc'},
                    {'created_at': 'asc'}
                ]
            )
            
            # Filter for SMS capability
            all_sms_numbers = []
            for number in all_numbers:
                capabilities = json.loads(number.capabilities) if isinstance(number.capabilities, str) else number.capabilities
                if capabilities.get('sms', False):
                    all_sms_numbers.append(number)
            
            if all_sms_numbers:
                selected = self.select_best_number_by_usage(all_sms_numbers, 'sms')
                if selected:
                    logger.warning(f"Using non-environment-matched SMS number {selected} for {country_code} (no {self.current_environment} numbers available)")
                    return selected
            
            # Fallback strategy using regional preferences
            if country_code != '+1':
                preferences = self.COUNTRY_PHONE_PREFERENCES.get(country_code, {})
                fallback_regions = preferences.get('fallback_regions', ['+1'])
                
                for fallback_code in fallback_regions:
                    logger.info(f"No SMS numbers found for {country_code}, trying fallback {fallback_code}")
                    fallback_numbers = self.db.twiliophonenumber.find_many(
                        where={
                            'country_code': fallback_code,
                            'is_active': True,
                            'twilio_verified': True,
                            'environment': self.current_environment,  # Filter by environment
                        },
                        order=[
                            {'priority': 'desc'},
                            {'created_at': 'asc'}
                        ]
                    )
                    
                    fallback_sms = []
                    for number in fallback_numbers:
                        capabilities = json.loads(number.capabilities) if isinstance(number.capabilities, str) else number.capabilities
                        if capabilities.get('sms', False):
                            fallback_sms.append(number)
                    
                    if fallback_sms:
                        selected = self.select_best_number_by_usage(fallback_sms, 'sms')
                        if selected:
                            logger.info(f"Selected fallback SMS number {selected} for country code {fallback_code}")
                            return selected
            
            logger.error("No SMS-capable phone numbers found")
            return None
            
        except Exception as e:
            logger.error(f"Error selecting SMS phone number: {e}")
            return None
    
    def get_phone_number_for_voice(self, target_phone: str, prefer_local: bool = True) -> Optional[str]:
        """
        Get best phone number for Voice based on:
        1. Target country code  
        2. Voice capability verified by Twilio
        3. Environment matching (dev/prod)
        4. Priority ranking
        5. Usage statistics (load balancing)
        """
        country_code = self.extract_country_code(target_phone)
        
        try:
            # Query for Voice-capable numbers for target country
            numbers = self.db.twiliophonenumber.find_many(
                where={
                    'country_code': country_code,
                    'is_active': True,
                    'twilio_verified': True,
                    'environment': self.current_environment,  # Filter by environment
                },
                order=[
                    {'priority': 'desc'},
                    {'created_at': 'asc'}
                ]
            )
            
            # Filter for Voice capability
            voice_numbers = []
            for number in numbers:
                capabilities = json.loads(number.capabilities) if isinstance(number.capabilities, str) else number.capabilities
                if capabilities.get('voice', False):
                    voice_numbers.append(number)
            
            if voice_numbers:
                selected = self.select_best_number_by_usage(voice_numbers, 'voice')
                if selected:
                    logger.info(f"Selected Voice number {selected} for country code {country_code} in {self.current_environment} environment")
                    return selected
            
            # Determine if we should try fallbacks:
            # - Always try fallbacks if prefer_local=False
            # - Try fallbacks if prefer_local=True but target country has NO numbers at all (not just no voice-capable numbers)
            # - If we have numbers for the country but none have voice capability, don't fallback (return None)
            should_fallback = not prefer_local or len(numbers) == 0
            
            # Fallback strategy using regional preferences
            if should_fallback and country_code != '+1':
                preferences = self.COUNTRY_PHONE_PREFERENCES.get(country_code, {})
                fallback_regions = preferences.get('fallback_regions', ['+1'])
                
                for fallback_code in fallback_regions:
                    logger.info(f"No Voice numbers found for {country_code}, trying fallback {fallback_code}")
                    fallback_numbers = self.db.twiliophonenumber.find_many(
                        where={
                            'country_code': fallback_code,
                            'is_active': True,
                            'twilio_verified': True,
                            'environment': self.current_environment,  # Filter by environment
                        },
                        order=[
                            {'priority': 'desc'},
                            {'created_at': 'asc'}
                        ]
                    )
                    
                    fallback_voice = []
                    for number in fallback_numbers:
                        capabilities = json.loads(number.capabilities) if isinstance(number.capabilities, str) else number.capabilities
                        if capabilities.get('voice', False):
                            fallback_voice.append(number)
                    
                    if fallback_voice:
                        selected = self.select_best_number_by_usage(fallback_voice, 'voice')
                        if selected:
                            logger.info(f"Selected fallback Voice number {selected} for country code {fallback_code}")
                            return selected
            
            logger.error("No Voice-capable phone numbers found")
            return None
            
        except Exception as e:
            logger.error(f"Error selecting Voice phone number: {e}")
            return None
    
    def select_best_number_by_usage(self, numbers: List[TwilioPhoneNumber], capability: str) -> Optional[str]:
        """Select number with lowest recent usage for load balancing."""
        if not numbers:
            return None
            
        # Get usage stats for the last 7 days
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        week_ago = today - timedelta(days=7)
        
        best_number = None
        lowest_usage = float('inf')
        
        for number in numbers:
            try:
                # Get usage count for this number in the last 7 days
                usage_records = self.db.phonenumberusage.find_many(
                    where={
                        'phone_number_id': number.id,
                        'date': {'gte': week_ago, 'lte': today}
                    }
                )
                
                # Sum usage based on capability type
                usage_count = 0
                for record in usage_records:
                    if capability == 'sms':
                        usage_count += record.sms_count
                    elif capability == 'voice':
                        usage_count += record.voice_count
                
                # Select number with lowest usage
                # In case of equal usage, preserve priority order (numbers are already ordered by priority desc)
                if usage_count < lowest_usage:
                    lowest_usage = usage_count
                    best_number = number
                elif usage_count == lowest_usage and best_number is None:
                    # First number with this usage level
                    lowest_usage = usage_count
                    best_number = number
                    
            except Exception as e:
                logger.warning(f"Error getting usage stats for number {number.phone_number}: {e}")
                # If we can't get usage stats, consider this number with 0 usage
                if lowest_usage > 0:
                    lowest_usage = 0
                    best_number = number
        
        return best_number.phone_number if best_number else numbers[0].phone_number
    
    def track_phone_number_usage(self, phone_number: str, capability: str, success: bool = True) -> None:
        """Track usage statistics for a phone number."""
        try:
            # Find the phone number record
            number_record = self.db.twiliophonenumber.find_first(
                where={'phone_number': phone_number}
            )
            
            if not number_record:
                logger.warning(f"Phone number {phone_number} not found for usage tracking")
                return
            
            # Get today's date as datetime (for Prisma compatibility)
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            
            # Try to find existing usage record for today
            usage_record = self.db.phonenumberusage.find_first(
                where={
                    'phone_number_id': number_record.id,
                    'date': today
                }
            )
            
            if usage_record:
                # Update existing record
                update_data = {}
                if capability == 'sms':
                    update_data['sms_count'] = usage_record.sms_count + 1
                elif capability == 'voice':
                    update_data['voice_count'] = usage_record.voice_count + 1
                
                if success:
                    update_data['success_count'] = usage_record.success_count + 1
                else:
                    update_data['failure_count'] = usage_record.failure_count + 1
                
                self.db.phonenumberusage.update(
                    where={'id': usage_record.id},
                    data=update_data
                )
            else:
                # Create new usage record
                create_data = {
                    'phone_number_id': number_record.id,
                    'date': today,
                    'sms_count': 1 if capability == 'sms' else 0,
                    'voice_count': 1 if capability == 'voice' else 0,
                    'success_count': 1 if success else 0,
                    'failure_count': 0 if success else 1,
                }
                
                self.db.phonenumberusage.create(data=create_data)
            
            logger.debug(f"Tracked {capability} usage for {phone_number}, success: {success}")
            
        except Exception as e:
            logger.error(f"Error tracking usage for {phone_number}: {e}")
    
    def get_usage_statistics(self, days: int = 7) -> List[Dict[str, Any]]:
        """Get enhanced usage statistics including CallRecord data for all phone numbers over specified days."""
        try:
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_date = today - timedelta(days=days)
            
            # Get all usage records in date range
            usage_records = self.db.phonenumberusage.find_many(
                where={
                    'date': {'gte': start_date, 'lte': today}
                },
                include={'phone_number': True}
            )
            
            # Get all call records in date range to extract phone numbers used
            call_records = self.db.callrecord.find_many(
                where={
                    'start_time': {'gte': start_date, 'lte': today}
                },
                include={
                    'student': {
                        'include': {'users': True}
                    }
                }
            )
            
            # Aggregate by phone number
            stats = {}
            
            # First, process usage records (existing logic)
            for record in usage_records:
                phone_number = record.phone_number.phone_number
                if phone_number not in stats:
                    stats[phone_number] = {
                        'phone_number': phone_number,
                        'country_code': record.phone_number.country_code,
                        'country_name': record.phone_number.country_name,
                        'total_sms': 0,
                        'total_voice': 0,
                        'total_success': 0,
                        'total_failure': 0,
                        'days_active': 0,
                        # Enhanced fields from CallRecord
                        'total_call_duration': 0,
                        'avg_call_duration': 0.0,
                        'total_answered_calls': 0,
                        'total_failed_calls': 0,
                        'call_success_rate': 0.0,
                        'recent_calls': []
                    }
                
                stats[phone_number]['total_sms'] += record.sms_count
                stats[phone_number]['total_voice'] += record.voice_count
                stats[phone_number]['total_success'] += record.success_count
                stats[phone_number]['total_failure'] += record.failure_count
                stats[phone_number]['days_active'] += 1
            
            # Process call records to extract phone numbers used and enhance stats
            for call_record in call_records:
                try:
                    # Extract the phone number that was actually used for this call
                    # We need to determine which Twilio number was used for this call
                    if call_record.student and call_record.student.users:
                        student_phone = call_record.student.users[0].phone_number
                        if student_phone:
                            # Get the phone number that would have been selected for this call
                            from_number = self.get_phone_number_for_voice(student_phone)
                            
                            if from_number:
                                # Initialize stats for this phone number if not exists
                                if from_number not in stats:
                                    # Get phone number details
                                    phone_record = self.db.twiliophonenumber.find_first(
                                        where={'phone_number': from_number}
                                    )
                                    if phone_record:
                                        stats[from_number] = {
                                            'phone_number': from_number,
                                            'country_code': phone_record.country_code,
                                            'country_name': phone_record.country_name,
                                            'total_sms': 0,
                                            'total_voice': 0,
                                            'total_success': 0,
                                            'total_failure': 0,
                                            'days_active': 0,
                                            'total_call_duration': 0,
                                            'avg_call_duration': 0.0,
                                            'total_answered_calls': 0,
                                            'total_failed_calls': 0,
                                            'call_success_rate': 0.0,
                                            'recent_calls': []
                                        }
                                
                                # Enhance stats with call record data
                                if from_number in stats:
                                    # Add call duration (convert to minutes)
                                    duration_minutes = (call_record.duration or 0) / 60.0
                                    stats[from_number]['total_call_duration'] += duration_minutes
                                    
                                    # Track call success/failure
                                    if call_record.status in ['COMPLETED', 'IN_PROGRESS']:
                                        stats[from_number]['total_answered_calls'] += 1
                                    elif call_record.status == 'FAILED':
                                        stats[from_number]['total_failed_calls'] += 1
                                    
                                    # Add to recent calls (limit to 5 most recent)
                                    call_info = {
                                        'call_sid': call_record.twilio_call_sid,
                                        'status': call_record.status.value if hasattr(call_record.status, 'value') else str(call_record.status),
                                        'duration': call_record.duration,
                                        'start_time': call_record.start_time.isoformat() if call_record.start_time else None,
                                        'student_id': call_record.student_id
                                    }
                                    
                                    stats[from_number]['recent_calls'].append(call_info)
                
                except Exception as call_error:
                    logger.warning(f"Error processing call record {call_record.twilio_call_sid}: {call_error}")
                    continue
            
            # Calculate derived metrics for each phone number
            for phone_number, stat in stats.items():
                # Calculate average call duration
                total_calls = stat['total_answered_calls'] + stat['total_failed_calls']
                if total_calls > 0:
                    stat['avg_call_duration'] = stat['total_call_duration'] / total_calls
                
                # Calculate call success rate
                if total_calls > 0:
                    stat['call_success_rate'] = (stat['total_answered_calls'] / total_calls) * 100
                
                # Sort recent calls by start time (most recent first) and limit to 5
                stat['recent_calls'].sort(key=lambda x: x['start_time'] or '', reverse=True)
                stat['recent_calls'] = stat['recent_calls'][:5]
                
                # Round values for cleaner display
                stat['total_call_duration'] = round(stat['total_call_duration'], 1)
                stat['avg_call_duration'] = round(stat['avg_call_duration'], 1)
                stat['call_success_rate'] = round(stat['call_success_rate'], 1)
            
            return list(stats.values())
            
        except Exception as e:
            logger.error(f"Error getting usage statistics: {e}")
            return []
    
    def update_priority(self, phone_number_id: str, priority: int) -> bool:
        """Update priority for a phone number."""
        try:
            self.db.twiliophonenumber.update(
                where={'id': phone_number_id},
                data={'priority': priority}
            )
            logger.info(f"Updated priority for phone number ID {phone_number_id} to {priority}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating priority: {e}")
            return False
    
    # ============================================================================
    # LEGACY COMPATIBILITY METHODS (from original PhoneNumberService)
    # ============================================================================
    
    def get_phone_number_for_country(self, target_phone: str) -> Optional[str]:
        """
        Legacy method for backwards compatibility.
        
        This method maintains compatibility with the original PhoneNumberService
        by delegating to the enhanced SMS-capable number selection.
        
        Args:
            target_phone: Target phone number in international format
            
        Returns:
            Selected Twilio phone number or None if no suitable number found
        """
        return self.get_phone_number_for_sms(target_phone)
    
    def get_phone_number_for_voice_legacy(self, target_phone: str) -> Optional[str]:
        """
        Legacy method for backwards compatibility with enhanced voice selection.
        
        Args:
            target_phone: Target phone number in international format
            
        Returns:
            Selected Twilio phone number or None if no suitable number found
        """
        return self.get_phone_number_for_voice(target_phone)
    
    def list_available_numbers(self) -> List[Dict[str, Any]]:
        """
        List all configured Twilio phone numbers (legacy compatibility method).
        
        Returns:
            List of phone number records with their details
        """
        try:
            phone_numbers = self.db.twiliophonenumber.find_many(
                order=[{'country_code': 'asc'}, {'phone_number': 'asc'}]
            )
            
            result = []
            for phone in phone_numbers:
                capabilities = json.loads(phone.capabilities) if isinstance(phone.capabilities, str) else phone.capabilities
                result.append({
                    'id': phone.id,
                    'phone_number': phone.phone_number,
                    'country_code': phone.country_code,
                    'country_name': phone.country_name,
                    'is_active': phone.is_active,
                    'capabilities': capabilities,
                    'twilio_verified': phone.twilio_verified,
                    'priority': phone.priority,
                    'created_at': phone.created_at.isoformat() if phone.created_at else None,
                    'updated_at': phone.updated_at.isoformat() if phone.updated_at else None
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error listing phone numbers: {str(e)}")
            return []
    
    def add_phone_number(
        self,
        phone_number: str,
        country_code: str,
        country_name: str,
        sms_capable: bool = True,
        voice_capable: bool = True,
        mms_capable: bool = False
    ) -> Optional[TwilioPhoneNumber]:
        """
        Add a new Twilio phone number to the database (legacy compatibility).
        
        Args:
            phone_number: The Twilio phone number in E.164 format
            country_code: The country code (e.g., "+1", "+55")
            country_name: The country name (e.g., "United States", "Brazil")
            sms_capable: Whether the number supports SMS
            voice_capable: Whether the number supports voice calls
            mms_capable: Whether the number supports MMS
            
        Returns:
            The created phone number record or None if failed
        """
        try:
            capabilities = json.dumps({
                'sms': sms_capable, 
                'voice': voice_capable,
                'mms': mms_capable
            })
            
            phone_record = self.db.twiliophonenumber.create(
                data={
                    'phone_number': phone_number,
                    'country_code': country_code,
                    'country_name': country_name,
                    'capabilities': capabilities,
                    'is_active': True,
                    'priority': 0,  # Default priority
                    'twilio_verified': False  # Will be verified during sync
                }
            )
            
            logger.info(f"Added new Twilio phone number: {phone_number} for {country_name}")
            return phone_record
            
        except Exception as e:
            logger.error(f"Error adding phone number: {str(e)}")
            return None
    
    def update_phone_number(
        self,
        phone_number_id: str,
        is_active: Optional[bool] = None,
        capabilities: Optional[Dict[str, bool]] = None,
        priority: Optional[int] = None,
        twilio_verified: Optional[bool] = None
    ) -> Optional[TwilioPhoneNumber]:
        """
        Update an existing Twilio phone number (enhanced legacy compatibility).
        
        Args:
            phone_number_id: The ID of the phone number to update
            is_active: Whether the number is active
            capabilities: SMS, voice, and MMS capabilities
            priority: Priority ranking for number selection
            twilio_verified: Whether the number is verified in Twilio
            
        Returns:
            The updated phone number record or None if failed
        """
        try:
            update_data = {}
            
            if is_active is not None:
                update_data['is_active'] = is_active
            
            if capabilities is not None:
                update_data['capabilities'] = json.dumps(capabilities)
            
            if priority is not None:
                update_data['priority'] = priority
            
            if twilio_verified is not None:
                update_data['twilio_verified'] = twilio_verified
            
            if not update_data:
                return None
            
            phone_record = self.db.twiliophonenumber.update(
                where={'id': phone_number_id},
                data=update_data
            )
            
            logger.info(f"Updated Twilio phone number: {phone_record.phone_number}")
            return phone_record
            
        except Exception as e:
            logger.error(f"Error updating phone number: {str(e)}")
            return None
    
    def delete_phone_number(self, phone_number_id: str) -> bool:
        """
        Delete a Twilio phone number from the database (legacy compatibility).
        
        Args:
            phone_number_id: The ID of the phone number to delete
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            self.db.twiliophonenumber.delete(
                where={'id': phone_number_id}
            )
            logger.info(f"Deleted Twilio phone number with ID: {phone_number_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting phone number: {str(e)}")
            return False
    
    # Legacy compatibility methods
    def get_phone_number_for_country(self, target_phone: str) -> Optional[str]:
        """
        Legacy method: Get phone number for a specific country (defaults to SMS capability).
        
        Args:
            target_phone: The target phone number to extract country from
            
        Returns:
            Phone number string or None if not found
        """
        return self.get_phone_number_for_sms(target_phone)
    
    def get_phone_number_for_voice_legacy(self, target_phone: str) -> Optional[str]:
        """
        Legacy method: Get phone number for voice capability.
        
        Args:
            target_phone: The target phone number to extract country from
            
        Returns:
            Phone number string or None if not found
        """
        return self.get_phone_number_for_voice(target_phone)
    
    def __del__(self):
        """Clean up database connection."""
        if hasattr(self, 'db') and self.db.is_connected():
            self.db.disconnect()