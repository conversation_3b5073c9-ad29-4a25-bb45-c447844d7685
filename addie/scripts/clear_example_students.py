#!/usr/bin/env python3
"""
Clear all students with @example.com email addresses.

This script removes test/generated students that typically use @example.com email domains.
Processes students in batches to handle large datasets efficiently.
"""

import sys
import os
import time
import logging
from typing import List, Optional

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from prisma import Prisma

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BATCH_SIZE = 10  # Process 10 students per batch


def count_example_students(db: Prisma) -> int:
    """Count total number of @example.com students."""
    logger.debug("Counting @example.com students...")
    start_time = time.time()
    
    count = db.user.count(
        where={
            'email': {
                'endsWith': '@example.com'
            },
            'role': 'STUDENT',
            'students': {
                'some': {}
            }
        }
    )
    
    elapsed = time.time() - start_time
    logger.debug(f"Count query completed in {elapsed:.2f}s")
    return count


def find_example_students_batch(db: Prisma, skip: int = 0, take: int = BATCH_SIZE) -> List[dict]:
    """Find a batch of users with @example.com email addresses who are students."""
    logger.debug(f"Querying batch: skip={skip}, take={take}")
    
    # Query User table directly with student relationship
    users = db.user.find_many(
        where={
            'email': {
                'endsWith': '@example.com'
            },
            'role': 'STUDENT',
            'students': {
                'some': {}
            }
        },
        include={
            'students': True
        },
        skip=skip,
        take=take
    )
    
    logger.debug(f"Found {len(users)} users in batch")
    
    # Convert to simplified format
    students_with_records = []
    for user in users:
        if user.students:
            students_with_records.append({
                'user_id': user.id,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'student_ids': [s.id for s in user.students]
            })
    
    return students_with_records


def delete_student_data(db: Prisma, student_id: str) -> None:
    """Delete all data associated with a student."""
    logger.debug(f"Deleting data for student {student_id}")
    
    # Delete student record (cascading deletes will handle related data)
    db.student.delete(where={'id': student_id})


def delete_user(db: Prisma, user_id: str) -> None:
    """Delete the user record."""
    logger.debug(f"Deleting user {user_id}")
    db.user.delete(where={'id': user_id})


def process_batch(db: Prisma, batch: List[dict], batch_num: int) -> int:
    """Process a batch of students for deletion."""
    logger.info(f"Processing batch {batch_num} ({len(batch)} students)")
    
    deleted_count = 0
    for student in batch:
        try:
            # Delete all student records for this user
            for student_id in student['student_ids']:
                delete_student_data(db, student_id)
            
            # Delete the user
            delete_user(db, student['user_id'])
            
            deleted_count += 1
            logger.info(f"  ✓ Deleted {student['email']} ({student['first_name']} {student['last_name']})")
            
        except Exception as e:
            logger.error(f"  ✗ Failed to delete {student['email']}: {e}")
    
    return deleted_count


def main():
    """Main function to clear example students in batches."""
    logger.info("Starting cleanup of @example.com students")
    
    db = Prisma()
    db.connect()
    
    try:
        # Count total students
        total_count = count_example_students(db)
        
        if total_count == 0:
            logger.info("No @example.com students found")
            return
        
        logger.info(f"Found {total_count} @example.com students to delete")
        
        # Show first batch as preview
        preview_batch = find_example_students_batch(db, skip=0, take=min(5, total_count))
        logger.info("Preview of students to delete:")
        for student in preview_batch:
            logger.info(f"  - {student['email']} ({student['first_name']} {student['last_name']})")
        if total_count > 5:
            logger.info(f"  ... and {total_count - 5} more")
        
        # Confirm deletion
        response = input(f"\nAre you sure you want to delete all {total_count} students? (yes/no): ")
        if response.lower() != 'yes':
            logger.info("Deletion cancelled")
            return
        
        # Process in batches
        total_deleted = 0
        batch_num = 1
        
        while True:
            # Always fetch from skip=0 since we're deleting records
            batch = find_example_students_batch(db, skip=0, take=BATCH_SIZE)
            
            if not batch:
                break
            
            deleted_in_batch = process_batch(db, batch, batch_num)
            total_deleted += deleted_in_batch
            
            logger.info(f"Batch {batch_num} completed: {deleted_in_batch}/{len(batch)} deleted")
            
            # Small delay between batches to reduce database load
            if len(batch) == BATCH_SIZE:  # More batches expected
                time.sleep(0.5)
            
            batch_num += 1
        
        logger.info(f"Cleanup completed. Successfully deleted {total_deleted} out of {total_count} students")
        
        # Verify cleanup
        remaining = count_example_students(db)
        if remaining > 0:
            logger.warning(f"Warning: {remaining} @example.com students still remain")
        else:
            logger.info("✓ All @example.com students successfully removed")
        
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")
        raise
    
    finally:
        db.disconnect()


if __name__ == '__main__':
    main()