#!/usr/bin/env python
"""
CLI script to test the majors_agent with real student data

Usage:
    python addie/scripts/test_majors_agent.py --student-id <student_id>
    python addie/scripts/test_majors_agent.py --list-students
"""

import click
import alog
import sys
import os
from typing import List, Dict, Any

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from addie.lib import prisma_client
from addie.majors_agent.agent import majors_agent
from addie.services.major_recommendations import MajorRecommendationService


@click.command()
@click.option('--student-id', type=str, help='Student ID to generate recommendations for')
@click.option('--list-students', is_flag=True, help='List students with OCEAN scores')
@click.option('--save-to-db', is_flag=True, help='Save recommendations to database')
@click.option('--check-existing', is_flag=True, help='Check for existing recommendations in database')
@click.option('--limit', type=int, default=10, help='Limit number of recommendations to show')
def main(student_id: str, list_students: bool, save_to_db: bool, check_existing: bool, limit: int):
    """Test the majors_agent with real student data."""
    
    if list_students:
        list_students_with_ocean_scores()
        return
    
    if not student_id:
        click.echo("Please provide --student-id or use --list-students to see available students")
        return

    try:
        service = MajorRecommendationService()

        # Check for existing recommendations first
        if check_existing:
            click.echo(f"Checking existing recommendations for student: {student_id}")
            click.echo("=" * 60)

            existing_recs = service.get_recommendations(student_id, limit=limit)
            if existing_recs:
                click.echo(f"Found {len(existing_recs)} existing recommendations:")
                click.echo()
                for i, rec in enumerate(existing_recs, 1):
                    like_status = ""
                    if rec.get('liked'):
                        like_status = " 👍"
                    elif rec.get('disliked'):
                        like_status = " 👎"
                    click.echo(f"{i:2d}. {rec['major']:<25} {rec['match_percent']:>6.1f}%{like_status}")
                click.echo()
                click.echo("Note: These were automatically generated when OCEAN scores were calculated.")
                click.echo(f"Ocean Score ID: {existing_recs[0].get('ocean_score_id', 'N/A')}")
            else:
                click.echo("No existing recommendations found.")
                click.echo("Recommendations are automatically generated when OCEAN scores are calculated.")
            return

        # Test the majors_agent directly
        click.echo(f"Generating major recommendations for student: {student_id}")
        click.echo("=" * 60)
        click.echo("Note: In production, this happens automatically when OCEAN scores are calculated.")
        click.echo()

        agent_response = majors_agent(student_id)
        recommendations = agent_response['recommendations']
        summary = agent_response.get('summary', '')

        if summary:
            click.echo("AI SUMMARY:")
            click.echo(summary)
            click.echo()

        click.echo(f"Generated {len(recommendations)} recommendations:")
        click.echo()
        
        # Display top recommendations
        display_limit = min(limit, len(recommendations))
        for i, rec in enumerate(recommendations[:display_limit], 1):
            click.echo(f"{i:2d}. {rec['major']:<25} {rec['match_percent']:>6.1f}%")
        
        if len(recommendations) > display_limit:
            click.echo(f"... and {len(recommendations) - display_limit} more")
        
        # Optionally save to database
        if save_to_db:
            click.echo()
            click.echo("Saving recommendations to database...")
            success = service.generate_and_save_recommendations(student_id)
            
            if success:
                click.echo("✓ Successfully saved recommendations to database")
                
                # Verify by retrieving from database
                stored_recs = service.get_recommendations(student_id, limit=5)
                click.echo()
                click.echo("Top 5 stored recommendations:")
                for i, rec in enumerate(stored_recs, 1):
                    click.echo(f"{i}. {rec['major']:<25} {rec['match_percent']:>6.1f}%")
            else:
                click.echo("✗ Failed to save recommendations to database")
        
        click.echo()
        click.echo("=" * 60)
        click.echo("Test completed successfully!")
        
    except ValueError as e:
        click.echo(f"Error: {str(e)}", err=True)
        click.echo("Make sure the student has OCEAN scores calculated.", err=True)
    except Exception as e:
        click.echo(f"Unexpected error: {str(e)}", err=True)
        import traceback
        traceback.print_exc()


def list_students_with_ocean_scores():
    """List all students who have OCEAN scores."""
    try:
        prisma = prisma_client()
        
        students = prisma.student.find_many(
            where={
                "ocean_scores": {
                    "is_not": None
                }
            },
            include={
                "ocean_scores": True,
                "users": True
            },
            take=20  # Limit to first 20 students
        )
        
        if not students:
            click.echo("No students found with OCEAN scores.")
            click.echo("Run OCEAN score calculation first using the BFI workflow.")
            return
        
        click.echo(f"Found {len(students)} students with OCEAN scores:")
        click.echo()
        click.echo(f"{'Student ID':<20} {'Name':<30} {'OCEAN Scores'}")
        click.echo("-" * 80)
        
        for student in students:
            # Get student name from users
            name = "Unknown"
            if student.users and len(student.users) > 0:
                user = student.users[0]
                name = f"{user.first_name or ''} {user.last_name or ''}".strip()
                if not name:
                    name = user.email or "Unknown"
            
            # Format OCEAN scores
            ocean = student.ocean_scores
            if ocean:
                scores = f"A:{ocean.agreeableness:.1f} C:{ocean.conscientiousness:.1f} E:{ocean.extroversion:.1f} N:{ocean.neuroticism:.1f} O:{ocean.openness_to_experience:.1f}"
            else:
                scores = "No scores"
            
            click.echo(f"{student.id:<20} {name:<30} {scores}")
        
        click.echo()
        click.echo("Use --student-id <ID> to generate recommendations for a specific student.")
        
    except Exception as e:
        click.echo(f"Error listing students: {str(e)}", err=True)


if __name__ == "__main__":
    main()
