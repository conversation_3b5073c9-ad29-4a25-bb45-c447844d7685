#!/usr/bin/env python
from fastapi import APIRouter, Request, Response, Depends, Header, HTTPException
from typing import Dict, Any
import logging
from twilio.request_validator import RequestValidator

# Import needed dependencies
from addie.lib import prisma_client
from addie.api.auth import verify_api_key
from addie.sms_agent.agent import process_sms_message, sms_unstructured_agent, send_conversation_initiation_sms, find_student_sms_workflow, get_priority_sms_workflow
from addie import settings
from prisma.enums import WorkflowType
from typing import Optional, Any

# Set up logger
alog = logging.getLogger(__name__)

# Create router
router = APIRouter()



async def route_sms_to_agent(
    prisma: Any,
    student_id: str,
    message_text: str,
    from_number: str,
    workflow_id: str,
    sms_workflow: Optional[Any] = None
) -> str:
    """
    Routes an SMS message to the appropriate agent based on workflow type.
    This helper function makes testing easier by separating the routing logic.

    Args:
        prisma: Prisma client instance
        student_id: ID of the student
        message_text: Text of the SMS message
        from_number: Phone number the message is from
        workflow_id: ID of the workflow
        sms_workflow: Optional StudentWorkflow object

    Returns:
        Response text from the appropriate agent
    """
    # Determine if this is an unstructured workflow
    workflow = prisma.workflow.find_unique(
        where={"id": workflow_id}
    )

    if workflow and workflow.workflow_type == WorkflowType.UNSTRUCTURED and sms_workflow and sms_workflow.mode == 'sms':
        # Use the unstructured SMS agent for unstructured workflows in SMS mode
        alog.info(f"Using unstructured SMS agent for workflow ID {workflow_id}")
        result = await sms_unstructured_agent(student_id, message_text, from_number, workflow_id)
        # Extract just the response text for SMS - metadata is for internal use only
        # Handle both dictionary and string return types for backward compatibility
        if isinstance(result, dict):
            return result["response"]
        else:
            # If result is a string, return it directly
            return result
    else:
        # Use the standard structured SMS agent for other workflows
        alog.info(f"Using structured SMS agent for workflow ID {workflow_id}")
        return process_sms_message(student_id, message_text, from_number, workflow_id)


def validate_twilio_request(request: Request, form_data: dict) -> bool:
    """
    Validate that the request is coming from Twilio.

    Args:
        request: The FastAPI request object
        form_data: The parsed form data from the request

    Returns:
        True if the request is valid, False otherwise
    """
    # Skip validation if Twilio credentials are not configured
    if not all([settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN]):
        alog.warning("Twilio credentials not configured. Skipping request validation.")
        return True

    # For testing, check for a special header to skip validation
    if request.headers.get("x-skip-twilio-validation") == "true":
        alog.warning("Skipping Twilio validation due to x-skip-twilio-validation header.")
        return True

    # For development/testing, you can disable validation based on environment
    if hasattr(settings, 'ENV') and settings.ENV in ["development", "test"]:
        alog.warning(f"{settings.ENV} environment detected. Skipping Twilio signature validation.")
        return True

    try:
        # log headers
        alog.info(alog.pformat(request.headers))
        # Get the Twilio signature from the request headers
        twilio_signature = request.headers.get("x-twilio-signature", "")

        # Get the full URL of the request, accounting for proxies
        # When behind a proxy, we need to reconstruct the URL using the X-Forwarded headers
        if request.headers.get("x-forwarded-host") and request.headers.get("x-forwarded-proto"):
            # Use the forwarded host and protocol
            forwarded_host = request.headers.get("x-forwarded-host")
            forwarded_proto = request.headers.get("x-forwarded-proto")
            forwarded_uri = request.headers.get("x-original-uri", request.url.path)
            url = f"{forwarded_proto}://{forwarded_host}{forwarded_uri}"
            alog.info(f"Using forwarded URL for Twilio validation: {url}")
        else:
            # Fall back to the request URL
            url = str(request.url)
            alog.info(f"Using direct URL for Twilio validation: {url}")

        # Use the form_data that was already parsed
        body = dict(form_data)

        # Create a validator with our auth token
        validator = RequestValidator(settings.TWILIO_AUTH_TOKEN)

        # Validate the request
        return validator.validate(url, body, twilio_signature)

    except Exception as e:
        alog.error(f"Error validating Twilio request: {str(e)}")
        return False


@router.post(
    "/api/sms/webhook",
    response_model={},
    name="sms_webhook",
)
async def sms_webhook(request: Request):
    """
    Webhook endpoint for Twilio SMS messages.

    This endpoint receives SMS messages from Twilio, processes them using the SMS agent,
    and returns a TwiML response with the agent's reply.
    """
    try:
        # Parse form data from the request
        form_data = await request.form()

        # Extract the phone number and message text
        from_number = form_data.get("From", "")
        message_text = form_data.get("Body", "")

        alog.info(f"Received SMS from {from_number}: {message_text}")

        # Validate the request is coming from Twilio
        if not validate_twilio_request(request, form_data):
            alog.error(f"Invalid Twilio request signature for {from_number}")
            return Response(
                content="<?xml version='1.0' encoding='UTF-8'?><Response><Message>Unauthorized request</Message></Response>",
                media_type="application/xml"
            )

        # Look up the student by phone number
        try:
            prisma = prisma_client()

            # Try to find the user by phone number
            user = None
            student_id = None

            try:
                user = prisma.user.find_first(
                    where={"phone_number": from_number}
                )
            except Exception as e:
                alog.warning(f"Error finding user by phone number: {str(e)}")

            if user:
                # Check if user is enabled before processing SMS
                if hasattr(user, "enabled") and user.enabled == False:
                    alog.warning(f"User {user.id} is disabled, sending account disabled message to {from_number}")

                    # Log the incoming message and outgoing response for disabled users
                    from addie.sms_agent.agent import log_sms_message_to_history
                    disabled_session_id = user.id
                    disabled_response_message = "Your account is currently disabled. Please contact your counselor, admin, or <EMAIL> to enable your account."

                    try:
                        # Log the incoming message from the user
                        log_sms_message_to_history(disabled_session_id, message_text, "human", "sms")
                        # Log the outgoing response to the user
                        log_sms_message_to_history(disabled_session_id, disabled_response_message, "ai", "sms")
                    except Exception as e:
                        alog.warning(f"Error logging disabled user SMS messages: {str(e)}")

                    return Response(
                        content=f"<?xml version='1.0' encoding='UTF-8'?><Response><Message>{disabled_response_message}</Message></Response>",
                        media_type="application/xml"
                    )

                # Try to find the student associated with this user
                try:
                    # Look for students related to this user
                    user_with_students = prisma.user.find_unique(
                        where={"id": user.id},
                        include={"students": True}
                    )

                    if user_with_students and user_with_students.students and len(user_with_students.students) > 0:
                        student = user_with_students.students[0]
                        student_id = student.id
                        alog.info(f"Found student {student_id} for user {user.id}")
                except Exception as e:
                    alog.warning(f"Error finding student for user {user.id}: {str(e)}")

            # If we still don't have a student ID, try a different approach
            if not student_id:
                alog.warning(f"No student found for phone number {from_number} using direct lookup")

                # For testing purposes, just get the first student in the database
                students = prisma.student.find_many(
                    take=1
                )

                if students and len(students) > 0:
                    student_id = students[0].id
                    alog.info(f"Using first student in database: {student_id}")

            if not student_id:
                alog.error(f"No student found for phone number {from_number}")
                return Response(
                    content="<?xml version='1.0' encoding='UTF-8'?><Response><Message>Sorry, we couldn't find your account. Please contact your counselor for assistance.</Message></Response>",
                    media_type="application/xml"
                )

            # Find active SMS conversation for this student using the priority function
            try:
                sms_workflow, user_id = get_priority_sms_workflow(student_id)

                if sms_workflow:
                    alog.info(f"Found priority SMS workflow {sms_workflow.id} (type: {sms_workflow.workflow.workflow_type}) for student {student_id}")
                    # Use the workflow ID from the active SMS conversation
                    workflow_id = sms_workflow.workflow_id
                else:
                    alog.warning(f"No SMS-ready workflows found for student {student_id}")
                    # Return a message indicating no active SMS conversation
                    return Response(
                        content="<?xml version='1.0' encoding='UTF-8'?><Response><Message>You don't have any assigned conversations. you <NAME_EMAIL>.</Message></Response>",
                        media_type="application/xml"
                    )
            except Exception as e:
                alog.error(f"Error finding active SMS conversation: {str(e)}")
                # Return a message indicating no active SMS conversation
                return Response(
                    content="<?xml version='1.0' encoding='UTF-8'?><Response><Message>You don't have any assigned conversations. you <NAME_EMAIL>.</Message></Response>",
                    media_type="application/xml"
                )
        except Exception as e:
            alog.error(f"Error looking up student: {str(e)}")
            return Response(
                content="<?xml version='1.0' encoding='UTF-8'?><Response><Message>Sorry, we encountered an error processing your message. Please try again later.</Message></Response>",
                media_type="application/xml"
            )

        # Process the message using the appropriate SMS agent based on workflow type
        if 'workflow_id' in locals():
            # If we found an active SMS conversation, pass the workflow ID
            alog.info(f"Processing SMS message with workflow ID {workflow_id}")

            # Call helper function to route to the appropriate agent
            response_text = await route_sms_to_agent(prisma, student_id, message_text, from_number, workflow_id, sms_workflow)
        else:
            # Otherwise, let the agent find the most recent workflow
            response_text = process_sms_message(student_id, message_text, from_number)

        # Return a TwiML response
        twiml_response = f"<?xml version='1.0' encoding='UTF-8'?><Response><Message>{response_text}</Message></Response>"
        return Response(content=twiml_response, media_type="application/xml")

    except Exception as e:
        alog.error(f"Error processing SMS webhook: {str(e)}")
        return Response(
            content="<?xml version='1.0' encoding='UTF-8'?><Response><Message>Sorry, we encountered an error processing your message. Please try again later.</Message></Response>",
            media_type="application/xml"
        )


@router.post(
    "/api/sms/initiate/{student_conversation_id}",
    response_model={},
    name="initiate_sms_conversation",
)
async def initiate_sms_conversation(student_conversation_id: str, api_key: str = Depends(verify_api_key)):
    """
    Initiate an SMS conversation with a student using a student conversation ID (StudentWorkflow.id).

    Args:
        student_conversation_id: The ID of the student conversation (StudentWorkflow.id)

    Returns:
        The welcome message and first question
    """
    # Use the new function from agent.py to handle SMS initiation
    return send_conversation_initiation_sms(student_conversation_id)


@router.post(
    "/api/sms/test",
    response_model={},
    name="sms_test",
)
async def sms_test(request: Request, api_key: str = Depends(verify_api_key)):
    """
    Test endpoint for SMS conversations that simulates SMS webhook functionality.

    This endpoint allows admin users to test SMS workflows through a web interface
    by simulating the SMS processing logic without requiring actual SMS messages.
    """
    try:
        # Parse JSON data from the request
        request_data = await request.json()

        student_id = request_data.get("student_id")
        user_id = request_data.get("user_id")
        message_text = request_data.get("message")
        session_id = request_data.get("session_id")

        alog.info(f"SMS Test: Received message from student {student_id}: {message_text}")

        if not all([student_id, user_id, message_text, session_id]):
            return {
                "success": False,
                "error": "Missing required fields: student_id, user_id, message, session_id"
            }

        # Look up the student and user
        try:
            prisma = prisma_client()

            # Get the student record
            student = prisma.student.find_unique(
                where={"id": student_id},
                include={"users": True}
            )

            if not student:
                return {
                    "success": False,
                    "error": "Student not found"
                }

            # Get the user record
            user = prisma.user.find_unique(where={"id": user_id})

            if not user:
                return {
                    "success": False,
                    "error": "User not found"
                }

            # Check if user is enabled
            if not user.enabled:
                return {
                    "success": True,
                    "response": "Your account is currently disabled. Please contact your counselor for assistance."
                }

            # Find active SMS workflow using the same logic as the webhook
            sms_workflow, user_id = get_priority_sms_workflow(student_id)

            if not sms_workflow:
                return {
                    "success": True,
                    "response": "You don't have any assigned conversations. you <NAME_EMAIL>."
                }

            # Use a fake phone number for testing
            fake_phone = f"test-{session_id}"

            # Process the message using the same logic as the webhook
            workflow_id = sms_workflow.workflow_id
            alog.info(f"SMS Test: Processing message with workflow ID {workflow_id}")
            response_text = await route_sms_to_agent(prisma, student_id, message_text, fake_phone, workflow_id, sms_workflow)

            return {
                "success": True,
                "response": response_text
            }

        except Exception as e:
            alog.error(f"SMS Test: Error processing message: {str(e)}")
            return {
                "success": False,
                "error": f"Error processing message: {str(e)}"
            }

    except Exception as e:
        alog.error(f"SMS Test: Error parsing request: {str(e)}")
        return {
            "success": False,
            "error": f"Error parsing request: {str(e)}"
        }


@router.post(
    "/api/sms/conversation-history",
    response_model={},
    name="sms_conversation_history",
)
async def sms_conversation_history(request: Request, api_key: str = Depends(verify_api_key)):
    """
    Get SMS conversation history for a student.

    This endpoint retrieves the existing conversation messages for a student's
    active SMS workflow to allow continuing conversations in the test interface.
    """
    try:
        request_data = await request.json()
        student_id = request_data.get("student_id")
        user_id = request_data.get("user_id")

        if not all([student_id, user_id]):
            return {
                "success": False,
                "error": "Missing required fields: student_id, user_id"
            }

        alog.info(f"SMS Test: Getting conversation history for student {student_id}")

        try:
            prisma = prisma_client()

            # Find active SMS workflow
            sms_workflow, workflow_user_id = get_priority_sms_workflow(student_id)

            if not sms_workflow:
                return {
                    "success": True,
                    "messages": [],
                    "workflow_info": None,
                    "message": "No active SMS workflow found"
                }

            # Get conversation messages using the correct session_id format: workflow_id-user_id
            session_id = f"{sms_workflow.workflow_id}-{user_id}"

            messages = prisma.messages.find_many(
                where={"session_id": session_id},
                order={"created_at": "asc"}
            )

            alog.info(f"SMS Test: Found {len(messages)} total messages for user {user_id}")

            # Convert messages to the format expected by the frontend
            formatted_messages = []
            for i, msg in enumerate(messages):
                try:
                    # The message field contains JSON with the actual message data
                    message_data = msg.message if isinstance(msg.message, dict) else {}

                    # Extract message type and content from the JSON structure
                    # The content is nested in the "data" field
                    data = message_data.get("data", {})
                    msg_type = data.get("type", "unknown")
                    msg_content = data.get("content", "")

                    # Skip tool messages for SMS display
                    if msg_type == "tool":
                        alog.info(f"SMS Test: Skipping tool message")
                        continue

                    # Only include human and ai messages
                    if msg_type in ["human", "ai"]:
                        formatted_messages.append({
                            "content": msg_content,
                            "type": msg_type,
                            "timestamp": msg.created_at.isoformat() if msg.created_at else None
                        })
                        alog.info(f"SMS Test: Added {msg_type} message")
                    else:
                        alog.info(f"SMS Test: Skipping message with type: {msg_type}")
                except Exception as e:
                    alog.warning(f"Could not parse message {msg.id}: {str(e)}")
                    continue

            # Get workflow name safely
            workflow_name = "Unknown"
            try:
                if hasattr(sms_workflow, 'workflow') and sms_workflow.workflow:
                    workflow_name = sms_workflow.workflow.name
                else:
                    # Load the workflow separately if not included
                    workflow = prisma.workflow.find_unique(where={"id": sms_workflow.workflow_id})
                    if workflow:
                        workflow_name = workflow.name
            except Exception as e:
                alog.warning(f"Could not get workflow name: {str(e)}")

            workflow_info = {
                "id": sms_workflow.id,
                "workflow_id": sms_workflow.workflow_id,
                "workflow_name": workflow_name,
                "mode": sms_workflow.mode,
                "status": sms_workflow.status
            }

            alog.info(f"SMS Test: Returning {len(formatted_messages)} formatted messages")

            return {
                "success": True,
                "messages": formatted_messages,
                "workflow_info": workflow_info
            }

        except Exception as e:
            alog.error(f"SMS Test: Error getting conversation history: {str(e)}")
            return {
                "success": False,
                "error": f"Error getting conversation history: {str(e)}"
            }

    except Exception as e:
        alog.error(f"SMS Test: Error parsing request: {str(e)}")
        return {
            "success": False,
            "error": f"Error parsing request: {str(e)}"
        }


@router.post(
    "/api/sms/test/clear",
    response_model={},
    name="sms_test_clear",
)
async def sms_test_clear(request: Request, api_key: str = Depends(verify_api_key)):
    """
    Clear SMS test session data and reset workflow progress.

    This endpoint:
    1. Clears conversation messages for the session
    2. Resets all workflow steps to completed=False for the student workflow
    3. Allows restarting the workflow from the beginning
    """
    try:
        request_data = await request.json()
        student_id = request_data.get("student_id")
        user_id = request_data.get("user_id")
        session_id = request_data.get("session_id")

        if not all([student_id, user_id, session_id]):
            return {
                "success": False,
                "error": "Missing required fields: student_id, user_id, session_id"
            }

        alog.info(f"SMS Test: Clearing session {session_id} for student {student_id}")

        try:
            prisma = prisma_client()

            # Find the active SMS workflow
            sms_workflow, workflow_user_id = get_priority_sms_workflow(student_id)

            if not sms_workflow:
                return {
                    "success": False,
                    "error": "No active SMS workflow found to clear"
                }

            # Clear conversation messages for this session
            session_id_for_messages = f"{sms_workflow.workflow_id}-{user_id}"
            deleted_messages = prisma.messages.delete_many(
                where={"session_id": session_id_for_messages}
            )
            alog.info(f"SMS Test: Deleted {deleted_messages} messages for session {session_id_for_messages}")

            # Reset all workflow steps for this student workflow to completed=False
            updated_steps = prisma.studentworkflowstep.update_many(
                where={"student_workflow_id": sms_workflow.id},
                data={"completed": False}
            )
            alog.info(f"SMS Test: Reset {updated_steps} workflow steps to incomplete")

            # Optionally reset the student workflow status to NOT_STARTED or IN_PROGRESS
            prisma.studentworkflow.update(
                where={"id": sms_workflow.id},
                data={"status": "NOT_STARTED"}
            )
            alog.info(f"SMS Test: Reset workflow {sms_workflow.id} status to NOT_STARTED")

            return {
                "success": True,
                "message": f"Session {session_id} cleared, {deleted_messages} messages deleted, {updated_steps} steps reset",
                "details": {
                    "messages_deleted": deleted_messages,
                    "steps_reset": updated_steps,
                    "workflow_id": sms_workflow.id
                }
            }

        except Exception as e:
            alog.error(f"SMS Test: Error clearing workflow data: {str(e)}")
            return {
                "success": False,
                "error": f"Error clearing workflow data: {str(e)}"
            }

    except Exception as e:
        alog.error(f"SMS Test: Error parsing clear request: {str(e)}")
        return {
            "success": False,
            "error": f"Error parsing clear request: {str(e)}"
        }
