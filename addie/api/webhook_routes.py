#!/usr/bin/env python
"""
Webhook routes for external integrations.

This module handles webhook requests from various external services.
"""

from fastapi import APIRouter, Request, HTTPException, Depends, Header
from typing import Dict, Any
import alog
import json
from datetime import datetime
from addie.lib import prisma_client
from addie.tasks import process_rosterstream_webhook_task
from addie import settings
from addie.api.auth import verify_api_key

router = APIRouter(prefix="/webhook", tags=["webhooks"])




@router.post("/oneroster")
async def rosterstream_webhook(request: Request, api_key: str = Depends(verify_api_key)):
    """
    Handle webhook requests from RosterStream.
    
    This endpoint receives student data from RosterStream and logs it for analysis.
    Once we understand the data structure, we'll implement proper schema handling.
    
    Args:
        request: The incoming HTTP request containing student data
        
    Returns:
        Success response acknowledging receipt of the webhook
    """
    try:
        # Get the request body as JSON
        try:
            body = await request.json()
        except Exception as json_error:
            # If JSON parsing fails, try to get raw body
            raw_body = await request.body()
            alog.error(f"Failed to parse JSON from RosterStream webhook: {json_error}")
            alog.info(f"Raw body received: {raw_body.decode('utf-8', errors='ignore')}")
            
            # Try to handle form data
            try:
                form_data = await request.form()
                body = dict(form_data)
                alog.info("Successfully parsed as form data")
            except Exception as form_error:
                alog.error(f"Failed to parse as form data: {form_error}")
                body = {"raw_body": raw_body.decode('utf-8', errors='ignore')}
        
        # Log the headers for debugging
        headers = dict(request.headers)
        alog.info(f"RosterStream webhook headers: {json.dumps(headers, indent=2)}")
        
        # Log the complete webhook payload
        alog.info(f"RosterStream webhook received at {datetime.utcnow().isoformat()}")
        alog.info(f"Request method: {request.method}")
        alog.info(f"Request URL: {request.url}")
        alog.info(f"Webhook payload: {json.dumps(body, indent=2, default=str)}")
        
        # Analyze RosterStream-specific structure
        if isinstance(body, dict):
            # Extract RosterStream metadata
            webhook_id = body.get('id')
            datasource_id = body.get('datasource_id')
            entity_type = body.get('entity')
            action = body.get('action')
            created_at = body.get('created_at')
            
            alog.info(f"RosterStream Event - ID: {webhook_id}, Entity: {entity_type}, Action: {action}, Datasource: {datasource_id}")
            
            # Analyze the data object
            data = body.get('data', {})
            obj = data.get('object', {})
            
            if obj:
                sourced_id = obj.get('sourcedId')
                status = obj.get('status')
                last_modified = obj.get('dateLastModified')
                
                alog.info(f"OneRoster Object - SourcedId: {sourced_id}, Status: {status}, Last Modified: {last_modified}")
                
                # Entity-specific logging
                if entity_type == 'course':
                    title = obj.get('title')
                    course_code = obj.get('courseCode')
                    org_id = obj.get('org', {}).get('sourcedId')
                    alog.info(f"Course Details - Title: '{title}', Code: '{course_code}', Org ID: {org_id}")
                
                elif entity_type == 'user':
                    username = obj.get('username')
                    given_name = obj.get('givenName')
                    family_name = obj.get('familyName')
                    role = obj.get('role')
                    alog.info(f"User Details - Username: '{username}', Name: '{given_name} {family_name}', Role: {role}")
                
                elif entity_type == 'enrollment':
                    user_id = obj.get('user', {}).get('sourcedId')
                    class_id = obj.get('class', {}).get('sourcedId')
                    role = obj.get('role')
                    alog.info(f"Enrollment Details - User ID: {user_id}, Class ID: {class_id}, Role: {role}")
                
                elif entity_type == 'class':
                    title = obj.get('title')
                    class_code = obj.get('classCode')
                    course_id = obj.get('course', {}).get('sourcedId')
                    school_id = obj.get('school', {}).get('sourcedId')
                    alog.info(f"Class Details - Title: '{title}', Code: '{class_code}', Course ID: {course_id}, School ID: {school_id}")
                
                elif entity_type == 'org':
                    name = obj.get('name')
                    org_type = obj.get('type')
                    parent_id = obj.get('parent', {}).get('sourcedId') if obj.get('parent') else None
                    alog.info(f"Organization Details - Name: '{name}', Type: {org_type}, Parent ID: {parent_id}")
                
                # Log any additional IDs from other systems
                other_ids = obj.get('_rs_other_ids', [])
                if other_ids:
                    for other_id in other_ids:
                        system_id = other_id.get('id')
                        system_type = other_id.get('type')
                        alog.info(f"External System ID - {system_type}: {system_id}")
            
            # Log bulk data if present (though RosterStream seems to send individual records)
            if 'users' in body:
                alog.info(f"Bulk users data received: {len(body['users'])} users")
            if 'classes' in body:
                alog.info(f"Bulk classes data received: {len(body['classes'])} classes")
            if 'enrollments' in body:
                alog.info(f"Bulk enrollments data received: {len(body['enrollments'])} enrollments")
            if 'orgs' in body:
                alog.info(f"Bulk orgs data received: {len(body['orgs'])} organizations")
        
        # Store webhook record in database for processing
        try:
            prisma = prisma_client()
            webhook_record = prisma.rosterstreamwebhook.create(
                data={
                    "webhook_id": body.get('id', f"unknown_{datetime.utcnow().timestamp()}"),
                    "datasource_id": body.get('datasource_id', 'unknown'),
                    "entity_type": body.get('entity', 'unknown'),
                    "action": body.get('action', 'unknown'),
                    "sourced_id": body.get('data', {}).get('object', {}).get('sourcedId'),
                    "payload": json.dumps(body, default=str),  # Serialize to JSON string
                    "processed": False
                }
            )
            alog.info(f"Stored webhook record with ID: {webhook_record.id}")
            
            # Queue the processing task with priority based on entity type
            # Process dependencies first: org -> course -> class -> user -> enrollment
            entity_priority = {
                'org': 1,
                'course': 2, 
                'class': 3,
                'user': 4,
                'enrollment': 5
            }
            priority = entity_priority.get(body.get('entity', 'unknown'), 6)
            
            try:
                task = process_rosterstream_webhook_task.apply_async(
                    kwargs={"webhook_id": webhook_record.id},
                    priority=priority
                )
                alog.info(f"Queued processing task {task.id} for webhook {webhook_record.id} with priority {priority}")
            except Exception as task_error:
                alog.error(f"Failed to queue processing task: {task_error}")
                # Continue - webhook is stored, can be processed manually later
                
        except Exception as db_error:
            alog.error(f"Failed to store webhook record: {db_error}")
            # Continue processing even if DB storage fails
        
        # Return success response
        return {
            "status": "success",
            "message": "Webhook received and logged successfully",
            "timestamp": datetime.utcnow().isoformat(),
            "data_received": True
        }
        
    except Exception as e:
        alog.error(f"Error processing RosterStream webhook: {str(e)}")
        alog.error(f"Error type: {type(e).__name__}")
        
        # Return error response but don't raise HTTP exception
        # This prevents the webhook from being retried unnecessarily
        return {
            "status": "error",
            "message": f"Error processing webhook: {str(e)}",
            "timestamp": datetime.utcnow().isoformat(),
            "data_received": False
        }


@router.get("/oneroster/health")
async def rosterstream_health():
    """
    Health check endpoint for RosterStream webhook.
    
    Returns:
        Simple health status for the webhook endpoint
    """
    return {
        "status": "healthy",
        "service": "RosterStream webhook",
        "timestamp": datetime.utcnow().isoformat()
    }