#!/usr/bin/env python
from langchain_core.messages import HumanMessage
from langchain_openai import OpenAIEmbeddings
from prisma.enums import User<PERSON><PERSON>
from langchain_qdrant import QdrantVectorStore
from pydantic import BaseModel
from qdrant_client import QdrantClient
from qdrant_client.http.models import models
from addie import celery_config, settings
from addie.agent import agent
from addie.agent.state.state import AgentState
from addie.counselor_agent.invoke import invoke_counselor_agent
from addie.counselor_agent.state import CounselorAgentState
from addie.data_model.gen_counselor_prompt_suggestions import (
    GeneratedCounselorPromptSuggestions,
)
from addie.data_model.gen_student_prompt_suggestions import (
    GeneratedStudentPromptSuggestions,
)
from addie.data_model.prompt import Prompt
from addie.data_model.student_context import StudentContext
from addie.lib import pick, prisma_client
from addie.api.auth import verify_api_key
from addie.scripts.create_evaluator import (
    create_evaluator_config,
    update_evaluator_config,
)
from addie.scripts.queue_cli import get_task_count_for_queue, clear_queue
from addie.tasks import app as celery_app, student_experiment_task
from prisma.enums import WorkflowType
from celery import Celery
from fastapi import APIRouter, Depends, HTTPException, FastAPI, Request, Form, Response, Header, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from typing import List, Optional, Dict, Any
import alog
import asyncio
import json
import base64
import websockets
from datetime import datetime
import hmac
import hashlib
import base64
from urllib.parse import parse_qs, urlparse
from twilio.request_validator import RequestValidator
from addie.twilio_service import send_verification_code, verify_code

from addie.sms_agent.agent import process_sms_message, sms_unstructured_agent, initiate_conversation, send_conversation_initiation_sms, send_conversation_notification_sms, handle_conversation_mode_response, detect_conversation_mode_preference, send_reminder_sms, get_session_id_for_sms
from addie.api import sms_routes, webhook_routes, conversation_summaries
from addie.voice_agent import voice_router
from addie.voice_agent.models import CallSession
from addie.voice_agent.config import VoiceAgentConfig
# from addie.voice_agent.openai_client import OpenAIRealtimeClient  # Removed - not used
# from addie.voice_agent.session_manager import VoiceSessionManager  # Removed - not used
from prisma.enums import CallStatus as PrismaCallStatus
from addie import settings

import sentry_sdk



if settings.RUN_MODE != "development":
    sentry_sdk.init(
        environment=settings.RUN_MODE,
        dsn="https://<EMAIL>/4508764498690048",
        # Add data like request headers and IP for users,
        # see https://docs.sentry.io/platforms/python/data-management/data-collected/ for more info
        send_default_pii=True,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for tracing.
        traces_sample_rate=1.0,
        _experiments={
            # Set continuous_profiling_auto_start to True
            # to automatically start the profiler on when
            # possible.
            "continuous_profiling_auto_start": True,
        },
    )

router = FastAPI()

# Include SMS routes
router.include_router(sms_routes.router)

# Include Voice routes
router.include_router(voice_router)

# Include webhook routes
router.include_router(webhook_routes.router)

# Include engagement tracking routes
from addie.api import engagement_routes
router.include_router(engagement_routes.router)

# Include conversation summary routes
router.include_router(conversation_summaries.router)

# Include OCEAN calculation routes
from addie.api import ocean_calculation_routes
router.include_router(ocean_calculation_routes.router)
origins = ["*"]

router.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)




from pydantic import BaseModel, Field
celery_app_local = Celery("addie")
conf = celery_app_local.config_from_object(celery_config)

gen_unstructured_conversation_summary_task = celery_app_local.signature(
    "addie.tasks.gen_unstructured_conversation_summary_task"
)

class ExperimentParams(BaseModel):
    config_id: str = Field(
        ..., description="The ID of the student agent configuration to use"
    )
    workflow_id: str = Field(
        ..., description="The ID of the workflow to associate with this experiment"
    )


class AssignConversationsParams(BaseModel):
    student_id: str = Field(
        ..., description="The ID of the student to assign conversations to"
    )
    school_id: str = Field(
        ..., description="The ID of the school the student belongs to"
    )


@router.post(
    "/experiment",
    response_model={},
    name="create_experiment",
)
async def create_experiment(params: ExperimentParams, api_key: str = Depends(verify_api_key)) -> dict:
    alog.info(params.model_dump())

    res = student_experiment_task.apply_async(kwargs=params.model_dump())

    return dict(
        **pick(["id"], res.__dict__),
        config_id=params.config_id,
        workflow_id=params.workflow_id,
    )


@router.get(
    "/experiment",
    response_model={},
    name="get_experiments",
)
async def get_experiments(api_key: str = Depends(verify_api_key)):
    # Get both active and cached task counts with a short timeout to prevent hanging
    task_stats = await get_task_count_for_queue(
        "student_experiment",
        timeout=2.0,  # Shorter timeout to prevent API hanging
        include_other_counts=True  # Include pending, reserved, and scheduled tasks
    )

    return task_stats


@router.post(
    "/assign-conversations",
    response_model={},
    name="assign_conversations_to_student",
)
async def assign_conversations_to_student(params: AssignConversationsParams, api_key: str = Depends(verify_api_key)) -> dict:
    """
    Queue a background task to assign published conversations to a new student.
    This prevents blocking during student creation and allows for batch operations.

    DISABLED: This feature is disabled to prevent auto-assignment of conversations.
    Only directly assigned conversations should be visible to students.
    """
    alog.info(f"Auto-assignment disabled for student {params.student_id} in school {params.school_id}")

    # Disable this feature for now
    return {
        "success": True,
        "task_id": "disabled",
        "message": f"Auto-assignment feature is disabled for student {params.student_id}",
        "student_id": params.student_id,
        "school_id": params.school_id,
    }

    # DISABLED AUTO-ASSIGNMENT LOGIC
    """
    try:
        # Queue the task asynchronously
        res = assign_published_conversations_to_student_task.apply_async(
            kwargs=params.model_dump()
        )

        alog.info(f"Conversation assignment task queued with ID: {res.id} for student {params.student_id} of school {params.school_id}")
        return {
            "success": True,
            "task_id": res.id,
            "message": f"Conversation assignment task queued for student {params.student_id}",
            "student_id": params.student_id,
            "school_id": params.school_id,
        }
    except Exception as e:
        alog.error(f"Failed to queue conversation assignment task: {e}")
        return {
            "success": False,
            "error": f"Failed to queue task: {str(e)}",
            "student_id": params.student_id,
            "school_id": params.school_id,
        }
    """


@router.delete(
    "/queue/{queue_name}",
    response_model={},
    name="clear_queue",
)
async def clear_queue_request(queue_name: str, api_key: str = Depends(verify_api_key)):
    clear_queue(queue_name)
    return dict()


class StudentChatParams(BaseModel):
    message: str
    user_id: str
    student_id: str
    session_id: str
    student_workflow_id: Optional[str] = None


@router.post(
    "/student_chat",
    response_model={},
    name="student_chat",
)
async def student_chat(params: StudentChatParams, api_key: str = Depends(verify_api_key)):
    message = params.message
    user_id = params.user_id
    student_id = params.student_id
    session_id = params.session_id
    student_workflow_id = params.student_workflow_id

    # Track student message received event
    try:
        from addie.services.engagement_tracker import EngagementTracker
        from prisma.enums import EngagementChannel, EngagementEventType
        
        # Get the actual workflow_id from student_workflow_id if provided
        actual_workflow_id = None
        if student_workflow_id:
            prisma = prisma_client()
            student_workflow = prisma.studentworkflow.find_unique(
                where={'id': student_workflow_id},
                include={'workflow': True}
            )
            if student_workflow and student_workflow.workflow:
                actual_workflow_id = student_workflow.workflow_id
        
        tracker = EngagementTracker()
        await tracker.track_message_event(
            user_id=user_id,
            student_id=student_id,
            session_id=session_id,
            channel=EngagementChannel.web_student,
            event_type=EngagementEventType.message_received,
            workflow_id=actual_workflow_id,
            metadata={"message_content": message[:100]}  # First 100 chars for context
        )
    except Exception as e:
        alog.error(f"Failed to track student chat engagement: {e}")
        # Continue processing even if tracking fails

    state = await AgentState.init(
        session_id=session_id,
        user_id=user_id,
        student_id=student_id,
        log_student_context=True,
        student_workflow_id=student_workflow_id,
        agent_type="student_chat"
    )

    state["messages"] += [HumanMessage(message)]

    alog.info(alog.pformat(state))

    state = agent().invoke(state)
    messages = state["messages"]
    
    messages = [dict(content=msg.content, type=msg.type) for msg in messages]
    messages = messages[-1:]

    # Track AI response sent event
    try:
        if messages and len(messages) > 0:
            await tracker.track_message_event(
                user_id=user_id,
                student_id=student_id,
                session_id=session_id,
                channel=EngagementChannel.web_student,
                event_type=EngagementEventType.message_sent,
                workflow_id=actual_workflow_id,
                metadata={"response_content": messages[0].get("content", "")[:100]}
            )
    except Exception as e:
        alog.error(f"Failed to track AI response engagement: {e}")

    return dict(messages=messages)


@router.delete(
    "/student_chat/{user_id}",
    response_model={},
    name="delete_student_chat",
)
async def delete_student_chat(user_id: str, api_key: str = Depends(verify_api_key)):
    embeddings = OpenAIEmbeddings(model="text-embedding-3-large")
    client = QdrantClient(
        settings.QDRANT_URL, api_key=settings.QDRANT__SERVICE__API_KEY
    )
    client.set_model("BAAI/bge-small-en-v1.5")
    collection_name = "chat_history"
    vector_store = QdrantVectorStore(
        client=client,
        collection_name=collection_name,
        embedding=embeddings,
    )

    filter = models.Filter(
        should=[
            models.FieldCondition(
                key="metadata.user_id",
                match=models.MatchValue(value=user_id),
            ),
        ]
    )

    docs = client.delete(
        collection_name=collection_name,
        points_selector=filter,
    )
    alog.info(docs)
    # alog.info(docs)
    #
    # alog.info(len(docs))
    # ids = []
    #
    # for doc in docs:
    #     alog.info(alog.pformat(doc))
    #     if isinstance(doc, list):
    #         for record in doc:
    #             alog.info(record.id)
    #             ids.append(record.id)
    #
    # client.delete(collection_name=collection_name, ids=ids,
    #               points_selector=models.PointsSelector)

    return dict()


@router.post(
    "/evaluator",
    response_model={},
    name="create_evaluator",
)
async def create_evaluator(params: dict, api_key: str = Depends(verify_api_key)):
    prompt = params["prompt"]
    user_id = params["user_id"]
    msg_type = params["msg_type"]
    use_raw_prompt = params.get("use_raw_prompt", False)

    name = params.get("name")
    return create_evaluator_config(prompt, user_id, msg_type, use_raw_prompt, name)


@router.put(
    "/evaluator/{config_id}",
    response_model={},
    name="update_evaluator",
)
async def update_evaluator(params: dict, config_id: str, api_key: str = Depends(verify_api_key)):
    prompt = params["prompt"]
    user_id = params["user_id"]
    msg_type = params["msg_type"]
    use_raw_prompt = params.get("use_raw_prompt", False)

    name = params.get("name")
    return update_evaluator_config(config_id, prompt, user_id, msg_type, use_raw_prompt, name)


@router.get(
    "/student_context/{student_id}",
    response_model={},
    name="get_student_context",
)
async def get_student_context(student_id: str, api_key: str = Depends(verify_api_key)):
    student = prisma_client().student.find_unique(
        where=dict(id=student_id),
        include=dict(users=True),
    )
    user_id = student.users[0].id

    student_context = await StudentContext(
        user_id=user_id, id=student_id, messages=[]
    ).async_init()

    return student_context.dump()


class CounselorChatParams(BaseModel):
    counselor_id: str
    message: str
    student_id: str
    user_id: str
    last_msg_id: int


@router.post(
    "/counselor_chat",
    response_model={},
    name="counselor_chat",
)
async def counselor_chat(params: CounselorChatParams, api_key: str = Depends(verify_api_key)):
    counselor_id = params.counselor_id
    message = params.message
    student_id = params.student_id
    user_id = params.user_id
    last_msg_id = params.last_msg_id

    user = prisma_client().user.find_unique_or_raise(where=dict(id=counselor_id))

    if user.role in [UserRole.COUNSELOR, UserRole.ADMIN]:
        # Track counselor message received event
        try:
            from addie.services.engagement_tracker import EngagementTracker
            from prisma.enums import EngagementChannel, EngagementEventType
            
            tracker = EngagementTracker()
            await tracker.track_message_event(
                user_id=user_id,
                student_id=student_id,
                session_id=f"counselor-{counselor_id}-{student_id}",
                channel=EngagementChannel.web_counselor,
                event_type=EngagementEventType.message_received,
                metadata={
                    "counselor_id": counselor_id,
                    "message_content": message[:100]
                }
            )
        except Exception as e:
            alog.error(f"Failed to track counselor chat engagement: {e}")

        state = await CounselorAgentState.init(
            id=counselor_id,
            user_id=user_id,
            student_id=student_id,
            last_msg_id=last_msg_id,
        )

        # alog.info(alog.pformat(state['student_context']['conversation_responses']))

        state["messages"] += [HumanMessage(message)]

        state = invoke_counselor_agent(state)

        messages = state["messages"]
        messages = [dict(content=msg.content, type=msg.type) for msg in messages]
        messages = messages[-1:]

        # Track AI response sent event
        try:
            if messages and len(messages) > 0:
                await tracker.track_message_event(
                    user_id=user_id,
                    student_id=student_id,
                    session_id=f"counselor-{counselor_id}-{student_id}",
                    channel=EngagementChannel.web_counselor,
                    event_type=EngagementEventType.message_sent,
                    metadata={
                        "counselor_id": counselor_id,
                        "response_content": messages[0].get("content", "")[:100]
                    }
                )
        except Exception as e:
            alog.error(f"Failed to track counselor response engagement: {e}")

        alog.info(messages)

        return dict(messages=messages)
    else:
        raise Exception("Unauthorized")


@router.put(
    "/addie_config",
    response_model={},
    name="update_addie_config",
)
async def update_addie_config(params: dict, api_key: str = Depends(verify_api_key)):
    prompt = params["prompt"]

    # get addie config
    addie_config = prisma_client().addieconfig.find_first()
    # create new prompt
    prompt = Prompt(content=prompt)

    # update addie config
    addie_config = prisma_client().addieconfig.update(
        where=dict(id=addie_config.id),
        data=dict(prompt_id=prompt.id, past_prompts=dict(connect=dict(id=prompt.id))),
    )

    return addie_config


class StudentPromptSuggestionsParams(BaseModel):
    student_id: str


@router.post(
    "/student/prompt_suggestions",
    response_model={},
    name="student_prompt_suggestions",
)
async def student_prompt_suggestions(params: StudentPromptSuggestionsParams, api_key: str = Depends(verify_api_key)):
    suggestions = GeneratedStudentPromptSuggestions(**params.model_dump()).result

    return dict(suggestions=suggestions)


class CounselorPromptSuggestionsParams(BaseModel):
    context: str


@router.post(
    "/counsellor/prompt_suggestions",
    response_model={},
    name="counselor_prompt_suggestions",
)
async def counselor_prompt_suggestions(params: CounselorPromptSuggestionsParams, api_key: str = Depends(verify_api_key)):
    suggestions = GeneratedCounselorPromptSuggestions(**params.model_dump()).result

    return dict(suggestions=suggestions)


class PhoneVerificationRequest(BaseModel):
    phone_number: str

class PhoneVerificationCheckRequest(BaseModel):
    phone_number: str
    code: str


class RegenerateSummariesRequest(BaseModel):
    prompt_id: str

@router.post(
    "/verify-phone-send",
    response_model={},
    name="phone_verification_send",
)
async def phone_verification_send(params: PhoneVerificationRequest, api_key: str = Depends(verify_api_key)):
    """Send a verification code to the specified phone number"""
    result = send_verification_code(params.phone_number)
    return result

@router.post(
    "/verify-phone-check",
    response_model={},
    name="phone_verification_check",
)
async def phone_verification_check(params: PhoneVerificationCheckRequest, api_key: str = Depends(verify_api_key)):
    """Verify the code sent to the phone number"""
    result = verify_code(params.phone_number, params.code)
    return result


@router.post(
    "/regenerate-unstructured-summaries",
    response_model={},
    name="regenerate_unstructured_summaries",
)
async def regenerate_unstructured_summaries(params: RegenerateSummariesRequest, api_key: str = Depends(verify_api_key)):
    """Regenerate all unstructured conversation summaries after prompt update

    This endpoint finds all student workflows of type UNSTRUCTURED
    and triggers regeneration of their summaries using the updated prompt.

    Args:
        params: The request parameters containing the new prompt_id

    Returns:
        Dictionary with count of workflows for which summary regeneration was triggered
    """
    alog.info(f"Regenerating all unstructured conversation summaries for prompt ID: {params.prompt_id}")

    # Find all student workflows with type UNSTRUCTURED
    prisma = prisma_client()
    student_workflows = prisma.studentworkflow.find_many(
        where=dict(
            workflow=dict(
                workflow_type=WorkflowType.UNSTRUCTURED
            ),
            status="COMPLETED"
        ),
        include=dict(
            student=True,
            workflow=True
        )
    )

    alog.info(f"Found {len(student_workflows)} completed unstructured conversation workflows")
    count = 0

    # For each workflow, trigger the summary generation task
    for sw in student_workflows:
        try:
            student_id = sw.student_id
            workflow_id = sw.workflow_id
            
            # Validate that the workflow still exists and is the correct type
            if not sw.workflow:
                alog.warning(f"Skipping StudentWorkflow {sw.id}: associated Workflow {workflow_id} no longer exists")
                continue
                
            if sw.workflow.workflow_type != WorkflowType.UNSTRUCTURED:
                alog.warning(f"Skipping StudentWorkflow {sw.id}: Workflow {workflow_id} is not UNSTRUCTURED type")
                continue
            # Queue the task for regeneration
            gen_unstructured_conversation_summary_task.apply_async(
                kwargs=dict(
                    student_id=student_id,
                    workflow_id=workflow_id
                )
            )
            count += 1
            alog.info(f"Triggered summary regeneration for student {student_id}, workflow {workflow_id}")
        except Exception as e:
            alog.error(f"Failed to trigger summary regeneration for workflow {sw.id}: {e}")

    return dict(
        status="success",
        total_workflows=len(student_workflows),
        regeneration_count=count,
        prompt_id=params.prompt_id
    )

def validate_twilio_request(request: Request, form_data: dict) -> bool:
    """
    Validate that the request is coming from Twilio.

    Args:
        request: The FastAPI request object
        form_data: The parsed form data from the request

    Returns:
        True if the request is valid, False otherwise
    """
    # Skip validation if Twilio credentials are not configured
    if not all([settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN]):
        alog.warning("Twilio credentials not configured. Skipping request validation.")
        return True

    # # For development/testing, you can disable validation
    # if settings.ENV == "development":
    #     alog.warning("Development environment detected. Skipping Twilio signature validation.")
    #     return True

    try:
        # log headers
        alog.info(alog.pformat(request.headers))
        # Get the Twilio signature from the request headers
        twilio_signature = request.headers.get("x-twilio-signature", "")

        # Get the full URL of the request, accounting for proxies
        # When behind a proxy, we need to reconstruct the URL using the X-Forwarded headers
        if request.headers.get("x-forwarded-host") and request.headers.get("x-forwarded-proto"):
            # Use the forwarded host and protocol
            forwarded_host = request.headers.get("x-forwarded-host")
            forwarded_proto = request.headers.get("x-forwarded-proto")
            forwarded_uri = request.headers.get("x-original-uri", request.url.path)
            url = f"{forwarded_proto}://{forwarded_host}{forwarded_uri}"
            alog.info(f"Using forwarded URL for Twilio validation: {url}")
        else:
            # Fall back to the request URL
            url = str(request.url)
            alog.info(f"Using direct URL for Twilio validation: {url}")

        # Use the form_data that was already parsed
        body = dict(form_data)

        # Create a validator with our auth token
        validator = RequestValidator(settings.TWILIO_AUTH_TOKEN)

        # Validate the request
        return validator.validate(url, body, twilio_signature)

    except Exception as e:
        alog.error(f"Error validating Twilio request: {str(e)}")
        return False

@router.post(
    "/api/sms/webhook",
    response_model={},
    name="sms_webhook",
)
async def sms_webhook(request: Request):
    """
    Webhook endpoint for Twilio SMS messages.

    This endpoint receives SMS messages from Twilio, processes them using the SMS agent,
    and returns a TwiML response with the agent's reply.

    The webhook follows this logic:
    1. First checks for active IN_PROGRESS SMS conversations for the student
       - This ensures that once a conversation is started, it continues without interruption
    2. If no active SMS conversation is found, checks for recent notifications
       - These are awaiting mode preference selection (text/call) after notification was sent
       - Routes to handle_conversation_mode_response for mode selection
    3. If neither is found, returns a message that no active SMS conversation exists
    """
    try:
        from addie.sms_agent.utils import has_sms_conversation_started, is_mode_selection_response

        # Parse form data from the request
        form_data = await request.form()

        # Extract the phone number and message text
        from_number = form_data.get("From", "")
        message_text = form_data.get("Body", "")

        alog.info(f"Received SMS from {from_number}: {message_text}")

        # Validate the request is coming from Twilio
        if not validate_twilio_request(request, form_data):
            alog.error(f"Invalid Twilio request signature for {from_number}")
            return Response(
                content="<?xml version='1.0' encoding='UTF-8'?><Response><Message>Unauthorized request</Message></Response>",
                media_type="application/xml"
            )

        # Look up the student by phone number
        try:
            prisma = prisma_client()

            # Try to find the user by phone number
            user = None
            student_id = None

            try:
                user = prisma.user.find_first(
                    where={"phone_number": from_number}
                )
            except Exception as e:
                alog.warning(f"Error finding user by phone number: {str(e)}")

            if user:
                # Try to find the student associated with this user
                try:
                    # Look for students related to this user
                    user_with_students = prisma.user.find_unique(
                        where={"id": user.id},
                        include={"students": True}
                    )

                    if user_with_students and user_with_students.students and len(user_with_students.students) > 0:
                        student = user_with_students.students[0]
                        student_id = student.id
                        alog.info(f"Found student {student_id} for user {user.id}")
                except Exception as e:
                    alog.warning(f"Error finding student for user {user.id}: {str(e)}")

            # If we still don't have a student ID, try a different approach
            if not student_id:
                alog.warning(f"No student found for phone number {from_number} using direct lookup")

                # For testing purposes, just get the first student in the database
                students = prisma.student.find_many(
                    take=1
                )

                if students and len(students) > 0:
                    student_id = students[0].id
                    alog.info(f"Using first student in database: {student_id}")

            if not student_id:
                alog.error(f"No student found for phone number {from_number}")
                return Response(
                    content="<?xml version='1.0' encoding='UTF-8'?><Response><Message>Sorry, we couldn't find your account. Please contact your counselor for assistance.</Message></Response>",
                    media_type="application/xml"
                )

            # First, check for active IN_PROGRESS SMS conversations
            # This takes priority over NOT_STARTED conversations to ensure
            # that once a conversation is started, it continues without interruption
            sms_workflow = prisma.studentworkflow.find_first(
                where=dict(
                    student_id=student_id,
                    mode="sms",
                    status="IN_PROGRESS",
                )
            )

            # If there's an active SMS conversation, always prioritize it
            # This ensures ongoing conversations are never interrupted
            if sms_workflow:
                alog.info(f"Found active SMS conversation {sms_workflow.id} for student {student_id}")
                # Use the workflow ID from the active SMS conversation
                workflow_id = sms_workflow.workflow_id
            else:
                # If no active SMS conversation, check if this is a mode selection response
                # First, find any workflows with notifications that don't have IN_PROGRESS status
                potential_workflows = prisma.studentworkflow.find_many(
                    where=dict(
                        student_id=student_id,
                        status={"not": "IN_PROGRESS"},
                    )
                )

                # Check each workflow to see if it has SMS messages (notifications)
                notified_workflow = None
                for workflow in potential_workflows:
                    if has_sms_conversation_started(student_id, workflow.workflow_id):
                        alog.info(f"Found workflow {workflow.id} with SMS messages for student {student_id}")
                        if is_mode_selection_response(message_text):
                            notified_workflow = workflow
                            break

                if notified_workflow:
                    alog.info(f"Treating message as mode selection response for workflow {notified_workflow.id}")

                    # Handle the conversation mode preference response
                    response_text = handle_conversation_mode_response(
                        student_id,
                        notified_workflow.workflow_id,
                        message_text
                    )

                    # Return a TwiML response
                    twiml_response = f"<?xml version='1.0' encoding='UTF-8'?><Response><Message>{response_text}</Message></Response>"
                    return Response(content=twiml_response, media_type="application/xml")
                else:
                    alog.warning(f"No active SMS conversation or workflows with notifications found for student {student_id}")
                    # Return a message indicating no active SMS conversation
                    return Response(
                        content="<?xml version='1.0' encoding='UTF-8'?><Response><Message>You don't have any assigned conversations. you <NAME_EMAIL>.</Message></Response>",
                        media_type="application/xml"
                    )

            # Track SMS message received event
            try:
                from addie.services.engagement_tracker import EngagementTracker
                from prisma.enums import EngagementChannel, EngagementEventType
                
                tracker = EngagementTracker()
                # Use standard session ID format
                session_id = get_session_id_for_sms(student_id, workflow_id) if 'workflow_id' in locals() else f"sms-{student_id}"
                
                await tracker.track_message_event(
                    user_id=user.id if user else "unknown",
                    student_id=student_id,
                    session_id=session_id,
                    channel=EngagementChannel.sms,
                    event_type=EngagementEventType.message_received,
                    workflow_id=workflow_id if 'workflow_id' in locals() else None,
                    metadata={
                        "phone_number": from_number,
                        "message_content": message_text[:100]
                    }
                )
            except Exception as e:
                alog.error(f"Failed to track SMS engagement: {e}")

            # Process the message using the SMS agent
            if 'workflow_id' in locals():
                # If we found an active SMS conversation, pass the workflow ID
                alog.info(f"Processing SMS message with workflow ID {workflow_id}")
                response_text = process_sms_message(student_id, message_text, from_number, workflow_id)
            else:
                # Otherwise, let the agent find the most recent workflow
                response_text = process_sms_message(student_id, message_text, from_number)

            # Track SMS response sent event
            try:
                await tracker.track_message_event(
                    user_id=user.id if user else "unknown",
                    student_id=student_id,
                    session_id=session_id,
                    channel=EngagementChannel.sms,
                    event_type=EngagementEventType.message_sent,
                    workflow_id=workflow_id if 'workflow_id' in locals() else None,
                    metadata={
                        "phone_number": from_number,
                        "response_content": response_text[:100]
                    }
                )
            except Exception as e:
                alog.error(f"Failed to track SMS response engagement: {e}")

            # Return a TwiML response
            twiml_response = f"<?xml version='1.0' encoding='UTF-8'?><Response><Message>{response_text}</Message></Response>"
            return Response(content=twiml_response, media_type="application/xml")

        except Exception as e:
            alog.error(f"Error looking up student: {str(e)}")
            return Response(
                content="<?xml version='1.0' encoding='UTF-8'?><Response><Message>Sorry, we encountered an error processing your message. Please try again later.</Message></Response>",
                media_type="application/xml"
            )

    except Exception as e:
        alog.error(f"Error processing SMS webhook: {str(e)}")
        return Response(
            content="<?xml version='1.0' encoding='UTF-8'?><Response><Message>Sorry, we encountered an error processing your message. Please try again later.</Message></Response>",
            media_type="application/xml"
        )

@router.post(
    "/api/sms/initiate/{student_conversation_id}",
    response_model={},
    name="initiate_sms_conversation",
)
async def initiate_sms_conversation(student_conversation_id: str, api_key: str = Depends(verify_api_key)):
    """
    Initiate an SMS conversation with a student using a student conversation ID (StudentWorkflow.id).

    Args:
        student_conversation_id: The ID of the student conversation (StudentWorkflow.id)

    Returns:
        The welcome message and first question
    """
    # Use the new function from agent.py to handle SMS initiation
    return send_conversation_initiation_sms(student_conversation_id)


@router.post(
    "/api/sms/send_reminder/{reminder_id}",
    response_model={},
    name="send_sms_reminder",
)
async def send_sms_reminder(reminder_id: str, api_key: str = Depends(verify_api_key)):
    """
    Send an SMS reminder with a reminder ID.

    Args:
        reminder_id: The ID of the reminder

    Returns:
        The reminder message
    """
    # Use the new function from agent.py to handle SMS initiation
    return send_reminder_sms(reminder_id)

@router.post(
    "/api/sms/notify/{student_conversation_id}",
    response_model={},
    name="send_conversation_notification",
)
async def send_notification_sms(student_conversation_id: str, api_key: str = Depends(verify_api_key)):
    """
    Send an SMS notification to a student when the conversation is assigned using a student conversation ID (StudentWorkflow.id).

    Args:
        student_conversation_id: The ID of the student conversation (StudentWorkflow.id)

    Returns:
        The result of the notification operation
    """
    # Use the function from agent.py to send notification SMS
    return send_conversation_notification_sms(student_conversation_id)

# Export the FastAPI app as 'app' for test compatibility
app = router
