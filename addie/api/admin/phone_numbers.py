"""
Admin API endpoints for phone number management.
"""

import logging
from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

from addie.services.twilio_discovery_service import TwilioDiscoveryService, SyncResult
from addie.services.phone_number_service import PhoneNumberService
from addie.api.auth import verify_api_key
from addie.lib import prisma_client

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/admin/phone-numbers", tags=["admin-phone-numbers"])


class PhoneNumberResponse(BaseModel):
    """Response model for phone number data."""
    id: str
    phone_number: str
    # twilio_sid: Removed for security - should not be exposed to clients
    country_code: str
    country_name: str
    is_active: bool
    capabilities: Dict[str, bool]
    twilio_verified: bool
    priority: int
    environment: Optional[str] = None
    webhook_validation_errors: Optional[str] = None
    voice_webhook_url: Optional[str] = None
    sms_webhook_url: Optional[str] = None
    last_synced_at: Optional[str] = None
    sync_error: Optional[str] = None
    created_at: str
    updated_at: str


class SyncResponse(BaseModel):
    """Response model for sync operations."""
    status: str
    numbers_added: int = 0
    numbers_updated: int = 0
    numbers_deactivated: int = 0
    errors: List[str] = []


class RecentCallInfo(BaseModel):
    """Model for recent call information."""
    call_sid: str
    status: str
    duration: Optional[int] = None
    start_time: Optional[str] = None
    student_id: str


class UsageStatsResponse(BaseModel):
    """Enhanced response model for usage statistics including call analytics."""
    phone_number: str
    country_code: str
    country_name: str
    total_sms: int
    total_voice: int
    total_success: int
    total_failure: int
    days_active: int
    # Enhanced fields from CallRecord integration
    total_call_duration: float = 0.0  # Total call time in minutes
    avg_call_duration: float = 0.0    # Average call duration in minutes
    total_answered_calls: int = 0     # Number of successfully answered calls
    total_failed_calls: int = 0       # Number of failed calls
    call_success_rate: float = 0.0    # Success rate percentage
    recent_calls: List[RecentCallInfo] = []  # Last 5 calls with details


@router.get("/test")
async def test_endpoint():
    """Simple test endpoint"""
    return {"message": "Phone numbers API is working"}


@router.get("/list", response_model=List[PhoneNumberResponse], dependencies=[Depends(verify_api_key)])
async def get_phone_numbers():
    """
    Get all phone numbers with their configurations and capabilities.
    """
    try:
        logger.info("Getting all phone numbers")
        
        # Use direct database query
        db = prisma_client()
        phone_numbers = db.twiliophonenumber.find_many(
            order=[{'country_code': 'asc'}, {'phone_number': 'asc'}]
        )
        
        # Convert to response format
        response_data = []
        for number in phone_numbers:
            # Parse capabilities JSON
            capabilities = number.capabilities
            if isinstance(capabilities, str):
                import json
                capabilities = json.loads(capabilities)
                
            # Parse webhook_validation_errors if it's a JSON string
            webhook_validation_errors = None
            if number.webhook_validation_errors:
                if isinstance(number.webhook_validation_errors, str):
                    # It's already a JSON string, parse it to get the original list
                    import json
                    try:
                        errors_list = json.loads(number.webhook_validation_errors)
                        webhook_validation_errors = "; ".join(errors_list) if errors_list else None
                    except (json.JSONDecodeError, TypeError):
                        webhook_validation_errors = str(number.webhook_validation_errors)
                else:
                    # It's already a list or other format
                    webhook_validation_errors = str(number.webhook_validation_errors)
            
            response_data.append(PhoneNumberResponse(
                id=number.id,
                phone_number=number.phone_number,
                # twilio_sid omitted for security
                country_code=number.country_code,
                country_name=number.country_name,
                is_active=number.is_active,
                capabilities=capabilities,
                twilio_verified=number.twilio_verified,
                priority=number.priority,
                environment=number.environment,
                webhook_validation_errors=webhook_validation_errors,
                voice_webhook_url=number.voice_webhook_url,
                sms_webhook_url=number.sms_webhook_url,
                last_synced_at=number.last_synced_at.isoformat() if number.last_synced_at else None,
                sync_error=number.sync_error,
                created_at=number.created_at.isoformat(),
                updated_at=number.updated_at.isoformat()
            ))
        
        logger.info(f"Retrieved {len(response_data)} phone numbers")
        return response_data
        
    except Exception as e:
        logger.error(f"Get phone numbers API endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get phone numbers: {str(e)}")


@router.post("/sync", response_model=SyncResponse, dependencies=[Depends(verify_api_key)])
async def sync_with_twilio():
    """
    Sync phone numbers with Twilio account.
    
    This endpoint:
    1. Fetches all phone numbers from Twilio
    2. Updates capabilities and configuration in database
    3. Marks numbers not found in Twilio as inactive
    4. Adds new numbers discovered in Twilio
    """
    try:
        logger.info("Starting Twilio sync via API - Fresh sync mode enabled")
        
        # Initialize the discovery service
        discovery_service = TwilioDiscoveryService()
        
        # Perform the sync with fresh data (clears stale webhooks)
        result = discovery_service.sync_database_with_twilio()
        
        if result.success:
            logger.info(f"Fresh sync completed successfully via API. Added: {result.numbers_added}, Updated: {result.numbers_updated}, Deactivated: {result.numbers_deactivated}")
            
            return SyncResponse(
                status="success",
                numbers_added=result.numbers_added,
                numbers_updated=result.numbers_updated,
                numbers_deactivated=result.numbers_deactivated,
                errors=result.errors
            )
        else:
            logger.error(f"Sync failed via API: {result.errors}")
            
            return SyncResponse(
                status="failed",
                errors=result.errors
            )
            
    except Exception as e:
        logger.error(f"Sync API endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=f"Sync failed: {str(e)}")


@router.get("/usage-stats", response_model=List[UsageStatsResponse], dependencies=[Depends(verify_api_key)])
async def get_usage_statistics(days: int = 7):
    """
    Get usage statistics for all phone numbers.
    
    Args:
        days: Number of days to look back for statistics (default: 7)
    """
    try:
        logger.info(f"Getting usage statistics for {days} days")
        
        # Use phone number service to get usage stats
        phone_service = PhoneNumberService()
        usage_stats = phone_service.get_usage_statistics(days=days)
        
        # Convert to response format
        response_data = []
        for stat in usage_stats:
            # Convert recent calls to RecentCallInfo objects
            recent_calls = []
            for call in stat.get('recent_calls', []):
                recent_calls.append(RecentCallInfo(
                    call_sid=call['call_sid'],
                    status=call['status'],
                    duration=call['duration'],
                    start_time=call['start_time'],
                    student_id=call['student_id']
                ))
            
            response_data.append(UsageStatsResponse(
                phone_number=stat['phone_number'],
                country_code=stat['country_code'],
                country_name=stat['country_name'],
                total_sms=stat['total_sms'],
                total_voice=stat['total_voice'],
                total_success=stat['total_success'],
                total_failure=stat['total_failure'],
                days_active=stat['days_active'],
                # Enhanced fields
                total_call_duration=stat.get('total_call_duration', 0.0),
                avg_call_duration=stat.get('avg_call_duration', 0.0),
                total_answered_calls=stat.get('total_answered_calls', 0),
                total_failed_calls=stat.get('total_failed_calls', 0),
                call_success_rate=stat.get('call_success_rate', 0.0),
                recent_calls=recent_calls
            ))
        
        logger.info(f"Retrieved usage statistics for {len(response_data)} phone numbers")
        return response_data
        
    except Exception as e:
        logger.error(f"Error getting usage statistics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get usage statistics: {str(e)}")


@router.post("/validate-config", dependencies=[Depends(verify_api_key)])
async def validate_phone_number_config():
    """
    Validate all active phone numbers against Twilio configuration.
    
    This endpoint checks:
    1. Numbers are active in Twilio
    2. Capabilities match Twilio data
    3. Webhook URLs are correctly configured
    """
    try:
        logger.info("Starting phone number configuration validation")
        
        # Get all active numbers from database
        from addie.common.db import prisma
        
        active_numbers = prisma.twiliophonenumber.find_many(
            where={
                'is_active': True,
                'twilio_verified': True
            }
        )
        
        if not active_numbers:
            return {
                "status": "success",
                "message": "No active phone numbers to validate",
                "results": []
            }
        
        # Initialize discovery service for validation
        discovery_service = TwilioDiscoveryService()
        
        validation_results = []
        total_issues = 0
        
        for number in active_numbers:
            try:
                result = discovery_service.validate_phone_number_config(number.phone_number)
                
                validation_results.append({
                    "phone_number": number.phone_number,
                    "is_valid": result.is_valid,
                    "issues": result.issues
                })
                
                if not result.is_valid:
                    total_issues += len(result.issues)
                    
            except Exception as e:
                logger.error(f"Error validating {number.phone_number}: {e}")
                validation_results.append({
                    "phone_number": number.phone_number,
                    "is_valid": False,
                    "issues": [f"Validation error: {e}"]
                })
                total_issues += 1
        
        logger.info(f"Validation completed. {total_issues} total issues found")
        
        return {
            "status": "success",
            "numbers_validated": len(active_numbers),
            "total_issues": total_issues,
            "results": validation_results
        }
        
    except Exception as e:
        logger.error(f"Validation endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=f"Validation failed: {str(e)}")


@router.put("/{phone_number_id}/priority", dependencies=[Depends(verify_api_key)])
async def update_phone_number_priority(phone_number_id: str, priority: int):
    """
    Update priority for a specific phone number.
    
    Args:
        phone_number_id: The ID of the phone number to update
        priority: New priority value (0-100)
    """
    try:
        if not 0 <= priority <= 100:
            raise HTTPException(status_code=400, detail="Priority must be between 0 and 100")
        
        # Use phone number service to update priority
        phone_service = PhoneNumberService()
        success = phone_service.update_priority(phone_number_id, priority)
        
        if success:
            return {"status": "success", "message": "Priority updated successfully"}
        else:
            raise HTTPException(status_code=404, detail="Phone number not found")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating priority for {phone_number_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update priority: {str(e)}")