#!/usr/bin/env python
"""
OCEAN Calculation API Routes

This module provides API endpoints for triggering OCEAN personality scores calculation.
"""

import alog
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import Dict, Any

from addie.api.auth import verify_api_key
from addie.services.ocean_scores import OceanScoresService
from addie.tasks import calculate_ocean_scores_task
from addie.lib import prisma_client


router = APIRouter()


class OceanCalculationRequest(BaseModel):
    """Request model for triggering OCEAN scores calculation."""
    student_id: str


class OceanCalculationResponse(BaseModel):
    """Response model for OCEAN calculation trigger."""
    success: bool
    message: str
    task_queued: bool
    student_id: str


@router.post(
    "/api/ocean-scores/calculate",
    response_model=OceanCalculationResponse,
    name="trigger_ocean_calculation"
)
async def trigger_ocean_calculation(
    request: OceanCalculationRequest,
    api_key: str = Depends(verify_api_key)
) -> OceanCalculationResponse:
    """
    Trigger OCEAN scores calculation for a student.
    
    This endpoint queues a background task to calculate OCEAN scores
    for the student's latest completed BFI workflow.
    
    Args:
        request: Request containing student_id
        api_key: API key for authentication
        
    Returns:
        Response indicating if the task was queued successfully
        
    Raises:
        HTTPException: If student not found or other errors
    """
    try:
        student_id = request.student_id
        
        # Verify student exists
        prisma = prisma_client()
        student = prisma.student.find_unique(where={"id": student_id})
        
        if not student:
            raise HTTPException(status_code=404, detail="Student not found")
        
        # Check if student has any completed BFI workflows
        ocean_service = OceanScoresService()
        latest_bfi_workflow = ocean_service.find_latest_bfi_workflow(student_id)
        
        if not latest_bfi_workflow:
            return OceanCalculationResponse(
                success=False,
                message="No completed BFI workflow found for this student",
                task_queued=False,
                student_id=student_id
            )
        
        # Queue the background task
        alog.info(f"Queuing OCEAN calculation task for student {student_id}")
        calculate_ocean_scores_task.apply_async(kwargs={"student_id": student_id})
        
        return OceanCalculationResponse(
            success=True,
            message="OCEAN scores calculation task queued successfully",
            task_queued=True,
            student_id=student_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        alog.error(f"Error triggering OCEAN calculation for student {request.student_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/api/ocean-scores/{student_id}",
    response_model=Dict[str, Any],
    name="get_ocean_scores"
)
async def get_ocean_scores(
    student_id: str,
    api_key: str = Depends(verify_api_key)
) -> Dict[str, Any]:
    """
    Retrieve OCEAN personality scores for a student.
    
    Args:
        student_id: The ID of the student
        api_key: API key for authentication
        
    Returns:
        OCEAN scores and metadata or null if not found
        
    Raises:
        HTTPException: If student not found
    """
    try:
        # Verify student exists
        prisma = prisma_client()
        student = prisma.student.find_unique(where={"id": student_id})
        
        if not student:
            raise HTTPException(status_code=404, detail="Student not found")
        
        # Get OCEAN scores
        ocean_service = OceanScoresService()
        scores = ocean_service.get_ocean_scores(student_id)
        
        if not scores:
            return {
                "student_id": student_id,
                "has_scores": False,
                "scores": None,
                "message": "OCEAN scores not found for this student"
            }
        
        return {
            "student_id": student_id,
            "has_scores": True,
            "scores": {
                "extroversion": scores["extroversion"],
                "agreeableness": scores["agreeableness"],
                "conscientiousness": scores["conscientiousness"],
                "neuroticism": scores["neuroticism"],
                "openness_to_experience": scores["openness_to_experience"]
            },
            "created_at": scores["created_at"],
            "updated_at": scores["updated_at"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        alog.error(f"Error retrieving OCEAN scores for student {student_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
