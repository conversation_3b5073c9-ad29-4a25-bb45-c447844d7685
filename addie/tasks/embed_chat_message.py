#!/usr/bin/env python
import hashlib
import json
from typing import List

from langchain_core.messages import BaseMessage
from langchain_openai import OpenAIEmbeddings
from langchain_qdrant import Qdrant as QdrantVectorStore
from pydantic import validate_call
from qdrant_client import QdrantClient
from qdrant_client.http.models import Distance, VectorParams, models
from langchain_core.documents import Document
import alog
import click
import pandas as pd
from addie import settings


@validate_call
def embed_chat_message(message: dict, user_id: str, **kwargs):
    embeddings = OpenAIEmbeddings(model="text-embedding-3-large")

    client = QdrantClient(settings.QDRANT_URL, api_key=settings.QDRANT__SERVICE__API_KEY)
    client.set_model('BAAI/bge-small-en-v1.5')

    collection_name = "chat_history"

    collection_exists = client.collection_exists(collection_name=collection_name)

    if not collection_exists:
        client.create_collection(
            collection_name=collection_name,
            vectors_config=VectorParams(size=3072, distance=Distance.COSINE),
        )

    vector_store = QdrantVectorStore(
        client=client,
        collection_name=collection_name,
        embeddings=embeddings,
    )

    content = json.dumps(dict(user_id=user_id, **message))

    # sha hash content
    content_hash = hashlib.sha256(content.encode()).hexdigest()[:32]

    metadata = dict(user_id=user_id, content_hash=content_hash)

    doc = Document(page_content=json.dumps(message), metadata=metadata)

    alog.info(doc)

    result = vector_store.add_documents([doc], ids=[content_hash])

    assert result[0] == content_hash
