#!/usr/bin/env python
import asyncio

from addie import celery_config
from addie.data_model.evaluator import run_for_user_id
from addie.data_model.gen_student_conversation_summary import GeneratedStudentConversationSummary
from addie.data_model.gen_teacher_comment_summary import GeneratedTeacherCommentSummary
from addie.data_model.gen_unstructured_conversation_summary import GeneratedUnstructuredConversationSummary
from addie.data_model.student_context import StudentContext
from addie.lib import is_production
from addie.tasks.save_comment import save_comment
from addie.scripts.send_engagement_email import send_engagement_emails
from addie.scripts.send_survey_reminder import send_survey_reminder
from addie.tasks.embed_chat_message import embed_chat_message
from addie.tasks.update_token_stats import update_token_usage
from addie.tasks.refresh_dashboard_view import refresh_dashboard_view_task
from addie.tasks.assign_conversations import assign_published_conversations_to_student
from addie.tasks.webhook_processing import process_rosterstream_webhook
from addie.services.ocean_scores import OceanScoresService
from addie.services.major_recommendations import MajorRecommendationService
from celery import Celery
from celery.schedules import crontab
from addie.experiments.questionnaire import run_questionnaire_test

import alog
import time
import asyncio
from addie.lib import prisma_client

app = Celery('addie')
conf = app.config_from_object(celery_config)

TIME_LIMIT = 60 * 4
HARD_TIME_LIMIT = TIME_LIMIT + 10
RETRIES = 3


@app.task(
    bind=True,
    retries=RETRIES,
    soft_time_limit=TIME_LIMIT,
    time_limit=HARD_TIME_LIMIT
)
def test_task(*args, **kwargs):
    alog.info(kwargs)
    time.sleep(3)
    # pass only **kwargs


@app.task(
    bind=True,
    retries=RETRIES,
    soft_time_limit=TIME_LIMIT,
    time_limit=HARD_TIME_LIMIT
)
def embed_message_task(*args, **kwargs):
    # alog.info(kwargs)
    embed_chat_message(**kwargs)


@app.task(
    bind=True,
    retries=RETRIES,
    soft_time_limit=60 * 20,
    time_limit=60 * 20
)
def student_experiment_task(self, *args, **kwargs):
    # Track the running task in Redis with an expiring key
    # The key will automatically expire after 30 minutes (1800 seconds) if not explicitly deleted
    task_id = self.request.id
    task_key = f"running_task:student_experiment:{task_id}"

    try:
        # Store the task in Redis with expiration
        with app.pool.acquire(block=True) as conn:
            conn.default_channel.client.setex(task_key, 60 * 5, 'RUNNING')
            alog.info(f"Task tracking started for {task_id}")

        # Execute the task
        alog.info(kwargs)

        result = asyncio.run(run_questionnaire_test(**kwargs))

        # Task completed successfully, remove the tracking key
        with app.pool.acquire(block=True) as conn:
            conn.default_channel.client.delete(task_key)
            alog.info(f"Task tracking ended for successful task {task_id}")

        return result
    except Exception as e:
        # If the task fails, also remove the tracking key
        try:
            with app.pool.acquire(block=True) as conn:
                conn.default_channel.client.delete(task_key)
                alog.info(f"Task tracking ended for failed task {task_id}")
        except Exception as redis_err:
            alog.error(f"Failed to remove task tracking for {task_id}: {redis_err}")

        # Re-raise the original exception
        raise


@app.task(
    bind=True,
    retries=RETRIES,
    soft_time_limit=30,
    time_limit=30
)
def search_chat_history_task(*args, **kwargs):
    # alog.info(kwargs)
    StudentContext.search_chat_history(**kwargs)


@app.task(
    bind=True,
    retries=RETRIES,
    soft_time_limit=30,
    time_limit=30
)
def search_teacher_comments_task(*args, **kwargs):
    # alog.info(kwargs)
    StudentContext.search_teacher_comments(**kwargs)


@app.task(
    bind=True,
    retries=RETRIES,
    soft_time_limit=30,
    time_limit=30
)
def run_evaluator_task(*args, **kwargs):
    # Ensure workflow_id is at least None if not provided
    if 'workflow_id' not in kwargs:
        kwargs['workflow_id'] = None
    run_for_user_id(**kwargs)


@app.task(
    bind=True,
    retries=0,
    soft_time_limit=60 * 5,
    time_limit=60 * 5
)
def send_engagement_emails_task(*args, **kwargs):
    if is_production():
        send_engagement_emails()


@app.task(
    bind=True,
    retries=3,
    soft_time_limit=60,
    time_limit=60
)
def save_comment_task(*args, **kwargs):
    save_comment(**kwargs)


@app.task(
    bind=True,
    retries=3,
    soft_time_limit=60,
    time_limit=60
)
def gen_teacher_comments_summary_for_student_task(*args, **kwargs):
    alog.info(alog.pformat(kwargs))
    GeneratedTeacherCommentSummary(**kwargs)


@app.task(
    bind=True,
    retries=3,
    soft_time_limit=60,
    time_limit=60
)
def gen_student_conversation_summary_task(*args, **kwargs):
    GeneratedStudentConversationSummary(**kwargs)

@app.task(
    bind=True,
    retries=3,
    soft_time_limit=60,
    time_limit=60
)
def gen_unstructured_conversation_summary_task(*args, **kwargs):
    GeneratedUnstructuredConversationSummary(**kwargs)

@app.task(
    bind=True,
    retries=1,
    soft_time_limit=60,
    time_limit=60
)
def send_survey_reminder_task(*args, **kwargs):
    send_survey_reminder()


@app.task(
    bind=True,
    retries=3,
    soft_time_limit=60 * 2,  # 2 minutes for OCEAN calculation
    time_limit=60 * 3        # 3 minutes hard limit
)
def calculate_ocean_scores_task(self, *args, **kwargs):
    """
    Celery task to calculate and store OCEAN scores for a student.
    Upon successful completion, automatically triggers major recommendations generation.

    Args:
        student_id (str): The ID of the student
    """
    try:
        student_id = kwargs.get('student_id')

        if not student_id:
            alog.error("Missing required parameter: student_id")
            return False

        alog.info(f"Starting OCEAN scores calculation for student {student_id}")

        # Initialize the OCEAN scores service
        ocean_service = OceanScoresService()

        # Process the OCEAN scores (will automatically find latest BFI workflow)
        success = ocean_service.process_student_ocean_scores(student_id)

        if success:
            alog.info(f"Successfully calculated and saved OCEAN scores for student {student_id}")

            # Automatically trigger major recommendations generation
            try:
                alog.info(f"Triggering major recommendations generation for student {student_id}")
                major_service = MajorRecommendationService()
                major_success = asyncio.run(major_service.generate_and_save_recommendations(student_id))

                if major_success:
                    alog.info(f"Successfully generated major recommendations for student {student_id}")
                else:
                    alog.error(f"Failed to generate major recommendations for student {student_id}")
                    # Don't fail the entire task if major recommendations fail

            except Exception as major_error:
                alog.error(f"Error generating major recommendations for student {student_id}: {str(major_error)}")
                # Don't fail the entire task if major recommendations fail
        else:
            alog.error(f"Failed to calculate OCEAN scores for student {student_id}")

        return success

    except Exception as e:
        alog.error(f"Error in calculate_ocean_scores_task: {str(e)}")
        return False


@app.task(
    bind=True,
    retries=3,
    soft_time_limit=60 * 10,  # 10 minutes soft limit
    time_limit=60 * 12        # 12 minutes hard limit
)
def update_token_usage_task(*args, **kwargs):
    update_token_usage()

# Disable this feature for now - no auto-assignment of conversations
"""
@app.task(
    bind=True,
    retries=3,
    soft_time_limit=60 * 5,  # 5 minutes for conversation assignment
    time_limit=60 * 6        # 6 minutes hard limit
)
def assign_published_conversations_to_student_task(*args, **kwargs):
    '''
    Celery task to assign published conversations to a new student.
    This runs in the background to avoid blocking student creation.

    Args:
        student_id (str): The ID of the student to assign conversations to
        school_id (str): The ID of the school the student belongs to
    '''
    from addie.tasks.assign_conversations import assign_published_conversations_to_student
    return assign_published_conversations_to_student(**kwargs)
"""

@app.task(
    bind=True,
    retries=3,
    soft_time_limit=60 * 2,  # 2 minutes for webhook processing
    time_limit=60 * 3        # 3 minutes hard limit
)
def process_rosterstream_webhook_task(*args, **kwargs):
    """
    Celery task to process a RosterStream webhook by ID.

    Args:
        webhook_id (str): The ID of the RosterStreamWebhook record to process
    """
    return process_rosterstream_webhook(**kwargs)

@app.task(
    bind=True,
    retries=0,
    soft_time_limit=60 * 10,  # 10 minutes for processing all students
    time_limit=60 * 12        # 12 minutes hard limit
)
def periodic_assign_published_conversations_task(self, *args, **kwargs):
    """
    Periodic task that checks all students and ensures each has corresponding
    student workflows for each published workflow they should have access to.

    Runs every 15 minutes and skips if previous run is still active.
    """
    task_id = self.request.id
    task_key = f"running_task:periodic_assign_conversations:{task_id}"
    lock_key = "lock:periodic_assign_conversations"

    try:
        # Check if another instance is already running
        with app.pool.acquire(block=True) as conn:
            if conn.default_channel.client.exists(lock_key):
                alog.info("Periodic conversation assignment task already running, skipping this execution")
                return {"skipped": True, "reason": "Previous task still running"}

            # Set lock with 15 minute expiration (task interval)
            conn.default_channel.client.setex(lock_key, 60 * 15, task_id)
            conn.default_channel.client.setex(task_key, 60 * 15, 'RUNNING')

        prisma = prisma_client()

        # Get all students using raw query instead of select parameter
        students_raw = prisma.query_raw("""
            SELECT id, school_id FROM "Student"
        """)

        # Convert raw query results to the expected format
        students = [
            {"id": student["id"], "school_id": student["school_id"]}
            for student in students_raw
        ]

        if not students:
            alog.info("No students found for conversation assignment")
            return {"success": True, "message": "No students found", "processed_count": 0}

        alog.info(f"Processing conversation assignments for {len(students)} students")

        total_processed = 0
        total_assigned = 0
        total_errors = 0

        for student in students:
            try:
                result = assign_published_conversations_to_student(
                    student_id=student["id"],
                    school_id=student["school_id"]
                )

                total_processed += 1
                if result.get("success"):
                    data = result.get("data", {})
                    total_assigned += data.get("assigned_count", 0)
                else:
                    total_errors += 1

            except Exception as student_error:
                total_errors += 1
                alog.warn(f"Failed to process student {student.id}: {student_error}")

        alog.info(f"Periodic conversation assignment completed: {total_processed} students processed, {total_assigned} workflows assigned, {total_errors} errors")

        return {
            "success": True,
            "message": f"Processed {total_processed} students",
            "data": {
                "processed_count": total_processed,
                "assigned_count": total_assigned,
                "error_count": total_errors
            }
        }

    except Exception as e:
        error_msg = f"Error in periodic conversation assignment: {e}"
        alog.error(error_msg)
        return {"success": False, "error": str(e)}

    finally:
        # Always clean up locks
        try:
            with app.pool.acquire(block=True) as conn:
                conn.default_channel.client.delete(lock_key)
                conn.default_channel.client.delete(task_key)
        except Exception as cleanup_error:
            alog.error(f"Failed to cleanup task locks: {cleanup_error}")

@app.task(
    bind=True,
    retries=1,
    soft_time_limit=60 * 10,  # 10 minutes for updating all student tiers
    time_limit=60 * 12        # 12 minutes hard limit
)
def update_engagement_tiers_task(self, *args, **kwargs):
    """
    Periodic task to update all student engagement tiers.
    Runs every 2 hours to ensure responsive tier classifications.
    """
    task_id = self.request.id
    task_key = f"running_task:update_engagement_tiers:{task_id}"
    lock_key = "lock:update_engagement_tiers"
    
    try:
        # Check if another instance is already running
        with app.pool.acquire(block=True) as conn:
            if conn.default_channel.client.exists(lock_key):
                alog.info("Engagement tier update task already running, skipping this execution")
                return {"skipped": True, "reason": "Previous task still running"}
            
            # Set lock with 30 minute expiration (shorter since task runs every 2h)
            conn.default_channel.client.setex(lock_key, 60 * 30, task_id)
            conn.default_channel.client.setex(task_key, 60 * 30, 'RUNNING')
        
        alog.info("Starting periodic engagement tier update for all students")
        
        # Import and run the tier service
        from addie.services.engagement_tiers import EngagementTierService
        tier_service = EngagementTierService()
        
        # Update all student tiers
        result = tier_service.batch_update_all_tiers()
        
        # Get tier distribution for monitoring
        distribution = tier_service.get_tier_distribution()
        
        alog.info(f"Engagement tier update completed. Updated: {result['updated']}, Failed: {result['failed']}")
        alog.info(f"Current tier distribution: {distribution}")
        
        return {
            "success": True,
            "message": f"Updated {result['updated']} student tiers",
            "data": {
                "updated_count": result['updated'],
                "failed_count": result['failed'],
                "tier_distribution": distribution
            }
        }
        
    except Exception as e:
        error_msg = f"Error in engagement tier update task: {e}"
        alog.error(error_msg)
        return {"success": False, "error": str(e)}
    
    finally:
        # Always clean up locks
        try:
            with app.pool.acquire(block=True) as conn:
                conn.default_channel.client.delete(lock_key)
                conn.default_channel.client.delete(task_key)
        except Exception as cleanup_error:
            alog.error(f"Failed to cleanup tier update task locks: {cleanup_error}")

app.conf.beat_schedule = dict(
    # send_engagement_emails_task=dict(
    #     task='addie.tasks.send_survey_reminder_task',
    #     schedule=crontab(minute=30, hour=15),
    # ),
    update_token_usage_task=dict(
        task='addie.tasks.update_token_usage_task',
        schedule=crontab(minute='*/15'),
    ),
    refresh_dashboard_view_task=dict(
        task='addie.tasks.refresh_dashboard_view_task',
        schedule=crontab(minute='*/15'),
    ),
    update_engagement_tiers_task=dict(
        task='addie.tasks.update_engagement_tiers_task',
        schedule=crontab(minute=0, hour='*/2'),  # Every 2 hours
    ),
    # periodic_assign_published_conversations_task=dict(
    #     task='addie.tasks.periodic_assign_published_conversations_task',
    #     schedule=crontab(minute='*/15'),
    # )
)

__all__ = ['app']
