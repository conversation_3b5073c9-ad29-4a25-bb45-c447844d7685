import re
from typing import Sequence, List, Optional

import alog
import psycopg
from celery import Celery
from langchain_core.messages import BaseMessage, messages_from_dict
from langchain_postgres import PostgresChatMessageHistory
from psycopg import sql
from langchain_core.messages import (
    HumanMessage, AIMessage, SystemMessage,
    ChatMessage, FunctionMessage, ToolMessage
)

from addie import celery_config

app = Celery('addie')
conf = app.config_from_object(celery_config)

student_experiment_task = app.signature(
    'addie.tasks.gen_video_for_slide_task'
)

embed_message_task = app.signature(
    'addie.tasks.embed_message_task',
)


def _get_messages_query(table_name: str, session_id: str, limit: int = 3, last_msg_id: int = 0) -> sql.Composed:
    """Make a SQL query to get messages for a given session."""
    if limit > 0:
        query_str = ("SELECT message "
                     f"FROM {table_name} "
                     f"WHERE session_id = '{session_id}' "
                     f"AND id > {last_msg_id} "
                     f"ORDER BY id DESC LIMIT {limit};")

        query = (query_str) \
            .format(table_name=table_name,
                    session_id=session_id)

        return sql.SQL(query)

    else:
        query = ("SELECT message "
                 f"FROM {table_name} "
                 f"WHERE session_id = '{session_id}' "
                 f"AND id > {last_msg_id} "
                 f"ORDER BY id DESC;") \
            .format(table_name=table_name,
                    session_id=session_id)

        return sql.SQL(query)


class EmbeddedHistory(PostgresChatMessageHistory):
    def __init__(
            self,
            table_name: str,
            session_id: str,
            last_msg_id: int = 0,
            /,
            *,
            sync_connection: Optional[psycopg.Connection] = None,
            async_connection: Optional[psycopg.AsyncConnection] = None,
    ) -> None:
        """Client for persisting chat message history in a Postgres database,

        This client provides support for both sync and async via psycopg >=3.

        The client can create schema in the database and provides methods to
        add messages, get messages, and clear the chat message history.

        The schema has the following columns:

        - id: A serial primary key.
        - session_id: The session ID for the chat message history.
        - message: The JSONB message content.
        - created_at: The timestamp of when the message was created.

        Messages are retrieved for a given session_id and are sorted by
        the id (which should be increasing monotonically), and correspond
        to the order in which the messages were added to the history.

        The "created_at" column is not returned by the interface, but
        has been added for the schema so the information is available in the database.

        A session_id can be used to separate different chat histories in the same table,
        the session_id should be provided when initializing the client.

        This chat history client takes in a psycopg connection object (either
        Connection or AsyncConnection) and uses it to interact with the database.

        This design allows to reuse the underlying connection object across
        multiple instantiations of this class, making instantiation fast.

        This chat history client is designed for prototyping applications that
        involve chat and are based on Postgres.

        As your application grows, you will likely need to extend the schema to
        handle more complex queries. For example, a chat application
        may involve multiple tables like a user table, a table for storing
        chat sessions / conversations, and this table for storing chat messages
        for a given session. The application will require access to additional
        endpoints like deleting messages by user id, listing conversations by
        user id or ordering them based on last message time, etc.

        Feel free to adapt this implementation to suit your application's needs.

        Args:
            session_id: The session ID to use for the chat message history
            table_name: The name of the database table to use
            sync_connection: An existing psycopg connection instance
            async_connection: An existing psycopg async connection instance

        Usage:
            - Use the create_tables or acreate_tables method to set up the table
              schema in the database.
            - Initialize the class with the appropriate session ID, table name,
              and database connection.
            - Add messages to the database using add_messages or aadd_messages.
            - Retrieve messages with get_messages or aget_messages.
            - Clear the session history with clear or aclear when needed.

        Note:
            - At least one of sync_connection or async_connection must be provided.

        Examples:

        .. code-block:: python

            import uuid

            from langchain_core.messages import SystemMessage, AIMessage, HumanMessage
            from langchain_postgres import PostgresChatMessageHistory
            import psycopg

            # Establish a synchronous connection to the database
            # (or use psycopg.AsyncConnection for async)
            sync_connection = psycopg2.connect(conn_info)

            # Create the table schema (only needs to be done once)
            table_name = "chat_history"
            PostgresChatMessageHistory.create_tables(sync_connection, table_name)

            session_id = str(uuid.uuid4())

            # Initialize the chat history manager
            chat_history = PostgresChatMessageHistory(
                table_name,
                session_id,
                sync_connection=sync_connection
            )

            # Add messages to the chat history
            chat_history.add_messages([
                SystemMessage(content="Meow"),
                AIMessage(content="woof"),
                HumanMessage(content="bark"),
            ])

            print(chat_history.messages)
        """
        self.last_msg_id = last_msg_id
        if not sync_connection and not async_connection:
            raise ValueError("Must provide sync_connection or async_connection")

        self._connection = sync_connection
        self._aconnection = async_connection

        assert session_id

        self._session_id = session_id

        if not re.match(r"^\w+$", table_name):
            raise ValueError(
                "Invalid table name. Table name must contain only alphanumeric "
                "characters and underscores."
            )
        self._table_name = table_name

    def add_messages(self, messages: Sequence[BaseMessage]) -> None:
        """Add messages to the chat history with validation for OpenAI tool/tool_calls requirements."""
        # TODO: enable this after improving qdrant performance
        # for msg in messages:
        #     if msg.type == 'human':
        #         data = dict(content=msg.content)
        #         embed_message_task.apply_async(kwargs=dict(
        #             message=data,
        #             user_id=self._session_id
        #         ))

        # Validate tool/tool_calls sequences before adding messages
        validated_messages = self._validate_tool_sequences(messages)
        
        # Override parent implementation to capture the last inserted message ID
        self._add_messages_with_id_tracking(validated_messages)

    def _add_messages_with_id_tracking(self, messages: Sequence[BaseMessage]) -> None:
        """Add messages to the chat history and track the last inserted ID."""
        alog.info(f"_add_messages_with_id_tracking called with {len(messages)} messages")
        
        if self._connection is None:
            raise ValueError(
                "Please initialize the PostgresChatMessageHistory "
                "with a sync connection or use the aadd_messages method instead."
            )

        from langchain_postgres.chat_message_histories import _insert_message_query, message_to_dict
        import json

        values = [
            (self._session_id, json.dumps(message_to_dict(message)))
            for message in messages
        ]

        query = _insert_message_query(self._table_name)

        # Reset the last message ID
        self.last_inserted_message_id = None

        with self._connection.cursor() as cursor:
            # Insert messages one by one to capture the last ID
            for i, value in enumerate(values):
                cursor.execute(query, value)
                # Get the ID of the last inserted row
                cursor.execute("SELECT lastval()")
                message_id = cursor.fetchone()[0]
                # Store the last message ID for retrieval
                self.last_inserted_message_id = message_id
                alog.info(f"Inserted message {i+1}/{len(values)} with ID: {message_id}")
                
        self._connection.commit()
        alog.info(f"Committed {len(messages)} messages, last ID: {self.last_inserted_message_id}")

    def get_last_message_id(self) -> Optional[int]:
        """Get the database ID of the last message that was added."""
        return getattr(self, 'last_inserted_message_id', None)

    def _validate_tool_sequences(self, new_messages: Sequence[BaseMessage]) -> List[BaseMessage]:
        """
        Validate that tool messages follow OpenAI's requirements:
        - A message with role "tool" must be a response to a preceding message with "tool_calls"
        
        This method will:
        1. Get the existing message history
        2. Combine it with new messages
        3. Validate the tool/tool_calls sequence
        4. Raise ValueError if invalid tool messages are found
        
        Returns the validated list of new messages to add.
        Raises ValueError if any orphaned tool messages are detected.
        """
        # Get existing messages for context
        try:
            existing_messages = self.get_messages()
        except Exception as e:
            alog.warning(f"Could not get existing messages for validation: {e}")
            existing_messages = []
        
        # Combine existing and new messages for validation
        all_messages = existing_messages + list(new_messages)
        
        # Track valid messages
        validated_new_messages = []
        
        # Keep track of pending tool calls that need responses
        pending_tool_calls = set()
        
        # Process all messages to build context of what tool calls are valid
        for i, msg in enumerate(all_messages):
            # If this is a new message (beyond existing messages)
            is_new_message = i >= len(existing_messages)
            
            if hasattr(msg, 'tool_calls') and msg.tool_calls:
                # AI message with tool calls - track the tool call IDs
                for tool_call in msg.tool_calls:
                    if hasattr(tool_call, 'id'):
                        pending_tool_calls.add(tool_call.id)
                    elif isinstance(tool_call, dict) and 'id' in tool_call:
                        pending_tool_calls.add(tool_call['id'])
                
                if is_new_message:
                    validated_new_messages.append(msg)
            
            elif msg.type == 'tool' or (hasattr(msg, 'role') and msg.role == 'tool'):
                # Tool message - validate it has a corresponding tool call
                tool_call_id = None
                
                # Extract tool_call_id from various possible locations
                if hasattr(msg, 'tool_call_id'):
                    tool_call_id = msg.tool_call_id
                elif hasattr(msg, 'additional_kwargs') and 'tool_call_id' in msg.additional_kwargs:
                    tool_call_id = msg.additional_kwargs['tool_call_id']
                elif isinstance(msg, dict):
                    tool_call_id = msg.get('tool_call_id')
                    if not tool_call_id and 'data' in msg:
                        tool_call_id = msg['data'].get('tool_call_id')
                
                if tool_call_id and tool_call_id in pending_tool_calls:
                    # Valid tool message - remove from pending
                    pending_tool_calls.remove(tool_call_id)
                    if is_new_message:
                        validated_new_messages.append(msg)
                elif is_new_message:
                    # Invalid tool message - raise ValueError
                    error_msg = f"Orphaned tool message detected: tool_call_id '{tool_call_id}' has no corresponding tool_calls message. Available tool_call_ids: {list(pending_tool_calls)}"
                    alog.error(error_msg)
                    raise ValueError(error_msg)
            else:
                # Regular message (human, ai without tool calls, system, etc.)
                if is_new_message:
                    validated_new_messages.append(msg)
        
        return validated_new_messages

    def get_messages(self, limit: int = 0) -> List[BaseMessage]:
        """Retrieve messages from the chat message history."""
        if self._connection is None:
            raise ValueError(
                "Please initialize the PostgresChatMessageHistory "
                "with a sync connection or use the async aget_messages method instead."
            )

        query = _get_messages_query(
            self._table_name, self._session_id, limit, self.last_msg_id)

        with self._connection.cursor() as cursor:
            cursor.execute(query, {"session_id": self._session_id})
            raw_items = [record[0] for record in cursor.fetchall()]
            
        # Ensure each message has a 'type' field before passing to messages_from_dict
        items = []
        for raw_item in raw_items:
            # Handle case where an item might be a list of message dictionaries
            if isinstance(raw_item, list):
                alog.debug(f"Flattening list of messages: {raw_item}")
                for sub_item in raw_item:
                    if isinstance(sub_item, dict):
                        items.append(sub_item)
                continue
                
            # Process individual message dictionaries
            item = raw_item
            # Create a proper structure for each message type
            if 'type' not in item:
                # Check if the message has a role field that can be mapped to a type
                if 'role' in item:
                    role_to_type = {
                        'user': 'human',
                        'assistant': 'ai',
                        'system': 'system',
                        'function': 'function',
                        'tool': 'tool'
                    }
                    item['type'] = role_to_type.get(item['role'], 'chat')
                # If there's no role field but there's content, default to 'human'
                elif 'content' in item and 'data' not in item:
                    # Move content to data field and set type
                    item = {'type': 'human', 'data': {'content': item['content']}}
                # If there's already a data field, assume it's a human message
                elif 'data' in item:
                    item['type'] = 'human'
                # Default fallback
                else:
                    item['type'] = 'chat'
            
            # Ensure data structure is correct for the message type
            if 'data' not in item:
                item['data'] = {'content': item.get('content', '')}
            
            # Fix inconsistencies between type and data
            if item['type'] == 'ai' and 'content' in item:
                # For AI messages, ensure proper structure
                item = {
                    'type': 'ai',
                    'data': {'content': item['content']}
                }
            elif item['type'] == 'human' and 'content' in item:
                # For human messages, ensure proper structure
                item = {
                    'type': 'human',
                    'data': {'content': item['content']}
                }
            
            items.append(item)

        try:
            # Create messages directly using the appropriate classes instead of using messages_from_dict
            # This gives us more control over the message creation process
            messages = []
            for item in items:
                try:
                    message_type = item.get('type', 'chat')
                    
                    # Get content from either direct content field or data.content
                    content = ""
                    if 'content' in item:
                        content = item['content']
                    elif 'data' in item and 'content' in item['data']:
                        content = item['data']['content']
                    
                    # Additional kwargs to pass to the message constructor
                    additional_kwargs = {}
                    if 'additional_kwargs' in item:
                        additional_kwargs = item['additional_kwargs']
                    elif 'data' in item and 'additional_kwargs' in item['data']:
                        additional_kwargs = item['data']['additional_kwargs']
                    
                   
                    if message_type == 'human' or message_type == 'user':
                        messages.append(HumanMessage(content=content, additional_kwargs=additional_kwargs))
                    elif message_type == 'ai' or message_type == 'assistant' or message_type == 'Addie':
                        # Handle tool_calls for AI messages
                        tool_calls = []
                        if 'tool_calls' in item:
                            tool_calls = item['tool_calls']
                        elif 'data' in item and 'tool_calls' in item['data']:
                            tool_calls = item['data']['tool_calls']
                        
                        messages.append(AIMessage(content=content, additional_kwargs=additional_kwargs, tool_calls=tool_calls))
                    elif message_type == 'system':
                        messages.append(SystemMessage(content=content, additional_kwargs=additional_kwargs))
                    elif message_type == 'function':
                        messages.append(FunctionMessage(content=content, additional_kwargs=additional_kwargs))
                    elif message_type == 'tool':
                        # Extract tool_call_id from the data dictionary or the item itself
                        tool_call_id = None
                        if 'data' in item and 'tool_call_id' in item['data']:
                            tool_call_id = item['data']['tool_call_id']
                        elif 'tool_call_id' in additional_kwargs:
                            tool_call_id = additional_kwargs.pop('tool_call_id')
                            
                        # If tool_call_id is present, pass it to the constructor
                        if tool_call_id:
                            messages.append(ToolMessage(content=content, tool_call_id=tool_call_id, additional_kwargs=additional_kwargs))
                        else:
                            # Fall back to generic ChatMessage for backward compatibility
                            alog.warning(f"Missing tool_call_id for tool message: {item}")
                            messages.append(ChatMessage(content=content, role='tool', additional_kwargs=additional_kwargs))
                    else:
                        # Default to ChatMessage for unknown types
                        messages.append(ChatMessage(content=content, role=message_type, additional_kwargs=additional_kwargs))
                        
                except Exception as msg_error:
                    alog.error(f"Error converting individual message: {msg_error}")
                    alog.error(f"Problematic message: {item}")
                    raise
                    # Skip this message and continue with others
            
            # Reverse to get messages in chronological order
            messages.reverse()

            if len(messages) > 0 and limit > 0:
                messages = self.remove_orphaned_tool_msgs(messages)
        except Exception as e:
            alog.error(f"Error in message conversion process: {e}")
            alog.error(f"Problematic message format: {items}")
            messages = []

        alog.info(f'''Got {len(messages)} messages from the database''')
        
        # Limit messages to those that amount to less than 128000 tokens
        try:
            # Simple token estimation - approximate tokens by counting characters and dividing by 4
            # This is a rough estimate as most tokenizers use ~4 chars per token on average
            def estimate_tokens(msg):
                if not hasattr(msg, 'content') or not msg.content:
                    return 0
                # Approximate token count based on character count
                content_length = len(str(msg.content))
                token_estimate = content_length // 4
                return token_estimate
            
            # Count total tokens and truncate if needed
            total_tokens = 0
            # Process messages from newest to oldest since we typically want the most recent messages
            truncated_messages = []
            max_tokens = 500000
            
            for i, msg in enumerate(messages):
                msg_tokens = estimate_tokens(msg)
                
                if total_tokens + msg_tokens <= max_tokens:
                    truncated_messages.append(msg)
                    total_tokens += msg_tokens
                else:
                    # We've hit the token limit
                    break
            
            # Only log summary information
            alog.info(f"Token usage: {total_tokens}/{max_tokens} tokens used ({(total_tokens/max_tokens)*100:.1f}%)")
            if len(truncated_messages) < len(messages):
                alog.info(f"Truncated history from {len(messages)} to {len(truncated_messages)} messages due to token limit")
            
            messages = truncated_messages
        except Exception as e:
            alog.error(f"Error during token limiting: {e}")
            # If token limiting fails, we'll just return all messages
        
        return messages

    def remove_orphaned_tool_msgs(self, messages):
        ai_message_ix = -1
        for i, msg in enumerate(messages):
            if hasattr(msg, 'type'):
                if msg.type == 'ai':
                    ai_message_ix = i
                    break
        last_msg = messages[0]
        if hasattr(last_msg, 'role'):
            if last_msg.role == 'tool':
                if ai_message_ix > 0:
                    messages = messages[len(messages) - ai_message_ix:]

        return messages
