import alog

from addie import settings
from addie.counselor_agent.context import StudentCounselorContext
from addie.history import get_history
from addie.lib import prisma_client, pick
from addie.student_agent.naive_student_context import NaiveStudentContext
from dataclasses import asdict
from langchain_core.messages import BaseMessage
from typing import Optional, TypedDict, List

prisma = prisma_client()


class CounselorAgentState(TypedDict):
    student_context: dict
    messages_id: str
    messages: List[BaseMessage]
    model: Optional[str]
    temperature: int
    history_len: int
    user_id: str
    id: str
    user: Optional[dict] = None
    last_msg_id: Optional[int] = 0

    @staticmethod
    async def init(
        id: str, student_id: str, user_id: str, last_msg_id: int
    ) -> "CounselorAgentState":
        model = settings.MODEL
        prisma = prisma_client()
        messages_id = f"{id}-{student_id}"
        user = prisma.user.find_unique_or_raise(where={"id": id})
        user = pick(["id", "first_name", "last_name"], user.dict())

        return CounselorAgentState(
            id=id,
            last_msg_id=last_msg_id,
            user=user,
            messages=[],
            messages_id=messages_id,
            model=model,
            temperature=settings.TEMP,
            user_id=user_id,
            student_context=asdict(
                await StudentCounselorContext(
                    student_id=student_id,
                    messages_id=messages_id,
                    user_id=user_id,
                    counselor_id=id,
                ).async_init()
            ),
        )
