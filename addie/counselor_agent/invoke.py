import alog
import asyncio
import threading
from langchain_core.runnables import RunnableWithMessageHistory

from addie.agent.call_tool import tools
from addie.counselor_agent.state import Counselor<PERSON>gentState
from addie.data_model.system_prompt_storage import SystemPromptStorage
from addie.history import get_history
from addie.lib import dict_to_json, pick
from addie.student_agent.state import StudentAgentState
from langchain_core.messages import SystemMessage, AIMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.utils.function_calling import convert_to_openai_tool
from langchain_openai import ChatOpenAI
from pydantic import validate_call


@validate_call()
def gen_student_system_prompt(state: dict) -> str:
    state = pick(['student_context', 'user'], state)
    system_prompt = f"""
    ## context begins
        context:
    {dict_to_json(state)}
    ## context ends
    You are an assistant for a college counsellor which tends to 11th graders. 
    When communicating with the counselor please consider the data/context above. 
    """

    return system_prompt


def invoke_counselor_agent(state: CounselorAgentState):
    messages = state['messages']
    model = state['model']
    last_msg_id = state['last_msg_id']
    session_id = state['messages_id']
    alog.info(f'### session-id {session_id} ###')
    alog.info(f'## last msg id: {last_msg_id} ##')

    if len(messages) > 0:
        last_msg = messages[-1]

        assert session_id

        if last_msg.type == 'human':
            prompt = ChatPromptTemplate.from_messages(
                [
                    SystemMessage(content=gen_student_system_prompt(state)),
                    MessagesPlaceholder("message_history", optional=True, n_history=2),
                    MessagesPlaceholder("question", optional=True),
                ]
            )

            chain = ChatOpenAI(model=model)

            # chain = chain.bind(tools=[convert_to_openai_tool(tool) for tool in tools])

            chain = prompt | chain

            chain_with_history = RunnableWithMessageHistory(
                chain,
                lambda: get_history(session_id, last_msg_id),
                input_messages_key="question",
                history_messages_key="message_history",
                name=session_id
            )

            config = {"configurable": {"session_id": session_id}}

            response = chain_with_history.invoke(dict(
                question=[last_msg],
            ), config)

            ai_message = AIMessage(content=response.content)
            messages.append(ai_message)

            state['messages'] = messages
            
            # Save SystemPromptContext record for this AI response
            # Get the database message ID by querying for the most recent AI message in this session
            try:
                history = get_history(session_id, last_msg_id)
                db_message_id = None
                
                # Query the database directly for the most recent AI message in this session
                with history._connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT id FROM messages 
                        WHERE session_id = %s 
                        AND message->>'type' = 'ai'
                        ORDER BY id DESC 
                        LIMIT 1
                    """, (session_id,))
                    result = cursor.fetchone()
                    if result:
                        db_message_id = result[0]
                        alog.info(f"Found most recent AI message with database ID: {db_message_id}")
                    else:
                        alog.warning("No AI message found in database for this session")
                
                if db_message_id:
                    # Generate the system prompt for storage
                    system_prompt = gen_student_system_prompt(state)
                    
                    # Save the system prompt context (async fire-and-forget)
                    # This prevents blocking the counselor conversation flow
                    def run_async_save():
                        """Run async save in a separate thread to avoid blocking."""
                        try:
                            # Create new event loop for this thread
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            loop.run_until_complete(
                                SystemPromptStorage.save_with_message_async(
                                    message_id=db_message_id,
                                    system_prompt=system_prompt,
                                    student_context=state.get('student_context', {}),
                                    agent_type="counselor"
                                )
                            )
                            loop.close()
                        except Exception as e:
                            alog.error(f"Error in background async save for counselor message {db_message_id}: {str(e)}")
                    
                    # Run in background thread (fire-and-forget)
                    thread = threading.Thread(target=run_async_save, daemon=True)
                    thread.start()
                    alog.info(f"Initiated background async save of SystemPromptContext for counselor message {db_message_id}")
                else:
                    alog.warning("Could not save SystemPromptContext: no valid database message ID found")
                    
            except Exception as e:
                alog.error(f"Error saving SystemPromptContext for counselor agent: {str(e)}")
                # Don't fail the entire conversation for this error

            return state

    return state
