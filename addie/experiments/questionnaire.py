"""
Questionnaire experiments module for running questionnaire tests.
"""

from addie.data_model.generated_student import GeneratedStudent
from addie.data_model.student_context import StudentContext
from addie.lib import prisma_client, dict_to_json
from addie.student_agent.invoke_student import invoke_student
from addie.student_agent.state import StudentAgentS<PERSON>
from addie.agent import agent
from addie.agent.state.state import AgentState
from datetime import datetime
from langchain_core.messages import HumanMessage
from langchain.globals import set_verbose
from pydantic import validate_call
from typing import Optional
import alog
import json
import time
import uuid

prisma = prisma_client()
set_verbose(True)


def is_complete(state):
    """Check if the questionnaire is complete."""
    return not state["student_context"]["current_question"]


@validate_call
async def run_questionnaire_test(
    config_id: str, workflow_id: str, max_steps: int = 100, delete_config: bool = False
):
    """
    Run a questionnaire test with the given configuration.
    
    Args:
        config_id: The ID of the student agent configuration
        workflow_id: The ID of the workflow to test
        max_steps: Maximum number of steps to execute (default: 100)
        delete_config: Whether to delete the config after the test (default: False)
    
    Returns:
        Dictionary with test results
    """
    # Validate the config_id is provided
    config = prisma.studentagentconfig.find_unique_or_raise(where=dict(id=config_id))
    gen_student = GeneratedStudent(prompt_id=config.prompt_id)
    user_id = gen_student.id

    # Make sure the workflow is properly configured for the test
    workflow = prisma.workflow.find_unique_or_raise(
        where=dict(id=workflow_id), include=dict(steps=True)
    )

    # Check workflow status and type
    alog.info(
        f"Workflow {workflow_id} status: {workflow.status}, type: {workflow.workflow_type}"
    )

    student_context = await StudentContext(
        _workflow_id=workflow_id,
        user_id=user_id,
        id=gen_student.student_id,
        messages=[],
        mode="Test",
        grade=11
    ).async_init()

    # Make sure workflow is published and unstructured for ensure_student_workflows to work
    if workflow.status != "PUBLISHED" or workflow.workflow_type != "UNSTRUCTURED":
        alog.info(f"Updating workflow {workflow_id} to be PUBLISHED and UNSTRUCTURED")
        workflow = prisma.workflow.update(
            where=dict(id=workflow_id),
            data=dict(status="PUBLISHED", workflow_type="UNSTRUCTURED"),
        )

    # Check if the workflow has steps
    if len(workflow.steps) == 0:
        alog.info(
            f"Workflow {workflow_id} has no steps. The StudentContext will handle creating steps as needed."
        )
    else:
        alog.info(f"Workflow {workflow_id} has {len(workflow.steps)} steps")

    # Verify the student workflow exists
    student_wf = prisma.studentworkflow.find_first_or_raise(
        where=dict(
            student_id=gen_student.student_id,
            workflow_id=workflow_id,
        ),
        include=dict(
            steps=True, workflow=True  # Make sure to include the workflow relationship
        ),
    )

    alog.info(f"Student workflow has {len(student_wf.steps)} steps before processing")

    # Use the student_wf we already looked up above
    wf = student_wf

    assert wf.workflow.id == workflow_id
    pending_wfs = student_context.pending_workflows
    alog.info(len(pending_wfs))
    alog.info(pending_wfs)

    wf_id = pending_wfs[0]["id"]

    alog.info(wf_id)

    student_wf = prisma.studentworkflow.find_unique_or_raise(
        where=dict(id=wf_id),
        include=dict(workflow=True, steps=True),
    )

    alog.info(student_wf.workflow.name)
    alog.info(len(student_wf.steps))

    data = dict(
        student_id=gen_student.student_id,
        workflow_id=workflow_id,
    )

    alog.info(alog.pformat(data))

    student_workflow = prisma.studentworkflow.find_first_or_raise(
        where=dict(
            student_id=gen_student.student_id,
            workflow_id=workflow_id,
        ),
        include=dict(workflow=True),  # Make sure to include the workflow relationship
    )
    wf_id = student_workflow.id

    alog.debug(f"### student_workflow_id: {student_workflow.id} ###")

    result = await _test_questionnaire(
        config_id=config_id,
        user_id=user_id,
        max_steps=max_steps,
        workflow_id=workflow_id,
        delete_config=delete_config,
    )
    
    return result


async def _test_questionnaire(
    config_id: str,
    user_id: str,
    max_steps: int = 0,
    workflow_id: str = None,
    delete_config: bool = True,
):
    """
    Internal function to execute the questionnaire test.
    """
    # Store config_id for cleanup
    config_to_delete = config_id
    try:
        msgs = []
        # Get the user and student information
        user = prisma.user.find_first(
            where=dict(id=user_id), include=dict(students=True)
        )
        student_id = user.students[0].id
        # Find a student with academic achievements for alternate_user_id
        ava = prisma.student.find_first(
            where=dict(academic_achievements=dict(some=dict())),
            include=dict(users=True),
        )

        alternate_user_id = None
        if ava and ava.users:
            alternate_user_id = ava.users[0].id

        student_context = await StudentContext(
            user_id=user_id,
            id=student_id,
            alternate_user_id=alternate_user_id,
            messages=[msg.dict() for msg in msgs],
            mode="Test",
            _workflow_id=workflow_id,
        ).async_init()

        assert student_context.student_workflow_id
        workflow_id = student_context.workflow_id

        # Create a session
        session_id = f"{workflow_id}-{user_id}"

        if not workflow_id:
            prisma.studentworkflow.delete_many(
                where=dict(
                    student_id=student_id,
                    workflow_id=workflow_id,
                )
            )

        # Create student agent
        config = prisma.studentagentconfig.find_unique_or_raise(
            where=dict(id=config_id)
        )

        student_agent = prisma.studentagent.create(
            data=dict(
                config_id=config.id, prompt_id=config.prompt_id, student_id=student_id
            ),
            include=dict(prompt=True),
        )

        student_agent_id = student_agent.id

        # Create additional IDs for history tracking
        agent_history_id = uuid.uuid4().hex
        student_history_id = uuid.uuid4().hex
        student_session_id = uuid.uuid4().hex

        state = await AgentState.init(
            session_id=session_id,
            user_id=user_id,
            student_id=student_id,
            log_student_context=True,
            alternate_user_id=alternate_user_id,
            mode="Test",  # Set mode to Test for testing environment
            student_workflow_id=student_context.student_workflow_id,
        )

        assert student_agent.prompt

        student_state = StudentAgentState.init(
            session_id=student_session_id,
            user_id=user_id,
            id=student_id,
            student_agent=student_agent,
        )

        step_count = 0

        student_msgs = student_state["messages"]

        assert len(student_msgs) == 0

        msgs = state["messages"]

        def should_continue():
            if max_steps == 0:
                return not is_complete(state)
            else:
                return step_count <= max_steps

        step_durations = []

        while should_continue():
            alog.info(f"### step {step_count} ###")

            if step_count % 2 == 0:
                # time this
                start_time = time.time()
                state["messages"] = msgs
                student_context = await StudentContext(
                    user_id=user_id,
                    id=student_id,
                    alternate_user_id=alternate_user_id,
                    messages=[msg.dict() for msg in msgs],
                    mode="Test",
                    _workflow_id=workflow_id,
                ).async_init()

                current_question = student_context.current_question
                if not current_question:
                    raise Exception("No current question")

                await student_context.async_init()

                state["student_context"] = student_context.dump()

                if not should_continue():
                    break

                state = agent().invoke(state)

                alog.info(f'### has messages: {"messages" in state}')

                msgs = state["messages"]

                # Get the last non-tool message
                non_tool_msgs = [
                    msg
                    for msg in msgs
                    if not hasattr(msg, "name") or msg.name != "goal_completion_tool"
                ]
                last_msg = non_tool_msgs[-1] if non_tool_msgs else msgs[-1]

                if last_msg not in student_msgs:
                    additional_kwargs = json.loads(
                        dict_to_json(dict(created_at=datetime.now()))
                    )
                msg = HumanMessage(content=last_msg.content, **additional_kwargs)
                student_msgs.append(msg)

                duration = time.time() - start_time

                step_durations.append(duration)
                # duration average
                duration_avg = sum(step_durations) / len(step_durations)

                alog.info((duration, duration_avg))

            else:
                student_state["messages"] = student_msgs
                student_state = invoke_student(student_state)
                student_msgs = student_state["messages"]

                last_msg = student_msgs[-1]

                msg = HumanMessage(content=last_msg.content)
                msgs.append(msg)

                if (
                    prisma.studentworkflow.find_unique(
                        where=dict(id=student_context.student_workflow_id)
                    ).status
                    == "COMPLETED"
                ):
                    alog.info("Student workflow completed")
                    break

            step_count += 1

        # Analyze results using history API
        from addie.history import get_history
        
        # Get message history from the session
        history = get_history(session_id)
        messages = history.get_messages()
        
        # Count messages
        human_messages = [m for m in messages if m.type == "human"]
        ai_messages = [m for m in messages if m.type == "ai"] 
        
        # Get completed steps by querying StudentWorkflow directly
        student_workflow = prisma.studentworkflow.find_first(
            where=dict(student_id=student_id, workflow_id=workflow_id),
            include=dict(steps=True)
        )
        
        # Check completed steps
        completed_steps = 0
        if student_workflow and student_workflow.steps:
            completed_steps = len([s for s in student_workflow.steps if s.completed])

        result = {
            "student_id": student_id,
            "message_count": len(messages),
            "human_messages": len(human_messages),
            "ai_messages": len(ai_messages),
            "completed_steps": completed_steps,
        }

        alog.info("Questionnaire Analysis Results: ")
        alog.info(alog.pformat(result))

        # Run evaluators if requested
        if completed_steps > 0:
            await run_evaluators(user_id=user_id, workflow_id=workflow_id)
            
        return result

    except Exception as e:
        alog.error(f"Error in questionnaire test: {e}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        # Clean up if requested
        if delete_config and config_to_delete:
            try:
                prisma.studentagentconfig.delete(where=dict(id=config_to_delete))
                alog.info(f"Deleted config {config_to_delete}")
            except Exception as e:
                alog.error(f"Failed to delete config {config_to_delete}: {e}")


async def run_evaluators(user_id: str, workflow_id: str = None):
    """
    Run raw prompt evaluators for the given user and workflow.
    """
    from addie import celery_config
    from celery import Celery
    
    app = Celery("addie")
    conf = app.config_from_object(celery_config)
    run_evaluator_task = app.signature("addie.tasks.run_evaluator_task")
    
    # Only run raw prompt evaluators
    configs = prisma.chatevaluatorconfig.find_many(
        where={"is_raw_prompt": True}
    )

    alog.info(f"Found {len(configs)} raw prompt evaluators to queue")

    for config in configs:
        args = dict(user_id=user_id, config_id=config.id, workflow_id=workflow_id)

        print("### raw prompt evaluator args ###")
        print(args)
        run_evaluator_task.apply_async(kwargs=args)

    alog.info(f"## {len(configs)} raw prompt evaluators queued ##")