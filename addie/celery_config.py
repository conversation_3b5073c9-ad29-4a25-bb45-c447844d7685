from addie import settings

worker_redirect_stdouts = False
worker_log_format = '%(asctime)s [%(levelname)s] %(message)s'
enable_utc = False
imports = []

task_routes = {
    'addie.tasks.student_experiment_task': dict(queue='student_experiment'),
    'addie.tasks.run_evaluator_task': dict(queue='student_experiment'),
    'addie.tasks.gen_unstructured_conversation_summary_task': dict(queue='student_experiment'),
    'addie.tasks.calculate_ocean_scores_task': dict(queue='student_experiment'),
    #     'addie.tasks.test_task': {'queue': 'default'},
}

result_backend = settings.REDIS_CONN
backend = settings.REDIS_CONN
broker_url = settings.REDIS_CONN
broker = settings.REDIS_CONN

worker_send_task_events = True
task_send_sent_event = True

concurrency = 2
max_threads = 2
