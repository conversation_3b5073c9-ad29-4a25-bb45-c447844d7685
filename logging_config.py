import logging
import sys

def configure_openai_logging(level=logging.DEBUG):
    """
    Configure logging for OpenAI API calls.
    
    Args:
        level: The logging level to use (default: logging.DEBUG)
    """
    # Create a formatter that includes timestamp, level, and message
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Create a handler for console output
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    
    # Create a handler for file output
    file_handler = logging.FileHandler('openai_api.log')
    file_handler.setFormatter(formatter)
    
    # Configure the openai logger
    openai_logger = logging.getLogger("openai")
    openai_logger.setLevel(level)
    openai_logger.addHandler(console_handler)
    openai_logger.addHandler(file_handler)
    
    # Configure httpx logger (used by OpenAI for HTTP requests)
    httpx_logger = logging.getLogger("httpx")
    httpx_logger.setLevel(level)
    httpx_logger.addHandler(console_handler)
    httpx_logger.addHandler(file_handler)
    
    # Configure httpcore logger (used by httpx)
    httpcore_logger = logging.getLogger("httpcore")
    httpcore_logger.setLevel(level)
    httpcore_logger.addHandler(console_handler)
    httpcore_logger.addHandler(file_handler)
    
    print(f"OpenAI logging configured at level: {logging.getLevelName(level)}")
    print(f"Logs will be written to: openai_api.log")

if __name__ == "__main__":
    configure_openai_logging()
