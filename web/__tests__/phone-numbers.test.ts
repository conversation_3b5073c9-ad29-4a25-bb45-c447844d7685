import { describe, it, expect, jest, beforeEach, afterEach } from "@jest/globals";

// Mock the auth function
const mockAuth = jest.fn();
jest.mock("@/auth", () => ({
  auth: mockAuth,
}));

// Mock the revalidatePath function
const mockRevalidatePath = jest.fn();
jest.mock("next/cache", () => ({
  revalidatePath: mockRevalidatePath,
}));

// Import after mocks are set up
const { 
  getPhoneNumbers,
  addPhoneNumber,
  updatePhoneNumberPriority,
  updatePhoneNumberStatus,
  updatePhoneNumberCapabilities,
  deletePhoneNumber,
  syncWithTwilio,
  getUsageStatistics
} = require("@/actions/phone-numbers");

// Mock fetch globally
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock environment variables
process.env.ADDIE_API_HOST = "http://localhost:8000";
process.env.ADDIE_API_KEY = "test-api-key";

describe("Phone Numbers Server Actions", () => {
  const mockSession = {
    user: {
      role: "ADMIN",
      id: "test-user-id",
      email: "<EMAIL>"
    }
  };

  const mockPhoneNumber = {
    id: "test-phone-id",
    phone_number: "+15551234567",
    twilio_sid: "PN123456789",
    country_code: "+1",
    country_name: "United States",
    is_active: true,
    capabilities: {
      sms: true,
      voice: true,
      mms: false
    },
    twilio_verified: true,
    voice_webhook_url: "https://example.com/voice",
    sms_webhook_url: "https://example.com/sms",
    priority: 10,
    last_synced_at: "2024-01-01T12:00:00Z",
    sync_error: null,
    created_at: "2024-01-01T10:00:00Z",
    updated_at: "2024-01-01T12:00:00Z"
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock successful auth by default
    mockAuth.mockResolvedValue(mockSession);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("getPhoneNumbers", () => {
    it("should successfully fetch phone numbers", async () => {
      // Mock successful API response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [mockPhoneNumber],
      });

      const result = await getPhoneNumbers();

      expect(result.success).toBe(true);
      expect(result.data).toEqual([mockPhoneNumber]);
      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/admin/phone-numbers",
        expect.objectContaining({
          headers: expect.objectContaining({
            "Authorization": "Bearer test-api-key",
            "Content-Type": "application/json"
          })
        })
      );
    });

    it("should handle unauthorized access", async () => {
      mockAuth.mockResolvedValueOnce(null);

      const result = await getPhoneNumbers();

      expect(result.success).toBe(false);
      expect(result.error).toBe("Unauthorized");
    });

    it("should handle API errors", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: async () => ({ message: "API Error" }),
      });

      const result = await getPhoneNumbers();

      expect(result.success).toBe(false);
      expect(result.error).toBe("API Error");
    });

    it("should handle network errors", async () => {
      mockFetch.mockRejectedValueOnce(new Error("Network Error"));

      const result = await getPhoneNumbers();

      expect(result.success).toBe(false);
      expect(result.error).toBe("Failed to fetch phone numbers");
    });
  });

  describe("addPhoneNumber", () => {
    it("should successfully add a phone number", async () => {
      const formData = new FormData();
      formData.set("phone_number", "+15551234567");
      formData.set("country_code", "+1");
      formData.set("country_name", "United States");
      formData.set("sms", "true");
      formData.set("voice", "true");
      formData.set("mms", "false");
      formData.set("priority", "10");

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockPhoneNumber,
      });

      const result = await addPhoneNumber(formData);

      expect(result.success).toBe(true);
      expect(result.message).toBe("Phone number added successfully");
      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/admin/phone-numbers",
        expect.objectContaining({
          method: "POST",
          body: JSON.stringify({
            phone_number: "+15551234567",
            country_code: "+1",
            country_name: "United States",
            capabilities: {
              sms: true,
              voice: true,
              mms: false
            },
            priority: 10
          })
        })
      );
    });

    it("should handle form data with default values", async () => {
      const formData = new FormData();
      formData.set("phone_number", "+15551234567");
      formData.set("country_code", "+1");
      formData.set("country_name", "United States");
      // Not setting priority should default to 0

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockPhoneNumber,
      });

      const result = await addPhoneNumber(formData);

      expect(result.success).toBe(true);
      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/admin/phone-numbers",
        expect.objectContaining({
          body: expect.stringContaining('"priority":0')
        })
      );
    });
  });

  describe("updatePhoneNumberPriority", () => {
    it("should successfully update phone number priority", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({}),
      });

      const result = await updatePhoneNumberPriority("test-phone-id", 15);

      expect(result.success).toBe(true);
      expect(result.message).toBe("Priority updated successfully");
      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/admin/phone-numbers/test-phone-id/priority",
        expect.objectContaining({
          method: "PUT",
          body: JSON.stringify({ priority: 15 })
        })
      );
    });

    it("should handle API errors for priority update", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: async () => ({ message: "Invalid priority value" }),
      });

      const result = await updatePhoneNumberPriority("test-phone-id", 150);

      expect(result.success).toBe(false);
      expect(result.error).toBe("Invalid priority value");
    });
  });

  describe("updatePhoneNumberStatus", () => {
    it("should successfully update phone number status to active", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({}),
      });

      const result = await updatePhoneNumberStatus("test-phone-id", true);

      expect(result.success).toBe(true);
      expect(result.message).toBe("Status updated successfully");
      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/admin/phone-numbers/test-phone-id",
        expect.objectContaining({
          method: "PUT",
          body: JSON.stringify({ is_active: true })
        })
      );
    });

    it("should successfully update phone number status to inactive", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({}),
      });

      const result = await updatePhoneNumberStatus("test-phone-id", false);

      expect(result.success).toBe(true);
      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/admin/phone-numbers/test-phone-id",
        expect.objectContaining({
          body: JSON.stringify({ is_active: false })
        })
      );
    });
  });

  describe("updatePhoneNumberCapabilities", () => {
    it("should successfully update phone number capabilities", async () => {
      const capabilities = { sms: true, voice: false, mms: true };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({}),
      });

      const result = await updatePhoneNumberCapabilities("test-phone-id", capabilities);

      expect(result.success).toBe(true);
      expect(result.message).toBe("Capabilities updated successfully");
      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/admin/phone-numbers/test-phone-id",
        expect.objectContaining({
          method: "PUT",
          body: JSON.stringify({ capabilities })
        })
      );
    });
  });

  describe("deletePhoneNumber", () => {
    it("should successfully delete a phone number", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({}),
      });

      const result = await deletePhoneNumber("test-phone-id");

      expect(result.success).toBe(true);
      expect(result.message).toBe("Phone number deleted successfully");
      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/admin/phone-numbers/test-phone-id",
        expect.objectContaining({
          method: "DELETE"
        })
      );
    });

    it("should handle delete errors", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: async () => ({ message: "Phone number not found" }),
      });

      const result = await deletePhoneNumber("nonexistent-id");

      expect(result.success).toBe(false);
      expect(result.error).toBe("Phone number not found");
    });
  });

  describe("syncWithTwilio", () => {
    it("should successfully sync with Twilio", async () => {
      const syncResult = {
        status: "success",
        numbers_added: 2,
        numbers_updated: 5,
        numbers_deactivated: 1,
        errors: []
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => syncResult,
      });

      const result = await syncWithTwilio();

      expect(result.success).toBe(true);
      expect(result.data).toEqual(syncResult);
      expect(result.message).toBe("Sync completed: 2 added, 5 updated, 1 deactivated");
      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/admin/phone-numbers/sync",
        expect.objectContaining({
          method: "POST"
        })
      );
    });

    it("should handle sync with no changes", async () => {
      const syncResult = {
        status: "success",
        numbers_added: 0,
        numbers_updated: 0,
        numbers_deactivated: 0,
        errors: []
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => syncResult,
      });

      const result = await syncWithTwilio();

      expect(result.success).toBe(true);
      expect(result.message).toBe("Sync completed: 0 added, 0 updated, 0 deactivated");
    });

    it("should handle sync failures", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: async () => ({ message: "Twilio API error" }),
      });

      const result = await syncWithTwilio();

      expect(result.success).toBe(false);
      expect(result.error).toBe("Twilio API error");
    });
  });

  describe("getUsageStatistics", () => {
    it("should successfully get usage statistics with enhanced analytics", async () => {
      const usageStats = [
        {
          phone_number: "+15551234567",
          country_code: "+1",
          country_name: "United States",
          total_sms: 150,
          total_voice: 25,
          total_success: 170,
          total_failure: 5,
          days_active: 7,
          // Enhanced fields from CallRecord integration
          total_call_duration: 45.5,
          avg_call_duration: 4.55,
          total_answered_calls: 20,
          total_failed_calls: 5,
          call_success_rate: 80.0,
          recent_calls: [
            {
              call_sid: "CA123456789",
              status: "COMPLETED",
              duration: 300,
              start_time: "2024-01-15T10:30:00Z",
              student_id: "student_001"
            },
            {
              call_sid: "CA987654321", 
              status: "FAILED",
              duration: 0,
              start_time: "2024-01-15T09:15:00Z",
              student_id: "student_002"
            }
          ]
        }
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => usageStats,
      });

      const result = await getUsageStatistics(7);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(usageStats);
      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/admin/phone-numbers/usage-stats?days=7",
        expect.objectContaining({
          headers: expect.objectContaining({
            "Authorization": "Bearer test-api-key"
          })
        })
      );
    });

    it("should handle custom days parameter", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [],
      });

      await getUsageStatistics(30);

      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/admin/phone-numbers/usage-stats?days=30",
        expect.any(Object)
      );
    });

    it("should use default days parameter", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [],
      });

      await getUsageStatistics();

      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/admin/phone-numbers/usage-stats?days=7",
        expect.any(Object)
      );
    });

    it("should handle enhanced analytics with call duration and success rates", async () => {
      const enhancedStats = [
        {
          phone_number: "+15551234567",
          country_code: "+1", 
          country_name: "United States",
          total_sms: 100,
          total_voice: 50,
          total_success: 140,
          total_failure: 10,
          days_active: 7,
          total_call_duration: 120.5,
          avg_call_duration: 2.41,
          total_answered_calls: 45,
          total_failed_calls: 5,
          call_success_rate: 90.0,
          recent_calls: []
        }
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => enhancedStats,
      });

      const result = await getUsageStatistics(7);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(enhancedStats);
      expect(result.data[0].total_call_duration).toBe(120.5);
      expect(result.data[0].call_success_rate).toBe(90.0);
      expect(result.data[0].total_answered_calls).toBe(45);
      expect(result.data[0].total_failed_calls).toBe(5);
    });

    it("should handle recent calls data structure", async () => {
      const statsWithCalls = [
        {
          phone_number: "+15551234567",
          country_code: "+1",
          country_name: "United States", 
          total_sms: 10,
          total_voice: 5,
          total_success: 14,
          total_failure: 1,
          days_active: 3,
          total_call_duration: 15.0,
          avg_call_duration: 3.0,
          total_answered_calls: 4,
          total_failed_calls: 1,
          call_success_rate: 80.0,
          recent_calls: [
            {
              call_sid: "CA123456789",
              status: "COMPLETED",
              duration: 180,
              start_time: "2024-01-15T10:30:00Z", 
              student_id: "student_001"
            },
            {
              call_sid: "CA987654321",
              status: "FAILED",
              duration: 0,
              start_time: "2024-01-15T09:15:00Z",
              student_id: "student_002"
            }
          ]
        }
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => statsWithCalls,
      });

      const result = await getUsageStatistics(7);

      expect(result.success).toBe(true);
      expect(result.data[0].recent_calls).toHaveLength(2);
      expect(result.data[0].recent_calls[0].call_sid).toBe("CA123456789");
      expect(result.data[0].recent_calls[0].status).toBe("COMPLETED");
      expect(result.data[0].recent_calls[0].duration).toBe(180);
      expect(result.data[0].recent_calls[1].status).toBe("FAILED");
    });

    it("should handle missing enhanced analytics fields gracefully", async () => {
      const basicStats = [
        {
          phone_number: "+15551234567",
          country_code: "+1",
          country_name: "United States",
          total_sms: 10,
          total_voice: 5,
          total_success: 14,
          total_failure: 1,
          days_active: 3
          // Missing enhanced fields - should be handled gracefully
        }
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => basicStats,
      });

      const result = await getUsageStatistics(7);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(basicStats);
      // Should not throw error even with missing enhanced fields
    });

    it("should handle empty recent calls array", async () => {
      const statsWithEmptyCalls = [
        {
          phone_number: "+15551234567",
          country_code: "+1",
          country_name: "United States",
          total_sms: 10,
          total_voice: 5,
          total_success: 14,
          total_failure: 1,
          days_active: 3,
          total_call_duration: 0.0,
          avg_call_duration: 0.0,
          total_answered_calls: 0,
          total_failed_calls: 0,
          call_success_rate: 0.0,
          recent_calls: []
        }
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => statsWithEmptyCalls,
      });

      const result = await getUsageStatistics(7);

      expect(result.success).toBe(true);
      expect(result.data[0].recent_calls).toEqual([]);
      expect(result.data[0].total_call_duration).toBe(0.0);
      expect(result.data[0].call_success_rate).toBe(0.0);
    });
  });

  describe("Authentication and Authorization", () => {
    it("should reject requests from non-admin users", async () => {
      mockAuth.mockResolvedValueOnce({
        user: {
          role: "USER",
          id: "test-user-id",
          email: "<EMAIL>"
        }
      });

      const result = await getPhoneNumbers();

      expect(result.success).toBe(false);
      expect(result.error).toBe("Unauthorized");
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it("should reject requests without authentication", async () => {
      mockAuth.mockResolvedValueOnce(null);

      const result = await getPhoneNumbers();

      expect(result.success).toBe(false);
      expect(result.error).toBe("Unauthorized");
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it("should handle auth errors gracefully", async () => {
      mockAuth.mockRejectedValueOnce(new Error("Auth service unavailable"));

      const result = await getPhoneNumbers();

      expect(result.success).toBe(false);
      expect(result.error).toBe("Failed to fetch phone numbers");
    });
  });

  describe("Error Handling", () => {
    it("should handle malformed JSON responses", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: async () => {
          throw new Error("Invalid JSON");
        },
      });

      const result = await getPhoneNumbers();

      expect(result.success).toBe(false);
      expect(result.error).toBe("Failed to fetch phone numbers");
    });

    it("should handle network timeouts", async () => {
      mockFetch.mockImplementationOnce(() => 
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error("Network timeout")), 100);
        })
      );

      const result = await getPhoneNumbers();

      expect(result.success).toBe(false);
      expect(result.error).toBe("Failed to fetch phone numbers");
    });

    it("should handle unexpected response formats", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ unexpected: "format" }),
      });

      const result = await getPhoneNumbers();

      expect(result.success).toBe(true);
      expect(result.data).toEqual({ unexpected: "format" });
    });
  });

  describe("Cache Revalidation", () => {
    it("should revalidate cache after successful operations", async () => {
      // Test add operation
      const formData = new FormData();
      formData.set("phone_number", "+15551234567");
      formData.set("country_code", "+1");
      formData.set("country_name", "United States");

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockPhoneNumber,
      });

      await addPhoneNumber(formData);

      expect(mockRevalidatePath).toHaveBeenCalledWith("/phone-numbers");

      // Test delete operation
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({}),
      });

      await deletePhoneNumber("test-phone-id");

      expect(mockRevalidatePath).toHaveBeenCalledWith("/phone-numbers");
      expect(mockRevalidatePath).toHaveBeenCalledTimes(2);
    });

    it("should not revalidate cache on failed operations", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: async () => ({ message: "Operation failed" }),
      });

      await deletePhoneNumber("test-phone-id");

      expect(mockRevalidatePath).not.toHaveBeenCalled();
    });
  });
});