/**
 * @jest-environment jsdom
 */

import { render, screen, waitFor } from "@testing-library/react";
import { describe, it, expect, jest, beforeEach } from "@jest/globals";
import PhoneNumberAnalytics from "@/app/(protected)/phone-numbers/analytics/page";

// Mock the getUsageStatistics action
const mockGetUsageStatistics = jest.fn();
jest.mock("@/actions/phone-numbers", () => ({
  getUsageStatistics: mockGetUsageStatistics,
}));

describe("PhoneNumberAnalytics Component", () => {
  const mockUsageStats = [
    {
      phone_number: "+15551234567",
      country_code: "+1",
      country_name: "United States",
      total_sms: 25,
      total_voice: 10,
      total_success: 30,
      total_failure: 5,
      days_active: 7,
      // Enhanced fields
      total_call_duration: 45.5, // 45.5 minutes
      avg_call_duration: 4.55,   // 4.55 minutes average
      total_answered_calls: 8,
      total_failed_calls: 2,
      call_success_rate: 80.0,
      recent_calls: [
        {
          call_sid: "CA123456789",
          status: "COMPLETED",
          duration: 300, // 5 minutes in seconds
          start_time: "2024-01-15T10:30:00Z",
          student_id: "student_001"
        },
        {
          call_sid: "CA987654321",
          status: "FAILED",
          duration: 0,
          start_time: "2024-01-15T09:15:00Z",
          student_id: "student_002"
        },
        {
          call_sid: "CA555666777",
          status: "COMPLETED",
          duration: 180, // 3 minutes in seconds
          start_time: "2024-01-14T14:20:00Z",
          student_id: "student_003"
        }
      ]
    },
    {
      phone_number: "+15559876543",
      country_code: "+1",
      country_name: "United States",
      total_sms: 15,
      total_voice: 5,
      total_success: 18,
      total_failure: 2,
      days_active: 5,
      // Enhanced fields
      total_call_duration: 20.0,
      avg_call_duration: 4.0,
      total_answered_calls: 5,
      total_failed_calls: 0,
      call_success_rate: 100.0,
      recent_calls: [
        {
          call_sid: "CA111222333",
          status: "COMPLETED",
          duration: 240, // 4 minutes
          start_time: "2024-01-15T11:45:00Z",
          student_id: "student_004"
        }
      ]
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders loading state initially", () => {
    mockGetUsageStatistics.mockReturnValue(
      new Promise(() => {}) // Never resolves to keep loading state
    );

    render(<PhoneNumberAnalytics />);
    
    expect(screen.getByText("Loading analytics...")).toBeInTheDocument();
  });

  it("displays enhanced analytics overview cards with correct data", async () => {
    mockGetUsageStatistics.mockResolvedValue({
      success: true,
      data: mockUsageStats
    });

    render(<PhoneNumberAnalytics />);

    await waitFor(() => {
      expect(screen.queryByText("Loading analytics...")).not.toBeInTheDocument();
    });

    // Verify basic overview cards
    expect(screen.getByText("Total SMS")).toBeInTheDocument();
    expect(screen.getByText("40")).toBeInTheDocument(); // 25 + 15 SMS

    expect(screen.getByText("Total Voice")).toBeInTheDocument();
    expect(screen.getByText("15")).toBeInTheDocument(); // 10 + 5 Voice

    // Verify enhanced call analytics cards
    expect(screen.getByText("Total Call Time")).toBeInTheDocument();
    expect(screen.getByText("65.5m")).toBeInTheDocument(); // 45.5 + 20.0 minutes

    expect(screen.getByText("Avg Call Duration")).toBeInTheDocument();
    expect(screen.getByText("5.0m")).toBeInTheDocument(); // (45.5 + 20.0) / (8+2+5+0) = 4.37 rounds to ~5.0

    expect(screen.getByText("Call Success Rate")).toBeInTheDocument();
    expect(screen.getByText("86.7%")).toBeInTheDocument(); // (8+5)/(8+2+5+0) = 13/15 = 86.7%

    expect(screen.getByText("Failed Calls")).toBeInTheDocument();
    expect(screen.getByText("2")).toBeInTheDocument(); // 2 + 0 failed calls
  });

  it("displays detailed phone number analytics with enhanced call data", async () => {
    mockGetUsageStatistics.mockResolvedValue({
      success: true,
      data: mockUsageStats
    });

    render(<PhoneNumberAnalytics />);

    await waitFor(() => {
      expect(screen.queryByText("Loading analytics...")).not.toBeInTheDocument();
    });

    // Verify first phone number details
    expect(screen.getByText("+15551234567")).toBeInTheDocument();
    expect(screen.getByText("United States (+1)")).toBeInTheDocument();

    // Verify SMS and Voice badges
    expect(screen.getByText("25 SMS")).toBeInTheDocument();
    expect(screen.getByText("10 Voice")).toBeInTheDocument();

    // Verify enhanced call analytics section
    expect(screen.getByText("Call Analytics")).toBeInTheDocument();
    
    // Check call duration display
    expect(screen.getByText("45.5m total")).toBeInTheDocument();
    expect(screen.getByText("Avg: 4.6m")).toBeInTheDocument(); // 4.55 rounds to 4.6

    // Check answered/failed calls
    expect(screen.getByText("8")).toBeInTheDocument(); // answered calls
    expect(screen.getByText("2")).toBeInTheDocument(); // failed calls (in the analytics section)

    // Check call success rate
    expect(screen.getByText("80.0%")).toBeInTheDocument();
  });

  it("displays recent calls with correct status badges and formatting", async () => {
    mockGetUsageStatistics.mockResolvedValue({
      success: true,
      data: mockUsageStats
    });

    render(<PhoneNumberAnalytics />);

    await waitFor(() => {
      expect(screen.queryByText("Loading analytics...")).not.toBeInTheDocument();
    });

    // Verify recent calls section
    expect(screen.getByText("Recent Calls")).toBeInTheDocument();

    // Check for COMPLETED status badge
    const completedBadges = screen.getAllByText("COMPLETED");
    expect(completedBadges).toHaveLength(3); // 2 from first phone + 1 from second phone

    // Check for FAILED status badge
    expect(screen.getByText("FAILED")).toBeInTheDocument();

    // Check call durations (converted from seconds to minutes)
    expect(screen.getByText("5m")).toBeInTheDocument(); // 300 seconds = 5 minutes
    expect(screen.getByText("3m")).toBeInTheDocument(); // 180 seconds = 3 minutes
    expect(screen.getByText("4m")).toBeInTheDocument(); // 240 seconds = 4 minutes

    // Check dates are formatted
    expect(screen.getByText("1/15/2024")).toBeInTheDocument(); // Date formatting
  });

  it("handles empty or no call data gracefully", async () => {
    const statsWithoutCalls = [
      {
        phone_number: "+15551111111",
        country_code: "+1",
        country_name: "United States",
        total_sms: 10,
        total_voice: 5,
        total_success: 15,
        total_failure: 0,
        days_active: 3,
        total_call_duration: 0,
        avg_call_duration: 0,
        total_answered_calls: 0,
        total_failed_calls: 0,
        call_success_rate: 0,
        recent_calls: []
      }
    ];

    mockGetUsageStatistics.mockResolvedValue({
      success: true,
      data: statsWithoutCalls
    });

    render(<PhoneNumberAnalytics />);

    await waitFor(() => {
      expect(screen.queryByText("Loading analytics...")).not.toBeInTheDocument();
    });

    // Should display basic stats
    expect(screen.getByText("+15551111111")).toBeInTheDocument();
    expect(screen.getByText("10 SMS")).toBeInTheDocument();
    expect(screen.getByText("5 Voice")).toBeInTheDocument();

    // Should not display call analytics section when no calls
    expect(screen.queryByText("Call Analytics")).not.toBeInTheDocument();

    // Enhanced metrics should show zeros
    expect(screen.getByText("0.0m")).toBeInTheDocument(); // Total call time
    expect(screen.getByText("0")).toBeInTheDocument(); // Failed calls
  });

  it("displays correct calculations for multiple phone numbers", async () => {
    mockGetUsageStatistics.mockResolvedValue({
      success: true,
      data: mockUsageStats
    });

    render(<PhoneNumberAnalytics />);

    await waitFor(() => {
      expect(screen.queryByText("Loading analytics...")).not.toBeInTheDocument();
    });

    // Verify totals across all phone numbers
    // Total SMS: 25 + 15 = 40
    expect(screen.getByText("40")).toBeInTheDocument();

    // Total Voice: 10 + 5 = 15
    expect(screen.getByText("15")).toBeInTheDocument();

    // Total Call Duration: 45.5 + 20.0 = 65.5
    expect(screen.getByText("65.5m")).toBeInTheDocument();

    // Total Answered Calls: 8 + 5 = 13
    // Total Failed Calls: 2 + 0 = 2
    // Call Success Rate: 13 / (13 + 2) = 86.7%
    expect(screen.getByText("86.7%")).toBeInTheDocument();

    // Active Numbers: 2 (both have SMS + Voice > 0)
    expect(screen.getByText("2")).toBeInTheDocument();
  });

  it("handles API errors gracefully", async () => {
    mockGetUsageStatistics.mockResolvedValue({
      success: false,
      error: "API Error"
    });

    // Mock console.error to avoid noise in test output
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    render(<PhoneNumberAnalytics />);

    await waitFor(() => {
      expect(screen.queryByText("Loading analytics...")).not.toBeInTheDocument();
    });

    // Should display empty state
    expect(screen.getByText("No usage data available for the selected period.")).toBeInTheDocument();

    // Should have called console.error
    expect(consoleSpy).toHaveBeenCalledWith("Error fetching usage stats:", "API Error");

    consoleSpy.mockRestore();
  });

  it("handles network errors gracefully", async () => {
    mockGetUsageStatistics.mockRejectedValue(new Error("Network Error"));

    // Mock console.error to avoid noise in test output
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    render(<PhoneNumberAnalytics />);

    await waitFor(() => {
      expect(screen.queryByText("Loading analytics...")).not.toBeInTheDocument();
    });

    // Should display empty state
    expect(screen.getByText("No usage data available for the selected period.")).toBeInTheDocument();

    // Should have called console.error
    expect(consoleSpy).toHaveBeenCalledWith("Error fetching usage stats:", expect.any(Error));

    consoleSpy.mockRestore();
  });

  it("displays recent calls limit correctly", async () => {
    const statsWithManyCalls = [{
      ...mockUsageStats[0],
      recent_calls: [
        ...mockUsageStats[0].recent_calls,
        {
          call_sid: "CA444555666",
          status: "COMPLETED",
          duration: 120,
          start_time: "2024-01-13T16:00:00Z",
          student_id: "student_005"
        },
        {
          call_sid: "CA777888999",
          status: "COMPLETED", 
          duration: 90,
          start_time: "2024-01-12T12:30:00Z",
          student_id: "student_006"
        }
      ]
    }];

    mockGetUsageStatistics.mockResolvedValue({
      success: true,
      data: statsWithManyCalls
    });

    render(<PhoneNumberAnalytics />);

    await waitFor(() => {
      expect(screen.queryByText("Loading analytics...")).not.toBeInTheDocument();
    });

    // Should display "+X more calls" when there are more than 3 calls
    expect(screen.getByText("+2 more calls")).toBeInTheDocument();
  });

  it("formats call durations correctly in different units", async () => {
    const statsWithLongCalls = [{
      ...mockUsageStats[0],
      total_call_duration: 125.5, // Over 60 minutes
    }];

    mockGetUsageStatistics.mockResolvedValue({
      success: true,
      data: statsWithLongCalls
    });

    render(<PhoneNumberAnalytics />);

    await waitFor(() => {
      expect(screen.queryByText("Loading analytics...")).not.toBeInTheDocument();
    });

    // Should display hours when duration > 60 minutes
    expect(screen.getByText("2.1h")).toBeInTheDocument(); // 125.5 / 60 = 2.09 rounded to 2.1
    expect(screen.getByText("125.5 minutes total")).toBeInTheDocument();
  });

  it("updates UI when data changes", async () => {
    const { rerender } = render(<PhoneNumberAnalytics />);

    // Initial data
    mockGetUsageStatistics.mockResolvedValueOnce({
      success: true,
      data: [mockUsageStats[0]]
    });

    await waitFor(() => {
      expect(screen.getByText("+15551234567")).toBeInTheDocument();
    });

    // Simulate data update with different stats
    const updatedStats = [{
      ...mockUsageStats[0],
      total_sms: 50, // Changed from 25
      total_voice: 20, // Changed from 10
    }];

    mockGetUsageStatistics.mockResolvedValueOnce({
      success: true,
      data: updatedStats
    });

    // Force re-fetch by re-rendering
    rerender(<PhoneNumberAnalytics />);

    await waitFor(() => {
      expect(screen.getByText("50 SMS")).toBeInTheDocument();
      expect(screen.getByText("20 Voice")).toBeInTheDocument();
    });
  });
});