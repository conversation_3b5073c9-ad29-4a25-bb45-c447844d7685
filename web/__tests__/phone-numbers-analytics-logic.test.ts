/**
 * Tests for phone number analytics logic and data processing
 * These tests focus on the data transformation and calculation logic
 * without depending on complex component imports
 */

import { describe, it, expect } from "@jest/globals";

// Mock data structure matching our enhanced analytics
interface RecentCallInfo {
  call_sid: string;
  status: string;
  duration?: number;
  start_time?: string;
  student_id: string;
}

interface UsageStats {
  phone_number: string;
  country_code: string;
  country_name: string;
  total_sms: number;
  total_voice: number;
  total_success: number;
  total_failure: number;
  days_active: number;
  total_call_duration: number;
  avg_call_duration: number;
  total_answered_calls: number;
  total_failed_calls: number;
  call_success_rate: number;
  recent_calls: RecentCallInfo[];
}

// Analytics calculation functions (extracted from component logic)
function calculateTotalCallDuration(stats: UsageStats[]): number {
  return stats.reduce((sum, stat) => sum + (stat.total_call_duration || 0), 0);
}

function calculateTotalAnsweredCalls(stats: UsageStats[]): number {
  return stats.reduce((sum, stat) => sum + (stat.total_answered_calls || 0), 0);
}

function calculateTotalFailedCalls(stats: UsageStats[]): number {
  return stats.reduce((sum, stat) => sum + (stat.total_failed_calls || 0), 0);
}

function calculateCallSuccessRate(stats: UsageStats[]): number {
  const totalAnswered = calculateTotalAnsweredCalls(stats);
  const totalFailed = calculateTotalFailedCalls(stats);
  const totalCalls = totalAnswered + totalFailed;
  return totalCalls > 0 ? (totalAnswered / totalCalls) * 100 : 0;
}

function calculateAvgCallDuration(stats: UsageStats[]): number {
  const totalDuration = calculateTotalCallDuration(stats);
  const totalAnswered = calculateTotalAnsweredCalls(stats);
  const totalFailed = calculateTotalFailedCalls(stats);
  const totalCalls = totalAnswered + totalFailed;
  return totalCalls > 0 ? totalDuration / totalCalls : 0;
}

function formatCallDuration(duration: number): string {
  if (duration < 60) {
    return `${duration.toFixed(1)}m`;
  } else {
    return `${(duration / 60).toFixed(1)}h`;
  }
}

function formatCallDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString();
}

function convertSecondsToDurationDisplay(seconds: number): string {
  return `${Math.round(seconds / 60)}m`;
}

describe("Phone Number Analytics Logic", () => {
  const mockUsageStats: UsageStats[] = [
    {
      phone_number: "+15551234567",
      country_code: "+1",
      country_name: "United States",
      total_sms: 25,
      total_voice: 10,
      total_success: 30,
      total_failure: 5,
      days_active: 7,
      total_call_duration: 45.5,
      avg_call_duration: 4.55,
      total_answered_calls: 8,
      total_failed_calls: 2,
      call_success_rate: 80.0,
      recent_calls: [
        {
          call_sid: "CA123456789",
          status: "COMPLETED",
          duration: 300,
          start_time: "2024-01-15T10:30:00Z",
          student_id: "student_001"
        },
        {
          call_sid: "CA987654321",
          status: "FAILED", 
          duration: 0,
          start_time: "2024-01-15T09:15:00Z",
          student_id: "student_002"
        }
      ]
    },
    {
      phone_number: "+15559876543",
      country_code: "+1", 
      country_name: "United States",
      total_sms: 15,
      total_voice: 5,
      total_success: 18,
      total_failure: 2,
      days_active: 5,
      total_call_duration: 20.0,
      avg_call_duration: 4.0,
      total_answered_calls: 5,
      total_failed_calls: 0,
      call_success_rate: 100.0,
      recent_calls: [
        {
          call_sid: "CA111222333",
          status: "COMPLETED",
          duration: 240,
          start_time: "2024-01-15T11:45:00Z",
          student_id: "student_004"
        }
      ]
    }
  ];

  describe("Total Calculations", () => {
    it("should calculate total call duration correctly", () => {
      const total = calculateTotalCallDuration(mockUsageStats);
      expect(total).toBe(65.5); // 45.5 + 20.0
    });

    it("should calculate total answered calls correctly", () => {
      const total = calculateTotalAnsweredCalls(mockUsageStats);
      expect(total).toBe(13); // 8 + 5
    });

    it("should calculate total failed calls correctly", () => {
      const total = calculateTotalFailedCalls(mockUsageStats);
      expect(total).toBe(2); // 2 + 0
    });

    it("should handle empty stats array", () => {
      expect(calculateTotalCallDuration([])).toBe(0);
      expect(calculateTotalAnsweredCalls([])).toBe(0);
      expect(calculateTotalFailedCalls([])).toBe(0);
    });

    it("should handle stats with missing enhanced fields", () => {
      const basicStats = [{
        ...mockUsageStats[0],
        total_call_duration: undefined as any,
        total_answered_calls: undefined as any,
        total_failed_calls: undefined as any,
      }];

      expect(calculateTotalCallDuration(basicStats)).toBe(0);
      expect(calculateTotalAnsweredCalls(basicStats)).toBe(0);
      expect(calculateTotalFailedCalls(basicStats)).toBe(0);
    });
  });

  describe("Success Rate Calculations", () => {
    it("should calculate call success rate correctly", () => {
      const rate = calculateCallSuccessRate(mockUsageStats);
      expect(rate).toBeCloseTo(86.67, 1); // 13/(13+2) = 86.67%
    });

    it("should return 0 for success rate when no calls", () => {
      const emptyStats = [
        {
          ...mockUsageStats[0],
          total_answered_calls: 0,
          total_failed_calls: 0
        }
      ];
      expect(calculateCallSuccessRate(emptyStats)).toBe(0);
    });

    it("should handle 100% success rate", () => {
      const perfectStats = [
        {
          ...mockUsageStats[0],
          total_answered_calls: 10,
          total_failed_calls: 0
        }
      ];
      expect(calculateCallSuccessRate(perfectStats)).toBe(100);
    });

    it("should handle 0% success rate", () => {
      const failedStats = [
        {
          ...mockUsageStats[0],
          total_answered_calls: 0,
          total_failed_calls: 10
        }
      ];
      expect(calculateCallSuccessRate(failedStats)).toBe(0);
    });
  });

  describe("Average Duration Calculations", () => {
    it("should calculate average call duration correctly", () => {
      const avg = calculateAvgCallDuration(mockUsageStats);
      expect(avg).toBeCloseTo(4.37, 1); // 65.5/(8+2+5+0) = 4.37
    });

    it("should return 0 for average when no calls", () => {
      const emptyStats = [
        {
          ...mockUsageStats[0],
          total_call_duration: 0,
          total_answered_calls: 0,
          total_failed_calls: 0
        }
      ];
      expect(calculateAvgCallDuration(emptyStats)).toBe(0);
    });
  });

  describe("Formatting Functions", () => {
    it("should format call duration in minutes for values < 60", () => {
      expect(formatCallDuration(45.5)).toBe("45.5m");
      expect(formatCallDuration(59.9)).toBe("59.9m");
    });

    it("should format call duration in hours for values >= 60", () => {
      expect(formatCallDuration(60)).toBe("1.0h");
      expect(formatCallDuration(125.5)).toBe("2.1h");
    });

    it("should convert seconds to duration display", () => {
      expect(convertSecondsToDurationDisplay(300)).toBe("5m"); // 5 minutes
      expect(convertSecondsToDurationDisplay(180)).toBe("3m"); // 3 minutes
      expect(convertSecondsToDurationDisplay(0)).toBe("0m");
    });

    it("should format dates correctly", () => {
      const date = formatCallDate("2024-01-15T10:30:00Z");
      expect(date).toBe("1/15/2024"); // US date format
    });
  });

  describe("Recent Calls Processing", () => {
    it("should handle empty recent calls array", () => {
      const statsWithNoCalls = [{
        ...mockUsageStats[0],
        recent_calls: []
      }];

      expect(statsWithNoCalls[0].recent_calls).toHaveLength(0);
    });

    it("should limit recent calls display to first 3", () => {
      const statsWithManyCalls = [{
        ...mockUsageStats[0],
        recent_calls: [
          ...mockUsageStats[0].recent_calls,
          {
            call_sid: "CA444555666",
            status: "COMPLETED",
            duration: 120,
            start_time: "2024-01-13T16:00:00Z",
            student_id: "student_005"
          },
          {
            call_sid: "CA777888999",
            status: "COMPLETED",
            duration: 90,
            start_time: "2024-01-12T12:30:00Z",
            student_id: "student_006"
          }
        ]
      }];

      const firstThree = statsWithManyCalls[0].recent_calls.slice(0, 3);
      expect(firstThree).toHaveLength(3);
      expect(statsWithManyCalls[0].recent_calls.length > 3).toBe(true);
    });

    it("should properly identify call status types", () => {
      const call1 = mockUsageStats[0].recent_calls[0];
      const call2 = mockUsageStats[0].recent_calls[1];

      expect(call1.status).toBe("COMPLETED");
      expect(call2.status).toBe("FAILED");
    });
  });

  describe("Data Validation", () => {
    it("should handle missing optional fields gracefully", () => {
      const incompleteStats = [{
        phone_number: "+15551111111",
        country_code: "+1",
        country_name: "United States",
        total_sms: 10,
        total_voice: 5,
        total_success: 15,
        total_failure: 0,
        days_active: 3,
        // Missing enhanced fields
      } as any];

      expect(() => calculateTotalCallDuration(incompleteStats)).not.toThrow();
      expect(() => calculateCallSuccessRate(incompleteStats)).not.toThrow();
    });

    it("should handle malformed recent calls data", () => {
      const malformedStats = [{
        ...mockUsageStats[0],
        recent_calls: null as any
      }];

      expect(malformedStats[0].recent_calls).toBeNull();
    });
  });

  describe("Edge Cases", () => {
    it("should handle very large numbers", () => {
      const largeStats = [{
        ...mockUsageStats[0],
        total_call_duration: 999999.5,
        total_answered_calls: 100000,
        total_failed_calls: 50000
      }];

      expect(calculateTotalCallDuration(largeStats)).toBe(999999.5);
      expect(calculateCallSuccessRate(largeStats)).toBeCloseTo(66.67, 1);
    });

    it("should handle zero values", () => {
      const zeroStats = [{
        ...mockUsageStats[0],
        total_call_duration: 0,
        total_answered_calls: 0,
        total_failed_calls: 0,
        recent_calls: []
      }];

      expect(calculateTotalCallDuration(zeroStats)).toBe(0);
      expect(calculateCallSuccessRate(zeroStats)).toBe(0);
      expect(calculateAvgCallDuration(zeroStats)).toBe(0);
    });

    it("should handle negative values (data corruption scenario)", () => {
      const negativeStats = [{
        ...mockUsageStats[0],
        total_call_duration: -10,
        total_answered_calls: -5,
        total_failed_calls: -2
      }];

      // Should still calculate without throwing errors
      expect(() => calculateTotalCallDuration(negativeStats)).not.toThrow();
      expect(calculateTotalCallDuration(negativeStats)).toBe(-10);
    });
  });
});