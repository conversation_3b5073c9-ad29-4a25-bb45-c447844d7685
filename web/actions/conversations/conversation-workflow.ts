"use server";

//Function create custom conversation workflow
import { auth } from "@/auth";
import {
  $Enums,
  MultipleChoiceType,
  Prisma,
  WorkflowStatus,
  WorkflowType,
} from "@prisma/client";

import { env } from "@/env.mjs";
import { baseApiUrl } from "@/common/api";
import { prisma } from "@/common/db";
import log from "@/common/logger";
import { convertQuestion } from "@/common/questionConverters";
import { PublishedWorkflow, UnstructuredStepData } from "@/common/types";
import {
  mapQuestionTypeToTable,
  updateExistingWorkflowStep,
} from "@/common/updateQuestionsHelper";
import { formatDate, hash, slugify } from "@/common/utils";

import StudentWorkflowStatus = $Enums.StudentWorkflowStatus;

// All the types and interfaces
export type ConversationStatus = "DRAFT" | "PUBLISHED" | "INACTIVE";
export type ConversationType = "custom" | "addie" | string;
// TODO: needs to update the interface or create a separate interface for response(answers)
export interface CustomConversation {
  id: string;
  name: string;
  type?: ConversationType;
  status: ConversationStatus;
  updatedAt: string;
  // For strucutured conversation
  questions?: QuestionItem[]; // now using the full structure
  workflow_type?: WorkflowType;
  // For unstrucutured conversation - goal, acceptance criteria etc, and a early end message
  goals?: Goal[];
  earlyEndMessage?: string;
  // Whether this is a system-owned conversation (owner <NAME_EMAIL>)
  isSystemConversation?: boolean;
  description?: string;
  disable_system_prompt?: boolean;
  tags?: string[];
}

// Define the valid question types
export type QuestionType =
  | "multiple-choice"
  | "binary"
  | "short-answer"
  | "long-answer"
  | "likert";

// Frontend QuestionItem interface
export interface QuestionItem {
  id: string; // temporary ID from front end
  // workflowStepId: string;
  text: string;
  type: QuestionType;

  options?: string[]; // only for multiple-choice questions or Likert question
  // TODO: lets rename this to `optional`
  canSkip: boolean; // extra field (not used in saving schema)
  characterLimit?: { min?: number; max?: number }; // extra field (not used in schema)
  workflowStepId: string;
  questionnaire_id?: string;
  tags?: string[];
}

export interface BaseGoal {
  id: string;
  goalText: string;
  acceptanceCriteria?: string;
  exampleAnswer?: string;
}

export interface UnstructuredGoal extends BaseGoal {
  mode?: "unstructured";
  discussionTime?: string;
}

export interface AssignmentGoal extends BaseGoal {
  // eg: number + unit(hours, days) -- 2 days || 3 hours - this is used for assignment type conversations
  mode?: "assignment";
  followUpNumber: number;
  followUpUnit: "hours" | "days";
}

export type Goal = UnstructuredGoal | AssignmentGoal;

// Helper function to safely extract description from workflow data
function getDescriptionFromData(data: any): string {
  if (typeof data === "object" && data !== null) {
    return (data as any).description || "";
  }
  return "";
}

// Functions
/* --------------------------------------------
 * 1) ONBOARDING CONVERSATION
 * -------------------------------------------- */

/**
 * Fetch pre-made onboarding questions from "Onboarding Questionnaire"
 */
export const fetchOnboardingQuestions = async () => {
  const onboardingWorkflow = await prisma.workflow.findFirst({
    where: { name: "Onboarding Questionnaire" },
    include: { steps: true },
  });

  if (!onboardingWorkflow || !onboardingWorkflow.steps.length) {
    throw new Error("Onboarding Workflow or steps not found.");
  }

  const parentStepId = onboardingWorkflow.steps[0].id;

  const subWorkflows = await prisma.workflow.findMany({
    where: { parent_step_id: parentStepId },
    include: { steps: true },
    orderBy: { created_at: "asc" },
  });

  // sort subWorkflows steps
  let allSteps: any[] = [];
  for (const wf of subWorkflows) {
    const sortedSteps = wf.steps.sort((a, b) => a.index - b.index);
    allSteps.push(...sortedSteps);
  }

  // Consolidate all steps from child workflows and convert to a unified format
  const allQuestions = allSteps
    .map((step: any) => {
      const data = step.data || {};
      return {
        // Use id from data if present; otherwise, fallback to step.id
        id: data.id || step.id,
        text: data.question || "",
        questionnaire_id: data.questionnaire_id || null,
        workflowStepId: step.id,
        canSkip: false,
        // Since all questions come from the Question table, set type as "short-answer"
        type: "short-answer",
      };
    })
    // Filter out any questions with empty text
    .filter((question) => question.text);

  return allQuestions;
};

/**
 * Create or retrieve the "Onboarding Conversation" workflow
 * and ensure it is assigned to all students with steps synced.
 * @param userId - The ID of the user creating the workflow
 */
export const getOrCreateOnboardingWorkflow = async (userId: string) => {
  let onboardingWorkflow = await prisma.workflow.findFirst({
    where: { name: "Onboarding Conversation" },
    include: { steps: true },
  });

  if (!onboardingWorkflow) {
    console.log("Creating a new onboarding workflow");
    const questions = await fetchOnboardingQuestions();
    onboardingWorkflow = await prisma.workflow.create({
      data: {
        name: "Onboarding Conversation",
        owner_id: userId,
        steps: {
          create: questions.map((q, index) => ({
            goal: "Ask question",
            name: `${q.text.substring(0, 30)}-${index}`,
            index,
            data: {
              question: q.text,
              table: "Question",
              questionId: q.id,
              questionnaire_id: q.questionnaire_id,
              canSkip: q.canSkip,
            },
          })),
        },
      },
      include: { steps: true },
    });
  }

  return onboardingWorkflow;
};
// export const getOrCreateOnboardingWorkflow = async () => {
//   // Step 1: Check if the Onboarding Conversation already exists
//   let onboardingWorkflow = await prisma.workflow.findFirst({
//     where: { name: "Onboarding Conversation" },
//     include: { steps: true },
//   });
//
//   // Step 2: If it exists, return it
//   if (onboardingWorkflow) {
//     // console.log("exisitng onboarding workflow ==>", onboardingWorkflow);
//     return onboardingWorkflow;
//   }
//
//   // Step 3: Fetch all 51 questions
//   const questions = await fetchOnboardingQuestions();
//
//   // Step 4: Create the Onboarding Conversation workflow
//   // Create a new "Onboarding Conversation" workflow with steps formatted as required
//   onboardingWorkflow = await prisma.workflow.create({
//     data: {
//       name: "Onboarding Conversation",
//       steps: {
//         create: questions.map((q, index) => ({
//           goal: "Ask question",
//           // Use the first 30 characters of the question text combined with the index as the step name
//           name: `${q.text.substring(0, 30)}-${index}`,
//           index: index,
//           data: {
//             question: q.text,
//             // Since all questions come from the Question table, the table is set to "Question"
//             table: "Question",
//             questionId: q.id,
//             questionnaire_id: q.questionnaire_id,
//             canSkip: q.canSkip,
//           },
//         })),
//       },
//     },
//     include: { steps: true },
//   });
//
//   // console.log("onboarding conversation ===>", onboardingWorkflow);
//   return onboardingWorkflow;
// };

// export interface CustomConversation {
//   id: string;
//   name: string;
//   type?: string;
//   status: string;
//   updatedAt: string;
//   questions: {
//     question: string;
//     table: string;
//     questionId: string;
//   }[];
// }

// TODO: needs to do a better filtering by owner or school.id
export const getCustomConversations = async () => {
  const customConversations: any = await prisma.workflow.findMany({
    where: {
      name: {
        startsWith: "Conversation:",
      },
    },
    include: { steps: true },
    orderBy: { updated_at: "desc" }, // Sort by updated time
  });

  return customConversations.map((conversation) => ({
    id: conversation.id,
    name: conversation.name.replace("Conversation: ", ""), // Remove the prefix for UI
    type: (conversation.tags ?? []).includes("custom") ? "custom" : "addie",
    // type: "custom", // Identify as a custom conversation
    status: conversation.status || WorkflowStatus.DRAFT,
    updatedAt: conversation.updated_at || new Date(),
    questions: conversation.steps.map((step: any) => step.data?.question || ""),
    workflow_type: conversation.workflow_type,
    description: getDescriptionFromData(conversation.data),
  })) as CustomConversation[];
};

// TODO: add school filter in the future
export const getUnstructuredConversations = async () => {
  const unstructuredWorkflows = await prisma.workflow.findMany({
    where: {
      workflow_type: WorkflowType.UNSTRUCTURED,
    },
    include: { steps: true },
    orderBy: { updated_at: "desc" },
  });

  const data = {};

  // log num steps for each wf
  for (const wf of unstructuredWorkflows) {
    data[wf.name] = wf.steps.length;
  }

  return unstructuredWorkflows.map((wf) => {
    // Find all steps that contain goals (they start with "unstructured-goal-")
    const goalSteps = wf.steps
      .filter((s) => s.name.startsWith("unstructured-goal-"))
      .sort((a, b) => a.index - b.index); // Sort by index to maintain order

    // Extract goals from each step
    const goals = goalSteps.map((step) => step.data as unknown as Goal);

    // Type guard to ensure wf.data is an object with earlyEndMessage property
    let earlyEndMessage: string | undefined;
    if (
      wf.data &&
      typeof wf.data === "object" &&
      "earlyEndMessage" in wf.data
    ) {
      earlyEndMessage = wf.data.earlyEndMessage as string;
    }
    return {
      id: wf.id,
      name: wf.name.replace("Unstructured Conversation: ", ""),
      type: (wf.tags ?? []).includes("custom") ? "custom" : "addie",
      status: wf.status || WorkflowStatus.DRAFT,
      updatedAt: wf.updated_at.toISOString() || new Date().toISOString(),
      goals,
      earlyEndMessage,
      workflow_type: wf.workflow_type,
      description: getDescriptionFromData(wf.data),
    } as CustomConversation;
  });
};

// Get assignment conversations

export const getAssignmentConversations = async () => {
  const assignmentConversations = await prisma.workflow.findMany({
    where: {
      workflow_type: WorkflowType.ASSIGNMENT,
    },
    include: { steps: true },
    orderBy: { updated_at: "desc" },
  });

  const data = {};

  // log num steps for each wf
  for (const wf of assignmentConversations) {
    data[wf.name] = wf.steps.length;
  }

  return assignmentConversations.map((wf) => {
    // Find all steps that contain goals (they start with "assignment-goal-")
    const goalSteps = wf.steps
      .filter((s) => s.name.startsWith("assignment-goal-"))
      .sort((a, b) => a.index - b.index); // Sort by index to maintain order

    // Extract goals from each step
    const goals = goalSteps.map((step) => step.data as unknown as Goal);

    // Type guard to ensure wf.data is an object with earlyEndMessage property
    let earlyEndMessage: string | undefined;
    if (
      wf.data &&
      typeof wf.data === "object" &&
      "earlyEndMessage" in wf.data
    ) {
      earlyEndMessage = wf.data.earlyEndMessage as string;
    }

    return {
      id: wf.id,
      name: wf.name.replace("Assignment: ", ""),
      type: (wf.tags ?? []).includes("custom") ? "custom" : "addie",
      status: wf.status || WorkflowStatus.DRAFT,
      updatedAt: wf.updated_at.toISOString() || new Date().toISOString(),
      goals,
      earlyEndMessage,
      workflow_type: wf.workflow_type,
      description: getDescriptionFromData(wf.data),
    } as CustomConversation;
  });
};

export const getAllConversations = async (userId: string) => {
  // Fetch the onboarding workflow
  const onboardingWorkflow = await getOrCreateOnboardingWorkflow(userId);

  // Format the onboarding workflow
  const onboardingConversation = {
    id: onboardingWorkflow.id,
    name: onboardingWorkflow.name,
    type: "onboarding",
    status: onboardingWorkflow.status || WorkflowStatus.DRAFT,
    updatedAt: onboardingWorkflow.updated_at || new Date().toISOString(),
    questions: onboardingWorkflow.steps.map(
      (step: any) => step.data?.question || "",
    ),
    workflow_type: onboardingWorkflow.workflow_type,
    description: getDescriptionFromData(onboardingWorkflow.data),
  };

  // Fetch custom conversations
  const customConversations = await getCustomConversations();

  // Fetch unstructured conversations
  const unstructured = await getUnstructuredConversations();

  // Fetch assignment conversations
  const assignmentConversations = await getAssignmentConversations();

  // Sort both custom(structured conversations) and unstrcutured by their updated time
  const sortedByUpdated = [
    ...customConversations,
    ...unstructured,
    ...assignmentConversations,
  ];

  sortedByUpdated.sort(
    (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
  );

  // Combine both into a single array
  return [...sortedByUpdated] as CustomConversation[];
};

// getConversation by their Ids, used for dynamic routing
export const getConversationById = async (
  id: string,
): Promise<CustomConversation | null> => {
  const conversation = await prisma.workflow.findUnique({
    where: { id },
    include: {
      steps: true,
      owner: true,
    },
  });
  if (!conversation) return null;
  const sortedSteps = conversation.steps.sort(
    (a: any, b: any) => a.index - b.index,
  );

  // Convert each step's data (raw question) into a QuestionItem.
  const convertedQuestions = sortedSteps.map((step: any) =>
    convertQuestion({
      question: step.data?.question || "",
      workflowStepId: step.id || "",
      table: step.data?.table || "",
      questionId: step.data?.questionId || "",
      options: step.data?.options, // include options from stored data
      questionnaire_id: step.data?.questionnaire_id,
      canSkip: step.data?.canSkip,
      characterLimit: step.data?.characterLimit,
      tags: step.tags,
    }),
  );

  if (conversation.name === "Onboarding Conversation") {
    return {
      id: conversation.id,
      name: conversation.name,
      type: "onboarding",
      status: conversation.status || WorkflowStatus.DRAFT,
      updatedAt: conversation.updated_at
        ? conversation.updated_at.toISOString()
        : new Date().toISOString(),
      questions: convertedQuestions,
      workflow_type: conversation.workflow_type,
      isSystemConversation: conversation.owner?.email === "<EMAIL>",
      description: getDescriptionFromData(conversation.data),
      disable_system_prompt: conversation.disable_system_prompt || false,
    } as CustomConversation;
  } else if (conversation.name.startsWith("Conversation:")) {
    return {
      id: conversation.id,
      name: conversation.name.replace("Conversation: ", ""),
      type: conversation.tags?.includes("custom") ? "custom" : "addie",
      status: conversation.status || WorkflowStatus.DRAFT,
      updatedAt: conversation.updated_at
        ? conversation.updated_at.toISOString()
        : new Date().toISOString(),
      questions: convertedQuestions,
      workflow_type: conversation.workflow_type,
      isSystemConversation: conversation.owner?.email === "<EMAIL>",
      description: getDescriptionFromData(conversation.data),
      disable_system_prompt: conversation.disable_system_prompt || false,
      tags: conversation.tags,
    } as CustomConversation;
  } else if (conversation.workflow_type === WorkflowType.UNSTRUCTURED) {
    // Find all steps that contain goals (they start with "unstructured-goal-")
    const goalSteps = conversation.steps
      .filter((s) => s.name.startsWith("unstructured-goal-"))
      .sort((a, b) => a.index - b.index); // Sort by index to maintain order

    // Extract goals from each step
    const goalsArray = goalSteps.map((step) => step.data as unknown as Goal);

    // Type guard to ensure wf.data is an object with earlyEndMessage property
    let earlyEndMessage: string | undefined;
    if (
      conversation.data &&
      typeof conversation.data === "object" &&
      "earlyEndMessage" in conversation.data
    ) {
      earlyEndMessage = conversation.data.earlyEndMessage as string;
    }
    return {
      id: conversation.id,
      name: `${conversation.name.replace("Unstructured Conversation: ", "")}(Open-ended)`,

      type: conversation.tags?.includes("custom") ? "custom" : "addie",
      status: conversation.status || WorkflowStatus.DRAFT,
      updatedAt: conversation.updated_at
        ? conversation.updated_at.toISOString()
        : new Date().toISOString(),
      goals: goalsArray,
      earlyEndMessage,
      workflow_type: conversation.workflow_type,
      isSystemConversation: conversation.owner?.email === "<EMAIL>",
      description: getDescriptionFromData(conversation.data),
      disable_system_prompt: conversation.disable_system_prompt || false,
      tags: conversation.tags,
    } as CustomConversation;
  } else if (conversation.workflow_type === WorkflowType.ASSIGNMENT) {
    // Find all steps that contain goals (they start with "assignment-goal-")
    const goalSteps = conversation.steps
      .filter((s) => s.name.startsWith("assignment-goal-"))
      .sort((a, b) => a.index - b.index); // Sort by index to maintain order

    // Extract goals from each step
    const goalsArray = goalSteps.map((step) => step.data as unknown as Goal);

    // Type guard to ensure conversation.data is an object with earlyEndMessage property
    let earlyEndMessage: string | undefined;
    if (
      conversation.data &&
      typeof conversation.data === "object" &&
      "earlyEndMessage" in conversation.data
    ) {
      earlyEndMessage = conversation.data.earlyEndMessage as string;
    }
    return {
      id: conversation.id,
      name: `${conversation.name.replace("Assignment: ", "")}(Assignment)`,

      type: conversation.tags?.includes("custom") ? "custom" : "addie",
      status: conversation.status || WorkflowStatus.DRAFT,
      updatedAt: conversation.updated_at
        ? conversation.updated_at.toISOString()
        : new Date().toISOString(),
      goals: goalsArray,
      earlyEndMessage,
      workflow_type: conversation.workflow_type,
      isSystemConversation: conversation.owner?.email === "<EMAIL>",
      description: getDescriptionFromData(conversation.data),
      disable_system_prompt: conversation.disable_system_prompt || false,
      tags: conversation.tags,
    } as CustomConversation;
  } else {
    return {
      id: conversation.id,
      name: conversation.name,
      type: "unknown",
      status: conversation.status || WorkflowStatus.DRAFT,
      updatedAt: conversation.updated_at
        ? conversation.updated_at.toISOString()
        : new Date().toISOString(),
      questions: convertedQuestions,
      workflow_type: conversation.workflow_type,
      isSystemConversation: conversation.owner?.email === "<EMAIL>",
      description: getDescriptionFromData(conversation.data),
      disable_system_prompt: conversation.disable_system_prompt || false,
    } as CustomConversation;
  }
};

// Create a STRUCTURED conversation
export const createConversation = async (name: string, userId: string, type: string = "General") => {
  try {
    const existingConversation = await prisma.workflow.findFirst({
      where: { name: `Conversation: ${name}` },
    });

    if (existingConversation) {
      console.log("existing convo");
      return {
        success: false,
        error: "A conversation with this name already exists!"
      };
    }

    const customConversation = await prisma.workflow.create({
      data: {
        name: `Conversation: ${name}`,
        steps: {
          create: [],
        },
        owner_id: userId,
        tags: ["custom", `type:${type}`],
      },
    });

    console.log("new convo is created!");

    return {
      success: true,
      data: customConversation
    };
  } catch (error) {
    console.error("Error creating conversation:", error);
    return {
      success: false,
      error: "Failed to create conversation"
    };
  }
};

// create an UNSTRUCTURED conversation

export const createUnStructuredConversation = async (
  name: string,
  userId: string,
) => {
  try {
    const conversationName = `Unstructured Conversation: ${name.trim()}`;
    const existingConversation = await prisma.workflow.findFirst({
      where: { name: conversationName },
    });

    if (existingConversation) {
      console.log("existing unstructured convo");
      return {
        success: false,
        error: "An unstructured conversation with this name already exists!"
      };
    }

    // Create a default empty goal
    const defaultGoal = {
      name: "Goal",
      goalText: "",
    };

    const customConversation = await prisma.workflow.create({
      data: {
        name: conversationName,
        steps: {
          create: [
            {
              name: "unstructured-goal-0", // Format required by getConversationById
              goal: "", // Empty goal string
              data: defaultGoal as unknown as Prisma.JsonObject,
              index: 0,
            },
          ],
        },
        workflow_type: WorkflowType.UNSTRUCTURED,
        owner_id: userId,
        // admin created conversation has no tags
        // tags: ["custom"],
      },
    });

    console.log("new unstructured convo is created with default goal!");

    return {
      success: true,
      data: customConversation
    };
  } catch (error) {
    console.error("Error creating unstructured conversation:", error);
    return {
      success: false,
      error: "Failed to create unstructured conversation"
    };
  }
};

export const createAssignmentConversation = async (
  name: string,
  userId: string,
) => {
  try {
    const conversationName = `Assignment: ${name.trim()}`;
    const existingConversation = await prisma.workflow.findFirst({
      where: { name: conversationName },
    });

    if (existingConversation) {
      console.log("existing assignment convo");
      return {
        success: false,
        error: "An assignment conversation with this name already exists!"
      };
    }

    const defaultGoal = {
      name: "Goal",
      goalText: "",
      followUpNumber: 3,
      followUpUnit: "days",
    };

    const customConversation = await prisma.workflow.create({
      data: {
        name: conversationName,
        steps: {
          create: [
            {
              name: "assignment-goal-0", // Format required by getConversationById
              goal: "", // Empty goal string
              data: defaultGoal as unknown as Prisma.JsonObject,
              index: 0,
            },
          ],
        },
        workflow_type: WorkflowType.ASSIGNMENT,
        owner: {
          connect: { id: userId },
        },
      },
    });

    console.log("new assignment convo is created!");

    return {
      success: true,
      data: customConversation
    };
  } catch (error) {
    console.error("Error creating assignment conversation:", error);
    return {
      success: false,
      error: "Failed to create assignment conversation"
    };
  }
};

async function upsertStudentWorkflow(
  studentId: string,
  workflowId: string,
  data: any,
) {
  return await prisma.studentWorkflow.upsert({
    where: {
      student_id_workflow_id: {
        student_id: studentId,
        workflow_id: workflowId,
      },
    },
    update: data,
    create: data,
  });
}

async function ensureStudentWorkflow(student, workflow: PublishedWorkflow) {
  const studentWfData = {
    student_id: student.id,
    workflow_id: workflow.id,
  };

  const existingStudentWf = await prisma.studentWorkflow.findFirst({
    where: {
      student_id: student.id,
      workflow_id: workflow.id,
    },
  });
  const stepDataHash = existingStudentWf?.step_data_hash;

  let wf = await upsertStudentWorkflow(student.id, workflow.id, studentWfData);

  const stepsDataHash = await ensureStudentWorkflowSteps(
    student.id,
    wf.id,
    workflow,
  );

  if (
    stepDataHash !== stepsDataHash ||
    !wf.status ||
    wf.status === StudentWorkflowStatus.COUNSELOR_UNPUBLISHED
  ) {
    // Determine the appropriate status
    let preservedStatus: StudentWorkflowStatus =
      StudentWorkflowStatus.NOT_STARTED;

    if (existingStudentWf?.status) {
      if (
        existingStudentWf.status === StudentWorkflowStatus.COUNSELOR_UNPUBLISHED
      ) {
        // If student was unpublished (either by counselor or conversation unpublish),
        // restore their original progress based on workflow type
        if (workflow.workflow_type === "UNSTRUCTURED") {
          // For unstructured conversations, check if student has any messages
          // Session ID format: workflowId-userId
          const student = await prisma.student.findUnique({
            where: { id: existingStudentWf.student_id },
            include: { users: true },
          });

          if (student?.users?.[0]) {
            const sessionId = `${workflow.id}-${student.users[0].id}`;
            const messageCount = await prisma.messages.count({
              where: { session_id: sessionId },
            });

            // Check if all steps are completed for COMPLETED status
            const completedSteps = await prisma.studentWorkFlowStep.count({
              where: {
                student_workflow_id: existingStudentWf.id,
                completed: true,
              },
            });

            const totalSteps = await prisma.studentWorkFlowStep.count({
              where: {
                student_workflow_id: existingStudentWf.id,
              },
            });

            if (completedSteps === totalSteps && totalSteps > 0) {
              preservedStatus = StudentWorkflowStatus.COMPLETED;
            } else if (messageCount > 0) {
              // Student has started the conversation (has messages)
              preservedStatus = StudentWorkflowStatus.IN_PROGRESS;
            } else {
              preservedStatus = StudentWorkflowStatus.NOT_STARTED;
            }
          } else {
            preservedStatus = StudentWorkflowStatus.NOT_STARTED;
          }
        } else {
          // For structured conversations, use the original step-based logic
          const completedSteps = await prisma.studentWorkFlowStep.count({
            where: {
              student_workflow_id: existingStudentWf.id,
              completed: true,
            },
          });

          const totalSteps = await prisma.studentWorkFlowStep.count({
            where: {
              student_workflow_id: existingStudentWf.id,
            },
          });

          if (completedSteps === totalSteps && totalSteps > 0) {
            preservedStatus = StudentWorkflowStatus.COMPLETED;
          } else if (completedSteps > 0) {
            preservedStatus = StudentWorkflowStatus.IN_PROGRESS;
          } else {
            preservedStatus = StudentWorkflowStatus.NOT_STARTED;
          }
        }
      } else {
        // For other statuses (IN_PROGRESS, COMPLETED, NOT_STARTED), preserve as is
        preservedStatus = existingStudentWf.status;
      }
    }

    wf = await upsertStudentWorkflow(student.id, workflow.id, {
      ...studentWfData,
      step_data_hash: stepsDataHash,
      status: preservedStatus,
    });
  }

  return wf;
}

/**
 * Assigns the specified Workflow (conversation) to all students.
 *
 * For each student, this function creates a StudentWorkflow record.
 * Since the workflow is initially created with empty steps, no StudentWorkflowStep records
 * are created here. They should be added later when new WorkflowSteps are added.
 *
 * @param workflow - The newly created Workflow.
 * @returns Array of created/updated StudentWorkflow records
 */
export async function assignWorkflowToAllStudents(
  workflow: PublishedWorkflow,
  schoolId?: string,
): Promise<any[]> {
  // Build filter conditions
  const whereConditions: any = {
    // Exclude any student if any associated user's email ends with "@example.com"
    users: {
      none: {
        email: {
          endsWith: "@example.com",
        },
      },
    },
  };

  // Add school filter if provided
  if (schoolId) {
    whereConditions.school_id = schoolId;
  }

  const students = await prisma.student.findMany({
    where: whereConditions,
  });

  console.log("# of filtered students ==>", students.length);

  const BATCH_SIZE = 10;
  const createdStudentWorkflows: any[] = [];

  // Process students in batches
  for (let i = 0; i < students.length; i += BATCH_SIZE) {
    const batch = students.slice(i, i + BATCH_SIZE);
    const batchResults = await Promise.all(
      batch.map(async (student) => {
        const studentWorkflow = await ensureStudentWorkflow(student, workflow);
        return studentWorkflow;
      }),
    );

    createdStudentWorkflows.push(...batchResults);
  }

  return createdStudentWorkflows;
}

/**
 * Assigns workflow to **specific students** in batches.
 *
 * @param workflow - The workflow to assign.
 * @param studentIds - The list of students to assign.
 * @returns Array of created/updated StudentWorkflow records
 */
export async function assignConversationToSpecificStudents(
  workflow: PublishedWorkflow,
  studentIds: string[],
): Promise<any[]> {
  const BATCH_SIZE = 10;
  const createdStudentWorkflows: any[] = [];

  for (let i = 0; i < studentIds.length; i += BATCH_SIZE) {
    const batch = studentIds.slice(i, i + BATCH_SIZE);

    const batchResults = await Promise.all(
      batch.map(async (studentId) => {
        // First check if student already has a workflow to determine the correct status
        const existingWorkflow = await prisma.studentWorkflow.findUnique({
          where: {
            student_id_workflow_id: {
              student_id: studentId,
              workflow_id: workflow.id,
            },
          },
        });

        // Determine the appropriate status for update
        let updateStatus: StudentWorkflowStatus | undefined;
        if (
          existingWorkflow?.status ===
          StudentWorkflowStatus.COUNSELOR_UNPUBLISHED
        ) {
          // If student was unpublished due to conversation unpublish, restore their original progress
          if (workflow.workflow_type === "UNSTRUCTURED") {
            // For unstructured conversations, check if student has any messages
            const student = await prisma.student.findUnique({
              where: { id: studentId },
              include: { users: true },
            });

            if (student?.users?.[0]) {
              const sessionId = `${workflow.id}-${student.users[0].id}`;
              const messageCount = await prisma.messages.count({
                where: { session_id: sessionId },
              });

              // Check if all steps are completed for COMPLETED status
              const completedSteps = await prisma.studentWorkFlowStep.count({
                where: {
                  student_workflow_id: existingWorkflow.id,
                  completed: true,
                },
              });

              const totalSteps = await prisma.studentWorkFlowStep.count({
                where: {
                  student_workflow_id: existingWorkflow.id,
                },
              });

              if (completedSteps === totalSteps && totalSteps > 0) {
                updateStatus = StudentWorkflowStatus.COMPLETED;
              } else if (messageCount > 0) {
                // Student has started the conversation (has messages)
                updateStatus = StudentWorkflowStatus.IN_PROGRESS;
              } else {
                updateStatus = StudentWorkflowStatus.NOT_STARTED;
              }
            } else {
              updateStatus = StudentWorkflowStatus.NOT_STARTED;
            }
          } else {
            // For structured conversations, use the original step-based logic
            const completedSteps = await prisma.studentWorkFlowStep.count({
              where: {
                student_workflow_id: existingWorkflow.id,
                completed: true,
              },
            });

            const totalSteps = await prisma.studentWorkFlowStep.count({
              where: {
                student_workflow_id: existingWorkflow.id,
              },
            });

            if (completedSteps === totalSteps && totalSteps > 0) {
              updateStatus = StudentWorkflowStatus.COMPLETED;
            } else if (completedSteps > 0) {
              updateStatus = StudentWorkflowStatus.IN_PROGRESS;
            } else {
              updateStatus = StudentWorkflowStatus.NOT_STARTED;
            }
          }
        }

        const studentWorkflow = await prisma.studentWorkflow.upsert({
          where: {
            student_id_workflow_id: {
              student_id: studentId,
              workflow_id: workflow.id,
            },
          },
          update: updateStatus ? { status: updateStatus } : {},
          create: {
            student: { connect: { id: studentId } },
            workflow: { connect: { id: workflow.id } },
            status: StudentWorkflowStatus.NOT_STARTED,
          },
        });
        // Ensure StudentWorkflowSteps
        await ensureStudentWorkflowSteps(
          studentId,
          studentWorkflow.id,
          workflow,
        );
        return studentWorkflow;
      }),
    );

    createdStudentWorkflows.push(...batchResults);
  }

  return createdStudentWorkflows;
}

/**
 * Publishes a conversation workflow, making it available to all students.
 *
 * This function:
 * 1. Checks if the workflow exists and is not already published
 * 2. Validates that the workflow has steps before publishing
 * 3. Updates the workflow status to PUBLISHED
 * 4. Assigns the workflow to all students
 *
 * @param workflowId - The ID of the workflow to publish
 * @returns A promise that resolves to an object with success status or error message
 */
export async function publishConversation(
  workflowId: string,
  studentIds?: string[],
  schoolId?: string,
): Promise<{ success?: boolean; data?: any; error?: string }> {
  // Check if workflow exists and get its current status
  const workflow = await prisma.workflow.findUniqueOrThrow({
    where: { id: workflowId },
    select: {
      id: true,
      status: true,
      steps: {
        select: { id: true },
      },
    },
  });

  // Check if already published
  if (workflow.status === WorkflowStatus.PUBLISHED) {
    return { error: "This conversation is already published" };
  }

  // Validate workflow has steps
  if (workflow.steps.length === 0) {
    return {
      error:
        "Cannot publish a conversation without any questions. Please add questions before publishing.",
    };
  }

  // Update workflow status to PUBLISHED

  const workflowResult = await prisma.workflow.update({
    where: { id: workflowId },
    data: { status: WorkflowStatus.PUBLISHED },
    include: { steps: true },
  });

  // Convert the Prisma result to PublishedWorkflow type
  const publishedWorkflow: PublishedWorkflow = {
    ...workflowResult,
    workflow_type: workflowResult.workflow_type,
    steps: workflowResult.steps.map((step) => ({
      ...step,
      data: step.data as {
        table: string;
        question: string;
        questionId: string;
        options?: string[];
      },
    })),
  };

  // Assign workflow to all students or specific students based on
  let assignedStudentWorkflows: any[] = [];
  if (studentIds && studentIds.length > 0) {
    assignedStudentWorkflows = await assignConversationToSpecificStudents(
      publishedWorkflow,
      studentIds,
    );
  } else {
    assignedStudentWorkflows = await assignWorkflowToAllStudents(
      publishedWorkflow,
      schoolId,
    );
  }

  // Send SMS notifications to all assigned students
  if (assignedStudentWorkflows.length > 0) {
    try {
      // Process notifications in parallel
      await Promise.all(
        assignedStudentWorkflows.map((sw) =>
          sendConversationNotificationSms(sw.id),
        ),
      );
      console.log(
        `Sent SMS notifications to ${assignedStudentWorkflows.length} students`,
      );
    } catch (error) {
      console.error("Error sending SMS notifications:", error);
      // Continue even if notifications fail - don't block the publish process
    }
  }

  return {
    success: true,
    data: publishedWorkflow,
  };
}

/**
 * Sends SMS notification to a student about a newly assigned conversation
 * @param studentWorkflowId The ID of the StudentWorkflow record
 */
export async function sendConversationNotificationSms(
  studentWorkflowId: string,
): Promise<{ success?: boolean; error?: string }> {
  try {
    // Call the Python backend API
    const response = await fetch(
      `${baseApiUrl}/api/sms/notify/${studentWorkflowId}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": env.ADDIE_API_KEY,
        },
      },
    );

    const result = await response.json();

    if (!response.ok) {
      console.error("Failed to send SMS notification:", result);
      return { error: result.error || "Failed to send SMS notification" };
    }

    return { success: true };
  } catch (error) {
    console.error("Error sending SMS notification:", error);
    return { error: "Error sending SMS notification" };
  }
}

/**
 * Unpublish a published conversation
 * Based on the status of this workflow.
 * If it is "PUBLISHED", then change it to "DRAFT"
 *
 * @param workflowId - The ID of the workflow.
 * @returns A promise that has {success:true/error:"This conversation is not published"}
 */

export const unpublishConversation = async (workflowId: string) => {
  const existingConvo = await prisma.workflow.findFirst({
    where: {
      id: workflowId,
    },
  });

  if (!existingConvo) {
    throw new Error("No conversation found in DB");
  }

  if (existingConvo.status === WorkflowStatus.DRAFT) {
    return { error: "This conversation is not published" };
  }

  // When unpublishing a conversation, mark all assigned students as COUNSELOR_UNPUBLISHED
  // This prevents them from seeing the conversation when it's republished to other students
  await prisma.studentWorkflow.updateMany({
    where: {
      workflow_id: workflowId,
      status: {
        not: StudentWorkflowStatus.COUNSELOR_UNPUBLISHED, // Don't update already unpublished ones
      },
    },
    data: {
      status: StudentWorkflowStatus.COUNSELOR_UNPUBLISHED,
    },
  });

  const updatedConversation = await prisma.workflow.update({
    where: {
      id: workflowId,
    },
    data: {
      status: WorkflowStatus.DRAFT,
    },
  });

  return { success: true, data: updatedConversation };
};

/**
 * Creates a question record (based on the question type) and the corresponding
 * WorkflowStep for that question.
 *
 * @param workflowId - The ID of the workflow.
 * @param questionnaireId - The ID of the questionnaire to connect to.
 * @param question - A QuestionItem from the front end.
 * @param indexOffset - The index to use (existing steps count + current index).
 * @returns A promise that resolves to an object containing the table name and question record ID.
 */
async function createQuestionWorkflowStep(
  workflowId: string,
  questionnaireId: string,
  question: QuestionItem,
  indexOffset: number,
): Promise<{ table: string; questionId: string; workflowStepId: string }> {
  let questionRef: {
    table: string;
    questionId: string;
    workflowStepId: string;
  } = {
    table: "",
    questionId: "",
    workflowStepId: "",
  };
  const questionHash = hash(question.text + workflowId + question.type);

  if (question.type === "binary") {
    const binaryQuestion = await prisma.binaryQuestion.upsert({
      where: {
        questionnaire_id_question_hash: {
          questionnaire_id: questionnaireId,
          question_hash: questionHash,
        },
      },
      create: {
        question: question.text,
        slug: slugify(question.text),
        question_hash: questionHash,
        questionnaire: { connect: { id: questionnaireId } },
        index: indexOffset,
        can_skip: question.canSkip,
      },
      update: {
        question: question.text,
        slug: slugify(question.text),
        index: indexOffset,
        can_skip: question.canSkip,
      },
    });
    questionRef.table = "BinaryQuestion";
    questionRef.questionId = binaryQuestion.id;
  } else if (question.type === "multiple-choice") {
    const mcQuestion = await prisma.multipleChoiceQuestion.upsert({
      where: {
        questionnaire_id_question_hash: {
          questionnaire_id: questionnaireId,
          question_hash: questionHash,
        },
      },
      create: {
        question: question.text,
        slug: slugify(question.text),
        question_hash: questionHash,
        questionnaire: { connect: { id: questionnaireId } },
        index: indexOffset,
        choices: {
          create: question.options?.map((option) => ({ option })) || [],
        },
        can_skip: question.canSkip,
      },
      update: {
        question: question.text,
        slug: slugify(question.text),
        index: indexOffset,
        choices: {
          deleteMany: {},
          create: question.options?.map((option) => ({ option })) || [],
        },
        can_skip: question.canSkip,
      },
    });
    questionRef.table = "MultipleChoiceQuestion";
    questionRef.questionId = mcQuestion.id;
  } else if (
    question.type === "short-answer" ||
    question.type === "long-answer"
  ) {
    const textQuestion = await prisma.question.upsert({
      where: {
        questionnaire_id_question_hash: {
          questionnaire_id: questionnaireId,
          question_hash: questionHash,
        },
      },
      create: {
        question: question.text,
        slug: slugify(question.text),
        question_hash: questionHash,
        questionnaire: { connect: { id: questionnaireId } },
        index: indexOffset,
        can_skip: question.canSkip,
      },
      update: {
        question: question.text,
        slug: slugify(question.text),
        index: indexOffset,
        can_skip: question.canSkip,
      },
    });
    questionRef.table = "Question";
    questionRef.questionId = textQuestion.id;
  } else if (question.type === "likert") {
    const mcQuestion = await prisma.multipleChoiceQuestion.upsert({
      where: {
        questionnaire_id_question_hash: {
          questionnaire_id: questionnaireId,
          question_hash: questionHash,
        },
      },
      create: {
        question: question.text,
        slug: slugify(question.text),
        question_hash: questionHash,
        questionnaire: { connect: { id: questionnaireId } },
        index: indexOffset,
        choices: {
          create: question.options?.map((option) => ({ option })) || [],
        },
        type: MultipleChoiceType.LIKERT,
        can_skip: question.canSkip,
      },
      update: {
        question: question.text,
        slug: slugify(question.text),
        index: indexOffset,
        choices: {
          deleteMany: {},
          create: question.options?.map((option) => ({ option })) || [],
        },
        type: MultipleChoiceType.LIKERT,
        can_skip: question.canSkip,
      },
    });
    questionRef.table = "LikertQuestion";
    questionRef.questionId = mcQuestion.id;
  } else {
    // Fallback: treat unknown type as a generic Question.
    const genericQuestion = await prisma.question.upsert({
      where: {
        questionnaire_id_question_hash: {
          questionnaire_id: questionnaireId,
          question_hash: questionHash,
        },
      },
      create: {
        question: question.text,
        slug: slugify(question.text),
        question_hash: questionHash,
        questionnaire: { connect: { id: questionnaireId } },
        index: indexOffset,
        can_skip: question.canSkip,
      },
      update: {
        question: question.text,
        slug: slugify(question.text),
        index: indexOffset,
        can_skip: question.canSkip,
      },
    });
    questionRef.table = "Question";
    questionRef.questionId = genericQuestion.id;
  }

  // Check for existing workflow step
  const existingStep = await prisma.workflowStep.findFirst({
    where: {
      name: `Question-${questionRef.table}-${questionRef.questionId}`,
      parent_workflow_id: workflowId,
    },
  });

  const stepData = {
    name: `Question-${questionRef.table}-${questionRef.questionId}`,
    goal: "Ask question",
    index: indexOffset,
    data: {
      question: question.text,
      table: questionRef.table,
      questionId: questionRef.questionId,
      questionnaire_id: questionnaireId,
      ...((question.type === "multiple-choice" ||
        question.type === "likert") && { options: question.options }),
      ...(question.type === "short-answer" || question.type === "long-answer"
        ? { characterLimit: question.characterLimit }
        : {}),
      canSkip: question.canSkip,
    },
    tags: question.tags,
  };

  const newStep = existingStep
    ? await prisma.workflowStep.update({
        where: { id: existingStep.id },
        data: stepData,
      })
    : await prisma.workflowStep.create({
        data: {
          ...stepData,
          parent_workflow: { connect: { id: workflowId } },
        },
      });
  questionRef.workflowStepId = newStep.id;
  return questionRef;
}

/**
 * Save an array of questions into the workflow's steps.
 *
 * This function:
 * 1. Retrieves the workflow.
 * 2. Creates a new Questionnaire for this workflow if one does not already exist.
 * 3. Iterates over each QuestionItem and, based on its type, calls createQuestionWorkflowStep().
 *
 * @param workflowId - The ID of the workflow.
 * @param questions - An array of QuestionItem objects from the front end.
 * @param userId - The ID of the user saving the questions.
 * @param description - Optional description for the conversation.
 * @returns A Promise<void>.
 */
export async function saveQuestionsToWorkflow(
  workflowId: string,
  questions: QuestionItem[],
  userId: string,
  description?: string,
): Promise<void> {
  log.debug(`Saving questions to workflow ${workflowId}`);

  // Retrieve the workflow (including its existing steps)
  const workflow = await prisma.workflow.findUnique({
    where: { id: workflowId },
    include: { steps: true },
  });
  if (!workflow) {
    throw new Error("Workflow not found");
  }

  // Determine the questionnaire name based on the workflow name.
  const questionnaireName = `Questionnaire-${workflow.name}-${workflow.id}`;
  let questionnaire = await prisma.questionnaire.findUnique({
    where: {
      name_creator_id: {
        name: questionnaireName,
        creator_id: workflow.owner_id || "",
      },
    },
  });

  // Create a new Questionnaire if needed.
  if (!questionnaire) {
    questionnaire = await prisma.questionnaire.create({
      data: {
        name: questionnaireName,
        description: `Auto-created questionnaire for workflow ${workflow.name}`,
        creator: workflow.owner_id
          ? { connect: { id: workflow.owner_id } }
          : undefined,
      },
    });
  }

  // Use the number of existing steps as an offset for new questions.
  const existingStepsCount = workflow.steps?.length || 0;

  // Process each question item.
  for (let i = 0; i < questions.length; i++) {
    const q = questions[i];
    const indexOffset = existingStepsCount + i;
    // Create the question record and corresponding workflow step.
    await createQuestionWorkflowStep(
      workflow.id,
      questionnaire.id,
      q,
      indexOffset,
    );
  }
  // Update workflow's updated_at timestamp and description if provided
  await prisma.workflow.update({
    where: {
      id: workflowId,
    },
    data: {
      updated_at: new Date(),
      ...(description !== undefined ? { data: { description } } : {}),
    },
  });
}

/**
 * Updates questions in a workflow.
 * For each question in the updated list, checks if its id exists in originalQuestionIds:
 *   - If it exists, updates the corresponding WorkflowStep (e.g., question text and options).
 *   - If it does not exist, creates a new record using createQuestionWorkflowStep.
 * Finally, deletes any WorkflowStep that exists in originalQuestionIds but not in the updated list.
 *
 * @param workflowId - The current workflow's id.
 * @param updatedQuestions - The latest array of questions from the front-end (order determines new index).
 * @param originalWorkflowStepIds - The list of WorkflowStep ids from the initial load.
 * @param userId - The ID of the user updating the questions.
 * @param description - Optional description for the conversation.
 * @returns A Promise<void>.
 */
export async function updateQuestionsInWorkflow(
  workflowId: string,
  updatedQuestions: QuestionItem[],
  originalWorkflowStepIds: string[],
  userId: string,
  description?: string,
  structuredType?: string,
): Promise<void> {
  // Retrieve the workflow and ensure it is in DRAFT status.
  const workflow = await prisma.workflow.findUnique({
    where: { id: workflowId },
    include: { steps: true },
  });
  if (!workflow) throw new Error("Workflow not found");
  if (workflow.status !== "DRAFT")
    throw new Error("Only draft workflows can be updated");

  // Use a unique questionnaire name based on the workflow id.
  const questionnaireName = `Questionnaire-${workflow.name}-${workflow.id}`;
  let questionnaire = await prisma.questionnaire.findUnique({
    where: {
      name_creator_id: {
        name: questionnaireName,
        creator_id: workflow.owner_id || "",
      },
    },
  });
  if (!questionnaire) {
    questionnaire = await prisma.questionnaire.create({
      data: {
        name: questionnaireName,
        description: `Auto-created questionnaire for workflow ${workflow.name}`,
        creator: workflow.owner_id
          ? { connect: { id: workflow.owner_id } }
          : undefined,
      },
    });
  }

  const newWorkflowStepIds: string[] = [];

  for (let i = 0; i < updatedQuestions.length; i++) {
    const question = updatedQuestions[i];
    const indexOffset = i;
    const newTable = mapQuestionTypeToTable(question.type);

    if (originalWorkflowStepIds.includes(question.workflowStepId)) {
      // Existing question: update the workflow step and underlying record.
      const updatedRecord = await updateExistingWorkflowStep(
        question.workflowStepId,
        indexOffset,
        question,
        newTable,
        questionnaire.id,
        workflowId,
      );
      newWorkflowStepIds.push(question.workflowStepId);
    } else {
      // New question: create a new WorkflowStep and underlying record.
      const result = await createQuestionWorkflowStep(
        workflow.id,
        questionnaire.id,
        question,
        indexOffset,
      );
      newWorkflowStepIds.push(result.workflowStepId);
    }
  }

  // Delete any WorkflowStep records that were originally present but are not in the new list.
  for (const origId of originalWorkflowStepIds) {
    if (!newWorkflowStepIds.includes(origId)) {
      const workflowStep = await prisma.workflowStep.findUnique({
        where: { id: origId },
        select: { data: true, parent_workflow_id: true },
      });

      if (workflowStep) {
        await prisma.workflowStep.delete({ where: { id: origId } });
      }
    }
  }
  // Update workflow's updated_at timestamp, description, and tags if provided
  const updateData: any = {
    updated_at: new Date(),
    ...(description !== undefined ? { data: { description } } : {}),
  };

  // Update tags if structuredType is provided
  if (structuredType !== undefined) {
    // Get current tags and update the type tag
    const currentTags = workflow.tags || [];
    const filteredTags = currentTags.filter(tag => !tag.startsWith("type:"));
    updateData.tags = [...filteredTags, `type:${structuredType}`];
  }

  await prisma.workflow.update({
    where: {
      id: workflowId,
    },
    data: updateData,
  });
}

/**
 * Ensures that a single StudentWorkflow has all the necessary StudentWorkflowStep records.
 * For each WorkflowStep in the parent workflow, it creates a corresponding StudentWorkflowStep
 * if one doesn't exist. If it exists, it updates the data to match the parent step.
 *
 * @param studentWorkflowId - The ID of the StudentWorkflow to ensure steps for
 */
export async function ensureStudentWorkflowSteps(
  studentId: string,
  studentWorkflowId: string,
  workflow: PublishedWorkflow,
): Promise<string> {
  const steps = workflow.steps;

  const dataValues = await Promise.all(
    steps.map(async (step) => {
      const stepData = {
        data: step.data,
        student_id: studentId,
        student_workflow_id: studentWorkflowId,
        step_id: step.id,
      };
      const studentStep = await prisma.studentWorkFlowStep.upsert({
        where: {
          student_id_step_id: {
            student_id: studentId,
            step_id: step.id,
          },
        },
        update: stepData,
        create: stepData,
      });

      return studentStep.data;
    }),
  );

  return hash(JSON.stringify(dataValues));
}

export const deleteConversation = async (workflowId: string) => {
  await prisma.workflow.delete({
    where: {
      id: workflowId,
    },
  });
};

/**
 * Deletes a specific workflow step and its related question.
 *
 * @param workflowStepId - The ID of the workflow step to delete
 * @param workflowId - The ID of the parent workflow
 * @returns The updated list of questions after deletion
 */
export const deleteWorkflowStep = async (
  workflowStepId: string,
): Promise<QuestionItem[]> => {
  // Get the workflow step to determine the question type and ID
  const workflowStep = await prisma.workflowStep.findUniqueOrThrow({
    where: { id: workflowStepId },
    select: { data: true, parent_workflow_id: true },
  });
  const workflowId = workflowStep.parent_workflow_id;

  // Extract the question data
  const data = workflowStep.data as any;
  const table = data?.table;
  const questionId = data?.questionId;

  // Delete the workflow step
  await prisma.workflowStep.delete({
    where: { id: workflowStepId },
  });

  // Delete the associated question if it exists
  if (table && questionId) {
    // Delete the question based on its type/table
    switch (table) {
      case "MultipleChoiceQuestion":
        await prisma.multipleChoiceQuestion.delete({
          where: { id: questionId },
        });
        break;
      // For other question types, we need to check if they exist in the schema
      // If these tables don't exist in your Prisma schema, you'll need to adjust accordingly
      case "Question":
        await prisma.question.delete({
          where: { id: questionId },
        });
        break;
      case "BinaryQuestion":
        await prisma.binaryQuestion.delete({
          where: { id: questionId },
        });
        break;
      case "LikertQuestion":
        await prisma.multipleChoiceQuestion.delete({
          where: { id: questionId },
        });
        break;
    }
  }

  // Fetch the updated workflow with steps
  const updatedWorkflow = await prisma.workflow.findFirstOrThrow({
    where: { id: workflowId },
    include: {
      steps: {
        orderBy: { index: "asc" },
      },
    },
  });

  // Reindex the remaining steps
  for (let i = 0; i < updatedWorkflow.steps.length; i++) {
    await prisma.workflowStep.update({
      where: { id: updatedWorkflow.steps[i].id },
      data: { index: i },
    });
  }

  // Convert the steps to QuestionItem format
  const updatedQuestions: QuestionItem[] = await Promise.all(
    updatedWorkflow.steps.map(async (step) => {
      const stepData = step.data as any;
      // Map the table name to the appropriate question type
      let questionType: QuestionType = "multiple-choice";
      if (stepData?.table) {
        switch (stepData.table) {
          case "MultipleChoiceQuestion":
            questionType = "multiple-choice";
            break;
          case "BinaryQuestion":
            questionType = "binary";
            break;
          case "Question":
            // For Question table, default to short-answer
            questionType = "short-answer";
            break;
          case "LikertQuestion":
            questionType = "likert";
            break;
          default:
            questionType = "multiple-choice";
        }
      }

      // Fetch the actual question data based on the table
      let questionData: any = {};
      if (stepData?.table && stepData?.questionId) {
        switch (stepData.table) {
          case "MultipleChoiceQuestion":
            const mcq = await prisma.multipleChoiceQuestion.findUnique({
              where: { id: stepData.questionId },
            });
            questionData = mcq || {};
            break;
          case "Question":
            const q = await prisma.question.findUnique({
              where: { id: stepData.questionId },
            });
            questionData = q || {};
            break;
          case "BinaryQuestion":
            const bq = await prisma.binaryQuestion.findUnique({
              where: { id: stepData.questionId },
            });
            questionData = bq || {};
            break;
          case "LikertQuestion":
            const likertOption = await prisma.multipleChoiceQuestion.findUnique(
              {
                where: { id: stepData.questionId },
              },
            );
            questionData = likertOption || {};
            break;
        }
      }

      return {
        id: stepData?.questionId || step.id,
        text: stepData?.question || questionData?.text || "",
        type: questionType,
        options: stepData?.options || [],
        canSkip: stepData?.canSkip || false,
        characterLimit: stepData?.characterLimit,
        workflowStepId: step.id,
        questionnaire_id: stepData?.questionnaire_id || null,
      };
    }),
  );

  return updatedQuestions;
};
