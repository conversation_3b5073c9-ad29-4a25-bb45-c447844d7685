"use server";

import { prisma } from "@/common/db";
import log from "@/common/logger";

export interface SystemPromptData {
  id: number;
  messageId: number;
  systemPrompt: string;
  studentContext: any;
  agentType: string;
  createdAt: Date;
  updatedAt: Date;
}

export const getSystemPromptByMessageId = async (
  messageId: number
): Promise<SystemPromptData | null> => {
  try {
    const systemPrompt = await prisma.systemPromptContext.findFirst({
      where: { messageId: messageId }
    });
    
    if (!systemPrompt) {
      return null;
    }
    
    return {
      id: systemPrompt.id,
      messageId: systemPrompt.messageId,
      systemPrompt: systemPrompt.systemPrompt,
      studentContext: systemPrompt.studentContext,
      agentType: systemPrompt.agentType,
      createdAt: systemPrompt.createdAt,
      updatedAt: systemPrompt.updatedAt,
    };
  } catch (error) {
    log.error("Error fetching system prompt:", error);
    return null;
  }
};

export const getSystemPromptsForSession = async (
  sessionId: string
): Promise<SystemPromptData[]> => {
  try {
    const systemPrompts = await prisma.systemPromptContext.findMany({
      where: {
        message: {
          session_id: sessionId
        }
      },
      include: {
        message: true
      },
      orderBy: {
        createdAt: 'asc'
      }
    });
    
    return systemPrompts.map(prompt => ({
      id: prompt.id,
      messageId: prompt.messageId,
      systemPrompt: prompt.systemPrompt,
      studentContext: prompt.studentContext,
      agentType: prompt.agentType,
      createdAt: prompt.createdAt,
      updatedAt: prompt.updatedAt,
    }));
  } catch (error) {
    log.error("Error fetching session system prompts:", error);
    return [];
  }
};

export const getSystemPromptsByAgentType = async (
  agentType: string,
  limit: number = 100
): Promise<SystemPromptData[]> => {
  try {
    const systemPrompts = await prisma.systemPromptContext.findMany({
      where: { agentType: agentType },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit
    });
    
    return systemPrompts.map(prompt => ({
      id: prompt.id,
      messageId: prompt.messageId,
      systemPrompt: prompt.systemPrompt,
      studentContext: prompt.studentContext,
      agentType: prompt.agentType,
      createdAt: prompt.createdAt,
      updatedAt: prompt.updatedAt,
    }));
  } catch (error) {
    log.error(`Error fetching prompts for agent type ${agentType}:`, error);
    return [];
  }
};