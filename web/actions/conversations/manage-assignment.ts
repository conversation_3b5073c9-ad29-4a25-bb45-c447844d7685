"use server";

import {
  StudentWorkflowStatus,
  UserRole,
  WorkflowStatus,
} from "@prisma/client";

import { prisma } from "@/common/db";
import log from "@/common/logger";
import { sendConversationNotificationSms } from "./conversation-workflow";
import { getCurrentUser } from "@/common/session";

// Simplified interface for student assignment management
interface StudentForAssignment {
  id: string;
  grade: number;
  first_name: string;
  last_name: string;
  email: string | null;
  student_workflow: Array<{
    id: string;
    status: StudentWorkflowStatus;
  }>;
}

// Interface for assignment summary
interface AssignmentSummary {
  totalStudents: number;
  assignedStudents: number;
  otherSchoolAssigned: number;
}

// Interface for workflow creator information
interface WorkflowCreatorInfo {
  createdBy: string;
  creatorRole: string;
  creatorSchool?: string;
  isSchoolSpecific: boolean;
}

/**
 * Get all students assigned to a published workflow
 * Filters students based on user role and school access
 */
export async function getAssignedStudents(
  workflowId: string,
  schoolFilter?: string | null | undefined,
): Promise<{
  success: boolean;
  assignedStudents?: StudentForAssignment[];
  allStudents?: StudentForAssignment[];
  assignmentSummary?: AssignmentSummary;
  workflowCreatorInfo?: WorkflowCreatorInfo;
  error?: string;
}> {
  try {
    // Get current user to check role and school access
    const currentUser = await getCurrentUser();
    if (!currentUser) {
      return { success: false, error: "Unauthorized" };
    }

    // Verify the workflow exists and is published, and get creator information
    const workflow = await prisma.workflow.findUnique({
      where: { id: workflowId },
      include: {
        steps: true,
        owner: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            role: true,
            Counselor: {
              select: {
                school: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!workflow) {
      return { success: false, error: "Workflow not found" };
    }

    if (workflow.status !== WorkflowStatus.PUBLISHED) {
      return { success: false, error: "Workflow is not published" };
    }

    // Build workflow creator information
    let workflowCreatorInfo: WorkflowCreatorInfo;
    if (workflow.owner) {
      const creatorName = `${workflow.owner.first_name} ${workflow.owner.last_name}`;
      const isCreatedByCounselor = workflow.owner.role === UserRole.COUNSELOR;
      const creatorSchool = workflow.owner.Counselor?.school?.name;

      workflowCreatorInfo = {
        createdBy: creatorName,
        creatorRole: workflow.owner.role,
        creatorSchool: creatorSchool,
        isSchoolSpecific: isCreatedByCounselor && !!creatorSchool,
      };
    } else {
      workflowCreatorInfo = {
        createdBy: "Unknown",
        creatorRole: "UNKNOWN",
        isSchoolSpecific: false,
      };
    }

    // Determine school filtering logic
    let userSchoolId: string | null = null;
    if (currentUser.role === UserRole.COUNSELOR) {
      // For counselors, always filter by their school
      const counselor = await prisma.counselor.findUnique({
        where: { user_id: currentUser.id },
        select: { school_id: true },
      });
      userSchoolId = counselor?.school_id || null;
    } else if (currentUser.role === UserRole.ADMIN) {
      // For admins, use the school filter from frontend
      // null/undefined means "all schools", otherwise use the specific school ID
      userSchoolId = schoolFilter || null;
    }

    // Build student filter based on user role
    const studentFilter: any = {
      // Exclude students with @example.com emails
      users: {
        none: {
          email: {
            endsWith: "@example.com",
          },
        },
      },
    };

    // Apply school filtering if needed
    if (userSchoolId) {
      studentFilter.school_id = userSchoolId;
    }

    // Get all students with their assignment status for this workflow
    const allStudents = await prisma.student.findMany({
      where: studentFilter,
      include: {
        users: {
          select: {
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        student_workflow: {
          where: {
            workflow_id: workflowId,
          },
          select: {
            id: true,
            status: true,
          },
        },
      },
    });

    // Transform the data to simplified interface
    const transformedStudents: StudentForAssignment[] = allStudents.map(
      (student) => {
        const user = student.users[0]; // Assuming one user per student
        return {
          id: student.id,
          grade: student.grade,
          first_name: user?.first_name || "",
          last_name: user?.last_name || "",
          email: user?.email || null,
          student_workflow: student.student_workflow,
        };
      },
    );

    // Filter assigned students (those with StudentWorkflow that's not COUNSELOR_UNPUBLISHED)
    const assignedStudents = transformedStudents.filter((student) => {
      const studentWorkflow = student.student_workflow[0];
      return (
        studentWorkflow &&
        studentWorkflow.status !== StudentWorkflowStatus.COUNSELOR_UNPUBLISHED
      );
    });

    // Get assignment summary including other schools' students if user is counselor
    let assignmentSummary: AssignmentSummary | undefined;
    if (currentUser.role === UserRole.COUNSELOR && userSchoolId) {
      // Get total count of all students assigned to this workflow (including other schools)
      const allAssignedCount = await prisma.studentWorkflow.count({
        where: {
          workflow_id: workflowId,
          status: { not: StudentWorkflowStatus.COUNSELOR_UNPUBLISHED },
        },
      });

      // Get count of students from other schools
      const otherSchoolAssignedCount = await prisma.studentWorkflow.count({
        where: {
          workflow_id: workflowId,
          status: { not: StudentWorkflowStatus.COUNSELOR_UNPUBLISHED },
          student: {
            school_id: { not: userSchoolId },
          },
        },
      });

      assignmentSummary = {
        totalStudents: transformedStudents.length,
        assignedStudents: assignedStudents.length,
        otherSchoolAssigned: otherSchoolAssignedCount,
      };
    }

    return {
      success: true,
      assignedStudents,
      allStudents: transformedStudents,
      assignmentSummary,
      workflowCreatorInfo,
    };
  } catch (error) {
    log.error("Error getting assigned students:", error);
    return { success: false, error: "Failed to get assigned students" };
  }
}

/**
 * Update student assignments for a published workflow
 * This function will:
 * - Create StudentWorkflow for newly assigned students
 * - Set status to COUNSELOR_UNPUBLISHED for students being removed (preserving their data)
 * - Set status back to NOT_STARTED for students being re-assigned
 */
export async function updateStudentAssignments(
  workflowId: string,
  selectedStudentIds: string[],
  schoolFilter?: string | null | undefined,
): Promise<{
  success: boolean;
  message?: string;
  error?: string;
}> {
  try {
    // Get current user to check role and school access
    const currentUser = await getCurrentUser();
    if (!currentUser) {
      return { success: false, error: "Unauthorized" };
    }

    // Verify the workflow exists and is published, and get creator information
    const workflow = await prisma.workflow.findUnique({
      where: { id: workflowId },
      include: {
        steps: true,
        owner: {
          select: {
            id: true,
            role: true,
            Counselor: {
              select: {
                school: {
                  select: {
                    id: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!workflow) {
      return { success: false, error: "Workflow not found" };
    }

    if (workflow.status !== WorkflowStatus.PUBLISHED) {
      return { success: false, error: "Workflow is not published" };
    }

    // Determine school filtering logic
    let userSchoolId: string | null = null;

    if (currentUser.role === UserRole.COUNSELOR) {
      // For counselors, always filter by their school
      const counselor = await prisma.counselor.findUnique({
        where: { user_id: currentUser.id },
        select: { school_id: true },
      });
      userSchoolId = counselor?.school_id || null;
    } else if (currentUser.role === UserRole.ADMIN) {
      // For admins, use the school filter from frontend
      userSchoolId = schoolFilter || null;
    }

    // Get all existing StudentWorkflow records for this workflow
    const existingStudentWorkflows = await prisma.studentWorkflow.findMany({
      where: { workflow_id: workflowId },
      select: {
        id: true,
        student_id: true,
        status: true,
        student: {
          select: {
            school_id: true,
          },
        },
      },
    });

    const existingStudentIds = existingStudentWorkflows.map(
      (sw) => sw.student_id,
    );

    // Note: In web folder, we give admin users full flexibility to assign students across schools

    // Filter currently assigned students based on school access
    let currentlyAssignedIds: string[];
    if (userSchoolId) {
      // For users with school filtering, only consider students from the specified school
      currentlyAssignedIds = existingStudentWorkflows
        .filter(
          (sw) =>
            sw.status !== StudentWorkflowStatus.COUNSELOR_UNPUBLISHED &&
            sw.student.school_id === userSchoolId,
        )
        .map((sw) => sw.student_id);
    } else {
      // For admins without school filtering, consider all students
      currentlyAssignedIds = existingStudentWorkflows
        .filter(
          (sw) => sw.status !== StudentWorkflowStatus.COUNSELOR_UNPUBLISHED,
        )
        .map((sw) => sw.student_id);
    }

    // Students to be newly assigned (not in existing records)
    const newStudentIds = selectedStudentIds.filter(
      (id) => !existingStudentIds.includes(id),
    );

    // Students to be re-assigned (currently hidden but now selected)
    const reAssignedIds = selectedStudentIds.filter(
      (id) =>
        existingStudentIds.includes(id) && !currentlyAssignedIds.includes(id),
    );

    // Students to be hidden (currently assigned but not selected)
    const toHideIds = currentlyAssignedIds.filter(
      (id) => !selectedStudentIds.includes(id),
    );

    // Create StudentWorkflow records for new students
    const newStudentWorkflows: { id: string; student_id: string }[] = [];
    for (const studentId of newStudentIds) {
      const studentWorkflow = await prisma.studentWorkflow.create({
        data: {
          student_id: studentId,
          workflow_id: workflowId,
          status: StudentWorkflowStatus.NOT_STARTED,
        },
      });

      newStudentWorkflows.push({ id: studentWorkflow.id, student_id: studentId });

      // Create StudentWorkflowStep records for each step
      for (const step of workflow.steps) {
        await prisma.studentWorkFlowStep.create({
          data: {
            student_workflow_id: studentWorkflow.id,
            step_id: step.id,
            student_id: studentId,
            completed: false,
            data: {},
          },
        });
      }
    }

    // Re-assign previously hidden students and get their StudentWorkflow IDs
    const reAssignedStudentWorkflows: { id: string; student_id: string }[] = [];
    if (reAssignedIds.length > 0) {
      // Get current workflows to check their original status before COUNSELOR_UNPUBLISHED
      const existingWorkflows = await prisma.studentWorkflow.findMany({
        where: {
          workflow_id: workflowId,
          student_id: { in: reAssignedIds },
        },
        select: {
          id: true,
          student_id: true,
          status: true,
          workflow: {
            select: {
              workflow_type: true,
            },
          },
        },
      });

      // Update status for each student individually to preserve their original progress
      for (const studentWorkflow of existingWorkflows) {
        // If the student was COUNSELOR_UNPUBLISHED, we need to restore their original status
        // We'll check if they have any completed steps to determine their actual progress
        let newStatus: StudentWorkflowStatus = StudentWorkflowStatus.NOT_STARTED;

        if (studentWorkflow.status === StudentWorkflowStatus.COUNSELOR_UNPUBLISHED) {
          // Check workflow type to determine the appropriate recovery logic
          if (studentWorkflow.workflow.workflow_type === "UNSTRUCTURED") {
            // For unstructured conversations, check if student has any messages
            const student = await prisma.student.findUnique({
              where: { id: studentWorkflow.student_id },
              include: { users: true },
            });

            if (student?.users?.[0]) {
              const sessionId = `${workflowId}-${student.users[0].id}`;
              const messageCount = await prisma.messages.count({
                where: { session_id: sessionId },
              });

              // Check if all steps are completed for COMPLETED status
              const completedSteps = await prisma.studentWorkFlowStep.count({
                where: {
                  student_workflow_id: studentWorkflow.id,
                  completed: true,
                },
              });

              const totalSteps = await prisma.studentWorkFlowStep.count({
                where: {
                  student_workflow_id: studentWorkflow.id,
                },
              });

              if (completedSteps === totalSteps && totalSteps > 0) {
                newStatus = StudentWorkflowStatus.COMPLETED;
              } else if (messageCount > 0) {
                // Student has started the conversation (has messages)
                newStatus = StudentWorkflowStatus.IN_PROGRESS;
              } else {
                newStatus = StudentWorkflowStatus.NOT_STARTED;
              }
            } else {
              newStatus = StudentWorkflowStatus.NOT_STARTED;
            }
          } else {
            // For structured conversations, use the original step-based logic
            const completedSteps = await prisma.studentWorkFlowStep.findMany({
              where: {
                student_workflow_id: studentWorkflow.id,
                completed: true,
              },
            });

            const totalSteps = await prisma.studentWorkFlowStep.count({
              where: {
                student_workflow_id: studentWorkflow.id,
              },
            });

            if (completedSteps.length === totalSteps && totalSteps > 0) {
              newStatus = StudentWorkflowStatus.COMPLETED;
            } else if (completedSteps.length > 0) {
              newStatus = StudentWorkflowStatus.IN_PROGRESS;
            } else {
              newStatus = StudentWorkflowStatus.NOT_STARTED;
            }
          }
        } else {
          // If not COUNSELOR_UNPUBLISHED, preserve the existing status
          newStatus = studentWorkflow.status;
        }

        await prisma.studentWorkflow.update({
          where: {
            id: studentWorkflow.id,
          },
          data: {
            status: newStatus,
          },
        });
      }

      reAssignedStudentWorkflows.push(...existingWorkflows.map(w => ({ id: w.id, student_id: w.student_id })));
    }

    // Hide students that are being removed
    if (toHideIds.length > 0) {
      await prisma.studentWorkflow.updateMany({
        where: {
          workflow_id: workflowId,
          student_id: { in: toHideIds },
        },
        data: {
          status: StudentWorkflowStatus.COUNSELOR_UNPUBLISHED,
        },
      });
    }

    // Send SMS notifications to newly assigned and re-assigned students
    const studentsToNotify = [...newStudentWorkflows, ...reAssignedStudentWorkflows];
    if (studentsToNotify.length > 0) {
      try {
        // Process notifications in parallel
        await Promise.all(
          studentsToNotify.map((sw) =>
            sendConversationNotificationSms(sw.id),
          ),
        );
        log.info(
          `Sent SMS notifications to ${studentsToNotify.length} students for workflow ${workflowId}`,
        );
      } catch (error) {
        log.error("Error sending SMS notifications:", error);
        // Continue even if notifications fail - don't block the assignment process
      }
    }

    const totalChanges =
      newStudentIds.length + reAssignedIds.length + toHideIds.length;

    log.info(
      `Updated assignments for workflow ${workflowId}: ${newStudentIds.length} new, ${reAssignedIds.length} re-assigned, ${toHideIds.length} unpublished`,
    );

    return {
      success: true,
      message: `Successfully updated assignments for ${totalChanges} students`,
    };
  } catch (error) {
    log.error("Error updating student assignments:", error);
    return { success: false, error: "Failed to update student assignments" };
  }
}
