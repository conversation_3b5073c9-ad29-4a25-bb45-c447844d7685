"use server";

import { prisma } from "@/common/db";
import { baseApiUrl } from "@/common/api";
import { env } from "@/env.mjs";
import { EngagementChannel, EngagementEventType, EngagementTier } from "@prisma/client";

export interface EngagementEvent {
  id: string;
  event_type: EngagementEventType;
  channel: EngagementChannel;
  student_id: string | null;
  user_id: string;
  session_id: string | null;
  workflow_id: string | null;
  reminder_id: string | null;
  message_id: number | null;
  metadata: any;
  created_at: Date;
  student?: {
    id: string;
    name: string;
    email: string | null;
    phone_number: string | null;
    school?: {
      id: string;
      name: string;
    };
  };
}

export interface EngagementEventsResponse {
  events: EngagementEvent[];
  total: number;
  page: number;
  pageSize: number;
}

export interface EngagementStats {
  totalEvents24h: number;
  activeStudents24h: number;
  mostActiveChannel: EngagementChannel | null;
  engagementTrend: number; // percentage change
}

export interface EngagementFilters {
  startDate?: Date;
  endDate?: Date;
  channel?: EngagementChannel;
  eventType?: EngagementEventType;
  studentId?: string;
  schoolId?: string;
  tier?: EngagementTier;
  searchQuery?: string;
}

export async function getEngagementEvents(
  page: number = 1,
  pageSize: number = 50,
  sortBy: string = "created_at",
  sortOrder: "asc" | "desc" = "desc",
  filters?: EngagementFilters
): Promise<EngagementEventsResponse> {
  try {
    const skip = (page - 1) * pageSize;
    
    // Build where clause based on filters
    const where: any = {};
    
    if (filters?.startDate || filters?.endDate) {
      where.created_at = {};
      if (filters.startDate) {
        where.created_at.gte = filters.startDate;
      }
      if (filters.endDate) {
        where.created_at.lte = filters.endDate;
      }
    }
    
    if (filters?.channel) {
      where.channel = filters.channel;
    }
    
    if (filters?.eventType) {
      where.event_type = filters.eventType;
    }
    
    if (filters?.studentId) {
      where.student_id = filters.studentId;
    }
    
    if (filters?.schoolId) {
      where.student = {
        school_id: filters.schoolId
      };
    }
    
    if (filters?.tier) {
      where.student = {
        ...where.student,
        engagement_tier: filters.tier
      };
    }
    
    if (filters?.searchQuery) {
      where.OR = [
        {
          student: {
            users: {
              some: {
                OR: [
                  {
                    first_name: {
                      contains: filters.searchQuery,
                      mode: 'insensitive'
                    }
                  },
                  {
                    last_name: {
                      contains: filters.searchQuery,
                      mode: 'insensitive'
                    }
                  },
                  {
                    email: {
                      contains: filters.searchQuery,
                      mode: 'insensitive'
                    }
                  }
                ]
              }
            }
          }
        },
        {
          session_id: {
            contains: filters.searchQuery,
            mode: 'insensitive'
          }
        }
      ];
    }
    
    // Get total count
    const total = await prisma.engagementEvent.count({ where });
    
    // Get events with student data
    const events = await prisma.engagementEvent.findMany({
      where,
      skip,
      take: pageSize,
      orderBy: {
        [sortBy]: sortOrder
      },
      include: {
        student: {
          include: {
            school: true,
            users: true
          }
        }
      }
    });
    
    return {
      events: events.map(event => ({
        ...event,
        metadata: event.metadata || {},
        student: event.student ? {
          id: event.student.id,
          name: event.student.users[0] ? `${event.student.users[0].first_name} ${event.student.users[0].last_name}` : "Unknown",
          email: event.student.users[0]?.email || null,
          phone_number: event.student.users[0]?.phone_number || null,
          school: event.student.school ? {
            id: event.student.school.id,
            name: event.student.school.name
          } : undefined
        } : undefined
      })),
      total,
      page,
      pageSize
    };
  } catch (error) {
    console.error("Error fetching engagement events:", error);
    throw new Error("Failed to fetch engagement events");
  }
}

export async function getEngagementStats(): Promise<EngagementStats> {
  try {
    const now = new Date();
    const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const fortyEightHoursAgo = new Date(now.getTime() - 48 * 60 * 60 * 1000);
    
    // Get events from last 24 hours
    const events24h = await prisma.engagementEvent.findMany({
      where: {
        created_at: {
          gte: twentyFourHoursAgo
        }
      },
      select: {
        channel: true,
        student_id: true
      }
    });
    
    // Get events from previous 24 hours for trend calculation
    const eventsPrevious24h = await prisma.engagementEvent.count({
      where: {
        created_at: {
          gte: fortyEightHoursAgo,
          lt: twentyFourHoursAgo
        }
      }
    });
    
    // Calculate stats
    const totalEvents24h = events24h.length;
    const activeStudents24h = new Set(events24h.map(e => e.student_id)).size;
    
    // Find most active channel
    const channelCounts = events24h.reduce((acc, event) => {
      acc[event.channel] = (acc[event.channel] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const mostActiveChannel = Object.entries(channelCounts).length > 0
      ? Object.entries(channelCounts).reduce((a, b) => a[1] > b[1] ? a : b)[0] as EngagementChannel
      : null;
    
    // Calculate trend percentage
    const engagementTrend = eventsPrevious24h > 0
      ? ((totalEvents24h - eventsPrevious24h) / eventsPrevious24h) * 100
      : 0;
    
    return {
      totalEvents24h,
      activeStudents24h,
      mostActiveChannel,
      engagementTrend
    };
  } catch (error) {
    console.error("Error fetching engagement stats:", error);
    throw new Error("Failed to fetch engagement stats");
  }
}

export async function getStudentEngagementDetails(studentId: string) {
  try {
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      include: {
        school: true,
        users: true,
        engagement_events: {
          take: 10,
          orderBy: {
            created_at: 'desc'
          }
        }
      }
    });
    
    if (!student) {
      throw new Error("Student not found");
    }
    
    // Get engagement summary
    const thirtyDaysAgo = new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000);
    const engagementSummary = await prisma.engagementEvent.groupBy({
      by: ['channel'],
      where: {
        student_id: studentId,
        created_at: {
          gte: thirtyDaysAgo
        }
      },
      _count: {
        id: true
      }
    });
    
    return {
      student: {
        id: student.id,
        name: student.users[0] ? `${student.users[0].first_name} ${student.users[0].last_name}` : "Unknown",
        email: student.users[0]?.email || null,
        phone_number: student.users[0]?.phone_number || null,
        engagement_tier: student.engagement_tier,
        school: student.school ? {
          id: student.school.id,
          name: student.school.name
        } : null
      },
      recentEvents: student.engagement_events,
      engagementSummary: engagementSummary.map(item => ({
        channel: item.channel,
        count: item._count.id
      }))
    };
  } catch (error) {
    console.error("Error fetching student engagement details:", error);
    throw new Error("Failed to fetch student engagement details");
  }
}

export async function exportEngagementEvents(filters?: EngagementFilters): Promise<string> {
  try {
    // For now, return a simple implementation
    // In production, this would generate a CSV file and return a download URL
    const events = await getEngagementEvents(1, 1000, "created_at", "desc", filters);
    
    const csv = [
      "Event Type,Channel,Student Name,Session ID,Workflow ID,Created At",
      ...events.events.map(event => 
        `${event.event_type},${event.channel},${event.student?.name || 'Unknown'},${event.session_id || ''},${event.workflow_id || ''},${event.created_at.toISOString()}`
      )
    ].join('\n');
    
    // In a real implementation, this would save to a file and return a URL
    return csv;
  } catch (error) {
    console.error("Error exporting engagement events:", error);
    throw new Error("Failed to export engagement events");
  }
}