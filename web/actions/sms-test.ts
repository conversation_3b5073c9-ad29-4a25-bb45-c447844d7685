"use server";

import { auth } from "@/auth";
import { UserRole } from "@prisma/client";
import { env } from "@/env.mjs";
import log from "@/common/logger";

interface SMSTestRequest {
  studentId: string;
  userId: string;
  message: string;
  sessionId: string;
}

interface SMSTestResponse {
  success: boolean;
  response?: string;
  error?: string;
}

export async function sendSMSTestMessage(request: SMSTestRequest): Promise<SMSTestResponse> {
  try {
    const session = await auth();
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return { success: false, error: "Unauthorized" };
    }

    const { studentId, userId, message, sessionId } = request;

    if (!studentId || !userId || !message || !sessionId) {
      return { success: false, error: "Missing required fields" };
    }

    // Call the backend SMS test API
    const protocol = env.ADDIE_API_HOST.includes("localhost") || env.ADDIE_API_HOST.includes("0.0.0.0") || env.ADDIE_API_HOST.includes("127.0.0.1") ? "http" : "https";
    const apiUrl = `${protocol}://${env.ADDIE_API_HOST}`;
    const response = await fetch(`${apiUrl}/api/sms/test`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": env.ADDIE_API_KEY,
      },
      body: JSON.stringify({
        student_id: studentId,
        user_id: userId,
        message: message,
        session_id: sessionId,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      log.error(`SMS test API error: ${response.status} - ${errorText}`);
      return { 
        success: false, 
        error: `API Error: ${response.status}` 
      };
    }

    const data = await response.json();
    
    if (data.success) {
      return {
        success: true,
        response: data.response,
      };
    } else {
      return {
        success: false,
        error: data.error || "Unknown error occurred",
      };
    }
  } catch (error) {
    log.error("Error in sendSMSTestMessage:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to send SMS test message",
    };
  }
}

export async function getSMSConversationHistory(studentId: string, userId: string): Promise<{
  success: boolean;
  messages?: any[];
  workflowInfo?: any;
  error?: string;
}> {
  try {
    const session = await auth();
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return { success: false, error: "Unauthorized" };
    }

    const protocol = env.ADDIE_API_HOST.includes("localhost") || env.ADDIE_API_HOST.includes("0.0.0.0") || env.ADDIE_API_HOST.includes("127.0.0.1") ? "http" : "https";
    const apiUrl = `${protocol}://${env.ADDIE_API_HOST}`;
    const response = await fetch(`${apiUrl}/api/sms/conversation-history`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": env.ADDIE_API_KEY,
      },
      body: JSON.stringify({
        student_id: studentId,
        user_id: userId,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      log.error(`SMS conversation history API error: ${response.status} - ${errorText}`);
      return { 
        success: false, 
        error: `API Error: ${response.status}` 
      };
    }

    const data = await response.json();
    
    if (data.success) {
      return {
        success: true,
        messages: data.messages || [],
        workflowInfo: data.workflow_info,
      };
    } else {
      return {
        success: false,
        error: data.error || "Unknown error occurred",
      };
    }
  } catch (error) {
    log.error("Error in getSMSConversationHistory:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get SMS conversation history",
    };
  }
}

export async function clearSMSTestSession(
  sessionId: string, 
  studentId: string, 
  userId: string
): Promise<{ success: boolean; message?: string; details?: any; error?: string }> {
  try {
    const session = await auth();
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return { success: false, error: "Unauthorized" };
    }

    // Call the backend to clear the test session and reset workflow
    const protocol = env.ADDIE_API_HOST.includes("localhost") || env.ADDIE_API_HOST.includes("0.0.0.0") || env.ADDIE_API_HOST.includes("127.0.0.1") ? "http" : "https";
    const apiUrl = `${protocol}://${env.ADDIE_API_HOST}`;
    const response = await fetch(`${apiUrl}/api/sms/test/clear`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": env.ADDIE_API_KEY,
      },
      body: JSON.stringify({
        session_id: sessionId,
        student_id: studentId,
        user_id: userId,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      log.error(`SMS test clear API error: ${response.status} - ${errorText}`);
      return { 
        success: false, 
        error: `API Error: ${response.status}` 
      };
    }

    const data = await response.json();
    return data;
  } catch (error) {
    log.error("Error in clearSMSTestSession:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to clear SMS test session",
    };
  }
}