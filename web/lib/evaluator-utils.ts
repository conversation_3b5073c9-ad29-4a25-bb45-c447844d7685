/**
 * Utility function to get the display name for a ChatEvaluatorConfig.
 * Uses a priority system with fallbacks for backwards compatibility.
 */
export function getEvaluatorName(config: {
  name?: string | null;
  questionnaire?: { name?: string | null } | null;
  prompt?: { content?: string | null } | null;
  is_raw_prompt?: boolean;
}): string {
  // Priority 1: Use explicit name if available and not empty
  if (config.name && config.name.trim() !== "") {
    return config.name;
  }
  
  // Priority 2: Use questionnaire name for traditional evaluators
  if (config.questionnaire?.name && config.questionnaire.name.trim() !== "") {
    return config.questionnaire.name;
  }
  
  // Priority 3: Use prompt preview for raw prompt evaluators
  if (config.is_raw_prompt && config.prompt?.content) {
    const promptPreview = config.prompt.content.substring(0, 50);
    return promptPreview.length < config.prompt.content.length
      ? `${promptPreview}...`
      : promptPreview;
  }
  
  // Priority 4: Generic fallback
  return config.is_raw_prompt ? "Raw Prompt Evaluator" : "Unnamed Evaluator";
}