import { UserRole } from "@prisma/client";
import getServerSession from "next-auth";
import { signIn } from "next-auth/react";

import { prisma } from "@/common/db";
import log from "@/common/logger";

export const getUserByEmail = async (email: string) => {
  try {
    const user = await prisma.user.findUnique({
      where: {
        email: email,
      },
      select: {
        first_name: true,
        emailVerified: true,
      },
    });

    return user;
  } catch {
    return null;
  }
};

export const getUserById = async (id: string) => {
  try {
    const user = await prisma.user.findUnique({ where: { id } });

    return user;
  } catch {
    return null;
  }
};

export async function simpleMessages(
  messagesId: string,
  humanType: string = "Student",
) {
  const messages = await prisma.messages.findMany({
    where: { session_id: messagesId },
    orderBy: { created_at: "asc" },
  });

  let minMessages = messages.map((message: any) => {
    const msgData = message.message.data;
    const data = {
      id: message.id,
      messageId: msgData.id,
      type: msgData.type,
      content: msgData.content,
      created_at: message.created_at,
    };

    return data;
  });

  for (const message of minMessages) {
    if (message.type === "human") {
      message.type = humanType;
    }
    if (message.type === "ai") {
      message.type = "Addie";
    }
  }

  // filter empty
  minMessages = minMessages.filter((message) => message.content !== "");
  // sort by id
  minMessages = minMessages.sort((a, b) => a.id - b.id);

  return minMessages;
}
