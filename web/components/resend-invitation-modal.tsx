import { useState } from "react";
import { inviteCounselor } from "@/actions/schools";
import { toast } from "@/components/ui/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";

interface ResendInvitationModalProps {
  counselorId: string;
  counselorName: string;
  counselorEmail: string | null;
  schoolId: string;
  schoolName: string;
  children?: React.ReactNode;
}

export default function ResendInvitationModal({
  counselorId,
  counselorName,
  counselorEmail,
  schoolId,
  schoolName,
  children
}: ResendInvitationModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  // Function to handle resending invitation
  async function handleResendInvitation() {
    if (!counselorEmail) {
      toast({
        title: "Error",
        description: "Counselor email is missing",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    
    try {
      const formData = new FormData();
      formData.append("email", counselorEmail);
      formData.append("schoolId", schoolId);
      formData.append("resend", "true");
      formData.append("forceResend", "true");
      
      const result = await inviteCounselor(formData);
      
      if (result.error) {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive"
        });
        setIsOpen(false);
        return;
      }
      
      toast({
        title: "Success",
        description: result.message || `Invitation resent to ${counselorEmail}`
      });
      
      setIsOpen(false);
    } catch (error) {
      console.error("Error resending invitation:", error);
      toast({
        title: "Error",
        description: "Failed to resend invitation",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        {children || (
          <Button variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Resend Invitation
          </Button>
        )}
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Resend Invitation</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to resend an invitation to <strong>{counselorName}</strong> at <strong>{counselorEmail}</strong>? 
            This will send them another email to join {schoolName}.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={(e) => {
              e.preventDefault();
              handleResendInvitation();
            }}
            disabled={isLoading}
            className="bg-amber-500 hover:bg-amber-600"
          >
            {isLoading ? "Sending..." : "Resend Invitation"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
