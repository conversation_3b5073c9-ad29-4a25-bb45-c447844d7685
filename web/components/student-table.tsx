"use client";

import * as React from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  batchDisableStudents,
  batchEnableStudents,
  disableStudent,
  enableStudent,
} from "@/actions";
import { triggerOnboarding } from "@/actions/onboarding";
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import {
  AlertTriangle,
  ArrowUpDown,
  Ban,
  Check,
  CheckCircle,
  ChevronDown,
  Columns,
  Mail,
  MessageCircle,
  MessageSquare,
  Minus,
  Phone,
  Send,
  XCircle,
} from "lucide-react";
import { isValidPhoneNumber } from "react-phone-number-input";
import { toast } from "sonner";

import { Student } from "@/common/model";
import { formatDate } from "@/common/utils";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import ResendStudentInvitationModal from "@/components/resend-student-invitation-modal";
import { TooltipWrapper } from "@/components/shared/TooltipWrapper";
import UserEnableDisableModal from "@/components/user-enable-disable-modal";
import { useUser } from "@/app/context/UserContext";

// Define a type that omits lastMessage from Student since we don't need it in the table
type StudentWithoutLastMessage = Omit<Student, "lastMessage">;

// Define extended type for student data with additional properties
export type EnhancedStudent = StudentWithoutLastMessage & {
  // These properties are added to extend the base Student type
  last_active?: Date | null;
  isProfileComplete?: boolean;
  phone_number?: string | null;
  onboarding_status?: boolean; // Computed field for sorting
  school_name?: string;
  school_id: string; // Ensure school_id is available
  student_id: string; // Ensure student_id is available
  enabled: boolean; // User enabled status
  // We keep lastMessage because it's required by the Student interface, but we don't use it
  lastMessage?: Date | undefined;
  // Assigned conversation information
  assignedConversations?: Array<{
    workflowName: string;
    workflowType: string;
    status: string;
  }>;
  // Onboarding activity information
  onboardingActivity?: {
    status: string;
    onboarding_started_at: Date | null;
  } | null;
  // Engagement tier information
  engagement_tier?: string | null;
  tier_updated_at?: Date | null;
  last_activity_at?: Date | null;
  last_activity_channel?: string | null;
};

const createColumns = (
  currentUserId: string | null,
  router: any,
): ColumnDef<EnhancedStudent>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        onClick={(e) => e.stopPropagation()}
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: "full_name",
    accessorFn: (row) => `${row.first_name} ${row.last_name}`,
    header: ({ column }) => (
      <TooltipWrapper tooltipContent="Student Name">
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="flex w-full items-center justify-between"
        >
          <span className="truncate">Name</span>
          <ArrowUpDown className="ml-2 h-4 w-4 shrink-0" />
        </Button>
      </TooltipWrapper>
    ),
    cell: ({ row }) => {
      const student = row.original;
      const fullName = `${student.first_name} ${student.last_name}`;
      return <div className="truncate capitalize">{fullName}</div>;
    },
  },
  {
    accessorKey: "email",
    header: ({ column }) => (
      <TooltipWrapper tooltipContent="Email">
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="flex w-full items-center justify-start gap-2"
        >
          <span className="truncate">Email</span>
          <ArrowUpDown className="h-4 w-4 shrink-0" />
        </Button>
      </TooltipWrapper>
    ),
    cell: ({ row }) => <div className="truncate">{row.getValue("email")}</div>,
  },
  // Commented out for now - Tokens and Steps columns
  // {
  //   accessorKey: "msgsCount",
  //   header: ({ column }) => (
  //     <TooltipWrapper tooltipContent="# Messages">
  //       <Button
  //         variant="ghost"
  //         onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //         className="flex w-full items-center justify-between"
  //       >
  //         <span className="truncate"># Messages</span>
  //         <ArrowUpDown className="ml-2 h-4 w-4 shrink-0" />
  //       </Button>
  //     </TooltipWrapper>
  //   ),
  //   cell: ({ row }) => (
  //     <div className="truncate">
  //       {row.getValue<Number>("msgsCount")?.toString()}
  //     </div>
  //   ),
  // },
  // {
  //   accessorKey: "completedWFSteps",
  //   header: ({ column }) => (
  //     <TooltipWrapper tooltipContent="# Steps">
  //       <Button
  //         variant="ghost"
  //         onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //         className="flex w-full items-center justify-between px-1"
  //       >
  //         <span className="truncate text-xs"># Steps</span>
  //         <ArrowUpDown className="ml-1 h-4 w-4 shrink-0" />
  //       </Button>
  //     </TooltipWrapper>
  //   ),
  //   cell: ({ row }) => (
  //     <div className="truncate">
  //       {row.getValue<Number>("completedWFSteps")?.toString()}
  //     </div>
  //   ),
  // },
  // {
  //   accessorKey: "tokenUsage",
  //   header: ({ column }) => (
  //     <TooltipWrapper tooltipContent="# Token">
  //       <Button
  //         variant="ghost"
  //         onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //         className="flex w-full items-center justify-between px-1"
  //       >
  //         <span className="truncate text-xs"># Token</span>
  //         <ArrowUpDown className="ml-1 h-4 w-4 shrink-0" />
  //       </Button>
  //     </TooltipWrapper>
  //   ),
  //   cell: ({ row }) => (
  //     <div className="truncate">
  //       {row.getValue<Number>("tokenUsage")?.toString()}
  //     </div>
  //   ),
  // },

  {
    accessorKey: "last_active",
    header: ({ column }) => (
      <TooltipWrapper tooltipContent="Last Active">
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="flex w-full items-center justify-between"
        >
          <span className="truncate">Last Active</span>
          <ArrowUpDown className="ml-2 h-4 w-4 shrink-0" />
        </Button>
      </TooltipWrapper>
    ),
    cell: ({ row }) => {
      const lastActive = row.getValue("last_active");
      if (lastActive) {
        return (
          <div className="truncate text-sm">
            {formatDate(lastActive.toString())}
          </div>
        );
      }
      return (
        <div className="truncate text-sm italic text-muted-foreground">
          Never
        </div>
      );
    },
  },
  {
    accessorKey: "enabled",
    header: ({ column }) => (
      <TooltipWrapper tooltipContent="Status">
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="flex w-full items-center justify-start gap-2"
        >
          <span className="truncate">Status</span>
          <ArrowUpDown className="h-4 w-4 shrink-0" />
        </Button>
      </TooltipWrapper>
    ),
    cell: ({ row }) => {
      const student = row.original;
      const enabled = student.enabled;

      return (
        <div className="flex items-center truncate">
          <UserEnableDisableModal
            action={enabled ? "disable" : "enable"}
            userType="student"
            userCount={1}
            userNames={[`${student.first_name} ${student.last_name}`]}
            schoolContext={{
              schoolId: student.school_id,
              schoolName: student.school_name || "",
            }}
            userEmails={[student.email || ""]}
            onConfirm={async () => {
              if (!currentUserId) {
                console.error("No current user found");
                return;
              }

              const result = enabled
                ? await disableStudent(student.id, currentUserId)
                : await enableStudent(student.id, currentUserId);

              if (!result.success) {
                console.error("Failed to update student:", result.error);
              }
            }}
          >
            <div
              className={`inline-flex cursor-pointer items-center rounded-full px-2 py-1 text-xs font-medium transition-opacity hover:opacity-80 ${
                enabled
                  ? "bg-green-100 text-green-800 hover:bg-green-200"
                  : "bg-red-100 text-red-800 hover:bg-red-200"
              }`}
              onClick={(e) => e.stopPropagation()}
            >
              {enabled ? "Enabled" : "Disabled"}
            </div>
          </UserEnableDisableModal>
        </div>
      );
    },
  },
  {
    accessorKey: "onboarding_status",
    header: ({ column }) => (
      <TooltipWrapper tooltipContent="Onboarded - Shows if the student has completed the onboarding wizard and has a valid phone number">
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="flex w-full items-center justify-start gap-2"
        >
          <span className="truncate">Onboarded</span>
          <ArrowUpDown className="h-4 w-4 shrink-0" />
        </Button>
      </TooltipWrapper>
    ),
    cell: ({ row }) => {
      const student = row.original;
      const isOnboarded = student.onboarding_status;

      return (
        <div className="flex items-center justify-center truncate">
          <TooltipWrapper
            tooltipContent={
              isOnboarded
                ? "Onboarded - Has completed onboarding and has a valid phone number"
                : "Not Onboarded - Has not completed onboarding or does not have a valid phone number"
            }
          >
            {isOnboarded ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <XCircle className="h-5 w-5 text-red-600" />
            )}
          </TooltipWrapper>
        </div>
      );
    },
  },
  {
    accessorKey: "engagement_tier",
    sortingFn: (rowA, rowB, columnId) => {
      const tierA = rowA.getValue(columnId) as string | null;
      const tierB = rowB.getValue(columnId) as string | null;
      
      // If both are null, they're equal
      if (!tierA && !tierB) return 0;
      
      // If only A is null, B comes first (A goes to bottom)
      if (!tierA) return 1;
      
      // If only B is null, A comes first (B goes to bottom)
      if (!tierB) return -1;
      
      // Both have values, sort by tier priority: Active > At_Risk > Dormant
      const tierOrder = { "Active": 0, "At_Risk": 1, "At-Risk": 1, "Dormant": 2 };
      const orderA = tierOrder[tierA as keyof typeof tierOrder] ?? 999;
      const orderB = tierOrder[tierB as keyof typeof tierOrder] ?? 999;
      
      return orderA - orderB;
    },
    header: ({ column }) => (
      <TooltipWrapper tooltipContent="Engagement Tier - Classification based on recent activity: Active (≤24h), At-Risk (2-5 days), Dormant (>5 days)">
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="flex w-full items-center justify-start gap-2"
        >
          <span className="truncate">Tier</span>
          <ArrowUpDown className="h-4 w-4 shrink-0" />
        </Button>
      </TooltipWrapper>
    ),
    cell: ({ row }) => {
      const student = row.original;
      const tier = student.engagement_tier;

      if (!tier) {
        return (
          <div className="flex items-center justify-center truncate">
            <span className="text-xs text-muted-foreground">-</span>
          </div>
        );
      }

      // Define tier styling
      const getTierStyle = (tier: string) => {
        switch (tier) {
          case "Active":
            return "border-green-200 bg-green-100 text-green-800";
          case "At_Risk":
          case "At-Risk":
            return "border-yellow-200 bg-yellow-100 text-yellow-800";
          case "Dormant":
            return "border-red-200 bg-red-100 text-red-800";
          default:
            return "border-gray-200 bg-gray-100 text-gray-800";
        }
      };

      const displayTier = tier === "At_Risk" ? "At-Risk" : tier;

      return (
        <div className="flex items-center justify-center truncate">
          <TooltipWrapper
            tooltipContent={`Engagement Tier: ${displayTier}${
              student.tier_updated_at
                ? ` (Updated: ${new Date(student.tier_updated_at).toLocaleDateString()})`
                : ""
            }`}
          >
            <div
              className={`inline-flex items-center rounded-full border px-2 py-1 text-xs font-medium ${getTierStyle(tier)}`}
            >
              {displayTier}
            </div>
          </TooltipWrapper>
        </div>
      );
    },
  },
  {
    accessorKey: "phone_number",
    header: ({ column }) => (
      <TooltipWrapper tooltipContent="Phone Number">
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="flex w-full items-center justify-start gap-2"
        >
          <span className="truncate">Phone</span>
          <ArrowUpDown className="h-4 w-4 shrink-0" />
        </Button>
      </TooltipWrapper>
    ),
    cell: ({ row }) => {
      const student = row.original;
      const phoneNumber = student.phone_number;
      const hasValidPhone = phoneNumber && isValidPhoneNumber(phoneNumber);

      return (
        <div className="flex items-center justify-center truncate">
          <TooltipWrapper
            tooltipContent={
              hasValidPhone ? `Phone: ${phoneNumber}` : "No phone number"
            }
          >
            <div className="flex items-center">
              {hasValidPhone ? (
                <Phone className="h-5 w-5 text-green-600" />
              ) : (
                <div className="relative">
                  <Phone className="h-5 w-5 text-red-500" />
                  <AlertTriangle className="absolute -bottom-1 -right-1 h-3 w-3 text-red-600" />
                </div>
              )}
            </div>
          </TooltipWrapper>
        </div>
      );
    },
  },
  {
    id: "assigned_conversations",
    header: ({ column }) => (
      <TooltipWrapper tooltipContent="Shows the first assigned unstructured conversation (if any) that is in 'NOT_STARTED' status">
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="flex w-full items-center justify-start gap-2"
        >
          <span className="truncate">Conversations</span>
          <ArrowUpDown className="h-4 w-4 shrink-0" />
        </Button>
      </TooltipWrapper>
    ),
    cell: ({ row }) => {
      const student = row.original;
      const assignedConversations = student.assignedConversations || [];
      const hasAssignedConversations = assignedConversations.length > 0;

      // Create tooltip content - show the earliest NOT_STARTED unstructured conversation (same as onboarding)
      const tooltipContent = hasAssignedConversations
        ? (() => {
            // Find the earliest NOT_STARTED unstructured conversation (same logic as onboarding)
            const unstructuredNotStarted = assignedConversations.filter(
              (conv) =>
                conv.workflowType === "UNSTRUCTURED" &&
                conv.status === "NOT_STARTED",
            );

            if (unstructuredNotStarted.length > 0) {
              // Show the first one (they should be sorted by created_at from DB)
              return unstructuredNotStarted[0].workflowName;
            } else {
              // Fallback to first conversation if no unstructured NOT_STARTED found
              return assignedConversations[0].workflowName;
            }
          })()
        : "No assigned conversations";

      return (
        <div className="flex items-center justify-center truncate">
          <TooltipWrapper tooltipContent={tooltipContent}>
            <div className="flex items-center">
              {hasAssignedConversations ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600" />
              )}
            </div>
          </TooltipWrapper>
        </div>
      );
    },
  },
  {
    id: "resend",
    header: () => (
      <TooltipWrapper tooltipContent="Resend Invitation">
        <span className="truncate text-xs">Invitation</span>
      </TooltipWrapper>
    ),
    cell: ({ row }) => {
      const student = row.original;
      const studentName = `${student.first_name} ${student.last_name}`;

      // Only show resend button for students who have never logged in (last_active is null)
      return (
        <div className="flex items-center truncate">
          {!student.last_active ? (
            <ResendStudentInvitationModal
              studentId={student.id}
              studentName={studentName}
              studentEmail={student.email}
              schoolId={student.school_id}
              schoolName={student.school_name || ""}
            >
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-amber-600 hover:bg-amber-50 hover:text-amber-600"
                title="Resend Invitation"
                onClick={(e) => e.stopPropagation()}
              >
                <Mail className="h-4 w-4" />
              </Button>
            </ResendStudentInvitationModal>
          ) : (
            <span className="px-2 text-sm text-muted-foreground">-</span>
          )}
        </div>
      );
    },
  },
  {
    id: "sms_test",
    header: () => (
      <TooltipWrapper tooltipContent="SMS Test">
        <span className="truncate text-xs">SMS Test</span>
      </TooltipWrapper>
    ),
    cell: ({ row }) => {
      const student = row.original;

      // Only show SMS test button for enabled students
      return (
        <div className="flex items-center truncate">
          {student.enabled ? (
            <Link href={`/sms-test/${student.id}`}>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-blue-600 hover:bg-blue-50 hover:text-blue-600"
                title="Test SMS Workflow"
                onClick={(e) => e.stopPropagation()}
              >
                <MessageSquare className="h-4 w-4" />
              </Button>
            </Link>
          ) : (
            <span className="px-2 text-sm text-muted-foreground">-</span>
          )}
        </div>
      );
    },
  },
  {
    id: "start_sms_onboarding",
    header: () => (
      <TooltipWrapper tooltipContent="Start SMS Onboarding - This will send out an SMS message to start their first unstructured conversation">
        <span className="truncate text-xs">Start SMS Onboarding</span>
      </TooltipWrapper>
    ),
    cell: ({ row }) => {
      const student = row.original;

      // Check if onboarding has already been sent
      const onboardingAlreadySent =
        student.onboardingActivity?.status === "sent";

      // Check if student has started their first unstructured conversation
      const hasStartedUnstructuredConversation =
        student.assignedConversations?.some(
          (conv) =>
            conv.workflowType === "UNSTRUCTURED" &&
            (conv.status === "IN_PROGRESS" || conv.status === "COMPLETED"),
        );

      // Check the three criteria for onboarding
      const hasValidPhone =
        student.phone_number && isValidPhoneNumber(student.phone_number);
      const hasUnstructuredConversation = student.assignedConversations?.some(
        (conv) =>
          conv.workflowType === "UNSTRUCTURED" && conv.status === "NOT_STARTED",
      );
      const isEnabled = student.enabled;

      // Determine if onboarding can start
      const canStartOnboarding =
        hasValidPhone &&
        hasUnstructuredConversation &&
        isEnabled &&
        !onboardingAlreadySent &&
        !hasStartedUnstructuredConversation;

      // Build tooltip message
      let tooltipContent: string;

      if (onboardingAlreadySent) {
        const sentDate = student.onboardingActivity?.onboarding_started_at
          ? new Date(
              student.onboardingActivity.onboarding_started_at,
            ).toLocaleDateString()
          : "Unknown date";
        tooltipContent = `SMS onboarding already sent on ${sentDate}`;
      } else if (hasStartedUnstructuredConversation) {
        tooltipContent =
          "Student has already started their first unstructured conversation";
      } else if (hasValidPhone && hasUnstructuredConversation && isEnabled) {
        tooltipContent =
          "Click to start SMS onboarding - This will send out an SMS message to start their first unstructured conversation";
      } else {
        const missingCriteria: string[] = [];
        if (!hasValidPhone) missingCriteria.push("Valid phone number");
        if (!hasUnstructuredConversation)
          missingCriteria.push("Unstructured conversation (NOT_STARTED)");
        if (!isEnabled) missingCriteria.push("Student must be enabled");
        tooltipContent = `Cannot start SMS onboarding. Missing: ${missingCriteria.join(", ")}`;
      }

      return (
        <div className="flex items-center justify-center truncate">
          <TooltipWrapper tooltipContent={tooltipContent}>
            {onboardingAlreadySent || hasStartedUnstructuredConversation ? (
              <div className="flex cursor-not-allowed items-center">
                <Check className="h-5 w-5 text-green-600" />
              </div>
            ) : canStartOnboarding ? (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-blue-600 hover:bg-blue-50 hover:text-blue-600"
                title="Start SMS Onboarding"
                onClick={async (e) => {
                  e.stopPropagation();
                  try {
                    const result = await triggerOnboarding(student.id);
                    if (result.success) {
                      toast.success("SMS Onboarding Started", {
                        description: `Successfully triggered SMS onboarding for ${student.first_name} ${student.last_name}`,
                      });
                      // Use router.refresh for better UX
                      router.refresh();
                    } else {
                      toast.error("Failed to Start SMS Onboarding", {
                        description: result.error,
                      });
                    }
                  } catch (error) {
                    toast.error("Error", {
                      description:
                        "An unexpected error occurred. Please try again.",
                    });
                  }
                }}
              >
                <Send className="h-5 w-5" />
              </Button>
            ) : (
              <div className="flex cursor-not-allowed items-center">
                <Ban className="h-5 w-5 text-red-500" />
              </div>
            )}
          </TooltipWrapper>
        </div>
      );
    },
  },

  // --------------------------------HIDE REMOVE BUTTON FOR NOW, WE MIGHT RE-ENABLE IT, DON'T DELETE --------------------------------------------
  // {
  //   id: "actions",
  //   header: "Actions",
  //   enableHiding: false,
  //   cell: ({ row }) => {
  //     const student = row.original;
  //     const studentName = `${student.first_name} ${student.last_name}`;
  //
  //     return (
  //       <div className="flex items-center">
  //         <RemoveStudentModal
  //           studentId={student.id}
  //           studentName={studentName}
  //           schoolId={student.school_id}
  //           schoolName={student.school_name || ""}
  //         >
  //           <Button variant="ghost" size="sm" className="px-2 text-destructive hover:bg-destructive/10 hover:text-destructive">
  //             <Trash2 className="mr-1 h-4 w-4" />
  //             Remove
  //           </Button>
  //         </RemoveStudentModal>
  //       </div>
  //     );
  //   },
  // },
];

export default function StudentTable(params: {
  students: EnhancedStudent[];
  inviteButton?: React.ReactNode;
}) {
  const { students, inviteButton } = params;
  const router = useRouter();
  // console.log("students===>", students);
  const { user } = useUser();
  const currentUserId = user?.id || null;
  const now = new Date();

  // Create columns with access to currentUserId and router
  const columns = React.useMemo(
    () => createColumns(currentUserId, router),
    [currentUserId, router],
  );

  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  // Initialize column visibility with localStorage persistence
  const getInitialColumnVisibility = (): VisibilityState => {
    if (typeof window === "undefined") {
      // Server-side rendering fallback
      return {
        last_active: false,  // Hide Last Active by default
        resend: false,       // Hide Invitation by default  
        sms_test: false,     // Hide SMS Test by default
      };
    }

    try {
      const stored = localStorage.getItem("student-table-column-visibility");
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.warn("Failed to parse stored column visibility:", error);
    }

    // Return defaults if no stored data or error
    return {
      last_active: false,  // Hide Last Active by default
      resend: false,       // Hide Invitation by default  
      sms_test: false,     // Hide SMS Test by default
    };
  };

  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>(getInitialColumnVisibility);

  // Save column visibility to localStorage whenever it changes
  React.useEffect(() => {
    try {
      localStorage.setItem(
        "student-table-column-visibility",
        JSON.stringify(columnVisibility)
      );
    } catch (error) {
      console.warn("Failed to save column visibility to localStorage:", error);
    }
  }, [columnVisibility]);
  const [rowSelection, setRowSelection] = React.useState({});
  const [globalFilter, setGlobalFilter] = React.useState("");

  // Custom filter function for global search across multiple columns
  const globalFilterFn = React.useCallback(
    (row: any, columnId: string, filterValue: string) => {
      if (!filterValue) return true;

      const searchValue = filterValue.toLowerCase();
      const firstName = row.original.first_name?.toLowerCase() || "";
      const lastName = row.original.last_name?.toLowerCase() || "";
      const fullName = `${firstName} ${lastName}`;
      const email = row.original.email?.toLowerCase() || "";

      return (
        firstName.includes(searchValue) ||
        lastName.includes(searchValue) ||
        fullName.includes(searchValue) ||
        email.includes(searchValue)
      );
    },
    [],
  );

  const table = useReactTable({
    data: students,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
  });

  // Dynamic column widths based on screen size
  const [columnWidths, setColumnWidths] = React.useState({
    select: "40px",
    last_active: "80px",
    full_name: "140px",
    email: "140px",
    // msgsCount: "75px", // Commented out - not used
    // completedWFSteps: "65px", // Commented out - not used
    // tokenUsage: "65px", // Commented out - not used
    enabled: "75px",
    onboarding_status: "85px",
    engagement_tier: "80px",
    phone_number: "75px",
    assigned_conversations: "85px",
    resend: "65px",
    sms_test: "65px",
    start_sms_onboarding: "120px",
    // actions: "60px", // Commented out to save space
  });

  // Set up responsive column visibility and widths on initial render
  React.useEffect(() => {
    const handleResize = () => {
      const isXSmallScreen = window.innerWidth < 550; // xs breakpoint
      const isSmallScreen = window.innerWidth >= 550 && window.innerWidth < 768; // sm breakpoint
      const isMediumScreen =
        window.innerWidth >= 768 && window.innerWidth < 1024; // md breakpoint
      const isLargeScreen = window.innerWidth >= 1024; // lg breakpoint

      if (isXSmallScreen) {
        // On extra small screens, show only names and message count
        setColumnVisibility({
          select: true,
          last_active: false,
          full_name: true,
          email: false,
          // msgsCount: true, // Commented out - not used
          // completedWFSteps: false, // Commented out - not used
          // tokenUsage: false, // Commented out - not used
          enabled: true,
          onboarding_status: false,
          engagement_tier: false,
          phone_number: false,
          assigned_conversations: false,
          resend: false,
          sms_test: false,
          start_sms_onboarding: false,
        });
        setColumnWidths({
          select: "40px",
          last_active: "80px",
          full_name: "140px",
          email: "140px",
          // msgsCount: "75px", // Commented out - not used
          // completedWFSteps: "65px", // Commented out - not used
          // tokenUsage: "65px", // Commented out - not used
          enabled: "75px",
          onboarding_status: "85px",
          engagement_tier: "80px",
          phone_number: "75px",
          assigned_conversations: "85px",
          resend: "65px",
          sms_test: "65px",
          start_sms_onboarding: "120px",
          // actions: "60px", // Commented out to save space
        });
      } else if (isSmallScreen) {
        // On small screens, show a bit more
        setColumnVisibility({
          select: true,
          last_active: false,
          full_name: true,
          email: true,
          // msgsCount: true, // Commented out - not used
          // completedWFSteps: false, // Commented out - not used
          // tokenUsage: false, // Commented out - not used
          enabled: true,
          onboarding_status: true,
          engagement_tier: false,
          phone_number: true,
          assigned_conversations: false,
          resend: false,
          sms_test: false,
          start_sms_onboarding: false,
        });
        setColumnWidths({
          select: "40px",
          last_active: "80px",
          full_name: "140px",
          email: "140px",
          // msgsCount: "75px", // Commented out - not used
          // completedWFSteps: "65px", // Commented out - not used
          // tokenUsage: "65px", // Commented out - not used
          enabled: "75px",
          onboarding_status: "85px",
          engagement_tier: "80px",
          phone_number: "75px",
          assigned_conversations: "85px",
          resend: "65px",
          sms_test: "65px",
          start_sms_onboarding: "120px",
          // actions: "60px", // Commented out to save space
        });
      } else if (isMediumScreen) {
        // On medium screens, show all columns with balanced widths
        setColumnVisibility({
          select: true,
          last_active: true,
          full_name: true,
          email: true,
          // msgsCount: true, // Commented out - not used
          // completedWFSteps: true, // Commented out - not used
          // tokenUsage: true, // Commented out - not used
          enabled: true,
          onboarding_status: true,
          engagement_tier: true,
          phone_number: true,
          assigned_conversations: true,
          resend: true,
          sms_test: true,
          start_sms_onboarding: true,
        });
        setColumnWidths({
          select: "45px",
          last_active: "85px",
          full_name: "150px",
          email: "150px",
          // msgsCount: "85px", // Commented out - not used
          // completedWFSteps: "75px", // Commented out - not used
          // tokenUsage: "75px", // Commented out - not used
          enabled: "80px",
          onboarding_status: "90px",
          engagement_tier: "80px",
          phone_number: "80px",
          assigned_conversations: "90px",
          resend: "70px",
          sms_test: "70px",
          start_sms_onboarding: "130px",
          // actions: "70px", // Commented out to save space
        });
      } else if (isLargeScreen) {
        // On large screens, show all columns and spread them out more
        setColumnVisibility({
          select: true,
          last_active: true,
          full_name: true,
          email: true,
          // msgsCount: true, // Commented out - not used
          // completedWFSteps: true, // Commented out - not used
          // tokenUsage: true, // Commented out - not used
          enabled: true,
          onboarding_status: true,
          engagement_tier: true,
          phone_number: true,
          assigned_conversations: true,
          resend: true,
          sms_test: true,
          start_sms_onboarding: true,
        });
        setColumnWidths({
          select: "45px",
          last_active: "90px",
          full_name: "160px",
          email: "160px",
          // msgsCount: "95px", // Commented out - not used
          // completedWFSteps: "85px", // Commented out - not used
          // tokenUsage: "85px", // Commented out - not used
          enabled: "85px",
          onboarding_status: "95px",
          engagement_tier: "85px",
          phone_number: "85px",
          assigned_conversations: "95px",
          resend: "75px",
          sms_test: "75px",
          start_sms_onboarding: "140px",
          // actions: "80px", // Commented out to save space
        });
      }
    };

    // Set initial visibility and widths
    handleResize();

    // Add resize listener
    window.addEventListener("resize", handleResize);

    // Clean up
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div className="w-full">
      <div className="flex items-center justify-between py-4">
        <div className="flex items-center gap-2">
          <Input
            placeholder="Search students by name or email..."
            value={globalFilter ?? ""}
            onChange={(event) => setGlobalFilter(event.target.value)}
            className="max-w-sm"
          />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Columns className="mr-2 h-4 w-4" />
                Columns
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  // Get a friendly name for the column
                  const getColumnName = (id: string) => {
                    switch (id) {
                      case "first_name": return "Name";
                      case "email": return "Email";
                      case "last_active": return "Last Active";
                      case "enabled": return "Status";
                      case "onboarding_status": return "Onboarded";
                      case "engagement_tier": return "Tier";
                      case "phone_number": return "Phone";
                      case "completedWFSteps": return "Completed";
                      case "resend": return "Invitation";
                      case "sms_test": return "SMS Test";
                      case "start_sms_onboarding": return "Start SMS Onboarding";
                      default: return id.charAt(0).toUpperCase() + id.slice(1).replace(/_/g, " ");
                    }
                  };

                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) => column.toggleVisibility(!!value)}
                    >
                      {getColumnName(column.id)}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        {inviteButton && <div>{inviteButton}</div>}
      </div>

      {/* Batch Actions */}
      {Object.keys(rowSelection).length > 0 && (
        <div className="mb-4 flex items-center gap-2 rounded-md bg-muted p-3">
          <span className="text-sm text-muted-foreground">
            {Object.keys(rowSelection).length} student(s) selected
          </span>
          <UserEnableDisableModal
            action="enable"
            userType="student"
            userCount={Object.keys(rowSelection).length}
            userNames={table
              .getFilteredSelectedRowModel()
              .rows.map(
                (row) => `${row.original.first_name} ${row.original.last_name}`,
              )}
            schoolContext={(() => {
              const selectedRows = table.getFilteredSelectedRowModel().rows;
              const firstStudent = selectedRows[0]?.original;
              return firstStudent ? {
                schoolId: firstStudent.school_id,
                schoolName: firstStudent.school_name || "",
              } : undefined;
            })()}
            userEmails={table
              .getFilteredSelectedRowModel()
              .rows.map((row) => row.original.email || "")}
            onConfirm={async () => {
              const selectedRows = table.getFilteredSelectedRowModel().rows;
              const selectedStudents = selectedRows.map((row) => row.original);
              const studentIds = selectedStudents.map((s) => s.id);

              if (!currentUserId) {
                console.error("No current user found");
                return;
              }

              const result = await batchEnableStudents(
                studentIds,
                currentUserId,
              );
              if (!result.success) {
                console.error("Failed to enable students:", result.error);
              }
            }}
          >
            <Button
              variant="outline"
              size="sm"
              className="text-green-600 hover:bg-green-50 hover:text-green-600"
            >
              <Check className="mr-1 h-4 w-4" />
              Enable Selected
            </Button>
          </UserEnableDisableModal>
          <UserEnableDisableModal
            action="disable"
            userType="student"
            userCount={Object.keys(rowSelection).length}
            userNames={table
              .getFilteredSelectedRowModel()
              .rows.map(
                (row) => `${row.original.first_name} ${row.original.last_name}`,
              )}
            onConfirm={async () => {
              const selectedRows = table.getFilteredSelectedRowModel().rows;
              const selectedStudents = selectedRows.map((row) => row.original);
              const studentIds = selectedStudents.map((s) => s.id);

              if (!currentUserId) {
                console.error("No current user found");
                return;
              }

              const result = await batchDisableStudents(
                studentIds,
                currentUserId,
              );
              if (!result.success) {
                console.error("Failed to disable students:", result.error);
              }
            }}
          >
            <Button
              variant="outline"
              size="sm"
              className="text-red-600 hover:bg-red-50 hover:text-red-600"
            >
              <Minus className="mr-1 h-4 w-4" />
              Disable Selected
            </Button>
          </UserEnableDisableModal>
        </div>
      )}

      {/* Responsive container with horizontal scroll for small screens */}
      <div className="max-w-[calc(100vw-2rem)] overflow-x-auto rounded-md border sm:max-w-[calc(100vw-4rem)] md:max-w-none">
        <Table className="w-full min-w-[950px] table-fixed">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className="whitespace-nowrap px-2 py-3"
                      style={{
                        width:
                          columnWidths[
                            header.id as keyof typeof columnWidths
                          ] || "auto",
                      }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
                {/* Actions column commented out to save space */}
                {/* <TableHead
                  className="whitespace-nowrap px-2 py-3 text-center"
                  style={{ width: columnWidths.actions }}
                >
                  <TooltipWrapper tooltipContent="Actions">
                    <span className="truncate text-xs">Actions</span>
                  </TooltipWrapper>
                </TableHead> */}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={(e) => {
                    // Only navigate if the click target is not a button or inside a button
                    const target = e.target as HTMLElement;
                    const isButton =
                      target.closest("button") ||
                      target.closest("[role='button']");
                    if (!isButton) {
                      window.location.href = `/students/${row.original.id}`;
                    }
                  }}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      className="truncate whitespace-nowrap px-2 py-3"
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length + 1}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="mr-2 text-sm text-muted-foreground">
          Page {table.getState().pagination.pageIndex + 1} of{" "}
          {table.getPageCount()}
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
