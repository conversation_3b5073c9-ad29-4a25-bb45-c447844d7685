"use client";

import { useState } from "react";
import { toast } from "sonner";
import { Loader2, RefreshCw } from "lucide-react";

import { inviteStudent } from "@/actions/schools";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface ResendStudentInvitationModalProps {
  studentId: string;
  studentName: string;
  studentEmail: string | null;
  schoolId: string;
  schoolName: string;
  children?: React.ReactNode;
}

export default function ResendStudentInvitationModal({
  studentId,
  studentName,
  studentEmail,
  schoolId,
  schoolName,
  children
}: ResendStudentInvitationModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  // Function to handle resending invitation
  async function handleResendInvitation() {
    if (!studentEmail) {
      toast.error("Student email is missing");
      return;
    }

    setIsLoading(true);
    
    try {
      const formData = new FormData();
      formData.append("email", studentEmail);
      formData.append("schoolId", schoolId);
      formData.append("resend", "true");
      formData.append("forceResend", "true");
      
      const result = await inviteStudent(formData);
      
      if (result.error) {
        toast.error(result.error);
        setIsOpen(false);
        return;
      }
      
      toast.success(result.message || `Invitation resent to ${studentEmail}`);
      setIsOpen(false);
    } catch (error) {
      console.error("Error resending invitation:", error);
      toast.error("Failed to resend invitation");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button 
            variant="ghost" 
            size="sm" 
            className="px-2 text-amber-600 hover:bg-amber-50 hover:text-amber-600"
          >
            <RefreshCw className="mr-1 h-4 w-4" />
            Resend Invitation
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Resend Invitation</DialogTitle>
          <DialogDescription>
            Resend invitation email to <strong>{studentName}</strong> ({studentEmail}) for <strong>{schoolName}</strong>.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="outline" disabled={isLoading}>
              Cancel
            </Button>
          </DialogClose>
          <Button onClick={handleResendInvitation} disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Resend Invitation
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
