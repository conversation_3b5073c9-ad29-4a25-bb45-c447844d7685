"use client";

import { useEffect, useMemo, useState } from "react";
import { Plus, Search } from "lucide-react";

import { ReminderTemplate } from "@/common/types";
import { useDebounce } from "@/hooks/useDebounce";
import { usePagination } from "@/hooks/usePagination";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Pagination from "@/components/pagination";
import TemplateConfigModal from "@/components/Reminders/Modals/TemplateConfigModal";
import TemplateConfigTable from "@/components/Reminders/TemplateConfigTable";

interface TemplateConfigProps {
  initialTemplates?: ReminderTemplate[];
}

export const TemplateConfig = ({ initialTemplates }: TemplateConfigProps) => {
  const [templates, setTemplates] = useState<ReminderTemplate[]>(
    initialTemplates || [],
  );
  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const [selectedTemplate, setSelectedTemplate] =
    useState<ReminderTemplate | null>(null);
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);
  const [isCreateMode, setIsCreateMode] = useState(false);

  // Update templates when initialTemplates changes (e.g., after a refresh)
  useEffect(() => {
    if (initialTemplates) {
      setTemplates(initialTemplates);
    }
  }, [initialTemplates]);

  // Filter data based on search term
  const filteredTemplates = useMemo(() => {
    return templates.filter((template) => {
      const templateName = template.name.toLowerCase();
      const templateTier = (template.tier || "").toLowerCase();

      return (
        templateName.includes(debouncedSearchTerm.toLowerCase()) ||
        templateTier.includes(debouncedSearchTerm.toLowerCase())
      );
    });
  }, [debouncedSearchTerm, templates]);

  // Pagination
  const {
    currentPage,
    itemsPerPage,
    paginatedData,
    totalItems,
    totalPages,
    handlePageChange,
    handleItemsPerPageChange,
    resetPagination,
  } = usePagination({ data: filteredTemplates, initialItemsPerPage: 10 });

  // Handle template selection for viewing/editing
  const handleTemplateSelect = (template: ReminderTemplate) => {
    setSelectedTemplate(template);
    setIsCreateMode(false);
    setIsTemplateModalOpen(true);
  };

  // Handle creating a new template
  const handleCreateTemplate = () => {
    setSelectedTemplate(null);
    setIsCreateMode(true);
    setIsTemplateModalOpen(true);
  };

  // Handle saving a template (create or update)
  const handleSaveTemplate = (template: ReminderTemplate) => {
    if (isCreateMode) {
      // Add new template
      setTemplates((prev) => [...prev, template]);
    } else {
      // Update existing template
      setTemplates((prev) =>
        prev.map((t) => (t.id === template.id ? template : t)),
      );
    }
    setIsTemplateModalOpen(false);
  };

  // Handle template deletion
  const handleTemplateDelete = (templateId: string) => {
    // Remove the template from local state
    setTemplates((prev) => prev.filter((t) => t.id !== templateId));
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="h-9 w-[250px]"
          />
          <Search className="h-4 w-4 text-muted-foreground" />
        </div>
        <Button onClick={handleCreateTemplate}>
          <Plus className="mr-2 h-4 w-4" />
          Create Template
        </Button>
      </div>

      <TemplateConfigTable
        templates={paginatedData}
        onTemplateSelect={handleTemplateSelect}
        onTemplateDelete={handleTemplateDelete}
      />

      {totalPages > 0 && (
        <Pagination
          currentPage={currentPage}
          itemsPerPage={itemsPerPage ?? 15}
          onPageChange={handlePageChange}
          onItemsPerPageChange={handleItemsPerPageChange}
          totalItems={totalItems}
        />
      )}

      <TemplateConfigModal
        isOpen={isTemplateModalOpen}
        onOpenChange={setIsTemplateModalOpen}
        template={selectedTemplate}
        isCreateMode={isCreateMode}
        onSave={handleSaveTemplate}
      />
    </div>
  );
};

export default TemplateConfig;
