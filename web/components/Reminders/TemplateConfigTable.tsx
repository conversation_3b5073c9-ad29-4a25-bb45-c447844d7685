"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { deleteReminderTemplate } from "@/actions/reminders/reminders";
import { format } from "date-fns";
import { ChevronDown, ChevronUp, ExternalLink, Eye } from "lucide-react";
import { toast } from "sonner";

import { ReminderTemplate } from "@/common/types";
import { cn, getTierColor } from "@/common/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import DestructiveModal from "@/components/destructive-modal";

interface TemplateConfigTableProps {
  templates: ReminderTemplate[];
  onTemplateSelect: (template: ReminderTemplate) => void;
  onTemplateDelete?: (templateId: string) => void;
}

const TemplateConfigTable = ({
  templates,
  onTemplateSelect,
  onTemplateDelete,
}: TemplateConfigTableProps) => {
  const [sortConfig, setSortConfig] = useState<{
    key: keyof ReminderTemplate | null;
    direction: "asc" | "desc";
  }>({
    key: "updated_at",
    direction: "desc",
  });

  const router = useRouter();

  const handleSort = (key: keyof ReminderTemplate) => {
    setSortConfig({
      key,
      direction:
        sortConfig.key === key && sortConfig.direction === "asc"
          ? "desc"
          : "asc",
    });
  };

  const sortedTemplates = [...templates].sort((a, b) => {
    if (!sortConfig.key) return 0;

    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];

    if (aValue === bValue) return 0;

    // Handle dates
    if (aValue instanceof Date && bValue instanceof Date) {
      return sortConfig.direction === "asc"
        ? aValue.getTime() - bValue.getTime()
        : bValue.getTime() - aValue.getTime();
    }

    // Handle strings
    if (typeof aValue === "string" && typeof bValue === "string") {
      return sortConfig.direction === "asc"
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    // Fallback for other types
    return sortConfig.direction === "asc"
      ? (aValue as any) > (bValue as any)
        ? 1
        : -1
      : (aValue as any) < (bValue as any)
        ? 1
        : -1;
  });

  const handleTemplateDelete = async (templateId: string) => {
    try {
      const result = await deleteReminderTemplate(templateId);
      if (!result.success) {
        toast.error((result as any)?.error || "Error deleting template");
        return;
      }
      toast.success("Template deleted successfully");

      // Call the parent component's onTemplateDelete callback
      if (onTemplateDelete) {
        onTemplateDelete(templateId);
      }

      router.refresh();
    } catch (error) {
      toast.error("Error deleting template");
      return;
    }
  };

  const renderSortIcon = (key: keyof ReminderTemplate) => {
    if (sortConfig.key !== key) {
      return <ChevronDown className="ml-1 h-4 w-4 text-muted-foreground" />;
    }

    return sortConfig.direction === "asc" ? (
      <ChevronUp className="ml-1 h-4 w-4" />
    ) : (
      <ChevronDown className="ml-1 h-4 w-4" />
    );
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead
              className="cursor-pointer"
              onClick={() => handleSort("name")}
            >
              <div className="flex items-center">
                Template Name {renderSortIcon("name")}
              </div>
            </TableHead>
            <TableHead
              className="cursor-pointer"
              onClick={() => handleSort("tier")}
            >
              <div className="flex items-center">
                Tier {renderSortIcon("tier")}
              </div>
            </TableHead>
            <TableHead
              className="cursor-pointer"
              onClick={() => handleSort("created_at")}
            >
              <div className="flex items-center">
                Created At {renderSortIcon("created_at")}
              </div>
            </TableHead>
            <TableHead
              className="cursor-pointer"
              onClick={() => handleSort("updated_at")}
            >
              <div className="flex items-center">
                Updated At {renderSortIcon("updated_at")}
              </div>
            </TableHead>
            <TableHead className="cursor-pointer">
              <div className="flex items-center">Actions</div>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {!sortedTemplates || sortedTemplates.length === 0 ? (
            <TableRow>
              <TableCell colSpan={4} className="h-24 text-center">
                No templates found.
              </TableCell>
            </TableRow>
          ) : (
            sortedTemplates.map((template) => (
              <TableRow
                key={template.id}
                className="cursor-pointer hover:bg-muted/50"
              >
                <TableCell onClick={() => onTemplateSelect(template)}>
                  <div className="flex items-center space-x-2">
                    <span>{template.name}</span>
                    <ExternalLink className="h-4 w-4 text-muted-foreground" />
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    variant="secondary"
                    className={cn(getTierColor(template.tier))}
                  >
                    {template.tier || "No Tier"}
                  </Badge>
                </TableCell>
                <TableCell>
                  {template?.updated_at
                    ? format(
                        new Date(template.created_at),
                        "MMM d, yyyy h:mm a",
                      )
                    : "N/A"}
                </TableCell>
                <TableCell>
                  {template?.updated_at
                    ? format(
                        new Date(template.updated_at),
                        "MMM d, yyyy h:mm a",
                      )
                    : "N/A"}
                </TableCell>
                <TableCell className="flex items-center gap-2">
                  <Button
                    variant="default"
                    size="sm"
                    className="bg-black hover:bg-gray-800"
                    onClick={(e) => {
                      e.stopPropagation();
                      onTemplateSelect(template);
                    }}
                  >
                    <Eye className="h-4 w-4" />
                    Preview
                  </Button>
                  <DestructiveModal
                    title="Delete Template"
                    description="Are you sure you want to delete this template, this action is irreversible?"
                    handler={() => handleTemplateDelete(template.id!)}
                    btnTitle=" "
                  />
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default TemplateConfigTable;
