"use client";

import { useMemo, useState } from "react";
import { Search } from "lucide-react";

import { <PERSON>mind<PERSON> } from "@/common/types";
import { useDebounce } from "@/hooks/useDebounce";
import { usePagination } from "@/hooks/usePagination";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Pagination from "@/components/pagination";
import ReminderPreviewModal from "@/components/Reminders/Modals/ReminderPreviewModal";
import RemindersTable from "@/components/Reminders/RemindersTable";

interface PastRemindersProps {
  initialReminders: Reminder[];
}

export const PastReminders = ({ initialReminders }: PastRemindersProps) => {
  const [reminders, setReminders] = useState<Reminder[]>(initialReminders);
  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const [selectedReminder, setSelectedReminder] = useState<Reminder | null>(
    null,
  );
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  // Filter data based on search term
  const filteredReminders = useMemo(() => {
    return reminders.filter((reminder) => {
      const studentName =
        `${reminder.student.first_name || ""} ${reminder.student.last_name || ""}`
          .trim()
          .toLowerCase();
      const templateName = reminder.template.name.toLowerCase();
      const searchLower = debouncedSearchTerm.toLowerCase();

      return (
        studentName.includes(searchLower) || templateName.includes(searchLower)
      );
    });
  }, [debouncedSearchTerm, reminders]);

  // Use pagination hook
  const {
    currentPage,
    itemsPerPage,
    paginatedData,
    totalItems,
    handlePageChange,
    handleItemsPerPageChange,
    resetPagination,
  } = usePagination({
    data: filteredReminders,
    initialItemsPerPage: 15,
  });

  // Reset pagination when search changes
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    resetPagination();
  };

  const handlePreviewReminder = (reminder: Reminder) => {
    setSelectedReminder(reminder);
    setIsPreviewOpen(true);
  };

  // Helper function to format date for display
  const formatDateForDisplay = (dateString: string): string => {
    if (!dateString) return "N/A";

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "Invalid Date";

      return date.toLocaleDateString("en-US", {
        day: "2-digit",
        month: "short",
        year: "numeric",
      });
    } catch (error) {
      console.error("Error formatting date for display:", error);
      return "Error";
    }
  };

  // Helper function to format time for display
  const formatTimeForDisplay = (dateString: string): string => {
    if (!dateString) return "N/A";

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "Invalid Time";

      return date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      });
    } catch (error) {
      console.error("Error formatting time for display:", error);
      return "Error";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">Past</h1>
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search"
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="w-64 pl-10"
            />
          </div>
          <Button variant="default" className="bg-black hover:bg-gray-800">
            Search
          </Button>
        </div>
      </div>

      <RemindersTable
        reminders={paginatedData}
        searchTerm={debouncedSearchTerm}
        showStatus={true}
        showSentTime={true}
        onPreview={handlePreviewReminder}
      />

      <Pagination
        currentPage={currentPage}
        totalItems={totalItems}
        itemsPerPage={itemsPerPage ?? 15}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
      />

      {/* Preview Modal  */}
      <ReminderPreviewModal
        isOpen={isPreviewOpen}
        onOpenChange={setIsPreviewOpen}
        reminder={selectedReminder}
        reviewOnly
      />
    </div>
  );
};
