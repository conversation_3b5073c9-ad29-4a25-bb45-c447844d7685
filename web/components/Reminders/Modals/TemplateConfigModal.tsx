"use client";

import { useEffect, useState } from "react";
import { upsertReminderTemplate } from "@/actions/reminders/reminders";
import { format } from "date-fns";
import { Edit } from "lucide-react";
import { toast } from "sonner";

import { ReminderTemplate } from "@/common/types";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface TemplateConfigModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  template: ReminderTemplate | null;
  isCreateMode: boolean;
  onSave: (template: ReminderTemplate) => void;
}

const TIER_OPTIONS = ["Active", "At Risk", "Dormant"];
// Predefined templates
const INITIAL_TEMPLATE_CONTENT = [
  {
    name: "Follow-up 1",
    content:
      "Hi {{student_name}}, looks like you were talking to me about a conversation. Don’t worry, it’s not too late to finish! Start the conversation here on SMS or visit https://student.getaddie.com/ to continue. Speak soon!!",
  },
  {
    name: "Follow-up 2",
    content:
      "Still with me, {{student_name}}? We didn’t quite finish your conversation. Want to pick it up where we left off? Just reply here or tap https://student.getaddie.com/.",
  },
  {
    name: "Follow-up 3",
    content:
      "Hey {{student_name}}, I noticed you haven’t finished your conversation yet. I’m still here and ready when you are! Tap student.getaddie.com to jump back in.",
  },
  {
    name: "Follow-up 4",
    content:
      "Hey {{student_name}}, it’s been a bit! You started your conversation, and I’d love to help you finish. If you're  ready now let me know.",
  },
];

export const TemplateConfigModal = ({
  isOpen,
  onOpenChange,
  template,
  isCreateMode,
  onSave,
}: TemplateConfigModalProps) => {
  const [name, setName] = useState("");
  const [tier, setTier] = useState<string | null>(null);
  const [content, setContent] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    if (template) {
      setIsEditing(false);
      setName(template.name);
      setTier(template.tier);
      setContent(template.content);
    } else {
      // Reset form for create mode
      setIsEditing(true);
      setName("");
      setTier("Active"); // Default tier
      setContent("Hi {{student_name}}, ");
    }
    setErrors({});
  }, [template, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = "Template name is required";
    }

    if (!content.trim()) {
      newErrors.content = "Message content is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      toast.error("Please fix the errors in the form");
      return;
    }

    // check if template is same as previous

    const updatedTemplate: ReminderTemplate = {
      ...(template?.id && { id: template.id }), // Only include id if it exists
      name,
      tier,
      content,
      created_at: template?.created_at || new Date(),
      updated_at: new Date(),
    };

    //check if template is same as previous
    if (
      template?.name === name &&
      template?.tier === tier &&
      template?.content === content
    ) {
      toast.info("No changes made.");
      setIsEditing(false);
      onOpenChange(false);
      return;
    }

    try {
      const result = await upsertReminderTemplate(updatedTemplate);
      if (result) {
        // Convert Prisma result to ReminderTemplate type
        const savedTemplate: ReminderTemplate = {
          id: result.id,
          name: result.name,
          tier: result.tier,
          content: result.content,
          created_at: result.created_at,
          updated_at: result.updated_at,
        };

        // Pass the converted template to onSave
        onSave(savedTemplate);
        toast.success(
          isCreateMode
            ? "Template created successfully"
            : "Template updated successfully",
        );
        onOpenChange(false);
      } else {
        throw new Error("Failed to save template");
      }
    } catch (e) {
      toast.error(e.message || "Error saving template");
      console.error(e);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {isCreateMode ? "Create Template" : "Template Configuration"}
          </DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(!isEditing)}
          >
            <Edit className="mr-1 h-4 w-4" />
            Edit config
          </Button>
          <div className="space-y-2">
            <Label htmlFor="name">Template Name *</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter template name"
              className={errors.name ? "border-red-500" : ""}
              disabled={!isEditing}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="tier">Tier *</Label>
            <Select
              value={tier || ""}
              onValueChange={(value) => setTier(value)}
              disabled={!isEditing}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a tier" />
              </SelectTrigger>
              <SelectContent>
                {TIER_OPTIONS.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="tier">Predefined Templates Suggestions</Label>
            <Select
              value={content || ""}
              onValueChange={(value) => setContent(value)}
              disabled={!isEditing}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a template..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value=" " disabled>
                  Select a template...
                </SelectItem>
                {INITIAL_TEMPLATE_CONTENT.map((template, index) => (
                  <SelectItem
                    key={`${template.name} - ${index}`}
                    value={template.content}
                  >
                    {template.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {content &&
              !INITIAL_TEMPLATE_CONTENT.some((t) => t.content === content) && (
                <p className="mt-1 text-sm italic text-muted-foreground">
                  Using custom content
                </p>
              )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="content">Message Content *</Label>
            </div>
            {isEditing ? (
              <Textarea
                id="content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder="Enter message content"
                className={`min-h-[150px] ${errors.content ? "border-red-500" : ""}`}
              />
            ) : (
              <div className="rounded-md bg-muted p-3">
                <p className="py-3 text-sm">{content}</p>
              </div>
            )}
            {errors.content && (
              <p className="text-sm text-red-500">{errors.content}</p>
            )}
            <p className="text-xs text-muted-foreground">
              {"Use {{ student_name }}, as placeholder for student name!!"}
            </p>
          </div>

          {!isCreateMode && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Created At</p>
                <p className="text-sm font-medium">
                  {template?.created_at
                    ? format(
                        new Date(template.created_at),
                        "MMM d, yyyy h:mm a",
                      )
                    : "N/A"}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Last Updated</p>
                <p className="text-sm font-medium">
                  {template?.updated_at
                    ? format(
                        new Date(template.updated_at),
                        "MMM d, yyyy h:mm a",
                      )
                    : "N/A"}
                </p>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            {isCreateMode ? "Create" : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TemplateConfigModal;
