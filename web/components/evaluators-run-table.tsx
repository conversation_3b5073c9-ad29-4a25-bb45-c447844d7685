"use client";

import * as React from "react";
import Link from "next/link";
import { redirect } from "next/navigation";
import { deleteEvaluator, deleteStudentAgentConfig } from "@/actions";
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import { ArrowUpDown, Plus, Trash2, View } from "lucide-react";

import log from "@/common/logger";
import { Student } from "@/common/model";
import { formatDate, timeAgo } from "@/common/utils";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import DeleteEvaluatorModal from "@/components/destructive-modal";

const columns: ColumnDef<Student>[] = [
  {
    accessorKey: "updated_at",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Last Updated
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => (
      <div className="capitalize">{timeAgo(row.getValue("updated_at"))}</div>
    ),
  },
  {
    accessorKey: "id",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Run ID
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => <div className="capitalize">{row.getValue("id")}</div>,
  },
  {
    accessorKey: "score",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Score
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => (
      <div className="">
        {Math.floor(parseFloat(row.getValue("score")) * 100) / 100}
      </div>
    ),
  },
  // {
  //   id: "actions",
  //   enableHiding: false,
  //   cell: ({ row }) => {
  //     const student = row.original;
  //
  //     return (
  //       <DropdownMenu>
  //         <DropdownMenuTrigger asChild>
  //           <Button variant="ghost" className="h-8 w-8 p-0">
  //             <span className="sr-only">Open menu</span>
  //             <MoreHorizontal className="h-4 w-4" />
  //           </Button>
  //         </DropdownMenuTrigger>
  //         <DropdownMenuContent align="end">
  //           <DropdownMenuLabel>Actions</DropdownMenuLabel>
  //           {/*<DropdownMenuItem*/}
  //           {/*  onClick={() =>*/}
  //           {/*    navigator.clipboard.writeText(student.id.toString())*/}
  //           {/*  }*/}
  //           {/*>*/}
  //           {/*  Copy student ID*/}
  //           {/*</DropdownMenuItem>*/}
  //           <DropdownMenuSeparator />
  //           <Link href={`/experiment/${student.student_agent_id}`}>
  //             <DropdownMenuItem>View Experiment details</DropdownMenuItem>
  //           </Link>
  //           <Link href={`/student_agent_config/edit/${student.config.id}`}>
  //             <DropdownMenuItem>Edit student</DropdownMenuItem>
  //           </Link>
  //         </DropdownMenuContent>
  //       </DropdownMenu>
  //     );
  //   },
  // },
];

export default function EvaluatorRunsTable(params: { data: any[] }) {
  const { data } = params;

  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  function redirectToCreatePage() {
    redirect("/chat_evaluator_config/create");
  }

  return (
    <div className="w-full">
      <div className="flex items-center py-4">
        {/*<Input*/}
        {/*  placeholder="Filter emails..."*/}
        {/*  value={(table.getColumn("email")?.getFilterValue() as string) ?? ""}*/}
        {/*  onChange={(event) =>*/}
        {/*    table.getColumn("email")?.setFilterValue(event.target.value)*/}
        {/*  }*/}
        {/*  className="max-w-sm"*/}
        {/*/>*/}
        {/*<DropdownMenu>*/}
        {/*  <DropdownMenuTrigger asChild>*/}
        {/*    <Button variant="outline" className="ml-auto">*/}
        {/*      Columns <ChevronDown className="ml-2 h-4 w-4" />*/}
        {/*    </Button>*/}
        {/*  </DropdownMenuTrigger>*/}
        {/*  <DropdownMenuContent align="end">*/}
        {/*    {table*/}
        {/*      .getAllColumns()*/}
        {/*      .filter((column) => column.getCanHide())*/}
        {/*      .map((column) => {*/}
        {/*        return (*/}
        {/*          <DropdownMenuCheckboxItem*/}
        {/*            key={column.id}*/}
        {/*            className="capitalize"*/}
        {/*            checked={column.getIsVisible()}*/}
        {/*            onCheckedChange={(value) =>*/}
        {/*              column.toggleVisibility(!!value)*/}
        {/*            }*/}
        {/*          >*/}
        {/*            {column.id}*/}
        {/*          </DropdownMenuCheckboxItem>*/}
        {/*        );*/}
        {/*      })}*/}
        {/*  </DropdownMenuContent>*/}
        {/*</DropdownMenu>*/}

        {/* comment here*/}
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                  <TableCell className="space-x-4 text-right">
                    <Link href={`/evaluator_run/${row.original.id}`}>
                      <Button variant="ghost" size="icon" aria-label="Details">
                        <View className="h-4 w-4" />
                      </Button>
                    </Link>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
