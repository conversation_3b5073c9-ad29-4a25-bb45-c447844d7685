import React from "react";
import Image from "next/image";
import { GraduationCap, NotepadText, Plus } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

interface EmptyMajorsStateProps {
  firstName?: string;
}

export const EmptyMajorsState: React.FC<EmptyMajorsStateProps> = ({
  firstName,
}) => {
  return (
    <div className="flex w-full flex-col items-center justify-center gap-4 rounded-lg border border-gray-300 bg-white p-12 shadow-sm">
      <Image
        className="mb-4"
        alt="no-image"
        src="/images/little-woman-with-long-list-2.png"
        width={147}
        height={158}
      />
      <h4 className="text-lg font-semibold text-gray-900">
        Nothing to show here yet
      </h4>
      <p className="max-w-md text-center text-sm text-gray-500">
        <PERSON><PERSON> will draw insights from the conversations {firstName} has over the
        summer and recommend major programs best suited for them.
      </p>
    </div>
  );
};

export default EmptyMajorsState;
