import React from "react";
import { Bo<PERSON> } from "lucide-react";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { useUI } from "@/app/context/UIContext";

interface PersonalityTrait {
  name: string;
  value: number;
  color: string;
}

interface PersonalityProfileProps {
  traits?: PersonalityTrait[];
}

export const PersonalityProfile: React.FC<PersonalityProfileProps> = ({
  traits = [
    { name: "Openness", value: 85, color: "bg-green-500" },
    { name: "Conscientiousness", value: 85, color: "bg-green-500" },
    { name: "Extraversion", value: 85, color: "bg-green-500" },
    { name: "Agreeableness", value: 75, color: "bg-blue-500" },
    { name: "Neuroticism", value: 30, color: "bg-orange-500" },
  ],
}) => {
  // Function to determine rating based on value
  const getRating = (value: number) => {
    if (value >= 80) return "High";
    if (value >= 60) return "Moderate";
    return "Low";
  };

  const { isChatOpen } = useUI();

  return (
    <Card className="sticky top-6">
      <CardHeader className="border-b">
        <CardTitle className="text-md">Personality Profile</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        {/* Personality traits display */}
        <div>
          {traits.map((trait, index) => (
            <div
              key={trait.name}
              className={`px-4 py-2 ${index !== traits.length - 1 ? "border-b" : ""}`}
            >
              {/* When chat is open: vertical layout */}
              {isChatOpen ? (
                <div className="flex flex-col gap-1">
                  <span className="text-sm text-muted-foreground">
                    {trait.name}
                  </span>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-semibold">
                      {trait.value}%
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {getRating(trait.value)}
                    </span>
                  </div>
                </div>
              ) : (
                /* When chat is closed: horizontal layout */
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    {trait.name}
                  </span>
                  <div className="flex flex-col items-end">
                    <span className="text-sm font-semibold">
                      {trait.value}%
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {getRating(trait.value)}
                    </span>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="border-t p-6 text-center">
          <p className="text-center text-xs text-muted-foreground">
            <Bot className="mr-1 inline h-3 w-3" />
            Powered by <span className="font-medium text-blue-600">Addie</span>
          </p>
          <p className="mt-1 text-center text-xs text-muted-foreground">
            This analysis based on Big 5 OCEAN Framework, Thrive Index, your
            conversations with Addie, and a sprinkle of Addie AI magic.{" "}
            <button className="text-blue-600 hover:underline">
              Learn more
            </button>
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default PersonalityProfile;
