"use client";

import React, { useState } from "react";
import { redirect } from "next/navigation";
import { updateEvaluator, updatePromptSuggestion } from "@/actions";
import { saveConfig } from "@/actions/student-agent";
import { MsgType } from "@prisma/client";

import log from "@/common/logger";
import { StudentAgentConfig } from "@/common/model";
import { ToastQueue } from "@/common/toastQueue";
import { formatDate } from "@/common/utils";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { LoadingButton } from "@/components/ui/loading-button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface Props {
  data: any;
  userId: string;
}

const toastQueue = new ToastQueue();

export default function PromptSuggestionEdit({ data, userId }: Props) {

  const [config, setConfig] = useState(data);
  const [requestInProgress, setRequestInProgress] = useState(false);
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConfig((prev) => ({
      ...prev,
      name: e.target.value,
    }));
  };

  const handlePromptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setConfig((prev) => {
      const prompt = prev.prompt;
      prompt[0].content = e.target.value;

      return {
        ...prev,
        prompt,
      };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setRequestInProgress(true);

    const res = await updatePromptSuggestion({
      suggestionId: config.id,
      name: config.name,
      promptStr: config.prompt[0].content,
    });

    toastQueue.addToast({
      title: "Prompt Suggestion Updated",
      // description: "Your Evaluator configuration has been updated.",
    });

    toastQueue.renderToasts();
    setRequestInProgress(false);

    redirect(`/prompt_suggestions/${config.id}`);
  };

  return (
    <div className="w-full p-4">
      <Card className="w-full max-w-4xl border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">
            Edit Evaluator Configuration
          </CardTitle>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <Label className="text-sm font-medium">ID</Label>
                <p className="mt-1 text-sm">{config.id}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Created At</Label>
                <p className="mt-1 text-sm">
                  {formatDate(config.created_at.toString())}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">Updated At</Label>
                <p className="mt-1 text-sm">
                  {formatDate(config.updated_at.toString())}
                </p>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                disabled={requestInProgress}
                id="name"
                value={config.name}
                onChange={handleNameChange}
                placeholder="Prompt Suggestion Name"
                required
              />
            </div>
            <div>
              <Label htmlFor="promptContent" className="text-sm font-medium">
                Prompt Content
              </Label>
              <Textarea
                id="promptContent"
                value={config.prompt[0].content}
                onChange={handlePromptChange}
                className="mt-1 h-[200px]"
              />
            </div>
          </CardContent>
          <CardFooter>
            {!requestInProgress && (
              <Button type="submit" className="w-full">
                Update
              </Button>
            )}
            {requestInProgress && (
              <LoadingButton className="w-full" loading>
                Updating...
              </LoadingButton>
            )}
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
