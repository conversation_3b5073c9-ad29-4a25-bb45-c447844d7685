"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { format } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Pagination from "@/components/pagination";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { ChevronDown, ChevronUp, Download } from "lucide-react";
import { EngagementEventsResponse } from "@/actions/engagement/engagement-events";
import { cn } from "@/common/utils";

interface EngagementEventsTableProps {
  initialData: EngagementEventsResponse;
  currentPage: number;
}

export function EngagementEventsTable({
  initialData,
  currentPage,
}: EngagementEventsTableProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const totalPages = Math.ceil(initialData.total / initialData.pageSize);

  const toggleRow = (eventId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(eventId)) {
      newExpanded.delete(eventId);
    } else {
      newExpanded.add(eventId);
    }
    setExpandedRows(newExpanded);
  };

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", page.toString());
    router.push(`/engagement-events?${params.toString()}`);
  };

  const getChannelBadgeVariant = (channel: string) => {
    switch (channel) {
      case "sms":
        return "default";
      case "email":
        return "secondary";
      case "web_student":
        return "outline";
      case "voice":
        return "destructive";
      default:
        return "default";
    }
  };

  const getEventTypeBadgeVariant = (eventType: string) => {
    if (eventType.includes("sent") || eventType.includes("opened")) {
      return "default";
    }
    if (eventType.includes("received") || eventType.includes("clicked")) {
      return "secondary";
    }
    return "outline";
  };

  const formatChannel = (channel: string) => {
    return channel.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
  };

  const formatEventType = (eventType: string) => {
    return eventType.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
  };

  if (initialData.events.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <p className="text-lg font-medium">No engagement events found</p>
        <p className="mt-1 text-sm text-muted-foreground">
          Try adjusting your filters or check back later
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]"></TableHead>
              <TableHead>Event Type</TableHead>
              <TableHead>Channel</TableHead>
              <TableHead>Student</TableHead>
              <TableHead>School</TableHead>
              <TableHead>Session ID</TableHead>
              <TableHead>Timestamp</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {initialData.events.map((event) => (
              <Collapsible key={event.id} asChild>
                <>
                  <TableRow>
                    <TableCell>
                      <CollapsibleTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleRow(event.id)}
                        >
                          {expandedRows.has(event.id) ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                        </Button>
                      </CollapsibleTrigger>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getEventTypeBadgeVariant(event.event_type)}>
                        {formatEventType(event.event_type)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getChannelBadgeVariant(event.channel)}>
                        {formatChannel(event.channel)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{event.student?.name || "Unknown"}</p>
                        {event.student?.email && (
                          <p className="text-xs text-muted-foreground">{event.student.email}</p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{event.student?.school?.name || "-"}</TableCell>
                    <TableCell>
                      <code className="rounded bg-muted px-1 py-0.5 text-xs">
                        {event.session_id || "-"}
                      </code>
                    </TableCell>
                    <TableCell>
                      {format(new Date(event.created_at), "MMM d, yyyy HH:mm")}
                    </TableCell>
                  </TableRow>
                  <CollapsibleContent asChild>
                    <TableRow>
                      <TableCell colSpan={7} className="bg-muted/50">
                        <div className="space-y-2 p-4">
                          {event.workflow_id && (
                            <div>
                              <span className="text-sm font-medium">Workflow ID:</span>{" "}
                              <code className="rounded bg-background px-1 py-0.5 text-xs">
                                {event.workflow_id}
                              </code>
                            </div>
                          )}
                          {event.reminder_id && (
                            <div>
                              <span className="text-sm font-medium">Reminder ID:</span>{" "}
                              <code className="rounded bg-background px-1 py-0.5 text-xs">
                                {event.reminder_id}
                              </code>
                            </div>
                          )}
                          {event.metadata && Object.keys(event.metadata).length > 0 && (
                            <div>
                              <span className="text-sm font-medium">Metadata:</span>
                              <pre className="mt-1 overflow-auto rounded bg-background p-2 text-xs">
                                {JSON.stringify(event.metadata, null, 2)}
                              </pre>
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  </CollapsibleContent>
                </>
              </Collapsible>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalItems={initialData.total}
          itemsPerPage={initialData.pageSize}
          onPageChange={handlePageChange}
          onItemsPerPageChange={() => {}} // We can add page size change functionality later
          pageSizeOptions={[50]} // Fixed page size for now
        />
      )}
    </div>
  );
}