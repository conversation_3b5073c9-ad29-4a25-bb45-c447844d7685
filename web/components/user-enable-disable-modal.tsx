"use client";

import * as React from "react";
import { Check, Minus, AlertTriangle } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { inviteStudent } from "@/actions/schools";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

interface UserEnableDisableModalProps {
  children: React.ReactNode;
  action: "enable" | "disable";
  userType: "counselor" | "student";
  userCount: number;
  userNames?: string[];
  onConfirm: () => Promise<void>;
  isLoading?: boolean;
  sendInvitation?: boolean;
  schoolContext?: {
    schoolId: string;
    schoolName: string;
  };
  userEmails?: string[];
}

export default function UserEnableDisableModal({
  children,
  action,
  userType,
  userCount,
  userNames,
  onConfirm,
  isLoading = false,
  sendInvitation = false,
  schoolContext,
  userEmails,
}: UserEnableDisableModalProps) {
  const [open, setOpen] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [shouldSendInvitation, setShouldSendInvitation] = React.useState(true);
  const router = useRouter();

  const handleConfirm = async () => {
    try {
      setLoading(true);

      await onConfirm();

      if (action === "enable" && userType === "student" && shouldSendInvitation && schoolContext && userEmails) {
        let invitationErrors: string[] = [];
        let successCount = 0;

        for (let i = 0; i < userEmails.length; i++) {
          const email = userEmails[i];
          const name = userNames?.[i] || email;

          try {
            const formData = new FormData();
            formData.append("email", email);
            formData.append("schoolId", schoolContext.schoolId);
            formData.append("resend", "true");
            formData.append("forceResend", "true");

            const result = await inviteStudent(formData);

            if (result.error) {
              invitationErrors.push(`${name}: ${result.error}`);
            } else {
              successCount++;
            }
          } catch (error) {
            invitationErrors.push(`${name}: Failed to send invitation`);
          }
        }

        if (successCount > 0) {
          toast.success("Students Enabled", {
            description: `${successCount} student${successCount > 1 ? 's' : ''} enabled and invitation${successCount > 1 ? 's' : ''} sent successfully.`,
          });
        }

        if (invitationErrors.length > 0) {
          toast.warning("Some Invitations Failed", {
            description: `${invitationErrors.length} invitation${invitationErrors.length > 1 ? 's' : ''} failed to send. Students were still enabled.`,
          });
        }
      }

      setOpen(false);
      router.refresh();
    } catch (error) {
      console.error("Error performing action:", error);
      toast.error("Error", {
        description: "An error occurred while performing the action.",
      });
    } finally {
      setLoading(false);
    }
  };

  const isSingle = userCount === 1;
  const isEnable = action === "enable";
  const showInvitationOption = isEnable && userType === "student" && schoolContext && userEmails;

  const actionText = isEnable ? "Enable" : "Disable";
  const actionPastText = isEnable ? "enabled" : "disabled";

  const title = isSingle
    ? `${actionText} ${userType}`
    : `${actionText} ${userCount} ${userType}s`;

  const description = isSingle
    ? `Are you sure you want to ${action} this ${userType}? ${isEnable ? "They will be able to access the platform." : "They will no longer be able to access the platform."}`
    : `Are you sure you want to ${action} ${userCount} ${userType}s? ${isEnable ? "They will be able to access the platform." : "They will no longer be able to access the platform."}`;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isEnable ? (
              <Check className="h-5 w-5 text-green-600" />
            ) : (
              <AlertTriangle className="h-5 w-5 text-red-600" />
            )}
            {title}
          </DialogTitle>
          <DialogDescription className="text-left">
            {description}
          </DialogDescription>

          {!isSingle && userNames && userNames.length > 0 && (
            <div className="mt-4">
              <p className="mb-2 text-sm font-medium text-muted-foreground">
                {userType === "counselor" ? "Counselors" : "Students"} to be {actionPastText}:
              </p>
              <div className="max-h-32 overflow-y-auto rounded-md border bg-muted/50 p-2">
                <ul className="space-y-1 text-sm">
                  {userNames.slice(0, 10).map((name, index) => (
                    <li key={index} className="text-muted-foreground">
                      • {name}
                    </li>
                  ))}
                  {userNames.length > 10 && (
                    <li className="font-medium text-muted-foreground">
                      • ... and {userNames.length - 10} more
                    </li>
                  )}
                </ul>
              </div>
            </div>
          )}

          {showInvitationOption && (
            <div className="mt-4 rounded-md border bg-blue-50/50 p-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="send-invitation"
                  checked={shouldSendInvitation}
                  onCheckedChange={(checked) => setShouldSendInvitation(!!checked)}
                />
                <label
                  htmlFor="send-invitation"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Send invitation email to enabled students
                </label>
              </div>
              <p className="mt-1 text-xs text-muted-foreground">
                Newly enabled students will receive access instructions via email.
              </p>
            </div>
          )}
        </DialogHeader>

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={loading || isLoading}
            className={
              isEnable
                ? "bg-green-600 text-white hover:bg-green-700"
                : "bg-red-600 text-white hover:bg-red-700"
            }
          >
            {(loading || isLoading) ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                Processing...
              </>
            ) : (
              <>
                {isEnable ? (
                  <Check className="mr-2 h-4 w-4" />
                ) : (
                  <Minus className="mr-2 h-4 w-4" />
                )}
                {actionText}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}