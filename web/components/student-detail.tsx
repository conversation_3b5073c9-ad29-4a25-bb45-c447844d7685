"use client";

import { useRef, useState } from "react";
import { User } from "lucide-react";

import { formatDate } from "@/common/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ChatUI from "@/components/chat-ui";
import ClearStudentConversation from "@/components/clear-student-conversation";
import { StudentConversationTab } from "@/components/Conversations/Student-ConversationTab/StudentConversationTab";
import { StudentMajorsTab } from "@/components/Majors/StudentMajorsTab";

interface StudentDetailArgs {
  value: any;
  messages?: any;
  qnaireResponses?: any;
}

export default function StudentDetail(params: StudentDetailArgs) {
  const { value, messages, qnaireResponses } = params;

  const [showFullPrompt, setShowFullPrompt] = useState(false);
  const fullName = `${value.first_name} ${value.last_name}`;

  const formatDateOfBirth = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const activeTabRef = useRef<string>("conversations");

  const handleTabChange = (value: string) => {
    activeTabRef.current = value;
  };

  return (
    <Card className="h-screen w-full overflow-y-scroll border-none p-4">
      <CardHeader className="flex flex-row items-center space-x-4 pb-2">
        <Avatar className="h-20 w-20">
          <AvatarImage
            src={value.image || ""}
            alt={`${value.first_name} ${value.last_name}`}
          />
          <AvatarFallback>
            <User className="h-10 w-10" />
          </AvatarFallback>
        </Avatar>
        <div>
          <CardTitle className="text-2xl font-bold">
            {value.first_name} {value.middle_name} {value.last_name}
          </CardTitle>
          <CardDescription>
            Student Agent ID: {value.student_agent_id}
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="font-semibold">Student ID:</span>
            <span className="ml-2">{value.student_id}</span>
          </div>
          <div>
            <span className="font-semibold">User ID:</span>
            <span className="ml-2 font-mono text-sm">{value.user_id}</span>
          </div>
          {/*<div>*/}
          {/*  <span className="font-semibold">Date of Birth:</span>*/}
          {/*  <span className="ml-2">*/}
          {/*    {formatDateOfBirth(value.date_of_birth)}*/}
          {/*  </span>*/}
          {/*</div>*/}
          <div>
            <span className="font-semibold">Email:</span>
            <span className="ml-2">{value.email}</span>
          </div>
          <div>
            <span className="font-semibold">Phone Number:</span>
            <span className="ml-2">{value.phone_number || "N/A"}</span>
          </div>
          <div>
            <span className="font-semibold">Email Verified:</span>
            <span className="ml-2">{value.emailVerified ? "Yes" : "No"}</span>
          </div>
          <div>
            <span className="font-semibold">Profile Complete:</span>
            <span className="ml-2">
              {value.isProfileComplete ? "Yes" : "No"}
            </span>
          </div>
          <div>
            <span className="font-semibold">Messages Count:</span>
            <span className="ml-2">{value.msgsCount}</span>
          </div>
          <div>
            <span className="font-semibold">Completed Workflow Steps:</span>
            <span className="ml-2">{value.completedWFSteps}</span>
          </div>
        </div>
        {/* Engagement Tier Section */}
        {value.engagement_tier && (
          <div className="rounded-lg border bg-muted/50 p-4">
            <h3 className="mb-3 text-lg font-semibold">Engagement Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="font-semibold">Engagement Tier:</span>
                <div className="mt-1">
                  <div
                    className={`inline-flex items-center rounded-full border px-3 py-1 text-sm font-medium ${
                      value.engagement_tier === "Active"
                        ? "border-green-200 bg-green-100 text-green-800"
                        : value.engagement_tier === "At_Risk" || value.engagement_tier === "At-Risk"
                        ? "border-yellow-200 bg-yellow-100 text-yellow-800"
                        : value.engagement_tier === "Dormant"
                        ? "border-red-200 bg-red-100 text-red-800"
                        : "border-gray-200 bg-gray-100 text-gray-800"
                    }`}
                  >
                    {value.engagement_tier === "At_Risk" ? "At-Risk" : value.engagement_tier}
                  </div>
                </div>
              </div>
              <div>
                <span className="font-semibold">Tier Last Updated:</span>
                <span className="ml-2 block text-sm">
                  {value.tier_updated_at
                    ? formatDate(value.tier_updated_at.toString())
                    : "Never"}
                </span>
              </div>
              <div>
                <span className="font-semibold">Last Activity:</span>
                <span className="ml-2 block text-sm">
                  {value.last_activity_at
                    ? formatDate(value.last_activity_at.toString())
                    : "No activity recorded"}
                </span>
              </div>
              <div>
                <span className="font-semibold">Last Activity Channel:</span>
                <span className="ml-2 block text-sm capitalize">
                  {value.last_activity_channel || "N/A"}
                </span>
              </div>
            </div>
          </div>
        )}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="font-semibold">Created At:</span>
            <span className="block text-sm">
              {formatDate(value.created_at.toLocaleString())}
            </span>
          </div>
          <div>
            <span className="font-semibold">Updated At:</span>
            <span className="block text-sm">
              {formatDate(value.updated_at.toLocaleString())}
            </span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-start space-x-2">
        {/*<Button variant="outline">*/}
        {/*  <Clipboard className="mr-2 h-4 w-4" />*/}
        {/*  Copy Details*/}
        {/*</Button>*/}
        {/*<Button>*/}
        {/*  <MessageCircle className="mr-2 h-4 w-4" />*/}
        {/*  View Messages*/}
        {/*</Button>*/}
        <ClearStudentConversation studentId={value.student_id} />
      </CardFooter>
      <CardContent className="space-y-4">
        <div className="flex justify-start space-x-2 pt-6">
          <div className="pr-6 pt-2 font-semibold">Token Usage</div>
          {/*<Tabs.Root defaultValue="all-time" className="flex">*/}
          {/*<Tabs.List className="flex overflow-hidden rounded-full border">*/}
          <div defaultValue="all-time" className="flex">
            <div className="flex overflow-hidden rounded-full border">
              <div className="bg-white px-4 py-2 text-sm text-gray-900 transition-colors">
                <div className="flex items-center justify-between gap-4">
                  <span>All Time</span>
                  <span className="font-semibold">{value.token_usage}</span>
                </div>
              </div>
              <div className="border-l bg-white px-4 py-2 text-sm text-gray-900 transition-colors">
                <div className="flex items-center justify-between gap-4">
                  <span>Last 7 Days</span>
                  <span className="font-semibold">
                    {value.token_usage_7_days}
                  </span>
                </div>
              </div>
              <div className="border-l bg-white px-4 py-2 text-sm text-gray-900 transition-colors">
                <div className="flex items-center justify-between gap-4">
                  <span>Last 30 Days</span>
                  <span className="font-semibold">
                    {value.token_usage_30_days}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      {/*<QAList data={qnaireResponses} />*/}
      <Tabs defaultValue="conversations" onValueChange={handleTabChange}>
        <TabsList>
          <TabsTrigger value="conversations">Conversations</TabsTrigger>
          <TabsTrigger value="majors">Majors</TabsTrigger>
        </TabsList>
        <TabsContent value="conversations">
          <StudentConversationTab studentId={value.student_id} />
        </TabsContent>
        <TabsContent value="majors">
          <StudentMajorsTab student={value} />
        </TabsContent>
      </Tabs>
      <h1 className="text-2xl font-bold">Messages</h1>
      <ChatUI
        messages={messages}
        avatarUrl={value.image}
        studentName={fullName}
      />
    </Card>
  );
}
