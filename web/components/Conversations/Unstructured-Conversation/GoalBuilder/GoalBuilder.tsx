"use client";

import type React from "react";
import { Goal } from "@/actions/conversations/conversation-workflow";
import { UserRole } from "@prisma/client";

import MultiGoalBuilder from "@/components/Conversations/Unstructured-Conversation/GoalBuilder/MultiGoalBuilder";

interface GoalBuilderProps {
  conversation: any;
  onGoalsChange: (goals: Goal[], allValid: boolean) => void;
  initialGoals?: Goal[];
  saveButtonComponent?: React.ReactNode;
  earlyEndMessage: string;
  onEarlyEndMessageChange: (msg: string) => void;
  onDisableSystemPromptChange?: (disabled: boolean) => void;
  user?: { role: UserRole };
}

export default function GoalBuilder({
  conversation,
  onGoalsChange,
  initialGoals = [],
  saveButtonComponent,
  earlyEndMessage,
  onEarlyEndMessageChange,
  onDisableSystemPromptChange,
  user,
}: GoalBuilderProps) {
  const handleChange = (forms: Goal[], allValid: boolean) => {
    onGoalsChange(forms, allValid);
  };

  return (
    <div className="flex h-full flex-col">
      <div className="flex-1 overflow-auto p-6">
        <MultiGoalBuilder
          conversationType={conversation.workflow_type}
          onChange={handleChange}
          initialGoals={initialGoals}
          saveButtonComponent={saveButtonComponent}
          earlyEndMessage={earlyEndMessage}
          onEarlyEndMessageChange={onEarlyEndMessageChange}
          conversation={conversation}
          onDisableSystemPromptChange={onDisableSystemPromptChange}
          user={user}
        />
      </div>
    </div>
  );
}
