"use client";

import React, { useEffect, useRef, useState } from "react";
import {
  getUnstructuredConvoMessagesBySessionId,
  getUnstructuredConvoSummary,
  regenerateUnstructuredConvoSummary,
} from "@/actions/conversations/unstructured-conversations";
import { getUserByStudentId } from "@/actions/students";
import { ExternalLink, RefreshCw, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Markdown } from "@/components/ui/content/markdown";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import GoalModal from "@/components/Conversations/Student-ConversationTab/GoalModal";
import { StudentConvoHeader } from "@/components/Conversations/Student-ConversationTab/StudentConvoHeader";
import { UnstructuredConvoModal } from "@/components/Conversations/Student-ConversationTab/UnstructuredConvoModal";
import { useConversation } from "@/app/context/ConversationContext";

export interface Message {
  id?: number; // Add message ID for system prompt lookup
  content: string;
  type: string;
  source?: string; // Channel source: "web", "sms", "voice"
}

export interface Steps {
  id: string;
  step_id: string;
  student_id: string;
  student_workflow_id: string;
  completed: boolean;
  data: {
    id: string;
    acceptanceCriteria: string;
    discussionTime: string;
    exampleAnswer: string;
    goalText: string;
  };
}

interface StudentUnstructuredConvoProps {
  conversation?: any;
  answerMap?: Record<string, string>;
  loading?: boolean;
  completionPercent?: number;
}

export const StudentUnstructuredConvo = ({
  conversation,
  loading = false,
  completionPercent = 0,
}: StudentUnstructuredConvoProps) => {
  const { steps = [], student_id, workflow_id } = conversation ?? {};
  const { setConversationData } = useConversation();
  const [summary, setSummary] = useState("");
  const [summaryUpdatedAt, setSummaryUpdatedAt] = useState<string | null>(null);
  const [insights, setInsights] = useState<{ goal: string; insight: string }[]>(
    [],
  );
  const [isFetching, setIsFetching] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [refreshError, setRefreshError] = useState<string | null>(null);

  const [isFullConversation, setIsFullConversation] = useState(false);
  
  // Helper function to format timestamp as "time ago"
  const formatTimeAgo = (timestamp: string): string => {
    const now = new Date();
    const updatedTime = new Date(timestamp);
    const diffInMs = now.getTime() - updatedTime.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInMinutes < 1) {
      return "Just now";
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes === 1 ? "" : "s"} ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours === 1 ? "" : "s"} ago`;
    } else {
      return `${diffInDays} day${diffInDays === 1 ? "" : "s"} ago`;
    }
  };
  const [conversationMessages, setConversationMessages] = useState<Message[]>(
    [],
  );
  const [studentName, setStudentName] = useState("");
  const [showGoalModal, setShowGoalModal] = useState(false);

  // Create a ref to store the previous data to avoid unnecessary updates
  const prevDataRef = useRef<string>("");

  // Store the full conversation data in the UIContext for easy access
  useEffect(() => {
    // Only update the context when we have all the necessary data
    if (conversationMessages.length > 0 && summary && workflow_id) {
      const fullConversationData = {
        messages: conversationMessages,
        summary: summary,
        insights: insights,
      };

      // Convert to string for comparison
      const dataString = JSON.stringify(fullConversationData);

      // Only update if the data has actually changed
      if (dataString !== prevDataRef.current) {
        prevDataRef.current = dataString;
        setConversationData(workflow_id, fullConversationData);
      }
    }
  }, [
    conversationMessages,
    summary,
    insights,
    workflow_id,
    setConversationData,
  ]);

  // fetch student messages
  useEffect(() => {
    const fetchMessages = async () => {
      try {
        const user = await getUserByStudentId(student_id);
        // console.log("user ===>", user);
        if (!user) {
          console.error("User not found");
          return; // Exit if user is not found
        }
        setStudentName(`${user.first_name} ${user.last_name}`);
        const sessionId = `${workflow_id}-${user.id}`;
        const messages =
          await getUnstructuredConvoMessagesBySessionId(sessionId);
        // console.log("messages ===>", messages);
        setConversationMessages(messages);
      } catch (error) {
        console.error("Error fetching messages:", error);
      }
    };

    fetchMessages();
  }, [student_id, workflow_id]);

  useEffect(() => {
    const fetchSummary = async () => {
      if (!student_id || !workflow_id) return;
      setIsFetching(true);

      try {
        const data = await getUnstructuredConvoSummary(student_id, workflow_id);
        if (data) {
          setSummary(data.content || "");
          setSummaryUpdatedAt(data.updated_at);
          setInsights(data.insights || []);
        } else {
          setSummary("");
          setSummaryUpdatedAt(null);
          setInsights([]);
        }
      } catch (e) {
        console.error("Failed to fetch summary", e);
      } finally {
        setIsFetching(false);
      }
    };

    fetchSummary();
  }, [student_id, workflow_id]);

  const handleRefreshSummary = async () => {
    if (!student_id || !workflow_id || isRefreshing) return;
    
    setIsRefreshing(true);
    setRefreshError(null);
    
    try {
      const data = await regenerateUnstructuredConvoSummary(student_id, workflow_id);
      setSummary(data.content || "");
      setSummaryUpdatedAt(data.updated_at);
      setInsights(data.insights || []);
      
    } catch (error) {
      console.error("Failed to regenerate summary:", error);
      setRefreshError(error instanceof Error ? error.message : "Failed to regenerate summary");
    } finally {
      setIsRefreshing(false);
    }
  };

  if (loading || isFetching) {
    return (
      <div className="flex h-full items-center justify-center p-8 text-muted-foreground">
        <LoadingSpinner />
        <span>Loading conversation...</span>
      </div>
    );
  }

  return (
    <ScrollArea className="h-full w-full bg-muted">
      <div
        id={`unstructured-convo-${workflow_id}`}
        className="w-full space-y-6 p-6"
      >
        <StudentConvoHeader
          conversation={conversation}
          completionPercent={completionPercent}
          steps={steps}
          setShowGoalModal={setShowGoalModal}
        />
        {/* Goals Section  --  Hide for now*/}
        {/*<section>*/}
        {/*  <div className="mb-2 space-y-1">*/}
        {/*    <div className="flex items-center justify-between">*/}
        {/*      <div className="flex items-center gap-x-2">*/}
        {/*        <h2 className="text-lg font-semibold">Goal</h2>*/}
        {/*        <span className="text-sm text-muted-foreground">*/}
        {/*          {steps?.length} {steps?.length === 1 ? "goal" : "goals"}*/}
        {/*        </span>*/}
        {/*      </div>*/}
        {/*      <Button*/}
        {/*        variant="link"*/}
        {/*        className="flex items-center justify-center text-blue-600"*/}
        {/*        onClick={() => setShowGoalModal(true)}*/}
        {/*      >*/}
        {/*        <span>View Goal</span>*/}
        {/*        <ExternalLink className="h-3 w-3" />*/}
        {/*      </Button>*/}
        {/*    </div>*/}
        {/*  </div>*/}
        {/*</section>*/}

        {/*<Separator className="my-6" />*/}

        {/* Summary + Insights */}
        <section>
          <div className="mb-2 flex items-center justify-between">
            <h2 className="mb-4 text-lg font-semibold">Report</h2>
            <Button
              variant="link"
              className="flex items-center justify-center text-blue-600"
              onClick={() => setIsFullConversation(true)}
            >
              <span>Full Conversation</span>
              <ExternalLink className="h-3 w-3" />
            </Button>
          </div>

          <Card className="mb-4 p-6" data-testid="conversation-summary">
            <div className="mb-2 flex items-center justify-between">
              <h3 className="text-lg font-semibold">Summary</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefreshSummary}
                disabled={isRefreshing}
                data-testid="refresh-summary-btn"
              >
                {isRefreshing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" data-testid="summary-loading" />
                    Regenerating...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Refresh
                  </>
                )}
              </Button>
            </div>
            {refreshError && (
              <div className="mb-2 text-sm text-red-500" data-testid="summary-error">
                Error: {refreshError}
              </div>
            )}
            {summaryUpdatedAt && (
              <div className="mb-3 text-xs text-gray-500">
                Last updated {formatTimeAgo(summaryUpdatedAt)}
              </div>
            )}
            <div className="whitespace-pre-line text-start text-gray-700" data-testid="summary-content">
              <Markdown
                content={summary || "No summary available at the moment."}
              />
            </div>
          </Card>

          {/*Hide the insights for now*/}

          {/*<div className="space-y-4">*/}
          {/*  {insights.length > 0 ? (*/}
          {/*    insights.map((insight, index) => (*/}
          {/*      <Card key={index} className="p-6">*/}
          {/*        <div className="mb-2 text-lg font-semibold">*/}
          {/*          Insight for goal {index + 1}:*/}
          {/*        </div>*/}
          {/*        <div className="whitespace-pre-line text-start text-gray-700">*/}
          {/*          <Markdown content={insight.insight || "No insight"} />*/}
          {/*        </div>*/}
          {/*      </Card>*/}
          {/*    ))*/}
          {/*  ) : (*/}
          {/*    <Card className="p-6">*/}
          {/*      <div className="mb-2 text-lg font-semibold">Insights</div>*/}
          {/*      <div className="whitespace-pre-line text-start text-gray-700">*/}
          {/*        No insights available for now.*/}
          {/*      </div>*/}
          {/*    </Card>*/}
          {/*  )}*/}
          {/*</div>*/}
        </section>
      </div>
      <UnstructuredConvoModal
        open={isFullConversation}
        onOpenChange={setIsFullConversation}
        messages={conversationMessages}
        title={conversation.workflow.name}
        studentName={studentName ?? ""}
      />
      <GoalModal
        open={showGoalModal}
        onOpenChange={setShowGoalModal}
        steps={steps}
      />
    </ScrollArea>
  );
};

export default StudentUnstructuredConvo;
