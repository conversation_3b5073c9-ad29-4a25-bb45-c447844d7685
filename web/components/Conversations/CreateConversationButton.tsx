"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  createAssignmentConversation,
  createConversation,
  createUnStructuredConversation,
} from "@/actions/conversations/conversation-workflow";
import { WorkflowType } from "@prisma/client";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { Icons } from "@/components/shared/icons";
import { useUser } from "@/app/context/UserContext";

export function CreateConversationButton() {
  const { user } = useUser();
  const userId = user?.id;
  if (!userId) {
    throw new Error("User ID is required");
  }

  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  // choose conversation type/format
  const [format, setFormat] = useState<WorkflowType>(WorkflowType.STRUCTURED);
  // choose structured conversation type
  const [structuredType, setStructuredType] = useState<string>("General");

  const router = useRouter();
  const handleCreate = async () => {
    if (!title) {
      toast.warning("Please enter a conversation name");
      return;
    }

    setIsCreating(true);
    //TODO: based on format to create a different conversation
    try {
      if (!format) {
        return toast.warning("Please select a conversation type");
      }

      if (format === WorkflowType.STRUCTURED) {
        // Attempt to create the conversation
        const result = await createConversation(title, userId, structuredType);
        if (result.success && result.data) {
          toast.success("New conversation created!");
          setOpen(false);
          setTitle("");
          setStructuredType("General");
          router.push(`/conversations/${result.data.id}`);
        } else {
          toast.error(result.error || "Failed to create conversation");
        }
      }
      // TODO: add a new server action to create open-ended conversation
      if (format === WorkflowType.UNSTRUCTURED) {
        const result = await createUnStructuredConversation(
          title,
          userId,
        );
        if (result.success && result.data) {
          console.log("open-ended conversation is created");
          toast.success("A new open-ended conversation is created");
          setOpen(false);
          setTitle("");
          router.push(`/conversations/${result.data.id}`);
        } else {
          toast.error(result.error || "Failed to create unstructured conversation");
        }
      }

      if (format === WorkflowType.ASSIGNMENT) {
        const result = await createAssignmentConversation(
          title,
          userId,
        );
        if (result.success && result.data) {
          toast.success("A new assignment conversation is created");
          setOpen(false);
          setTitle("");
          router.push(`/conversations/${result.data.id}`);
        } else {
          toast.error(result.error || "Failed to create assignment conversation");
        }
      }
    } catch (error) {
      // Handle any unexpected errors
      console.error("Unexpected error creating conversation:", error);
      toast.error("An unexpected error occurred. Please try again.");
    } finally {
      setIsCreating(false); // Reset loading state
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <div className="flex items-center justify-between p-4">
          <span className="pl-3 font-semibold">Conversations</span>
          <Button>
            <Icons.add />
            Create
          </Button>
        </div>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Conversation</DialogTitle>
          {/*<DialogDescription>*/}
          {/*  Create a custom conversation with your own questions*/}
          {/*</DialogDescription>*/}
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Conversation Format</label>
            <Select
              value={format}
              onValueChange={(val) => setFormat(val as WorkflowType)}
              defaultValue={WorkflowType.STRUCTURED}
            >
              {/*
                We manually render the trigger text here:
                If no selection, show "Select format"
                Otherwise show "Structured" or "Open Ended"
              */}
              <SelectTrigger className="w-full">
                {format === WorkflowType.STRUCTURED && "Structured"}
                {format === WorkflowType.UNSTRUCTURED && "Open Ended"}
                {format === WorkflowType.ASSIGNMENT && "Assignment"}
              </SelectTrigger>

              <SelectContent>
                <SelectItem value={WorkflowType.STRUCTURED}>
                  <div className="flex flex-col gap-1">
                    <span className="font-medium">Structured</span>
                    <span className="text-sm text-muted-foreground">
                      Use predefined questions for straightforward, targeted
                      responses and efficient data collection.
                    </span>
                  </div>
                </SelectItem>
                <SelectItem value={WorkflowType.UNSTRUCTURED}>
                  <div className="flex flex-col gap-1">
                    <span className="font-medium">Open Ended</span>
                    <span className="text-sm text-muted-foreground">
                      Engage in flexible, natural dialogues to explore detailed
                      insights and complex topics.
                    </span>
                  </div>
                </SelectItem>
                <SelectItem value={WorkflowType.ASSIGNMENT}>
                  <div className="flex flex-col gap-1">
                    <span className="font-medium">Assignment</span>
                    <span className="text-sm text-muted-foreground">
                      Encourage students to complete real-world tasks, with
                      Addie following up to ensure progress and completion.
                    </span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Structured Type Dropdown - Only show for STRUCTURED workflows */}
          {format === WorkflowType.STRUCTURED && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Structured Type</label>
              <Select
                value={structuredType}
                onValueChange={setStructuredType}
                defaultValue="General"
              >
                <SelectTrigger className="w-full">
                  {structuredType}
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="General">
                    <div className="flex flex-col gap-1">
                      <span className="font-medium">General</span>
                      <span className="text-sm text-muted-foreground">
                        Standard structured conversation
                      </span>
                    </div>
                  </SelectItem>
                  <SelectItem value="The Big Five Personality Test (BFPT)">
                    <div className="flex flex-col gap-1">
                      <span className="font-medium">The Big Five Personality Test (BFPT)</span>
                      <span className="text-sm text-muted-foreground">
                        Short survey for student&apos;s big five personality traits
                      </span>
                    </div>
                  </SelectItem>
                  <SelectItem value="Comprehensive Inventory of Thriving (CIT)">
                    <div className="flex flex-col gap-1">
                      <span className="font-medium">Comprehensive Inventory of Thriving (CIT)</span>
                      <span className="text-sm text-muted-foreground">
                        Detailed assessment for comprehensive student evaluation
                      </span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="space-y-4">
            <Input
              placeholder="Enter conversation name"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button disabled={isCreating} onClick={handleCreate}>
              {isCreating ? <LoadingSpinner /> : "Create"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
