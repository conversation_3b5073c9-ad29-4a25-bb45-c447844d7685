"use client";

import React from "react";
import {
  CustomConversation,
  QuestionItem,
} from "@/actions/conversations/conversation-workflow";

import MultiQuestionBuilder from "@/components/Conversations/QuestionBuilder/MultiQuestionBuilder";

interface BuildQuestionProps {
  conversation: CustomConversation;
  onQuestionsChange: (
    questions: QuestionItem[],
    allValid: boolean,
    description?: string,
    structuredType?: string,
  ) => void;
  saveButtonComponent?: React.ReactNode;
}

export default function BuildQuestion({
  conversation,
  onQuestionsChange,
  saveButtonComponent,
}: BuildQuestionProps) {
  // BuildQuestion simply wraps MultiQuestionBuilder and passes down a callback
  // that receives the full list of unsaved builder forms.
  const handleChange = (
    forms: QuestionItem[],
    allValid: boolean,
    description?: string,
    structuredType?: string,
  ) => {
    onQuestionsChange(forms, allValid, description, structuredType);
  };

  // Get description from conversation if it exists
  const initialDescription = conversation.description || "";

  return (
    <div className="flex h-full flex-col">
      <div className="flex-1 overflow-auto p-6">
        <MultiQuestionBuilder
          onChange={handleChange}
          initialQuestions={conversation.questions}
          saveButtonComponent={saveButtonComponent}
          initialDescription={initialDescription}
          conversation={conversation}
        />
      </div>
    </div>
  );
}
