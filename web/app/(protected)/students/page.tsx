import assert from "assert";
import * as React from "react";
import { cookies } from "next/headers";
import { pick } from "@contentlayer2/utils";
import { UserRole } from "@prisma/client";

import { prisma } from "@/common/db";
import log from "@/common/logger";
import { Student } from "@/common/model";
import { Callout } from "@/components/shared/callout";
import type { EnhancedStudent } from "@/components/student-table";
import StudentTable from "@/components/student-table";

export default async function IndexPage() {
  // const cookieStore = await cookies();
  // const tzOffset = parseInt(cookieStore.get("tz-offset")?.value || "0");

  let users = await prisma.user.findMany({
    include: {
      students: {
        include: {
          school: true,
          student_workflow: {
            include: {
              workflow: {
                select: {
                  name: true,
                  status: true,
                  workflow_type: true,
                },
              },
            },
          },
          onboarding_activity: true,
        },
      },
    },
  });

  // filter those without email
  users = users.filter(
    (user) =>
      user.email &&
      !user.email?.includes("@example.com") &&
      user.students.length == 1,
  );

  const tokenUsageAllTime = await prisma.user.aggregate({
    _sum: {
      token_usage: true,
    },
    where: {
      role: {
        in: [UserRole.STUDENT],
      },
    },
  });

  const studentData: EnhancedStudent[] = await Promise.all(
    users.map(async (user) => {
      const student = user.students[0];

      const completedWFSteps = (
        await prisma.studentWorkFlowStep.findMany({
          where: { student_id: student.id, completed: true },
        })
      ).length;

      const msgsCount = await prisma.messages.count({
        where: { session_id: user.id },
      });

      const lastMessage = await prisma.messages.findFirst({
        where: { session_id: user.id },
        orderBy: { created_at: "desc" },
      });

      // Get assigned conversations from student.student_workflow
      // Filter for published workflows with valid student workflow status
      const assignedConversationsData = (student.student_workflow || [])
        .filter((sw) => {
          // Check if workflow is published and student workflow has valid status
          return (
            sw.workflow?.status === "PUBLISHED" &&
            sw.workflow?.workflow_type === "UNSTRUCTURED" &&
            ["NOT_STARTED", "IN_PROGRESS", "COMPLETED"].includes(sw.status)
          );
        })
        .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()) // Sort by created_at ascending
        .map((sw) => ({
          workflowName: sw.workflow.name,
          workflowType: sw.workflow.workflow_type,
          status: sw.status,
        }));

      // Get school information directly from the student object
      const school_id = student.school_id || "";
      const school_name = student.school?.name;

      return {
        ...pick(user, ["id", "first_name", "last_name", "email"]),
        student_id: student.id,
        school_id,
        school_name,
        msgsCount,
        completedWFSteps,
        lastMessage: lastMessage?.created_at,
        tokenUsage: user.token_usage,
        last_active: user.last_active,
        enabled: user.enabled,
        isProfileComplete: user.isProfileComplete,
        phone_number: user.phone_number,
        onboarding_status: user.isProfileComplete && !!user.phone_number,
        assignedConversations: assignedConversationsData,
        onboardingActivity: student.onboarding_activity,
        engagement_tier: (user.isProfileComplete && !!user.phone_number) ? student.engagement_tier : null,
        tier_updated_at: student.tier_updated_at,
        last_activity_at: student.last_activity_at,
        last_activity_channel: student.last_activity_channel,
      };
    }),
  );
  // sort by lastMessage
  studentData.sort((a, b) => {
    if (a.lastMessage && b.lastMessage) {
      return (
        new Date(b.lastMessage).getTime() - new Date(a.lastMessage).getTime()
      );
    }
    return 0;
  });

  return (
    <div className="w-full p-4">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold">Students</h1>
        <Callout type="default" twClass="mt-0">
          Token Usage (All Time):{" "}
          {tokenUsageAllTime._sum.token_usage?.toLocaleString()}
        </Callout>
      </div>
      <StudentTable students={studentData} />
    </div>
  );
}
