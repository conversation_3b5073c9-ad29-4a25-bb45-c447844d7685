import { prisma } from "@/common/db";
import log from "@/common/logger";
import { StudentAgent } from "@/common/model";
import { simpleMessages } from "@/common/user";
import { omit } from "@/common/utils";
import ExperimentDetail from "@/components/experiment-detail";
import StudentDetail from "@/components/student-detail";

export default async function StudentAgentPage({ params }) {
  const { student_id } = await params;
  const student = await prisma.student.findUniqueOrThrow({
    where: { id: student_id },
    select: {
      id: true,
      users: true,
      engagement_tier: true,
      tier_updated_at: true,
      last_activity_at: true,
      last_activity_channel: true,
      ocean_scores: true,
      student_majors: {
        include: {
          matches: {
            orderBy: {
              match_percentage: "desc",
            },
          },
        },
      },
    },
  });
  const user = student.users[0];
  const messagesId = student.users[0].id!;
  const msgsCount = await prisma.messages.count({
    where: { session_id: messagesId },
  });
  const completedWFSteps = (
    await prisma.studentWorkFlowStep.findMany({
      where: { student_id: student_id, completed: true },
    })
  ).length;

  const result = {
    msgsCount,
    completedWFSteps,
    student_id,
    user_id: user.id,
    engagement_tier: (user.isProfileComplete && !!user.phone_number) ? student.engagement_tier : null,
    tier_updated_at: student.tier_updated_at,
    last_activity_at: student.last_activity_at,
    last_activity_channel: student.last_activity_channel,
    ocean_scores: student.ocean_scores,
    student_majors: student.student_majors,
    ...omit(user, ["id", "role"]),
  };

  const minMessages = await simpleMessages(messagesId);

  // questionresponse
  // const responses = await prisma.questionResponse.findMany({
  //   where: {
  //     student_id,
  //   },
  //   include: {
  //     question: true,
  //   },
  // });
  //
  // const qnaireResponses = await Promise.all(
  //   responses.map(async (response) => {
  //     const workflowStep = await prisma.workflowStep.findUniqueOrThrow({
  //       where: { id: response.step_id },
  //     });
  //
  //     return {
  //       id: response.question?.id,
  //       question: response.question,
  //       response: response.response,
  //       workflow_step_id: response.step_id,
  //       workflow_id: workflowStep.parent_workflow_id,
  //     };
  //   }),
  // );
  //
  return (
    <StudentDetail
      value={result}
      messages={minMessages}
      // qnaireResponses={qnaireResponses}
    />
  );
}
