import * as React from "react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { WorkflowType } from "@prisma/client";
import { ChevronLeft, UserPlus } from "lucide-react";

import { prisma } from "@/common/db";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import CounselorTable from "@/components/counselor-table";
// Import custom components
import InviteCounselorModal from "@/components/invite-counselor-modal";
import InviteStudentModal from "@/components/invite-student-modal";
import { Callout } from "@/components/shared/callout";
import StudentTable, { type EnhancedStudent } from "@/components/student-table";

// Define the correct type for Next.js 15 App Router pages
interface SchoolPageProps {
  params: Promise<{ school_id: string }>;
}

export default async function SchoolPage({ params }: SchoolPageProps) {
  const resolvedParams = await params;
  const school_id = resolvedParams.school_id;

  // TypeScript Prisma client is asynchronous, so we need to use await
  const schoolData = await prisma.school.findUnique({
    where: { id: school_id },
    include: {
      counselors: {
        include: {
          user: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
              token_usage: true,
              last_active: true,
              isProfileComplete: true,
              enabled: true,
            },
          },
        },
      },
      students: {
        select: {
          id: true,
          student_id: true,
          created_at: true,
          engagement_tier: true,
          tier_updated_at: true,
          last_activity_at: true,
          last_activity_channel: true,
          student_workflow: {
            include: {
              workflow: {
                select: {
                  name: true,
                  workflow_type: true,
                  status: true,
                },
              },
            },
          },
          onboarding_activity: true,
        },
      },
      _count: {
        select: {
          counselors: true,
          students: true,
        },
      },
    },
  });

  if (!schoolData) {
    notFound();
  }

  // Process counselor data with message count information
  const counselorData = await Promise.all(
    schoolData.counselors.map(async (counselor) => {
      // Get message count for each counselor
      const msgsCount = await prisma.messages.count({
        where: { session_id: counselor.user.id },
      });

      // We can now directly access all fields since we explicitly selected them in the query
      return {
        id: counselor.id,
        user_id: counselor.user.id, // Add user_id for linking to counselor page
        first_name: counselor.user.first_name,
        last_name: counselor.user.last_name,
        email: counselor.user.email,
        tokenUsage: counselor.user.token_usage || 0,
        msgsCount: msgsCount,
        // Include status and login information
        last_active: counselor.user.last_active,
        isProfileComplete: counselor.user.isProfileComplete,
        // Include school information for remove actions
        school_id: schoolData.id,
        school_name: schoolData.name,
        enabled: counselor.user.enabled,
      };
    }),
  );

  // Fetch associated users for each student
  const studentIds = schoolData.students.map((student) => student.id);

  // Fetch all users who are associated with these students
  const usersWithStudents = await prisma.user.findMany({
    where: {
      students: {
        some: {
          id: {
            in: studentIds,
          },
        },
      },
    },
    select: {
      id: true,
      first_name: true,
      last_name: true,
      email: true,
      token_usage: true,
      last_active: true,
      isProfileComplete: true,
      enabled: true,
      phone_number: true,
      students: {
        select: {
          id: true,
          student_id: true,
        },
      },
    },
  });

  // Create a map of student IDs to their associated users
  const studentToUsersMap = new Map();

  usersWithStudents.forEach((user) => {
    user.students.forEach((student) => {
      if (!studentToUsersMap.has(student.id)) {
        studentToUsersMap.set(student.id, []);
      }
      studentToUsersMap.get(student.id).push(user);
    });
  });

  // Process student data with additional fields needed by StudentTable
  const studentData: EnhancedStudent[] = await Promise.all(
    schoolData.students.map(async (student) => {
      // Get the user associated with this student
      const users = studentToUsersMap.get(student.id) || [];
      const userInfo = users.length > 0 ? users[0] : null;

      // Get message count for the student if there's an associated user
      let msgsCount = 0;
      let last_active = userInfo?.last_active || null;
      let completedWFSteps = 0;

      if (userInfo) {
        // Get message count
        msgsCount = await prisma.messages.count({
          where: { session_id: userInfo.id },
        });

        // Get completed workflow steps count
        completedWFSteps = await prisma.studentWorkFlowStep.count({
          where: { student_id: student.id, completed: true },
        });
      }

      // Calculate onboarding status for sorting
      const hasPhoneNumber = !!userInfo?.phone_number;
      const isProfileComplete = userInfo?.isProfileComplete || false;
      const onboarding_status = hasPhoneNumber && isProfileComplete;

      return {
        // Basic student info
        id: student.id,
        student_id: student.student_id || "",
        first_name: userInfo?.first_name || "",
        last_name: userInfo?.last_name || "",
        email: userInfo?.email || null,
        // Computed fields
        msgsCount,
        completedWFSteps,
        tokenUsage: userInfo?.token_usage || 0,
        last_active: last_active,
        isProfileComplete: userInfo?.isProfileComplete || false,
        phone_number: userInfo?.phone_number || null,
        onboarding_status, // Add computed field for sorting
        school_name: schoolData.name,
        school_id: schoolData.id,
        // Add lastMessage property to satisfy TypeScript
        lastMessage: undefined,
        enabled: userInfo?.enabled || false,
        // Get assigned conversations from student.student_workflow
        // Filter for published workflows with valid student workflow status
        assignedConversations: (student.student_workflow || [])
          .filter((sw) => {
            // Check if workflow is published and student workflow has valid status
            return (
              sw.workflow?.status === "PUBLISHED" &&
              sw.workflow?.workflow_type === WorkflowType.UNSTRUCTURED &&
              ["NOT_STARTED", "IN_PROGRESS", "COMPLETED"].includes(sw.status)
            );
          })
          .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()) // Sort by created_at ascending
          .map((sw) => ({
            workflowName: sw.workflow.name,
            workflowType: sw.workflow.workflow_type,
            status: sw.status,
          })),
        onboardingActivity: student.onboarding_activity,
        engagement_tier: onboarding_status ? student.engagement_tier : null,
        tier_updated_at: student.tier_updated_at,
        last_activity_at: student.last_activity_at,
        last_activity_channel: student.last_activity_channel,
      };
    }),
  );

  // Get current user ID for audit trail

  return (
    <div className="h-screen w-full overflow-y-auto p-4">
      <div className="mb-2">
        <Link href="/schools">
          <Button variant="ghost" size="sm">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Schools
          </Button>
        </Link>
      </div>

      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">{schoolData.name}</h1>
          <p className="text-muted-foreground">School ID: {schoolData.id}</p>
        </div>
        <div className="flex items-center gap-4">
          <Callout type="default" twClass="mt-0">
            Counselors: {schoolData._count.counselors} | Students:{" "}
            {schoolData._count.students}
          </Callout>
          {/* No invite buttons in the header - they will be shown inside the tabs */}
        </div>
      </div>

      <Tabs defaultValue="students" className="w-full">
        <TabsList>
          <TabsTrigger value="counselors">Counselors</TabsTrigger>
          <TabsTrigger value="students">Students</TabsTrigger>
        </TabsList>
        <TabsContent value="counselors" className="py-4">
          {counselorData.length > 0 ? (
            <CounselorTable
              counselors={counselorData}
              inviteButton={
                <InviteCounselorModal
                  schoolId={schoolData.id}
                  schoolName={schoolData.name}
                >
                  <Button>
                    <UserPlus className="mr-2 h-4 w-4" /> Invite Counselor
                  </Button>
                </InviteCounselorModal>
              }
            />
          ) : (
            <div className="py-8 text-center">
              <p className="text-muted-foreground">
                No counselors associated with this school yet
              </p>
              <div className="mt-4">
                <InviteCounselorModal
                  schoolId={schoolData.id}
                  schoolName={schoolData.name}
                >
                  <Button>
                    <UserPlus className="mr-2 h-4 w-4" /> Invite Counselor
                  </Button>
                </InviteCounselorModal>
              </div>
            </div>
          )}
        </TabsContent>
        <TabsContent value="students" className="py-4">
          {studentData.length > 0 ? (
            <StudentTable
              students={studentData}
              inviteButton={
                <InviteStudentModal
                  schoolId={schoolData.id}
                  schoolName={schoolData.name}
                >
                  <Button>
                    <UserPlus className="mr-2 h-4 w-4" /> Invite Student
                  </Button>
                </InviteStudentModal>
              }
            />
          ) : (
            <div className="py-8 text-center">
              <p className="text-muted-foreground">
                No students associated with this school yet
              </p>
              <div className="mt-4">
                <InviteStudentModal
                  schoolId={schoolData.id}
                  schoolName={schoolData.name}
                >
                  <Button>
                    <UserPlus className="mr-2 h-4 w-4" /> Invite Student
                  </Button>
                </InviteStudentModal>
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
