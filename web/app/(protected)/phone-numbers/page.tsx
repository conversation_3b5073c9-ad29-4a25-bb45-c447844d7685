"use client";

import { useState, useEffect, useCallback } from "react";
import { formatDistanceToNow } from "date-fns";
import { Plus, Edit, Trash2, Phone, MessageSquare, PhoneCall, ToggleLeft, ToggleRight, RefreshCw, Loader2, CheckCircle, AlertCircle, TrendingUp, ChevronUp, ChevronDown } from "lucide-react";
import {
  getPhoneNumbers,
  addPhoneNumber,
  updatePhoneNumberStatus,
  updatePhoneNumberCapabilities,
  deletePhoneNumber,
  syncWithTwilio
} from "@/actions/phone-numbers";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  Alert<PERSON>ialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { TooltipWrapper } from "@/components/shared/TooltipWrapper";

interface TwilioPhoneNumber {
  id: string;
  phone_number: string;
  // twilio_sid: Removed for security - not exposed by API
  country_code: string;
  country_name: string;
  is_active: boolean;
  capabilities: {
    sms: boolean;
    voice: boolean;
    mms?: boolean;
  };
  twilio_verified: boolean;
  voice_webhook_url?: string;
  sms_webhook_url?: string;
  environment?: string;
  webhook_validation_errors?: string;
  priority: number;
  last_synced_at?: string;
  sync_error?: string;
  created_at: string;
  updated_at: string;
}

export default function PhoneNumbersPage() {
  const [phoneNumbers, setPhoneNumbers] = useState<TwilioPhoneNumber[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [isSyncDialogOpen, setIsSyncDialogOpen] = useState(false);
  const [selectedNumber, setSelectedNumber] = useState<TwilioPhoneNumber | null>(null);
  const [syncStatus, setSyncStatus] = useState<"idle" | "syncing" | "success" | "error">("idle");
  const [lastSync, setLastSync] = useState<string | null>(null);
  const [sortField, setSortField] = useState<keyof TwilioPhoneNumber | null>(null);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [environmentFilter, setEnvironmentFilter] = useState<string>("all");
  const { toast } = useToast();

  // Form state
  const [formData, setFormData] = useState({
    phone_number: "",
    country_code: "",
    country_name: "",
    sms: true,
    voice: true,
  });

  const fetchPhoneNumbers = useCallback(async () => {
    try {
      const result = await getPhoneNumbers();
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch phone numbers");
      }
      setPhoneNumbers(result.data || []);

      // Update last sync time based on most recent sync
      const syncTimes = (result.data || [])
        .map((num: TwilioPhoneNumber) => num.last_synced_at)
        .filter(Boolean)
        .sort()
        .reverse();
      if (syncTimes.length > 0) {
        setLastSync(syncTimes[0] || null);
      }
    } catch (error: any) {
      console.error("Error fetching phone numbers:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to load phone numbers",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchPhoneNumbers();
  }, [fetchPhoneNumbers]);

  const handleSync = async () => {
    setSyncStatus("syncing");
    setIsSyncDialogOpen(false);
    try {
      const result = await syncWithTwilio();

      if (!result.success) {
        throw new Error(result.error || "Sync failed");
      }

      setSyncStatus("success");
      toast({
        title: "Sync Successful",
        description: result.message || "Sync completed successfully",
      });

      // Refresh the phone numbers list
      fetchPhoneNumbers();

      // Reset status after 3 seconds
      setTimeout(() => setSyncStatus("idle"), 3000);

    } catch (error: any) {
      setSyncStatus("error");
      toast({
        title: "Sync Failed",
        description: error.message || "Failed to sync with Twilio",
        variant: "destructive",
      });

      // Reset status after 5 seconds
      setTimeout(() => setSyncStatus("idle"), 5000);
    }
  };

  const handleAdd = async () => {
    try {
      const formDataObj = new FormData();
      formDataObj.set("phone_number", formData.phone_number);
      formDataObj.set("country_code", formData.country_code);
      formDataObj.set("country_name", formData.country_name);
      formDataObj.set("sms", formData.sms.toString());
      formDataObj.set("voice", formData.voice.toString());
      formDataObj.set("mms", "false");
      formDataObj.set("priority", "0");

      const result = await addPhoneNumber(formDataObj);

      if (!result.success) {
        throw new Error(result.error || "Failed to add phone number");
      }

      toast({
        title: "Success",
        description: result.message || "Phone number added successfully",
      });

      setIsAddDialogOpen(false);
      resetForm();
      fetchPhoneNumbers();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to add phone number",
        variant: "destructive",
      });
    }
  };

  const handleEdit = async () => {
    if (!selectedNumber) return;

    try {
      const result = await updatePhoneNumberCapabilities(selectedNumber.id, {
        sms: formData.sms,
        voice: formData.voice,
      });

      if (!result.success) {
        throw new Error(result.error || "Failed to update phone number");
      }

      toast({
        title: "Success",
        description: result.message || "Phone number updated successfully",
      });

      setIsEditDialogOpen(false);
      resetForm();
      fetchPhoneNumbers();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update phone number",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async () => {
    if (!selectedNumber) return;

    try {
      const result = await deletePhoneNumber(selectedNumber.id);

      if (!result.success) {
        throw new Error(result.error || "Failed to delete phone number");
      }

      toast({
        title: "Success",
        description: result.message || "Phone number deleted successfully",
      });

      setIsDeleteDialogOpen(false);
      setSelectedNumber(null);
      fetchPhoneNumbers();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete phone number",
        variant: "destructive",
      });
    }
  };

  const handleToggleActive = async (number: TwilioPhoneNumber) => {
    setIsStatusDialogOpen(false);
    try {
      const result = await updatePhoneNumberStatus(number.id, !number.is_active);

      if (!result.success) {
        throw new Error(result.error || "Failed to update phone number status");
      }

      toast({
        title: "Success",
        description: result.message || `Phone number ${!number.is_active ? "activated" : "deactivated"} successfully`,
      });

      fetchPhoneNumbers();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update phone number status",
        variant: "destructive",
      });
    }
  };

  const openStatusDialog = (number: TwilioPhoneNumber) => {
    setSelectedNumber(number);
    setIsStatusDialogOpen(true);
  };


  const resetForm = () => {
    setFormData({
      phone_number: "",
      country_code: "",
      country_name: "",
      sms: true,
      voice: true,
    });
    setSelectedNumber(null);
  };

  const handleSort = (field: keyof TwilioPhoneNumber) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const getSortedPhoneNumbers = () => {
    // Apply environment filter first
    let filteredNumbers = phoneNumbers;
    if (environmentFilter !== "all") {
      filteredNumbers = phoneNumbers.filter(number => {
        if (!number.environment && environmentFilter === "unknown") return true;
        return number.environment === environmentFilter;
      });
    }

    if (!sortField) return filteredNumbers;

    return [...filteredNumbers].sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      // Handle nested capabilities object
      if (sortField === "capabilities") {
        // Sort by number of capabilities
        aValue = Object.values(a.capabilities).filter(Boolean).length;
        bValue = Object.values(b.capabilities).filter(Boolean).length;
      }

      // Handle string comparisons
      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortDirection === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      // Handle number/boolean comparisons
      if (typeof aValue === "number" && typeof bValue === "number") {
        return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
      }

      if (typeof aValue === "boolean" && typeof bValue === "boolean") {
        return sortDirection === "asc"
          ? Number(aValue) - Number(bValue)
          : Number(bValue) - Number(aValue);
      }

      // Handle dates
      if (aValue instanceof Date && bValue instanceof Date) {
        return sortDirection === "asc"
          ? aValue.getTime() - bValue.getTime()
          : bValue.getTime() - aValue.getTime();
      }

      // Handle string dates
      if (typeof aValue === "string" && typeof bValue === "string") {
        const aDate = new Date(aValue);
        const bDate = new Date(bValue);
        if (!isNaN(aDate.getTime()) && !isNaN(bDate.getTime())) {
          return sortDirection === "asc"
            ? aDate.getTime() - bDate.getTime()
            : bDate.getTime() - aDate.getTime();
        }
      }

      return 0;
    });
  };

  const renderSortIcon = (field: keyof TwilioPhoneNumber) => {
    if (sortField !== field) {
      return <ChevronUp className="h-4 w-4 opacity-50" />;
    }
    return sortDirection === "asc"
      ? <ChevronUp className="h-4 w-4" />
      : <ChevronDown className="h-4 w-4" />;
  };

  const formatDateAgo = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return "Invalid date";
    }
  };

  const openEditDialog = (number: TwilioPhoneNumber) => {
    setSelectedNumber(number);
    setFormData({
      phone_number: number.phone_number,
      country_code: number.country_code,
      country_name: number.country_name,
      sms: number.capabilities.sms,
      voice: number.capabilities.voice,
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (number: TwilioPhoneNumber) => {
    setSelectedNumber(number);
    setIsDeleteDialogOpen(true);
  };

  const countryCodes = [
    { code: "+1", name: "United States" },
    { code: "+55", name: "Brazil" },
    { code: "+39", name: "Italy" },
    { code: "+44", name: "United Kingdom" },
    { code: "+33", name: "France" },
    { code: "+49", name: "Germany" },
    { code: "+34", name: "Spain" },
  ];

  if (loading) {
    return <div className="flex h-screen items-center justify-center">Loading...</div>;
  }

  return (
    <div className="container mx-auto py-10">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl">Phone Number Management</CardTitle>
              <CardDescription>
                Manage Twilio phone numbers with automatic capability detection
                {lastSync && (
                  <span className="ml-2 text-xs text-muted-foreground">
                    • Last synced: {new Date(lastSync).toLocaleString()}
                  </span>
                )}
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={() => setIsSyncDialogOpen(true)}
                disabled={syncStatus === "syncing"}
                variant="outline"
              >
                {syncStatus === "syncing" ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Syncing...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Sync with Twilio
                  </>
                )}
              </Button>
            </div>
          </div>

          {syncStatus === "error" && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Sync Failed</AlertTitle>
              <AlertDescription>
                Failed to sync with Twilio. Check your API credentials and try again.
              </AlertDescription>
            </Alert>
          )}

          {syncStatus === "success" && (
            <Alert className="mt-4">
              <CheckCircle className="h-4 w-4" />
              <AlertTitle>Sync Successful</AlertTitle>
              <AlertDescription>
                Phone numbers have been successfully synchronized with your Twilio account.
              </AlertDescription>
            </Alert>
          )}
        </CardHeader>
        <CardContent>
          <div className="mb-4 flex items-center gap-4">
            <Label htmlFor="environment-filter" className="text-sm font-medium">
              Filter by Environment:
            </Label>
            <select
              id="environment-filter"
              value={environmentFilter}
              onChange={(e) => setEnvironmentFilter(e.target.value)}
              className="flex h-10 w-48 rounded-md border border-input bg-background px-3 py-2 text-sm"
            >
              <option value="all">All Environments</option>
              <option value="production">Production Only</option>
              <option value="development">Development Only</option>
              <option value="unused">Unused Only</option>
              <option value="misconfigured">Misconfigured Only</option>
            </select>
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead
                  className="cursor-pointer select-none hover:bg-muted/50"
                  onClick={() => handleSort("phone_number")}
                >
                  <div className="flex items-center gap-2">
                    Phone Number
                    {renderSortIcon("phone_number")}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer select-none hover:bg-muted/50"
                  onClick={() => handleSort("country_name")}
                >
                  <div className="flex items-center gap-2">
                    Country
                    {renderSortIcon("country_name")}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer select-none hover:bg-muted/50"
                  onClick={() => handleSort("capabilities")}
                >
                  <div className="flex items-center gap-2">
                    Capabilities
                    {renderSortIcon("capabilities")}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer select-none hover:bg-muted/50"
                  onClick={() => handleSort("environment")}
                >
                  <div className="flex items-center gap-2">
                    Environment
                    {renderSortIcon("environment")}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer select-none hover:bg-muted/50"
                  onClick={() => handleSort("is_active")}
                >
                  <div className="flex items-center gap-2">
                    Status
                    {renderSortIcon("is_active")}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer select-none hover:bg-muted/50"
                  onClick={() => handleSort("last_synced_at")}
                >
                  <div className="flex items-center gap-2">
                    Last Synced
                    {renderSortIcon("last_synced_at")}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer select-none hover:bg-muted/50"
                  onClick={() => handleSort("created_at")}
                >
                  <div className="flex items-center gap-2">
                    Created
                    {renderSortIcon("created_at")}
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {phoneNumbers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center text-muted-foreground">
                    No phone numbers configured. Add your first phone number to get started.
                  </TableCell>
                </TableRow>
              ) : (
                getSortedPhoneNumbers().map((number) => (
                  <TableRow key={number.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        {number.phone_number}
                        {number.twilio_verified && (
                          <Badge variant="outline" className="text-xs">
                            <CheckCircle className="mr-1 h-3 w-3" />
                            Verified
                          </Badge>
                        )}
                        {number.sync_error && (
                          <Badge variant="destructive" className="text-xs">
                            <AlertCircle className="mr-1 h-3 w-3" />
                            Error
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{number.country_name}</div>
                        <div className="text-sm text-muted-foreground">{number.country_code}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-2">
                        {number.capabilities.sms && (
                          <Badge variant="secondary">
                            <MessageSquare className="mr-1 h-3 w-3" />
                            SMS
                          </Badge>
                        )}
                        {number.capabilities.voice && (
                          <Badge variant="secondary">
                            <PhoneCall className="mr-1 h-3 w-3" />
                            Voice
                          </Badge>
                        )}
                        {number.capabilities.mms && (
                          <Badge variant="secondary">
                            <TrendingUp className="mr-1 h-3 w-3" />
                            MMS
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {number.environment === 'production' && (
                          <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
                            🟢 PROD
                          </Badge>
                        )}
                        {number.environment === 'development' && (
                          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                            🔵 DEV
                          </Badge>
                        )}
                        {number.environment === 'unused' && (
                          <Badge variant="secondary">
                            ⚪ UNUSED
                          </Badge>
                        )}
                        {number.environment === 'misconfigured' && (
                          <Badge variant="destructive">
                            🔴 ERROR
                          </Badge>
                        )}
                        {!number.environment && (
                          <Badge variant="outline" className="text-gray-500">
                            Unknown
                          </Badge>
                        )}
                        {number.webhook_validation_errors && (
                          <TooltipWrapper tooltipContent="Click to see configuration issues">
                            <AlertCircle 
                              className="h-4 w-4 text-yellow-600 cursor-pointer" 
                              onClick={() => {
                                toast({
                                  title: "Webhook Configuration Issues",
                                  description: number.webhook_validation_errors,
                                  variant: "destructive",
                                });
                              }}
                            />
                          </TooltipWrapper>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <button
                        onClick={() => openStatusDialog(number)}
                        className="p-0 border-0 bg-transparent cursor-pointer"
                      >
                        {number.is_active ? (
                          <ToggleRight className="h-6 w-10 text-green-600" />
                        ) : (
                          <ToggleLeft className="h-6 w-10 text-gray-400" />
                        )}
                      </button>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {number.last_synced_at ? (
                          <span title={new Date(number.last_synced_at).toLocaleString()}>
                            {formatDateAgo(number.last_synced_at)}
                          </span>
                        ) : (
                          <span className="text-muted-foreground">Never</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span title={new Date(number.created_at).toLocaleString()}>
                        {formatDateAgo(number.created_at)}
                      </span>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Phone Number</DialogTitle>
            <DialogDescription>
              Add a new Twilio phone number for SMS and voice communications.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="phone_number" className="text-right">
                Phone Number
              </Label>
              <Input
                id="phone_number"
                value={formData.phone_number}
                onChange={(e) => setFormData({ ...formData, phone_number: e.target.value })}
                placeholder="+15551234567"
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="country" className="text-right">
                Country
              </Label>
              <select
                id="country"
                className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                value={formData.country_code}
                onChange={(e) => {
                  const selected = countryCodes.find(c => c.code === e.target.value);
                  setFormData({
                    ...formData,
                    country_code: e.target.value,
                    country_name: selected?.name || ""
                  });
                }}
              >
                <option value="">Select a country</option>
                {countryCodes.map((country) => (
                  <option key={country.code} value={country.code}>
                    {country.name} ({country.code})
                  </option>
                ))}
              </select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Capabilities</Label>
              <div className="col-span-3 flex gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="sms"
                    checked={formData.sms}
                    onCheckedChange={(checked) => setFormData({ ...formData, sms: checked as boolean })}
                  />
                  <Label htmlFor="sms">SMS</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="voice"
                    checked={formData.voice}
                    onCheckedChange={(checked) => setFormData({ ...formData, voice: checked as boolean })}
                  />
                  <Label htmlFor="voice">Voice</Label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAdd}>Add Phone Number</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Phone Number</DialogTitle>
            <DialogDescription>
              Update the capabilities for {selectedNumber?.phone_number}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Capabilities</Label>
              <div className="col-span-3 flex gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit-sms"
                    checked={formData.sms}
                    onCheckedChange={(checked) => setFormData({ ...formData, sms: checked as boolean })}
                  />
                  <Label htmlFor="edit-sms">SMS</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit-voice"
                    checked={formData.voice}
                    onCheckedChange={(checked) => setFormData({ ...formData, voice: checked as boolean })}
                  />
                  <Label htmlFor="edit-voice">Voice</Label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEdit}>Update Phone Number</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Status Change Confirmation Dialog */}
      <AlertDialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {selectedNumber?.is_active ? "Deactivate" : "Activate"} Phone Number?
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to {selectedNumber?.is_active ? "deactivate" : "activate"} the phone number {selectedNumber?.phone_number}?
              {selectedNumber?.is_active
                ? " This will stop routing calls and messages to this number."
                : " This will enable routing calls and messages to this number."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={() => selectedNumber && handleToggleActive(selectedNumber)}>
              {selectedNumber?.is_active ? "Deactivate" : "Activate"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Twilio Sync Confirmation Dialog */}
      <AlertDialog open={isSyncDialogOpen} onOpenChange={setIsSyncDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Sync with Twilio?</AlertDialogTitle>
            <AlertDialogDescription>
              This will synchronize your phone numbers with your Twilio account. The sync process will:
              <ul className="ml-4 mt-2 list-disc space-y-1">
                <li>Update phone number capabilities</li>
                <li>Add new numbers found in Twilio</li>
                <li>Mark numbers not found in Twilio as inactive</li>
              </ul>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleSync}>
              {syncStatus === "syncing" ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Syncing...
                </>
              ) : (
                "Sync Now"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the phone number {selectedNumber?.phone_number}.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
