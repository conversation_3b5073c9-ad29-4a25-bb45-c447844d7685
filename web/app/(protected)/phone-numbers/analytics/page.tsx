"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { MessageSquare, PhoneCall, CheckCircle, XCircle, TrendingUp, Clock, BarChart, Target } from "lucide-react";
import { getUsageStatistics } from "@/actions/phone-numbers";

interface RecentCallInfo {
  call_sid: string;
  status: string;
  duration?: number;
  start_time?: string;
  student_id: string;
}

interface UsageStats {
  phone_number: string;
  country_code: string;
  country_name: string;
  total_sms: number;
  total_voice: number;
  total_success: number;
  total_failure: number;
  days_active: number;
  // Enhanced fields from CallRecord integration
  total_call_duration: number;     // Total call time in minutes
  avg_call_duration: number;       // Average call duration in minutes
  total_answered_calls: number;    // Number of successfully answered calls
  total_failed_calls: number;      // Number of failed calls
  call_success_rate: number;       // Success rate percentage
  recent_calls: RecentCallInfo[];  // Last 5 calls with details
}

export default function PhoneNumberAnalytics() {
  const [usageStats, setUsageStats] = useState<UsageStats[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUsageStats();
  }, []);

  const fetchUsageStats = async () => {
    try {
      const result = await getUsageStatistics(7);
      if (result.success) {
        setUsageStats(result.data || []);
      } else {
        console.error("Error fetching usage stats:", result.error);
      }
    } catch (error) {
      console.error("Error fetching usage stats:", error);
    } finally {
      setLoading(false);
    }
  };

  // Calculate totals
  const totalSMS = usageStats.reduce((sum, stat) => sum + stat.total_sms, 0);
  const totalVoice = usageStats.reduce((sum, stat) => sum + stat.total_voice, 0);
  const totalSuccess = usageStats.reduce((sum, stat) => sum + stat.total_success, 0);
  const totalFailure = usageStats.reduce((sum, stat) => sum + stat.total_failure, 0);
  const totalAttempts = totalSuccess + totalFailure;
  const successRate = totalAttempts > 0 ? (totalSuccess / totalAttempts) * 100 : 0;
  
  // Calculate enhanced call metrics
  const totalCallDuration = usageStats.reduce((sum, stat) => sum + (stat.total_call_duration || 0), 0);
  const totalAnsweredCalls = usageStats.reduce((sum, stat) => sum + (stat.total_answered_calls || 0), 0);
  const totalFailedCalls = usageStats.reduce((sum, stat) => sum + (stat.total_failed_calls || 0), 0);
  const totalCalls = totalAnsweredCalls + totalFailedCalls;
  const callSuccessRate = totalCalls > 0 ? (totalAnsweredCalls / totalCalls) * 100 : 0;
  const avgCallDuration = totalCalls > 0 ? totalCallDuration / totalCalls : 0;

  if (loading) {
    return <div className="flex h-screen items-center justify-center">Loading analytics...</div>;
  }

  return (
    <div className="container mx-auto space-y-6 py-10">
      <div>
        <h1 className="text-3xl font-bold">Phone Number Analytics</h1>
        <p className="text-muted-foreground">
          Usage statistics and performance metrics for your Twilio phone numbers (Last 7 days)
        </p>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total SMS</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalSMS.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Voice</CardTitle>
            <PhoneCall className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalVoice.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{successRate.toFixed(1)}%</div>
            <Progress value={successRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Numbers</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {usageStats.filter(stat => stat.total_sms + stat.total_voice > 0).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Call Analytics */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Call Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {totalCallDuration < 60 
                ? `${totalCallDuration.toFixed(1)}m`
                : `${(totalCallDuration / 60).toFixed(1)}h`
              }
            </div>
            <p className="text-xs text-muted-foreground">
              {totalCallDuration.toFixed(1)} minutes total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Call Duration</CardTitle>
            <BarChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgCallDuration.toFixed(1)}m</div>
            <p className="text-xs text-muted-foreground">
              Per call average
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Call Success Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{callSuccessRate.toFixed(1)}%</div>
            <Progress value={callSuccessRate} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {totalAnsweredCalls} answered / {totalCalls} total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed Calls</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">{totalFailedCalls}</div>
            <p className="text-xs text-muted-foreground">
              Failed attempts
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Stats by Number */}
      <Card>
        <CardHeader>
          <CardTitle>Usage by Phone Number</CardTitle>
          <CardDescription>Detailed breakdown of usage across all phone numbers</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {usageStats.length === 0 ? (
              <div className="py-8 text-center text-muted-foreground">
                No usage data available for the selected period.
              </div>
            ) : (
              usageStats.map((stat) => {
                const numberTotal = stat.total_sms + stat.total_voice;
                const numberSuccess = stat.total_success;
                const numberFailure = stat.total_failure;
                const numberAttempts = numberSuccess + numberFailure;
                const numberSuccessRate = numberAttempts > 0 ? (numberSuccess / numberAttempts) * 100 : 0;

                return (
                  <div key={stat.phone_number} className="rounded-lg border p-4">
                    <div className="mb-3 flex items-center justify-between">
                      <div>
                        <div className="font-medium">{stat.phone_number}</div>
                        <div className="text-sm text-muted-foreground">
                          {stat.country_name} ({stat.country_code})
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Badge variant="outline">
                          <MessageSquare className="mr-1 h-3 w-3" />
                          {stat.total_sms} SMS
                        </Badge>
                        <Badge variant="outline">
                          <PhoneCall className="mr-1 h-3 w-3" />
                          {stat.total_voice} Voice
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-4">
                      <div>
                        <div className="text-muted-foreground">Total Communications</div>
                        <div className="font-medium">{numberTotal}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Success Rate</div>
                        <div className="flex items-center gap-2 font-medium">
                          {numberSuccessRate.toFixed(1)}%
                          <Progress value={numberSuccessRate} className="h-2 w-16" />
                        </div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Call Duration</div>
                        <div className="font-medium">
                          {(stat.total_call_duration || 0).toFixed(1)}m total
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Avg: {(stat.avg_call_duration || 0).toFixed(1)}m
                        </div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Days Active</div>
                        <div className="font-medium">{stat.days_active}/7 days</div>
                      </div>
                    </div>

                    {/* Enhanced Call Analytics per Number */}
                    {(stat.total_answered_calls > 0 || stat.total_failed_calls > 0) && (
                      <div className="mt-3 rounded bg-muted/30 p-3">
                        <div className="mb-2 text-sm font-medium">Call Analytics</div>
                        <div className="grid grid-cols-2 gap-4 text-sm md:grid-cols-4">
                          <div>
                            <div className="text-muted-foreground">Answered</div>
                            <div className="font-medium text-green-600">
                              {stat.total_answered_calls || 0}
                            </div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Failed</div>
                            <div className="font-medium text-red-600">
                              {stat.total_failed_calls || 0}
                            </div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Call Success</div>
                            <div className="font-medium">
                              {(stat.call_success_rate || 0).toFixed(1)}%
                            </div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Recent Calls</div>
                            <div className="font-medium">
                              {(stat.recent_calls || []).length}
                            </div>
                          </div>
                        </div>
                        
                        {/* Recent Calls Details */}
                        {stat.recent_calls && stat.recent_calls.length > 0 && (
                          <div className="mt-3">
                            <div className="mb-2 text-xs font-medium text-muted-foreground">Recent Calls</div>
                            <div className="space-y-1">
                              {stat.recent_calls.slice(0, 3).map((call, index) => (
                                <div key={call.call_sid} className="flex items-center justify-between text-xs">
                                  <div className="flex items-center gap-2">
                                    <Badge variant={call.status === 'COMPLETED' ? 'default' : call.status === 'FAILED' ? 'destructive' : 'secondary'} className="text-xs">
                                      {call.status}
                                    </Badge>
                                    <span className="text-muted-foreground">
                                      {call.duration ? `${Math.round(call.duration / 60)}m` : 'N/A'}
                                    </span>
                                  </div>
                                  <span className="text-muted-foreground">
                                    {call.start_time ? new Date(call.start_time).toLocaleDateString() : 'N/A'}
                                  </span>
                                </div>
                              ))}
                              {stat.recent_calls.length > 3 && (
                                <div className="text-xs text-muted-foreground">
                                  +{stat.recent_calls.length - 3} more calls
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {numberFailure > 0 && (
                      <div className="mt-3 rounded bg-destructive/10 p-2 text-sm">
                        <div className="flex items-center gap-1 text-destructive">
                          <XCircle className="h-3 w-3" />
                          {numberFailure} failed attempts
                        </div>
                      </div>
                    )}
                  </div>
                );
              })
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}