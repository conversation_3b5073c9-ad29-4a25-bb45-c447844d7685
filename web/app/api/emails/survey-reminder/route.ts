import React from "react";
import { NextRequest, NextResponse } from "next/server";
import SurveyReminderEmail from "@/emails/survey-reminder";
import { Resend } from "resend";

import { env } from "@/env.mjs";
import log from "@/common/logger";
import { getUserById } from "@/common/user";

const resend = new Resend(env.RESEND_API_KEY);

export async function POST(req: NextRequest) {
  let userId: string | undefined;

  try {
    const body = await req.json();
    userId = body.userId;
    const surveyLinks = body.surveyLinks;
    if (!userId || !Array.isArray(surveyLinks) || surveyLinks.length === 0) {
      return NextResponse.json(
        {
          message: "userId and surveyLinks (non-empty array) are required in request body",
        },
        { status: 400 },
      );
    }

    const user = await getUserById(userId);
    if (!user) {
      return NextResponse.json(
        { message: `User with ID "${userId}" not found in the database.` },
        { status: 404 },
      );
    }

    // Generate email content based on the survey reminder template
    const emailContent = SurveyReminderEmail({
      firstName: user?.first_name,
      surveyLinks,
    });
    const emailSubject = "Survey Reminder: Please complete your surveys";

    if (!React.isValidElement(emailContent)) {
      return NextResponse.json(
        { message: "Email content generation failed" },
        { status: 500 },
      );
    }

    const result = await resend.emails.send({
      from: env.EMAIL_FROM,
      to: user?.email!,
      subject: emailSubject,
      react: emailContent,
      headers: {
        "X-Entity-Ref-ID": new Date().getTime() + "",
      },
    });

    return NextResponse.json(
      { message: "Email sent successfully" },
      { status: 200 },
    );
  } catch (error) {
    console.error("Error sending email", error, userId);
    return NextResponse.json(
      { message: "Failed to send emails", error: error.message },
      { status: 500 },
    );
  }
}
