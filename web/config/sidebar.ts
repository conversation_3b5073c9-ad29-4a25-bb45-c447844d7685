import {
  Activity,
  Bell,
  ChartBarBig,
  FileText,
  FlaskConical,
  Lightbulb,
  MessagesSquare,
  School,
  Settings,
} from "lucide-react";

export interface MenuItem {
  path: string;
  label: string;
  icon?: any;
  exact: boolean;
  children?: MenuItem[];
}

export const sidebarConfig: { menuItems: MenuItem[] } = {
  menuItems: [
    {
      path: "/experiment",
      label: "Experiments",
      icon: FlaskConical,
      exact: true,
    },
    {
      path: "/chat_evaluator_config",
      label: "Evaluators",
      icon: ChartBarBig,
      exact: false,
    },
    {
      path: "/student_agent_prompts",
      label: "Student Agent Prompts",
      icon: Settings,
      exact: true,
    },
    {
      path: "/student-chat-prompt",
      label: "Student Chat Prompt",
      icon: Settings,
      exact: true,
    },
    {
      path: "/student-chat-system-prompt",
      label: "Student Chat System Prompt",
      icon: Settings,
      exact: true,
    },
    {
      path: "/schools",
      label: "Schools",
      icon: School,
      exact: false,
    },
    // {
    //   path: "/students",
    //   label: "Students",
    //   icon: Users,
    //   exact: false,
    // },
    // {
    //   path: "/counselors",
    //   label: "Counselors",
    //   icon: Users,
    //   exact: false,
    // },
    {
      path: "/prompt_suggestions",
      label: "Prompt Suggestions",
      icon: Lightbulb,
      exact: false,
    },
    {
      path: "/conversations",
      label: "Conversations",
      icon: MessagesSquare,
      exact: false,
    },
    {
      path: "/unstructured-conversation-summary",
      label: "Unstructured Summary Prompt",
      icon: FileText,
      exact: true,
    },
    {
      path: "/engagement-events",
      label: "Engagement Events",
      icon: Activity,
      exact: false,
    },
    {
      path: "/notifications",
      label: "Notifications",
      icon: Bell,
      exact: false,
      children: [
        {
          path: "/notifications/upcoming",
          label: "Upcoming",
          // icon: MessagesSquare,
          exact: false,
        },
        {
          path: "/notifications/past",
          label: "Past",
          // icon: MessagesSquare,
          exact: false,
        },
        {
          path: "/notifications/template-config",
          label: "Template Config",
          exact: false,
        },
      ],
    },
  ],
};
