import { Text } from "@react-email/components";
import { BaseAddieEmail } from "./EngagementEmails/BaseAddieEmail";

type CounselorInvitationEmailProps = {
  firstName?: string;
  schoolName: string;
  actionUrl: string;
  resend?: boolean;
};

export default function CounselorInvitationEmail({
  firstName = "Counselor",
  schoolName,
  actionUrl,
  resend = false,
}: CounselorInvitationEmailProps) {
  const previewText = `${resend ? "Reminder: " : ""}You've been invited to join ${schoolName} as a counselor`;
  const subject = `${resend ? "Reminder: " : ""}Invitation to Join ${schoolName}`;
  
  return (
    <BaseAddieEmail
      firstName={firstName}
      previewText={previewText}
      subject={subject}
      ctaText="Accept Invitation"
      ctaLink={actionUrl}
    >
      <Text className="mb-4 text-lg text-gray-800">
        {resend
          ? "This is a reminder that you've been invited"
          : "You've been invited"} to join <strong>{schoolName}</strong> as a counselor.
      </Text>
      
      <Text className="mb-4 text-lg text-gray-800">
        As a counselor at {schoolName}, you'll be able to:
      </Text>
      
      <ul className="mb-4 list-disc pl-6 text-lg text-gray-800">
        <li>Access student profiles and track their progress</li>
        <li>Guide students through their college application process</li>
        <li>Provide tailored support and insights for each student</li>
        <li>Collaborate with other counselors at your school</li>
      </ul>
      
      <Text className="mb-4 text-lg text-gray-800">
        Please click the button below to accept this invitation and set up your account.
      </Text>
      
      <Text className="text-base text-gray-600">
        If you didn't request this invitation, you can safely ignore this email.
      </Text>
    </BaseAddieEmail>
  );
}
