version: "3.8"

services:
  postgres:
    image: postgres:16
    env_file:
      - .env
    ports:
      - "0.0.0.0:5432:5432"
    volumes:
      - "$HOME/addie_data/data:/var/lib/postgresql/data"

  redis:
    image:
      redis
    env_file:
      - .env
    volumes:
      - "./redis.conf:/usr/local/etc/redis/redis.conf"
    ports:
      - 0.0.0.0:6379:6379
    command: redis-server /usr/local/etc/redis/redis.conf
