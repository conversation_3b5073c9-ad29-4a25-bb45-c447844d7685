version: '3.8'

services:
  beat-fixer:
    deploy:
      replicas: 1
    image: us-docker.pkg.dev/addie-440119/addie/addie:dev
    environment:
      - HOST_IP=${HOST_IP:-*************}
      - DATABASE_URL=${DATABASE_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - POSTGRES_USER=${POSTGRES_USER:-admin}
      - POSTGRES_DB=${POSTGRES_DB:-qa_dev}
      - POSTGRES_HOST=${POSTGRES_HOST:-*************}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - DATABASE_PORT=${DATABASE_PORT:-5432}
      - DATA_DIR=${DATA_DIR:-/app/data}
      - QDRANT_URL=${QDRANT_URL:-http://*************:6333}
      - QDRANT__SERVICE__API_KEY=${QDRANT__SERVICE__API_KEY}
      - REDIS_HOST=${REDIS_HOST:-*************}
      - REDIS_USER=${REDIS_USER:-admin}
      - REDIS_PASS=${REDIS_PASS:-addie!==}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - CELERY_CONFIG_MODULE=${CELERY_CONFIG_MODULE:-addie.celery_config}
      - REDIS_CONN=${REDIS_CONN}
      - TZ=${TZ:-America/Los_Angeles}
      - SLACK_TOKEN=${SLACK_TOKEN}
      - SLACK_CHANNEL=${SLACK_CHANNEL}
      - AUTH_SECRET=${AUTH_SECRET}
      - ADDIE_API_HOST=${ADDIE_API_HOST:-localhost}
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - TWILIO_VERIFY_SERVICE_SID=${TWILIO_VERIFY_SERVICE_SID}
    networks:
      - addie
    command: -c "celery -A addie.tasks.app beat --loglevel=error"

  default:
    deploy:
      replicas: 1
    image: us-docker.pkg.dev/addie-440119/addie/addie:dev
    environment:
      - HOST_IP=${HOST_IP:-*************}
      - DATABASE_URL=${DATABASE_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - POSTGRES_USER=${POSTGRES_USER:-admin}
      - POSTGRES_DB=${POSTGRES_DB:-qa_dev}
      - POSTGRES_HOST=${POSTGRES_HOST:-*************}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - DATABASE_PORT=${DATABASE_PORT:-5432}
      - DATA_DIR=${DATA_DIR:-/app/data}
      - QDRANT_URL=${QDRANT_URL:-http://*************:6333}
      - QDRANT__SERVICE__API_KEY=${QDRANT__SERVICE__API_KEY}
      - REDIS_HOST=${REDIS_HOST:-*************}
      - REDIS_USER=${REDIS_USER:-admin}
      - REDIS_PASS=${REDIS_PASS:-addie!==}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - CELERY_CONFIG_MODULE=${CELERY_CONFIG_MODULE:-addie.celery_config}
      - REDIS_CONN=${REDIS_CONN}
      - TZ=${TZ:-America/Los_Angeles}
      - SLACK_TOKEN=${SLACK_TOKEN}
      - SLACK_CHANNEL=${SLACK_CHANNEL}
      - AUTH_SECRET=${AUTH_SECRET}
      - ADDIE_API_HOST=${ADDIE_API_HOST:-localhost}
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - TWILIO_VERIFY_SERVICE_SID=${TWILIO_VERIFY_SERVICE_SID}
    networks:
      - addie
    command: -c "celery -A addie.tasks.app worker --loglevel=error -c 1"

  experiment:
    deploy:
      replicas: 1
    image: us-docker.pkg.dev/addie-440119/addie/addie:dev
    environment:
      - HOST_IP=${HOST_IP:-*************}
      - DATABASE_URL=${DATABASE_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - POSTGRES_USER=${POSTGRES_USER:-admin}
      - POSTGRES_DB=${POSTGRES_DB:-qa_dev}
      - POSTGRES_HOST=${POSTGRES_HOST:-*************}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - DATABASE_PORT=${DATABASE_PORT:-5432}
      - DATA_DIR=${DATA_DIR:-/app/data}
      - QDRANT_URL=${QDRANT_URL:-http://*************:6333}
      - QDRANT__SERVICE__API_KEY=${QDRANT__SERVICE__API_KEY}
      - REDIS_HOST=${REDIS_HOST:-*************}
      - REDIS_USER=${REDIS_USER:-admin}
      - REDIS_PASS=${REDIS_PASS:-addie!==}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - CELERY_CONFIG_MODULE=${CELERY_CONFIG_MODULE:-addie.celery_config}
      - REDIS_CONN=${REDIS_CONN}
      - TZ=${TZ:-America/Los_Angeles}
      - SLACK_TOKEN=${SLACK_TOKEN}
      - SLACK_CHANNEL=${SLACK_CHANNEL}
      - AUTH_SECRET=${AUTH_SECRET}
      - ADDIE_API_HOST=${ADDIE_API_HOST:-localhost}
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - TWILIO_VERIFY_SERVICE_SID=${TWILIO_VERIFY_SERVICE_SID}
    networks:
      - addie
    command: -c "celery -A addie.tasks.app worker --loglevel=error -c 1 -Q student_experiment"

networks:
  addie:
    external: true
    name: addie

