#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile --output-file=requirements.txt requirements.in
#
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.14
    # via
    #   aiohttp-retry
    #   datasets
    #   fsspec
    #   langchain
    #   langchain-community
    #   twilio
aiohttp-retry==2.9.1
    # via twilio
aiosignal==1.4.0
    # via aiohttp
alembic==1.13.3
    # via -r requirements.in
alog==1.1.0
    # via -r requirements.in
altair==5.5.0
    # via streamlit
amqp==5.3.1
    # via kombu
annotated-types==0.7.0
    # via
    #   -r requirements.in
    #   pydantic
anthropic==0.40.0
    # via
    #   -r requirements.in
    #   langchain-anthropic
anyio==4.6.2.post1
    # via
    #   -r requirements.in
    #   anthropic
    #   groq
    #   httpx
    #   openai
    #   starlette
    #   watchfiles
arrow==1.3.0
    # via times
asgiref==3.8.1
    # via
    #   -r requirements.in
    #   opentelemetry-instrumentation-asgi
async-timeout==4.0.3
    # via
    #   aiohttp
    #   asyncpg
    #   langchain
    #   redis
asyncpg==0.30.0
    # via langchain-postgres
attrs==24.2.0
    # via
    #   -r requirements.in
    #   aiohttp
    #   jsonschema
    #   referencing
backoff==2.2.1
    # via posthog
bcrypt==4.2.0
    # via
    #   -r requirements.in
    #   chromadb
billiard==4.2.1
    # via celery
blinker==1.9.0
    # via streamlit
boto3==1.35.63
    # via -r requirements.in
botocore==1.35.63
    # via
    #   -r requirements.in
    #   boto3
    #   s3transfer
build==1.2.2.post1
    # via
    #   -r requirements.in
    #   chromadb
cachetools==5.5.2
    # via
    #   google-auth
    #   streamlit
celery==5.4.0
    # via -r requirements.in
certifi==2024.8.30
    # via
    #   -r requirements.in
    #   httpcore
    #   httpx
    #   kubernetes
    #   requests
    #   sentry-sdk
cffi==1.17.1
    # via
    #   -r requirements.in
    #   cryptography
charset-normalizer==3.4.0
    # via
    #   -r requirements.in
    #   requests
chroma-hnswlib==0.7.6
    # via chromadb
chromadb==0.5.20
    # via
    #   -r requirements.in
    #   langchain-chroma
click==8.1.7
    # via
    #   -r requirements.in
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
    #   prisma
    #   streamlit
    #   typer
    #   uvicorn
click-didyoumean==0.3.1
    # via celery
click-plugins==*******
    # via celery
click-repl==0.3.0
    # via celery
cloudpickle==3.1.0
    # via -r requirements.in
coloredlogs==15.0.1
    # via
    #   -r requirements.in
    #   onnxruntime
cryptography==43.0.3
    # via -r requirements.in
dataclasses-json==0.6.7
    # via
    #   -r requirements.in
    #   langchain-community
datasets==3.1.0
    # via -r requirements.in
dateparser==1.2.2
    # via moment
defusedxml==0.7.1
    # via langchain-anthropic
dill==0.3.8
    # via
    #   datasets
    #   multiprocess
distro==1.9.0
    # via
    #   -r requirements.in
    #   anthropic
    #   groq
    #   openai
dnspython==2.7.0
    # via email-validator
docker==7.1.0
    # via -r requirements.in
duckdb==1.1.3
    # via -r requirements.in
durationpy==0.10
    # via kubernetes
email-validator==2.2.0
    # via -r requirements.in
exceptiongroup==1.3.0
    # via
    #   anyio
    #   pytest
execnet==2.1.1
    # via pytest-xdist
faker==30.8.2
    # via -r requirements.in
fastapi==0.115.4
    # via
    #   -r requirements.in
    #   chromadb
    #   langchain-chroma
fastapi-cli==0.0.5
    # via -r requirements.in
filelock==3.16.1
    # via
    #   -r requirements.in
    #   datasets
    #   huggingface-hub
flatbuffers==25.2.10
    # via onnxruntime
frozenlist==1.5.0
    # via
    #   -r requirements.in
    #   aiohttp
    #   aiosignal
fsspec[http]==2024.9.0
    # via
    #   -r requirements.in
    #   datasets
    #   huggingface-hub
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via streamlit
google-auth==2.40.3
    # via kubernetes
googleapis-common-protos==1.70.0
    # via opentelemetry-exporter-otlp-proto-grpc
greenlet==3.1.1
    # via -r requirements.in
groq==0.30.0
    # via langchain-groq
grpcio==1.73.1
    # via
    #   chromadb
    #   grpcio-tools
    #   opentelemetry-exporter-otlp-proto-grpc
    #   qdrant-client
grpcio-tools==1.71.2
    # via qdrant-client
h11==0.14.0
    # via
    #   -r requirements.in
    #   httpcore
    #   uvicorn
h2==4.2.0
    # via httpx
hpack==4.1.0
    # via h2
httpcore==1.0.6
    # via
    #   -r requirements.in
    #   httpx
httptools==0.6.4
    # via
    #   -r requirements.in
    #   uvicorn
httpx[http2]==0.27.2
    # via
    #   -r requirements.in
    #   anthropic
    #   chromadb
    #   groq
    #   langgraph-sdk
    #   langsmith
    #   openai
    #   prisma
    #   qdrant-client
httpx-sse==0.4.0
    # via
    #   -r requirements.in
    #   langchain-community
huggingface-hub==0.26.2
    # via
    #   -r requirements.in
    #   datasets
    #   tokenizers
humanfriendly==10.0
    # via
    #   -r requirements.in
    #   coloredlogs
humanize==4.11.0
    # via -r requirements.in
hyperframe==6.1.0
    # via h2
idna==3.10
    # via
    #   -r requirements.in
    #   anyio
    #   email-validator
    #   httpx
    #   requests
    #   yarl
importlib-metadata==8.5.0
    # via
    #   -r requirements.in
    #   opentelemetry-api
importlib-resources==6.5.2
    # via chromadb
iniconfig==2.1.0
    # via pytest
jinja2==3.1.4
    # via
    #   -r requirements.in
    #   altair
    #   prisma
    #   pydeck
jiter==0.10.0
    # via
    #   anthropic
    #   openai
jmespath==1.0.1
    # via
    #   -r requirements.in
    #   boto3
    #   botocore
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
jsonschema==4.24.1
    # via altair
jsonschema-specifications==2025.4.1
    # via jsonschema
kombu==5.4.2
    # via
    #   -r requirements.in
    #   celery
kubernetes==33.1.0
    # via chromadb
langchain==0.3.7
    # via
    #   -r requirements.in
    #   langchain-community
langchain-anthropic==0.2.4
    # via -r requirements.in
langchain-chroma==0.1.4
    # via -r requirements.in
langchain-community==0.3.7
    # via
    #   -r requirements.in
    #   langchain-experimental
langchain-core==0.3.17
    # via
    #   -r requirements.in
    #   langchain
    #   langchain-anthropic
    #   langchain-chroma
    #   langchain-community
    #   langchain-experimental
    #   langchain-groq
    #   langchain-openai
    #   langchain-postgres
    #   langchain-qdrant
    #   langchain-text-splitters
    #   langgraph
    #   langgraph-checkpoint
langchain-experimental==0.3.3
    # via -r requirements.in
langchain-groq==0.2.1
    # via -r requirements.in
langchain-openai==0.2.8
    # via -r requirements.in
langchain-postgres==0.0.15
    # via -r requirements.in
langchain-qdrant==0.2.0
    # via -r requirements.in
langchain-text-splitters==0.3.2
    # via
    #   -r requirements.in
    #   langchain
langgraph==0.2.56
    # via -r requirements.in
langgraph-checkpoint==2.1.1
    # via langgraph
langgraph-sdk==0.1.73
    # via langgraph
langsmith==0.1.143
    # via
    #   -r requirements.in
    #   langchain
    #   langchain-community
    #   langchain-core
lark==1.2.2
    # via -r requirements.in
mako==1.3.6
    # via
    #   -r requirements.in
    #   alembic
markdown-it-py==3.0.0
    # via rich
markdown-to-json==2.1.2
    # via -r requirements.in
markupsafe==3.0.2
    # via
    #   -r requirements.in
    #   jinja2
    #   mako
marshmallow==3.23.1
    # via
    #   -r requirements.in
    #   dataclasses-json
mdurl==0.1.2
    # via markdown-it-py
mmh3==5.0.1
    # via
    #   -r requirements.in
    #   chromadb
moment==0.12.1
    # via -r requirements.in
monotonic==1.6
    # via
    #   -r requirements.in
    #   posthog
mpmath==1.3.0
    # via sympy
multidict==6.1.0
    # via
    #   -r requirements.in
    #   aiohttp
    #   yarl
multiprocess==0.70.16
    # via datasets
mypy-extensions==1.0.0
    # via
    #   -r requirements.in
    #   typing-inspect
narwhals==1.47.1
    # via altair
nodeenv==1.9.1
    # via prisma
nose2==0.15.1
    # via -r requirements.in
numpy==1.26.4
    # via
    #   -r requirements.in
    #   chroma-hnswlib
    #   chromadb
    #   datasets
    #   langchain
    #   langchain-chroma
    #   langchain-community
    #   langchain-postgres
    #   onnxruntime
    #   pandas
    #   pgvector
    #   pydeck
    #   qdrant-client
    #   streamlit
oauthlib==3.3.1
    # via
    #   kubernetes
    #   requests-oauthlib
onnxruntime==1.22.1
    # via chromadb
openai==1.54.0
    # via
    #   -r requirements.in
    #   langchain-openai
opentelemetry-api==1.35.0
    # via
    #   chromadb
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-exporter-otlp-proto-common==1.35.0
    # via opentelemetry-exporter-otlp-proto-grpc
opentelemetry-exporter-otlp-proto-grpc==1.35.0
    # via chromadb
opentelemetry-instrumentation==0.56b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-asgi==0.56b0
    # via opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-fastapi==0.56b0
    # via chromadb
opentelemetry-proto==1.35.0
    # via
    #   opentelemetry-exporter-otlp-proto-common
    #   opentelemetry-exporter-otlp-proto-grpc
opentelemetry-sdk==1.35.0
    # via
    #   chromadb
    #   opentelemetry-exporter-otlp-proto-grpc
opentelemetry-semantic-conventions==0.56b0
    # via
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-sdk
opentelemetry-util-http==0.56b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
orjson==3.10.11
    # via
    #   -r requirements.in
    #   chromadb
    #   langgraph-sdk
    #   langsmith
ormsgpack==1.10.0
    # via langgraph-checkpoint
overrides==7.7.0
    # via
    #   -r requirements.in
    #   chromadb
packaging==24.2
    # via
    #   -r requirements.in
    #   altair
    #   build
    #   datasets
    #   huggingface-hub
    #   langchain-core
    #   marshmallow
    #   onnxruntime
    #   opentelemetry-instrumentation
    #   pytest
    #   streamlit
pandas==2.2.3
    # via
    #   -r requirements.in
    #   datasets
    #   streamlit
passlib==1.7.4
    # via -r requirements.in
pgvector==0.3.6
    # via langchain-postgres
phonenumbers==8.13.49
    # via -r requirements.in
pillow==11.0.0
    # via
    #   -r requirements.in
    #   streamlit
pluggy==1.6.0
    # via pytest
portalocker==2.10.1
    # via qdrant-client
posthog==3.7.4
    # via
    #   -r requirements.in
    #   chromadb
prisma==0.15.0
    # via -r requirements.in
prompt-toolkit==3.0.51
    # via click-repl
propcache==0.3.2
    # via
    #   aiohttp
    #   yarl
protobuf==5.28.3
    # via
    #   -r requirements.in
    #   googleapis-common-protos
    #   grpcio-tools
    #   onnxruntime
    #   opentelemetry-proto
    #   streamlit
psycopg==3.2.9
    # via
    #   -r requirements.in
    #   langchain-postgres
psycopg-pool==3.2.6
    # via langchain-postgres
pyarrow==18.0.0
    # via
    #   -r requirements.in
    #   datasets
    #   streamlit
pyarrow-hotfix==0.6
    # via -r requirements.in
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via
    #   -r requirements.in
    #   cffi
pydantic==2.10.1
    # via
    #   -r requirements.in
    #   anthropic
    #   chromadb
    #   fastapi
    #   groq
    #   langchain
    #   langchain-anthropic
    #   langchain-core
    #   langchain-qdrant
    #   langsmith
    #   openai
    #   prisma
    #   pydantic-settings
    #   qdrant-client
pydantic-core==2.27.1
    # via
    #   -r requirements.in
    #   pydantic
pydantic-settings==2.10.1
    # via langchain-community
pydeck==0.9.1
    # via streamlit
pygments==2.19.2
    # via rich
pyjwt==2.9.0
    # via
    #   -r requirements.in
    #   twilio
pypika==0.48.9
    # via chromadb
pyproject-hooks==1.2.0
    # via
    #   -r requirements.in
    #   build
pytest==8.3.3
    # via
    #   -r requirements.in
    #   pytest-asyncio
    #   pytest-xdist
pytest-asyncio==0.24.0
    # via -r requirements.in
pytest-xdist==3.6.0
    # via -r requirements.in
python-dateutil==2.9.0.post0
    # via
    #   -r requirements.in
    #   arrow
    #   botocore
    #   celery
    #   dateparser
    #   faker
    #   kubernetes
    #   pandas
    #   posthog
python-dotenv==1.0.1
    # via
    #   -r requirements.in
    #   prisma
    #   pydantic-settings
    #   uvicorn
python-multipart==0.0.12
    # via -r requirements.in
pytz==2024.2
    # via
    #   -r requirements.in
    #   dateparser
    #   moment
    #   pandas
pyyaml==6.0.2
    # via
    #   -r requirements.in
    #   chromadb
    #   datasets
    #   huggingface-hub
    #   kubernetes
    #   langchain
    #   langchain-community
    #   langchain-core
    #   uvicorn
qdrant-client==1.12.1
    # via
    #   -r requirements.in
    #   langchain-qdrant
redis==5.2.0
    # via -r requirements.in
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
regex==2024.11.6
    # via
    #   -r requirements.in
    #   dateparser
    #   tiktoken
requests==2.32.3
    # via
    #   -r requirements.in
    #   datasets
    #   docker
    #   huggingface-hub
    #   kubernetes
    #   langchain
    #   langchain-community
    #   langsmith
    #   posthog
    #   requests-oauthlib
    #   requests-toolbelt
    #   streamlit
    #   tiktoken
    #   twilio
requests-oauthlib==2.0.0
    # via kubernetes
requests-toolbelt==1.0.0
    # via langsmith
rich==13.9.4
    # via
    #   chromadb
    #   streamlit
    #   typer
rpds-py==0.26.0
    # via
    #   jsonschema
    #   referencing
rsa==4.9.1
    # via google-auth
s3transfer==0.10.3
    # via
    #   -r requirements.in
    #   boto3
sentry-sdk==2.18.0
    # via -r requirements.in
shellingham==1.5.4
    # via
    #   -r requirements.in
    #   typer
six==1.16.0
    # via
    #   -r requirements.in
    #   kubernetes
    #   posthog
    #   python-dateutil
slack-sdk==3.33.3
    # via -r requirements.in
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   -r requirements.in
    #   anthropic
    #   anyio
    #   groq
    #   httpx
    #   openai
sqlalchemy==2.0.35
    # via
    #   -r requirements.in
    #   alembic
    #   langchain
    #   langchain-community
    #   langchain-postgres
starlette==0.41.2
    # via
    #   -r requirements.in
    #   fastapi
streamlit==1.40.1
    # via -r requirements.in
strenum==0.4.15
    # via prisma
sympy==1.14.0
    # via onnxruntime
tenacity==9.0.0
    # via
    #   -r requirements.in
    #   chromadb
    #   langchain
    #   langchain-community
    #   langchain-core
    #   streamlit
tiktoken==0.8.0
    # via
    #   -r requirements.in
    #   langchain-openai
times==0.7
    # via moment
tokenizers==0.20.3
    # via
    #   -r requirements.in
    #   chromadb
toml==0.10.2
    # via streamlit
tomli==2.2.1
    # via
    #   build
    #   pytest
tomlkit==0.13.3
    # via prisma
tornado==6.5.1
    # via streamlit
tqdm==4.67.0
    # via
    #   -r requirements.in
    #   chromadb
    #   datasets
    #   huggingface-hub
    #   openai
twilio==9.3.6
    # via -r requirements.in
typer==0.13.1
    # via
    #   -r requirements.in
    #   chromadb
    #   fastapi-cli
types-python-dateutil==2.9.0.20250708
    # via arrow
types-requests==2.32.0.20241016
    # via -r requirements.in
typing-extensions==4.12.2
    # via
    #   -r requirements.in
    #   aiosignal
    #   alembic
    #   altair
    #   anthropic
    #   anyio
    #   asgiref
    #   chromadb
    #   exceptiongroup
    #   faker
    #   fastapi
    #   groq
    #   huggingface-hub
    #   langchain-core
    #   multidict
    #   openai
    #   opentelemetry-api
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
    #   prisma
    #   psycopg
    #   psycopg-pool
    #   pydantic
    #   pydantic-core
    #   referencing
    #   rich
    #   sqlalchemy
    #   streamlit
    #   typer
    #   typing-inspect
    #   typing-inspection
    #   uvicorn
typing-inspect==0.9.0
    # via dataclasses-json
typing-inspection==0.4.1
    # via pydantic-settings
tzdata==2024.2
    # via
    #   -r requirements.in
    #   celery
    #   kombu
    #   pandas
tzlocal==5.3.1
    # via dateparser
urllib3==2.2.3
    # via
    #   -r requirements.in
    #   botocore
    #   docker
    #   kubernetes
    #   qdrant-client
    #   requests
    #   sentry-sdk
    #   types-requests
uvicorn[standard]==0.32.0
    # via
    #   -r requirements.in
    #   chromadb
    #   fastapi-cli
uvloop==0.21.0
    # via
    #   -r requirements.in
    #   uvicorn
vine==5.1.0
    # via
    #   -r requirements.in
    #   amqp
    #   celery
    #   kombu
watchfiles==0.24.0
    # via
    #   -r requirements.in
    #   uvicorn
wcwidth==0.2.13
    # via prompt-toolkit
websocket-client==1.8.0
    # via kubernetes
websockets==13.1
    # via
    #   -r requirements.in
    #   uvicorn
wrapt==1.17.2
    # via opentelemetry-instrumentation
xxhash==3.5.0
    # via
    #   -r requirements.in
    #   datasets
yarl==1.17.1
    # via
    #   -r requirements.in
    #   aiohttp
zipp==3.20.2
    # via
    #   -r requirements.in
    #   importlib-metadata

# The following packages are considered to be unsafe in a requirements file:
# setuptools