import { PrismaClient } from '@prisma/client';
import { randomUUID } from 'crypto';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting seed...');

    // unseed TODO
    console.log("Unseeding first") 
    await prisma.teacherComment.deleteMany();
    await prisma.growthOpportunity.deleteMany();
    await prisma.academicAchievement.deleteMany();
    await prisma.messages.deleteMany();
    await prisma.student.deleteMany();
    await prisma.user.deleteMany();
  
  // Create test user
  const user = await prisma.user.create({
    data: {
      id: '22e9a05e93888040a85ac44053ffe7a5',
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'Student',
      role: 'STUDENT', // Replace with a valid UserRole enum if needed
      isProfileComplete: true,
    },
  });

  // Create test student
  const teststudent = await prisma.student.create({
    data: {
      grade: 10,
      student_id: 'stu-22e9a05e93888040a85ac44053ffe7a4',
      users: {
        connect: { id: user.id },
      },
    },
  });

  const student = await prisma.student.create({
    data: {
      grade: 10,
      student_id: '22e9a05e93888040a85ac44053ffe7a4',
      users: {
        connect: { id: user.id },
      },
    },
  });
// coment Seed 
await prisma.teacherComment.createMany({
  data: [
    {
      id: randomUUID(),
      student_id: student.id,
      comment_hash: randomUUID(), // must be unique
      class_name: 'English Literature',
      comment: 'Shows strong analytical thinking in essays.',
      counselor_name: 'Mr. Thompson',
      department: 'Humanities',
      grade_level: 10,
      grading_period: 'Fall',
      letter_grade: 'A',
      school_year: '2024–2025',
      teacher_name: 'Ms. Davis',
      context: 'Essay analysis and participation',
    },
  ],
  skipDuplicates: true,
});


  // Seed academic achievements
  await prisma.academicAchievement.createMany({
    data: [
      {
        id: randomUUID(),
        student_id: student.id,
        title: 'Math Olympiad Finalist',
        description: 'Placed in top 5 regionally.',
      },
    ],
    skipDuplicates: true,
  });

  // Seed growth opportunities
await prisma.growthOpportunity.createMany({
  data: [
    {
      id: randomUUID(),
      student_id: student.id,
      area: 'Class Participation',
      suggested_improvement: 'Speak up at least once per class',
      description: 'Needs to improve engagement in group discussions and ask more questions.',
      data: {}, // use `{}` as a placeholder if nothing specific is needed
    },
  ],
  skipDuplicates: true,
});


  // Seed message history
  const sessionId = 'a2ed4e2f50844a14aa97ad46c3de08f8';
  await prisma.messages.createMany({
    data: [
      {
        session_id: sessionId,
        message: {
          role: 'user',
          context: 'How can I do better in school?',
        },
      },
      {
        session_id: sessionId,
        message: {
          role: 'system',
          context: 'Review notes weekly and ask for help when needed.',
        },
      },
    ],
  });

  console.log('✅ Seeding complete!');
}

main()
  .catch((e) => {
    console.error('❌ Seed error:', e);
    process.exit(1);
  })
  .finally(() => prisma.$disconnect());

