-- Remove <PERSON>_INIT from StudentWorkflowStatus enum
-- We're now using messages history instead of a status flag

-- First we need to change any rows with SMS_INIT status to NOT_STARTED
UPDATE "StudentWorkflow" SET status = 'NOT_STARTED' WHERE status = 'SMS_INIT';

-- Create new enum type without SMS_INIT
CREATE TYPE "StudentWorkflowStatus_new" AS ENUM ('IN_PROGRESS', 'NOT_STARTED', 'COMPLETED');

-- Update the column to use the new type, handling the default value
ALTER TABLE "StudentWorkflow" 
  ALTER COLUMN status DROP DEFAULT,
  ALTER COLUMN status TYPE "StudentWorkflowStatus_new" 
  USING (status::text::"StudentWorkflowStatus_new"),
  ALTER COLUMN status SET DEFAULT 'NOT_STARTED';

-- Drop the old type
DROP TYPE "StudentWorkflowStatus";

-- Rename the new type to the original name
ALTER TYPE "StudentWorkflowStatus_new" RENAME TO "StudentWorkflowStatus";