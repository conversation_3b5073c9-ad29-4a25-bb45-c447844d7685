/*
  Warnings:

  - You are about to drop the `message_templates` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "message_templates" DROP CONSTRAINT "message_templates_created_by_fkey";

-- DropForeignKey
ALTER TABLE "system_prompt_context" DROP CONSTRAINT "system_prompt_context_message_id_fkey";

-- DropTable
DROP TABLE "message_templates";

-- DropEnum
DROP TYPE "TemplateType";

-- CreateTable
CREATE TABLE "OceanScores" (
    "id" TEXT NOT NULL,
    "student_id" TEXT NOT NULL,
    "extroversion" DOUBLE PRECISION NOT NULL,
    "agreeableness" DOUBLE PRECISION NOT NULL,
    "conscientiousness" DOUBLE PRECISION NOT NULL,
    "neuroticism" DOUBLE PRECISION NOT NULL,
    "openness_to_experience" DOUBLE PRECISION NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "OceanScores_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "OceanScores_student_id_key" ON "OceanScores"("student_id");

-- AddForeignKey
ALTER TABLE "OceanScores" ADD CONSTRAINT "OceanScores_student_id_fkey" FOREIGN KEY ("student_id") REFERENCES "Student"("id") ON DELETE CASCADE ON UPDATE CASCADE;
