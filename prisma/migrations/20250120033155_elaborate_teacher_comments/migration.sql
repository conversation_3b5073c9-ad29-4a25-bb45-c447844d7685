/*
  Warnings:

  - A unique constraint covering the columns `[comment_hash]` on the table `TeacherComment` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `class` to the `TeacherComment` table without a default value. This is not possible if the table is not empty.
  - Added the required column `comment_hash` to the `TeacherComment` table without a default value. This is not possible if the table is not empty.
  - Added the required column `counselor_name` to the `TeacherComment` table without a default value. This is not possible if the table is not empty.
  - Added the required column `department` to the `TeacherComment` table without a default value. This is not possible if the table is not empty.
  - Added the required column `grade_level` to the `TeacherComment` table without a default value. This is not possible if the table is not empty.
  - Added the required column `grading_period` to the `TeacherComment` table without a default value. This is not possible if the table is not empty.
  - Added the required column `school_year` to the `TeacherComment` table without a default value. This is not possible if the table is not empty.
  - Made the column `teacher_name` on table `TeacherComment` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "TeacherComment" ADD COLUMN     "class" TEXT NOT NULL,
ADD COLUMN     "comment_hash" TEXT NOT NULL,
ADD COLUMN     "counselor_name" TEXT NOT NULL,
ADD COLUMN     "department" TEXT NOT NULL,
ADD COLUMN     "grade_level" INTEGER NOT NULL,
ADD COLUMN     "grading_period" TEXT NOT NULL,
ADD COLUMN     "school_year" TEXT NOT NULL,
ALTER COLUMN "teacher_name" SET NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "TeacherComment_comment_hash_key" ON "TeacherComment"("comment_hash");
