-- AlterTable
ALTER TABLE "messages" ADD COLUMN     "prompt_suggestion_id" TEXT;

-- CreateTable
CREATE TABLE "PromptSuggestion" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "message_id" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PromptSuggestion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_PromptToPromptSuggestion" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "PromptSuggestion_message_id_key" ON "PromptSuggestion"("message_id");

-- CreateIndex
CREATE UNIQUE INDEX "_PromptToPromptSuggestion_AB_unique" ON "_PromptToPromptSuggestion"("A", "B");

-- CreateIndex
CREATE INDEX "_PromptToPromptSuggestion_B_index" ON "_PromptToPromptSuggestion"("B");

-- AddForeignKey
ALTER TABLE "PromptSuggestion" ADD CONSTRAINT "PromptSuggestion_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptSuggestion" ADD CONSTRAINT "PromptSuggestion_message_id_fkey" FOREIGN KEY ("message_id") REFERENCES "messages"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_PromptToPromptSuggestion" ADD CONSTRAINT "_PromptToPromptSuggestion_A_fkey" FOREIGN KEY ("A") REFERENCES "Prompt"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_PromptToPromptSuggestion" ADD CONSTRAINT "_PromptToPromptSuggestion_B_fkey" FOREIGN KEY ("B") REFERENCES "PromptSuggestion"("id") ON DELETE CASCADE ON UPDATE CASCADE;
