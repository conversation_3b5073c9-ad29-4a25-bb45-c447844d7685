-- CreateTable: StudentMajors (Session Root)
-- Each record represents one recommendation session based on a specific OceanScores entry
CREATE TABLE "StudentMajors" (
    "id" TEXT NOT NULL,
    "student_id" TEXT NOT NULL,
    "ocean_score_id" TEXT NOT NULL,
    "summary" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "StudentMajors_pkey" PRIMARY KEY ("id")
);

-- CreateTable: StudentMajorMatches (Individual Major Results)
-- Each record represents one major match result linked to a StudentMajors session
CREATE TABLE "StudentMajorMatches" (
    "id" TEXT NOT NULL,
    "student_major_id" TEXT NOT NULL,
    "major_name" TEXT NOT NULL,
    "match_percentage" DOUBLE PRECISION NOT NULL,
    "liked" BOOLEAN,
    "disliked" BOOLEAN,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "StudentMajorMatches_pkey" PRIMARY KEY ("id")
);

-- CreateIndex: StudentMajors indexes
CREATE INDEX "StudentMajors_student_id_idx" ON "StudentMajors"("student_id");
CREATE INDEX "StudentMajors_ocean_score_id_idx" ON "StudentMajors"("ocean_score_id");
CREATE UNIQUE INDEX "StudentMajors_ocean_score_id_key" ON "StudentMajors"("ocean_score_id");

-- CreateIndex: StudentMajorMatches indexes
CREATE INDEX "StudentMajorMatches_student_major_id_idx" ON "StudentMajorMatches"("student_major_id");
CREATE INDEX "StudentMajorMatches_match_percentage_idx" ON "StudentMajorMatches"("match_percentage");
CREATE UNIQUE INDEX "StudentMajorMatches_student_major_id_major_name_key" ON "StudentMajorMatches"("student_major_id", "major_name");

-- AddForeignKey: StudentMajors relationships
ALTER TABLE "StudentMajors" ADD CONSTRAINT "StudentMajors_student_id_fkey" FOREIGN KEY ("student_id") REFERENCES "Student"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "StudentMajors" ADD CONSTRAINT "StudentMajors_ocean_score_id_fkey" FOREIGN KEY ("ocean_score_id") REFERENCES "OceanScores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey: StudentMajorMatches relationships
ALTER TABLE "StudentMajorMatches" ADD CONSTRAINT "StudentMajorMatches_student_major_id_fkey" FOREIGN KEY ("student_major_id") REFERENCES "StudentMajors"("id") ON DELETE CASCADE ON UPDATE CASCADE;
