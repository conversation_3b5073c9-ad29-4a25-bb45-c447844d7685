-- CreateEnum
CREATE TYPE "CallStatus" AS ENUM ('INITIATED', 'IN_PROGRESS', 'COMPLETED', 'FAILED');

-- CreateTable
CREATE TABLE "CallRecord" (
    "id" TEXT NOT NULL,
    "student_id" TEXT NOT NULL,
    "student_workflow_id" TEXT NOT NULL,
    "twilio_call_sid" TEXT NOT NULL,
    "status" "CallStatus" NOT NULL DEFAULT 'INITIATED',
    "start_time" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "end_time" TIMESTAMP(3),
    "duration" INTEGER,
    "transcript" TEXT,
    "failure_reason" TEXT,
    "retry_count" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CallRecord_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CallRecord_twilio_call_sid_key" ON "CallRecord"("twilio_call_sid");

-- CreateIndex
CREATE INDEX "CallRecord_student_id_idx" ON "CallRecord"("student_id");

-- CreateIndex
CREATE INDEX "CallRecord_student_workflow_id_idx" ON "CallRecord"("student_workflow_id");

-- CreateIndex
CREATE INDEX "CallRecord_twilio_call_sid_idx" ON "CallRecord"("twilio_call_sid");

-- AddForeignKey
ALTER TABLE "CallRecord" ADD CONSTRAINT "CallRecord_student_id_fkey" FOREIGN KEY ("student_id") REFERENCES "Student"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CallRecord" ADD CONSTRAINT "CallRecord_student_workflow_id_fkey" FOREIGN KEY ("student_workflow_id") REFERENCES "StudentWorkflow"("id") ON DELETE CASCADE ON UPDATE CASCADE;
