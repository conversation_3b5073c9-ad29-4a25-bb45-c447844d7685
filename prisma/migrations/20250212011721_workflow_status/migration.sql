-- CreateEnum
CREATE TYPE "WorkflowStatus" AS ENUM ('INACTIVE', 'DRAFT', 'PUBLISHED');

-- CreateEnum
CREATE TYPE "StudentWorkflowStatus" AS ENUM ('IN_PROGRESS', 'NOT_STARTED', 'COMPLETED');

-- AlterTable
ALTER TABLE "StudentWorkflow" ADD COLUMN     "status" "StudentWorkflowStatus" NOT NULL DEFAULT 'NOT_STARTED';

-- AlterTable
ALTER TABLE "Workflow" ADD COLUMN     "owner_id" TEXT,
ADD COLUMN     "status" "WorkflowStatus" NOT NULL DEFAULT 'DRAFT';

-- AddForeignKey
ALTER TABLE "Workflow" ADD CONSTRAINT "Workflow_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
