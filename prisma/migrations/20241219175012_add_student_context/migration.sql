-- CreateTable
CREATE TABLE "StudentContext" (
    "id" TEXT NOT NULL,
    "data" JSONB NOT NULL,
    "message_id" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "StudentContext_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "StudentContext" ADD CONSTRAINT "StudentContext_message_id_fkey" FOREIGN KEY ("message_id") REFERENCES "messages"("id") ON DELETE CASCADE ON UPDATE CASCADE;
