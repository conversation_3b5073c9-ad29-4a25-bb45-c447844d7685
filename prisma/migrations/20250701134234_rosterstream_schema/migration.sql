/*
  Warnings:

  - A unique constraint covering the columns `[sourced_id]` on the table `User` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[username]` on the table `User` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "School" ADD COLUMN     "organization_id" TEXT;

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "date_last_modified" TIMESTAMP(3),
ADD COLUMN     "enabled_user" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "family_name" TEXT,
ADD COLUMN     "given_name" TEXT,
ADD COLUMN     "grades" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "metadata" JSONB,
ADD COLUMN     "middle_name_oneroster" TEXT,
ADD COLUMN     "oneroster_status" TEXT,
ADD COLUMN     "password" TEXT,
ADD COLUMN     "profile_sourced_ids" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "resource_sourced_ids" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "sourced_id" TEXT,
ADD COLUMN     "user_ids" JSONB,
ADD COLUMN     "user_master_identifier" TEXT,
ADD COLUMN     "username" TEXT;

-- CreateTable
CREATE TABLE "ExternalSystemMapping" (
    "id" TEXT NOT NULL,
    "internal_id" TEXT NOT NULL,
    "external_id" TEXT NOT NULL,
    "external_type" TEXT NOT NULL,
    "entity_type" TEXT NOT NULL,
    "channel_id" TEXT,
    "datasource_id" TEXT,
    "sourced_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ExternalSystemMapping_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Course" (
    "id" TEXT NOT NULL,
    "sourced_id" TEXT,
    "title" TEXT NOT NULL,
    "course_code" TEXT,
    "subject_area" TEXT,
    "grade_levels" TEXT[],
    "org_id" TEXT,
    "status" TEXT NOT NULL DEFAULT 'active',
    "date_last_modified" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Course_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SchoolClass" (
    "id" TEXT NOT NULL,
    "sourced_id" TEXT,
    "title" TEXT NOT NULL,
    "class_code" TEXT,
    "class_type" TEXT,
    "location" TEXT,
    "periods" TEXT[],
    "subjects" TEXT[],
    "course_id" TEXT,
    "school_id" TEXT,
    "status" TEXT NOT NULL DEFAULT 'active',
    "date_last_modified" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SchoolClass_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Enrollment" (
    "id" TEXT NOT NULL,
    "sourced_id" TEXT,
    "student_id" TEXT NOT NULL,
    "class_id" TEXT NOT NULL,
    "school_id" TEXT,
    "role" TEXT NOT NULL DEFAULT 'student',
    "primary" BOOLEAN NOT NULL DEFAULT false,
    "begin_date" TIMESTAMP(3),
    "end_date" TIMESTAMP(3),
    "status" TEXT NOT NULL DEFAULT 'active',
    "date_last_modified" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Enrollment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Organization" (
    "id" TEXT NOT NULL,
    "sourced_id" TEXT,
    "name" TEXT NOT NULL,
    "org_type" TEXT NOT NULL,
    "identifier" TEXT,
    "parent_id" TEXT,
    "status" TEXT NOT NULL DEFAULT 'active',
    "date_last_modified" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Organization_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RosterStreamWebhook" (
    "id" TEXT NOT NULL,
    "webhook_id" TEXT NOT NULL,
    "datasource_id" TEXT NOT NULL,
    "entity_type" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "sourced_id" TEXT,
    "processed" BOOLEAN NOT NULL DEFAULT false,
    "error_message" TEXT,
    "payload" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "RosterStreamWebhook_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ExternalSystemMapping_external_id_entity_type_idx" ON "ExternalSystemMapping"("external_id", "entity_type");

-- CreateIndex
CREATE INDEX "ExternalSystemMapping_internal_id_entity_type_idx" ON "ExternalSystemMapping"("internal_id", "entity_type");

-- CreateIndex
CREATE UNIQUE INDEX "ExternalSystemMapping_internal_id_external_id_entity_type_key" ON "ExternalSystemMapping"("internal_id", "external_id", "entity_type");

-- CreateIndex
CREATE UNIQUE INDEX "Course_sourced_id_key" ON "Course"("sourced_id");

-- CreateIndex
CREATE INDEX "Course_course_code_idx" ON "Course"("course_code");

-- CreateIndex
CREATE INDEX "Course_org_id_idx" ON "Course"("org_id");

-- CreateIndex
CREATE UNIQUE INDEX "SchoolClass_sourced_id_key" ON "SchoolClass"("sourced_id");

-- CreateIndex
CREATE INDEX "SchoolClass_class_code_idx" ON "SchoolClass"("class_code");

-- CreateIndex
CREATE INDEX "SchoolClass_course_id_idx" ON "SchoolClass"("course_id");

-- CreateIndex
CREATE INDEX "SchoolClass_school_id_idx" ON "SchoolClass"("school_id");

-- CreateIndex
CREATE UNIQUE INDEX "Enrollment_sourced_id_key" ON "Enrollment"("sourced_id");

-- CreateIndex
CREATE INDEX "Enrollment_student_id_idx" ON "Enrollment"("student_id");

-- CreateIndex
CREATE INDEX "Enrollment_class_id_idx" ON "Enrollment"("class_id");

-- CreateIndex
CREATE INDEX "Enrollment_school_id_idx" ON "Enrollment"("school_id");

-- CreateIndex
CREATE UNIQUE INDEX "Enrollment_student_id_class_id_key" ON "Enrollment"("student_id", "class_id");

-- CreateIndex
CREATE UNIQUE INDEX "Organization_sourced_id_key" ON "Organization"("sourced_id");

-- CreateIndex
CREATE INDEX "Organization_org_type_idx" ON "Organization"("org_type");

-- CreateIndex
CREATE INDEX "Organization_parent_id_idx" ON "Organization"("parent_id");

-- CreateIndex
CREATE UNIQUE INDEX "RosterStreamWebhook_webhook_id_key" ON "RosterStreamWebhook"("webhook_id");

-- CreateIndex
CREATE INDEX "RosterStreamWebhook_entity_type_action_idx" ON "RosterStreamWebhook"("entity_type", "action");

-- CreateIndex
CREATE INDEX "RosterStreamWebhook_datasource_id_idx" ON "RosterStreamWebhook"("datasource_id");

-- CreateIndex
CREATE INDEX "RosterStreamWebhook_processed_idx" ON "RosterStreamWebhook"("processed");

-- CreateIndex
CREATE UNIQUE INDEX "User_sourced_id_key" ON "User"("sourced_id");

-- CreateIndex
CREATE UNIQUE INDEX "User_username_key" ON "User"("username");

-- CreateIndex
CREATE INDEX "User_sourced_id_idx" ON "User"("sourced_id");

-- CreateIndex
CREATE INDEX "User_username_idx" ON "User"("username");

-- AddForeignKey
ALTER TABLE "School" ADD CONSTRAINT "School_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Course" ADD CONSTRAINT "Course_org_id_fkey" FOREIGN KEY ("org_id") REFERENCES "School"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SchoolClass" ADD CONSTRAINT "SchoolClass_course_id_fkey" FOREIGN KEY ("course_id") REFERENCES "Course"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SchoolClass" ADD CONSTRAINT "SchoolClass_school_id_fkey" FOREIGN KEY ("school_id") REFERENCES "School"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Enrollment" ADD CONSTRAINT "Enrollment_student_id_fkey" FOREIGN KEY ("student_id") REFERENCES "Student"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Enrollment" ADD CONSTRAINT "Enrollment_class_id_fkey" FOREIGN KEY ("class_id") REFERENCES "SchoolClass"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Enrollment" ADD CONSTRAINT "Enrollment_school_id_fkey" FOREIGN KEY ("school_id") REFERENCES "School"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Organization" ADD CONSTRAINT "Organization_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;
