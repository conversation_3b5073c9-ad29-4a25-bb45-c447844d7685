# Sister Test Plan: Successful Voice Call Tool Execution Without OpenAI Errors

## Research Summary

### Current Architecture Analysis

1. **RunnableWithMessageHistory Integration**: 
   - The `call_model.py` uses `RunnableWithMessageHistory` which calls our `get_history()` function
   - `RunnableWithMessageHistory` retrieves messages via the `message_history` placeholder with `n_history=2` limit
   - The actual OpenAI API call happens inside the LangChain chain, not directly in our code

2. **Current Validation Logic**:
   - `EmbeddedHistory._validate_tool_sequences()` exists but only validates **new messages being added**
   - `EmbeddedHistory.remove_orphaned_tool_msgs()` exists but has limited logic (only handles trailing tool messages)
   - Current validation doesn't prevent existing orphaned tool calls from being sent to OpenAI

3. **Tool Call Error Pattern**:
   - OpenAI rejects messages where an AI message with `tool_calls` is not followed by corresponding `ToolMessage` responses
   - Error occurs when: `AIMessage(tool_calls=[...])` → `HumanMessage` (no `ToolMessage` in between)

## Sister Test Design: `test_successful_voice_call_with_sanitization`

### Test Objective
Create an E2E test that demonstrates the **opposite** scenario where:
1. The same problematic message history exists (orphaned tool calls)
2. The system **prevents** the OpenAI error through message sanitization
3. The voice call tool executes successfully
4. No error logs are generated

### Test Structure

```python
async def test_successful_voice_call_with_sanitization(self):
    """Test that orphaned tool calls are sanitized before reaching OpenAI API"""
    
    # 1. Setup identical production messages (same as reproduction test)
    production_messages = self.create_exact_production_messages()
    
    # 2. Apply sanitization approach before agent processing
    # (Implementation will depend on chosen approach below)
    
    # 3. Trigger SMS agent with voice call request
    response = await sms_unstructured_agent(...)
    
    # 4. Verify successful voice call tool execution
    # 5. Verify NO error logs generated
    # 6. Verify user gets successful voice call response
```

## Implementation Approaches (Research-Based)

### Approach 1: **History Sanitization at Retrieval**

**Implementation Point**: Modify `EmbeddedHistory.get_messages()` method

**Pros**:
- ✅ Centralized solution - fixes the issue for all agent types
- ✅ No changes needed to LangChain integration
- ✅ Transparent to existing code
- ✅ Handles the problem at the source

**Cons**:
- ⚠️ Affects all message retrieval, not just problem cases

**Technical Implementation**:
```python
def get_messages(self, limit: int = 0) -> List[BaseMessage]:
    messages = # ... existing retrieval logic ...
    
    # Apply sanitization before returning
    if messages:
        messages = self._sanitize_orphaned_tool_calls(messages)
    
    return messages

def _sanitize_orphaned_tool_calls(self, messages: List[BaseMessage]) -> List[BaseMessage]:
    """Remove AI messages with orphaned tool calls or add synthetic tool responses"""
    # Implementation using Approach 2 from testing (add synthetic ToolMessage responses)
```

### Approach 2: **Custom History Wrapper for Agent Chain** ⭐ **RECOMMENDED**

**Implementation Point**: Create a wrapper around `get_history` specifically for `call_model.py`

**Pros**:
- ✅ **Targeted solution** - only affects agent chains where OpenAI API calls occur
- ✅ **Preserves original history** - doesn't modify underlying message storage
- ✅ **Easy to test and validate** - can be feature-flagged and A/B tested
- ✅ **Non-breaking** - existing history queries remain unchanged
- ✅ **Surgical precision** - only fixes the exact problem without side effects
- ✅ **Rollback-friendly** - can be easily disabled if issues arise
- ✅ **Explicit control** - clear about which code paths use sanitization

**Cons**:
- ⚠️ Requires changes to multiple agent files (call_model.py, student agent, counselor agent)
- ⚠️ Need to maintain wrapper function

**Technical Implementation**:

**Step 1: Create sanitization wrapper function**:
```python
# In new file: addie/history/sanitization.py

import os
import alog
from typing import List
from langchain_core.messages import BaseMessage, ToolMessage
from addie.history import get_history

def get_sanitized_history_for_agent(session_id: str, last_msg_id: int = 0):
    """
    Wrapper around get_history that sanitizes orphaned tool calls for agent chains.
    
    This function is specifically designed for use with RunnableWithMessageHistory
    to prevent OpenAI API errors caused by orphaned tool calls.
    
    Feature flag: ENABLE_TOOL_CALL_SANITIZATION (default: True)
    """
    history = get_history(session_id, last_msg_id)
    
    # Feature flag for easy rollback
    sanitization_enabled = os.getenv('ENABLE_TOOL_CALL_SANITIZATION', 'true').lower() == 'true'
    
    if not sanitization_enabled:
        alog.info(f"Tool call sanitization disabled via feature flag for session {session_id}")
        return history
    
    # Monkey patch the get_messages method to apply sanitization
    original_get_messages = history.get_messages
    
    def sanitized_get_messages(*args, **kwargs):
        messages = original_get_messages(*args, **kwargs)
        return _sanitize_orphaned_tool_calls_for_openai(messages, session_id)
    
    history.get_messages = sanitized_get_messages
    return history

def _sanitize_orphaned_tool_calls_for_openai(messages: List[BaseMessage], session_id: str) -> List[BaseMessage]:
    """
    Sanitize message history to prevent OpenAI API errors from orphaned tool calls.
    
    Adds synthetic ToolMessage responses for any AI messages with tool_calls
    that are not followed by corresponding tool responses.
    """
    result = []
    pending_tool_calls = {}
    
    for msg in messages:
        if hasattr(msg, 'tool_calls') and msg.tool_calls:
            # Track tool calls that need responses
            for tc in msg.tool_calls:
                pending_tool_calls[tc.get('id')] = tc.get('name', 'unknown_tool')
            result.append(msg)
        elif msg.type == 'tool':
            # Remove resolved tool calls from pending
            tool_call_id = getattr(msg, 'tool_call_id', None)
            if tool_call_id in pending_tool_calls:
                del pending_tool_calls[tool_call_id]
            result.append(msg)
        else:
            # Before adding human/system message, resolve any pending tool calls
            for tool_call_id, tool_name in pending_tool_calls.items():
                synthetic_tool_msg = ToolMessage(
                    content="Tool execution completed successfully",
                    tool_call_id=tool_call_id,
                    name=tool_name
                )
                result.append(synthetic_tool_msg)
                alog.info(f"Sanitization: Added synthetic ToolMessage for {tool_call_id} in session {session_id}")
            pending_tool_calls.clear()
            result.append(msg)
    
    return result
```

**Step 2: Update call_model.py to use sanitized history**:
```python
# In addie/agent/call_model.py

from addie.history.sanitization import get_sanitized_history_for_agent

# Replace line 170:
# get_history,
# With:
get_sanitized_history_for_agent,

chain_with_history = RunnableWithMessageHistory(
    chain,
    get_sanitized_history_for_agent,  # Use sanitized wrapper
    input_messages_key="question",
    history_messages_key="message_history",
    name=session_id,
)
```

**Step 3: Update other agent files**:
- `addie/student_agent/invoke_student.py`
- `addie/counselor_agent/invoke.py`

### Approach 3: **Database-Level Cleanup**

**Implementation Point**: Clean up orphaned records in the messages table before agent processing

**Pros**:
- ✅ Fixes the data at the source
- ✅ Permanent solution

**Cons**:
- ❌ Modifies historical data (potentially problematic for auditing)
- ❌ Complex to implement safely
- ❌ Could affect other systems using the same data

## Recommended Sanitization Strategy

Based on testing, **Approach 2 from the sanitization tests** (adding synthetic `ToolMessage` responses) is optimal:

```python
def _sanitize_orphaned_tool_calls(self, messages: List[BaseMessage]) -> List[BaseMessage]:
    result = []
    pending_tool_calls = {}
    
    for msg in messages:
        if hasattr(msg, 'tool_calls') and msg.tool_calls:
            # Track tool calls that need responses
            for tc in msg.tool_calls:
                pending_tool_calls[tc.get('id')] = tc.get('name', 'unknown_tool')
            result.append(msg)
        elif msg.type == 'tool':
            # Remove resolved tool calls from pending
            tool_call_id = getattr(msg, 'tool_call_id', None)
            if tool_call_id in pending_tool_calls:
                del pending_tool_calls[tool_call_id]
            result.append(msg)
        else:
            # Before adding human/system message, resolve any pending tool calls
            for tool_call_id, tool_name in pending_tool_calls.items():
                synthetic_tool_msg = ToolMessage(
                    content=f"Tool execution completed successfully",
                    tool_call_id=tool_call_id,
                    name=tool_name
                )
                result.append(synthetic_tool_msg)
            pending_tool_calls.clear()
            result.append(msg)
    
    return result
```

**Why this approach**:
- ✅ Maintains conversation context
- ✅ Satisfies OpenAI's tool calling requirements
- ✅ Doesn't lose information
- ✅ Makes the conversation history "complete"

## Test Implementation Plan (Approach 2)

### Phase 1: Create Sanitization Wrapper
1. **Create new file** `addie/history/sanitization.py` with:
   - `get_sanitized_history_for_agent()` wrapper function
   - `_sanitize_orphaned_tool_calls_for_openai()` core sanitization logic
   - Comprehensive logging for debugging and monitoring

2. **Update call_model.py** to use the wrapper:
   - Import the new sanitization function
   - Replace `get_history` with `get_sanitized_history_for_agent` in `RunnableWithMessageHistory`
   - Add feature flag support for easy rollback

3. **Optional: Update other agent files** (student_agent, counselor_agent) for consistency

### Phase 2: Sister Test Creation
1. **Copy test structure** from `test_chinnno15_e2e_reproduction.py`
2. **Create new test method** `test_successful_voice_call_with_sanitization()`
3. **Key test differences**:
   - Use the **same** problematic production messages
   - Enable sanitization by using the new wrapper (or feature flag)
   - Mock voice call tool to return success
   - Capture logs to verify **NO** OpenAI API errors occur

4. **Add comprehensive assertions**:
   - ✅ Voice call tool executes successfully (mocked)
   - ✅ No OpenAI API error logs generated  
   - ✅ No SMS agent error logs generated
   - ✅ User receives successful voice call response
   - ✅ Message history contains synthetic `ToolMessage` responses
   - ✅ Agent processes request without errors

### Phase 3: Validation & Testing
1. **Dual test validation**:
   - Original reproduction test: **MUST FAIL** (reproduces error)
   - Sister test with sanitization: **MUST PASS** (prevents error)
   - Both tests use identical production message data

2. **Feature flag testing**:
   - Test with sanitization **enabled** (sister test passes)
   - Test with sanitization **disabled** (reproduction test fails)
   - Verify easy rollback capability

3. **Performance & Integration testing**:
   - Measure sanitization overhead (should be minimal)
   - Test with various message history sizes
   - Verify no side effects on other system components
   - Test edge cases (multiple orphaned tool calls, nested scenarios)

## Expected Test Outcomes

**Sister Test Success Criteria**:
- ✅ Voice call tool executes successfully (mocked)
- ✅ No OpenAI API errors in logs
- ✅ User receives successful voice call response
- ✅ Message history shows synthetic `ToolMessage` responses were added
- ✅ No SMS agent error logging occurs

**Validation that Fix Works**:
- Original reproduction test: **FAILS** (reproduces OpenAI error)
- Sister test with sanitization: **PASSES** (prevents OpenAI error)

This approach provides a comprehensive solution that prevents the production error while maintaining full conversation context and history integrity.