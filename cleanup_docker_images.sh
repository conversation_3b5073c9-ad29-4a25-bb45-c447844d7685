#!/bin/bash

# Set the script to exit on error
set -e

echo "=== Docker Cleanup Script ===" 
echo "This script will remove all Docker images except qdrant, redis, and postgres"

# List current running containers
echo -e "\n=== Currently Running Containers ===" 
docker ps

# Keep track of images to keep
echo -e "\n=== Images to keep ===" 
echo "- qdrant/qdrant (any tag)"
echo "- redis (any tag)"
echo "- postgres (any tag)"

# Get all dangling images (untagged images)
echo -e "\n=== Removing dangling images (untagged) ===" 
docker image prune -f

# Get all image IDs except qdrant, postgres, and redis
echo -e "\n=== Removing all images except qdrant, redis, and postgres ===" 
docker images --format "{{.Repository}}:{{.Tag}}" | grep -v -E 'qdrant|redis|postgres' | xargs -r docker rmi -f

# Show remaining images
echo -e "\n=== Remaining Docker Images ===" 
docker images

echo -e "\n=== Cleanup complete ===" 
echo "Only qdrant, redis, and postgres images should remain. If you see other images, they might be referenced by containers or still in use."
