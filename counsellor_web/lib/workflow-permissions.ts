import { UserRole } from "@prisma/client";

export interface WorkflowPermissions {
  canEdit: boolean;
  canDelete: boolean;
  canPublish: boolean;
  canUnpublish: boolean;
  restrictionReason?: string;
}

/**
 * Check if a user has permission to perform actions on a workflow
 * @param userRole - The role of the current user
 * @param userId - The ID of the current user
 * @param workflowOwnerId - The ID of the workflow owner
 * @returns Object with permission flags and restriction reason
 */
export function checkWorkflowPermissions(
  userRole: UserRole | string,
  userId: string,
  workflowOwnerId?: string | null,
): WorkflowPermissions {
  // Admin users can do everything
  if (userRole === UserRole.ADMIN) {
    return {
      canEdit: true,
      canDelete: true,
      canPublish: true,
      canUnpublish: true,
    };
  }

  // For counselors, check ownership
  if (userRole === UserRole.COUNSELOR) {
    const isOwner = workflowOwnerId === userId;

    if (isOwner) {
      return {
        canEdit: true,
        canDelete: true,
        canPublish: true,
        canUnpublish: true,
      };
    } else {
      return {
        canEdit: false,
        canDelete: false,
        canPublish: false,
        canUnpublish: false,
        restrictionReason:
          "Only the creator of this conversation can perform this action.",
      };
    }
  }

  // Default: no permissions for other roles
  return {
    canEdit: false,
    canDelete: false,
    canPublish: false,
    canUnpublish: false,
    restrictionReason: "You do not have permission to perform this action.",
  };
}

/**
 * Check if a user can perform a specific action on a workflow
 * @param action - The action to check
 * @param userRole - The role of the current user
 * @param userId - The ID of the current user
 * @param workflowOwnerId - The ID of the workflow owner
 * @returns true if the user can perform the action, false otherwise
 */
export function canPerformWorkflowAction(
  action: "edit" | "delete" | "publish" | "unpublish",
  userRole: UserRole | string,
  userId: string,
  workflowOwnerId?: string | null,
): boolean {
  const permissions = checkWorkflowPermissions(
    userRole,
    userId,
    workflowOwnerId,
  );

  switch (action) {
    case "edit":
      return permissions.canEdit;
    case "delete":
      return permissions.canDelete;
    case "publish":
      return permissions.canPublish;
    case "unpublish":
      return permissions.canUnpublish;
    default:
      return false;
  }
}
