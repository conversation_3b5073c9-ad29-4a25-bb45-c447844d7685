---
title: Config Files
description: Make it your own with config files.
---

The `config` folder houses various files ready for customization to suit your needs.

## List of files

Here's the list of config files available:

<Steps>

### Landing

Within `landing.ts`, discover data configurations for different sections on the homepage.

Explore all homepage components in the `components/sections` folder.

### Navigations

`marketing.ts`, `dashboard.ts` and `docs.ts` contain configuration links tailored for their respective sections.

### Site

In `site.ts`, manage metadata and footer links effortlessly.

### Subscriptions

`subscriptions.ts` holds all necessary data for configuring pricing cards.

Here's the structure for a pricing card:

```js
{
  title: 'Pro',
  description: 'Unlock Advanced Features',
  benefits: [
    'Up to 500 monthly posts',
    'Advanced analytics and reporting',
    'Access to business templates',
    'Priority customer support',
    'Exclusive webinars and training.',
  ],
  limitations: [
    'No custom branding',
    'Limited access to business resources.',
  ],
  prices: {
    monthly: 15,
    yearly: 144,
  },
  stripeIds: {
    monthly: env.NEXT_PUBLIC_STRIPE_PRO_MONTHLY_PLAN_ID,
    yearly: env.NEXT_PUBLIC_STRIPE_PRO_YEARLY_PLAN_ID,
  },
},
```

</Steps>
