---
title: Email
description: How to manage emails in this project.
---

<Callout type="success" twClass="mt-0">
  The magic-link feature with Resend works with Auth.js v5! <br />
  You can use it in your local environment and in your own production setup.
</Callout>

<Callout type="warning" twClass="mt-2.5">
  However, you can't test it on this demo app because I don't want to use the
  premium Resend plan for this demo.
</Callout>

## Steps

<Callout type="note">
  The email part is similar at the [resend](https://resend.com/) documentation.
  You can find the official documentation
  [here](https://authjs.dev/getting-started/installation#setup-environment) if
  you want.
</Callout>

<Steps>

### Create an account

If don't have an account on Resend, just follow their steps after signup [here](https://resend.com/signup).

### Create an API key

After signin on Resend, he propurse you to create your first API key.

Copy/paste in your `.env` file.

```js
RESEND_API_KEY = re_your_resend_api_key;
```

</Steps>

{/* 
react-email
> [!WARNING]  
> You need update `.react-email` folder before use `pnpm run email`. Check the link [here](https://github.com/resend/react-email/issues/868#issuecomment-**********) if you have the error : `renderToReadableStream not found`

*/}