---
title: Subscriptions
description: How to manage subscriptions with <PERSON><PERSON>.
---

<Callout type="warning" twClass="mt-0">
  To complete this guide, you'll need to create an account on
  [Stripe](https://stripe.com/).
</Callout>

## Configuration

<Steps>

### Create a Project

After logging in, create a project on Stripe.

### Obtain Stripe API Key

In "Developer" mode, go to "API Keys" and copy the secret key.

Paste it into your `.env` file.

```js
STRIPE_API_KEY = sk_your_secret_key
```

### Set up Stripe Webhook

Create a webhook to manage Stripe events within your app.

1. **Create Webhook Endpoint:** Go to your Stripe dashboard and navigate to "Developers" > "Webhooks". Click on "Add endpoint" to create a new webhook.
2. **Configure Webhook:** Enter the endpoint URL where Stripe will send events. This URL should point to a route in your application that can handle Stripe webhook events.

3. **Select Events:** Choose the events you want to receive notifications for.
   Common events include `checkout.session.completed`, `customer.subscription.updated`, `invoice.payment_succeeded`, etc.

4. **Obtain Webhook Signing Secret:** After setting up the webhook, <PERSON><PERSON> will generate a signing secret. Copy this secret and paste it into your `.env` file as `STRIPE_WEBHOOK_SECRET`.

```js
STRIPE_WEBHOOK_SECRET = whsec_your_secret_webhook
```

### Create Price Cards

Create price cards to obtain price IDs for both monthly and yearly plans.

1. **Navigate to Products:** In your Stripe dashboard, go to "Products" > "Create Product". Enter the details for your subscription product, such as name, description, and pricing.

2. **Create Pricing Plans:** Once the product is created, go to the "Pricing" section and click on "Add pricing plan". Set up the details for your pricing plan, including the amount, currency, billing interval (monthly or yearly), and any other relevant information.

3. **Obtain Price IDs:** After creating the pricing plans, Stripe will generate unique price IDs for each plan. These IDs are used to identify the specific pricing plan when creating subscriptions. Copy the price IDs for both the monthly and yearly plans and paste them into your `.env` file as follows:

```js
NEXT_PUBLIC_STRIPE_PRO_MONTHLY_PLAN_ID = price_FaKeId
NEXT_PUBLIC_STRIPE_PRO_YEARLY_PLAN_ID = price_FaKeId

NEXT_PUBLIC_STRIPE_BUSINESS_MONTHLY_PLAN_ID = price_FaKeId
NEXT_PUBLIC_STRIPE_BUSINESS_YEARLY_PLAN_ID = price_FaKeId
```

Ensure that you replace `price_FaKeId` with the actual price IDs generated by Stripe for your pricing plans.

<Callout type="warning">
  Don't forget to change the prices in `config/subscriptions.ts`.
</Callout>

</Steps>
