---
title: Blog
description: How works blog with categories and authors.
---

Blog posts can have multiple categories, authors and related posts.

## Authors

### Create one author

Blog posts can have **one or multiple authors**. <br /> Add a new object with **name, image url and twitter handle** to add a new author to your blog.

```ts title="config/blog.ts" {7-11}
export const BLOG_AUTHORS = {
  mickasmt: {
    name: "mickasmt",
    image: "/_static/avatars/mickasmt.png",
    twitter: "miickasmt",
  },
  newauthor: {
    name: "shadcn",
    image: "/_static/avatars/shadcn.jpeg",
    twitter: "shadcn",
  },
};
```

### Add in a blog post

Add your new author in your blog post like that :

```mdx {6-8}
---
title: Deploying Next.js Apps
description: How to deploy your Next.js apps on Vercel.
image: /_static/blog/blog-post-3.jpg
date: "2023-01-02"
authors:
  - newauthor
  - mickasmt
categories:
  - news
related:
  - server-client-components
  - preview-mode-headless-cms
---
```

## Categories

### Create one category

Add a new object and a slug to add a new category to your blog.

```ts title="config/blog.ts" {3,6-10}
export const BLOG_CATEGORIES: {
  title: string;
  slug: "news" | "education";
  description: string;
}[] = [
  {
    title: "News",
    slug: "news",
    description: "Updates and announcements from Next SaaS Starter.",
  },
  {
    title: "Education",
    slug: "education",
    description: "Educational content about SaaS management.",
  },
];
```

### Add in a blog post

Add your new author in your blog post like that :

```mdx {9-10}
---
title: Deploying Next.js Apps
description: How to deploy your Next.js apps on Vercel.
image: /_static/blog/blog-post-3.jpg
date: "2023-01-02"
authors:
  - newauthor
  - mickasmt
categories:
  - news
related:
  - server-client-components
  - preview-mode-headless-cms
---
```

<Callout type="warning">
  The blog post can belong to multiple categories, but currently, only the first
  category in the list is being displayed.
</Callout>

## Related posts

Each blog post can have a list of related posts. <br/> Get the filenames of the blog posts that you want and remove the `.mdx` or `.md`. That's all!

```mdx {11-13}
---
title: Deploying Next.js Apps
description: How to deploy your Next.js apps on Vercel.
image: /_static/blog/blog-post-3.jpg
date: "2023-01-02"
authors:
  - newauthor
  - mickasmt
categories:
  - news
related:
  - server-client-components
  - preview-mode-headless-cms
---
```
