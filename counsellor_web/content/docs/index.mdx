---
title: Introduction
description: Welcome to the Next SaaS Stripe Starter documentation.
---

Next SaaS Stripe Starter is <b>Open Source Boilerplate</b>.

Built on the [Taxonomy](https://github.com/shadcn-ui/taxonomy) app by shadcn, it integrates Next.js 14, Prisma, Neon, Auth.js v5, Resend, React Email, Shadcn/ui, and Stripe.

With Next SaaS Stripe Starter, you get a solid foundation for your SaaS journey.

This documentation is your go-to resource for configuring and using the starter effectively.

Let's get started and happy coding!
