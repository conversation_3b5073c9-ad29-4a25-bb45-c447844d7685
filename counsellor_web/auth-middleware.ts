// This file provides a version of the auth module that's safe to use in middleware
// without any Prisma dependencies that would cause Edge Runtime compatibility issues

import { NextAuthRequest } from "next-auth/lib";
import { jwtVerify, importJWK } from "jose";

// JWT verification without Prisma
export async function verifyJwt(token: string, secret: string) {
  try {
    const key = await importJWK({ kty: 'oct', k: secret }, 'HS256');
    const { payload } = await jwtVerify(token, key);
    return payload;
  } catch {
    return null;
  }
}

// Edge-compatible auth middleware
export async function middleware(request: NextAuthRequest) {
  // The auth logic is already handled by the Next.js Auth middleware
  // This is just a placeholder to ensure we have a middleware export that
  // doesn't rely on Prisma
}
