"use server";

import { UserRole } from "@prisma/client";
import { prisma } from "@/common/db";
import { DashboardStudent } from "@/common/types";

interface FetchDashboardStudentsParams {
  grade?: string | null | undefined;
  counselorId?: string | null | undefined;
  search?: string | null | undefined;
  user: { role: UserRole; id: string };
  schoolId?: string | null | undefined;
}

/**
 * Fetches students from the materialized view with all needed information
 * This is an optimized version that uses the materialized view for faster queries
 *
 * @param params - Filter parameters including grade, counselorId, search, user role, and schoolId
 * @returns Array of DashboardStudent objects
 */
export const fetchDashboardStudents = async (
  params: FetchDashboardStudentsParams,
): Promise<DashboardStudent[]> => {
  // Get counselor's school if not provided and user is a counselor
  let schoolId = params.schoolId;
  if (!schoolId && params.user?.role === UserRole.COUNSELOR && params.user?.id) {
    const counselor = await prisma.counselor.findUnique({
      where: { user_id: params.user.id },
      select: { school_id: true }
    });
    if (counselor?.school_id) {
      schoolId = counselor.school_id;
    }
  }

  // Query the materialized view and then filter by enabled status in code
  // This approach is more reliable than JOIN since materialized view structure is uncertain
  const query = `
    SELECT
      id, student_id, grade, created_at, updated_at, school_id,
      first_name, last_name, email, phone_number, is_profile_complete,
      onboarding_answered_count, onboarding_total_questions
    FROM counsellor_dashboard_view
    WHERE 1=1
    ${params.grade
      ? `AND grade = ${parseInt(params.grade.replace("Grade ", ""))}`
      : ""}
    ${schoolId
      ? `AND school_id = '${schoolId}'`
      : ""}
    ${params.counselorId && params.user?.role === UserRole.COUNSELOR
      ? `AND counselor_ids::jsonb @> '["${params.counselorId}"]'::jsonb`
      : ""}
    ${params.search
      ? `AND (
          student_id ILIKE '%${params.search}%' OR
          first_name ILIKE '%${params.search}%' OR
          last_name ILIKE '%${params.search}%'
        )`
      : ""}
    ORDER BY updated_at DESC
  `;

  // Execute the raw query to get all student data from the materialized view
  const results = await prisma.$queryRawUnsafe(query);

  // Convert to known type right away to avoid TypeScript errors
  const typedResults = Array.isArray(results) ? results as any[] : [];

  if (typedResults.length === 0) {
    return [];
  }

  // Filter out disabled users by getting all email addresses and checking enabled status in batch
  const emails = typedResults.map(student => student.email).filter(Boolean);

  if (emails.length === 0) {
    // No emails to check, return all students
    return typedResults.map(student => ({
      id: student.id,
      student_id: student.student_id,
      grade: student.grade,
      created_at: student.created_at,
      updated_at: student.updated_at,
      first_name: student.first_name,
      last_name: student.last_name,
      email: student.email,
      phone_number: student.phone_number,
      isProfileComplete: student.is_profile_complete || false,
      onboardingAnsweredCount: parseInt(student.onboarding_answered_count || "0"),
      onboardingTotalQuestions: parseInt(student.onboarding_total_questions || "0"),
    })) as DashboardStudent[];
  }

  // Get disabled users in a single query
  const disabledUsers = await prisma.user.findMany({
    where: {
      email: { in: emails },
      enabled: false  // Only get explicitly disabled users
    },
    select: { email: true }
  });

  const disabledEmails = new Set(disabledUsers.map(user => user.email));

  // Filter out students whose users are explicitly disabled
  const enabledStudents = typedResults.filter(student =>
    !student.email || !disabledEmails.has(student.email)
  );

  // Map the results to the expected format
  return enabledStudents.map(student => ({
    id: student.id,
    student_id: student.student_id,
    grade: student.grade,
    created_at: student.created_at,
    updated_at: student.updated_at,
    first_name: student.first_name,
    last_name: student.last_name,
    email: student.email,
    phone_number: student.phone_number,
    isProfileComplete: student.is_profile_complete || false,
    onboardingAnsweredCount: parseInt(student.onboarding_answered_count || "0"),
    onboardingTotalQuestions: parseInt(student.onboarding_total_questions || "0"),
  })) as DashboardStudent[];
};

