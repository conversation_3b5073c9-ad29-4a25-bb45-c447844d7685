"use server";

import { revalidatePath } from "next/cache";
import { auth } from "@/auth";

import { prisma } from "@/common/db";
import { splitName } from "@/common/prisma-auth-adapter";
import { userNameSchema } from "@/common/validations/user";

export type FormData = {
  name: string;
};

export async function updateUserName(userId: string, formData: FormData) {
  try {
    const session = await auth();

    if (!session?.user || session?.user.id !== userId) {
      throw new Error("Unauthorized");
    }

    const { name } = userNameSchema.parse(formData);
    const data = {};
    splitName(name, data);

    await prisma.user.update({
      where: {
        id: userId,
      },
      data,
    });

    revalidatePath("/dashboard/settings");
    return { status: "success" };
  } catch (error) {
    // console.log(error)
    return { status: "error" };
  }
}
