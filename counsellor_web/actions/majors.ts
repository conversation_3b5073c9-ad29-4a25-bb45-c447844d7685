"use server";

import { revalidatePath } from "next/cache";
import { EditAction, MajorSource } from "@prisma/client";

import { prisma } from "@/common/db";
import { getCurrentUser } from "@/common/session";

/**
 * Helper function to create edit history record and update edit count
 */
async function createEditHistory(
  majorId: string,
  action: EditAction,
  oldValue: string | null,
  newValue: string | null,
  userId: string,
  metadata?: any,
) {
  await prisma.$transaction([
    // Create edit history record
    prisma.studentMajorEditHistory.create({
      data: {
        student_major_match_id: majorId,
        action,
        old_value: oldValue,
        new_value: newValue,
        user_type: "COUNSELOR",
        user_id: userId,
        metadata: metadata || null,
      },
    }),
    // Increment edit count and update last_edited_at
    prisma.studentMajorMatches.update({
      where: { id: majorId },
      data: {
        edit_count: { increment: 1 },
        last_edited_at: new Date(),
      },
    }),
  ]);
}

/**
 * Update a major's source to COUNSELOR_ADDED when counselor selects it from dropdown
 */
export async function updateMajorSource(majorId: string) {
  const currentUser = await getCurrentUser();
  if (!currentUser) {
    return { success: false, error: "Unauthorized" };
  }

  try {
    // Get the major to be updated
    const majorToUpdate = await prisma.studentMajorMatches.findUnique({
      where: { id: majorId },
    });

    if (!majorToUpdate) {
      return { success: false, error: "Major not found" };
    }

    // Check if this major already exists as non-deleted in the student's list
    const existingMajor = await prisma.studentMajorMatches.findFirst({
      where: {
        student_major_id: majorToUpdate.student_major_id,
        major_name: {
          equals: majorToUpdate.major_name,
          mode: "insensitive",
        },
        deleted: false,
        id: { not: majorId }, // Exclude the current major being updated
      },
    });

    if (existingMajor) {
      return { success: false, error: "Major already exists!" };
    }

    await prisma.studentMajorMatches.update({
      where: {
        id: majorId,
      },
      data: {
        source: MajorSource.COUNSELOR_ADDED,
        deleted: false, // Ensure it's not deleted when counselor adds it
      },
    });

    // Create edit history record
    await createEditHistory(
      majorId,
      EditAction.ADDED,
      majorToUpdate.source,
      MajorSource.COUNSELOR_ADDED,
      currentUser.id,
      { action_type: "source_update", major_name: majorToUpdate.major_name },
    );

    // Revalidate the page to show updated data
    revalidatePath("/students");

    return { success: true };
  } catch (error) {
    console.error("Error updating major source:", error);
    return { success: false, error: "Failed to update major" };
  }
}

/**
 * Create a new custom major with COUNSELOR_ADDED source
 */
export async function createCustomMajor(
  studentMajorId: string,
  majorName: string,
  summary: string,
) {
  const currentUser = await getCurrentUser();
  if (!currentUser) {
    return { success: false, error: "Unauthorized" };
  }

  try {
    // Check if major already exists (including deleted ones)
    const existingMajor = await prisma.studentMajorMatches.findFirst({
      where: {
        student_major_id: studentMajorId,
        major_name: {
          equals: majorName,
          mode: "insensitive",
        },
      },
    });

    if (existingMajor) {
      if (!existingMajor.deleted) {
        // Major exists and is not deleted
        return { success: false, error: "Major already exists!" };
      } else {
        // Major exists but is deleted - restore it
        await prisma.studentMajorMatches.update({
          where: { id: existingMajor.id },
          data: {
            deleted: false,
            deleted_by: null,
            summary: summary, // Update summary with new value
            source: MajorSource.COUNSELOR_ADDED,
            liked: null,
            disliked: null,
          },
        });

        // Create edit history record for restoration
        await createEditHistory(
          existingMajor.id,
          EditAction.ADDED,
          "deleted",
          "restored",
          currentUser.id,
          { action_type: "major_restoration", major_name: majorName, summary },
        );

        revalidatePath("/students");

        return { success: true };
      }
    }

    // Major doesn't exist - create new one
    const newMajor = await prisma.studentMajorMatches.create({
      data: {
        student_major_id: studentMajorId,
        major_name: majorName,
        match_percentage: 0, // Default match percentage for custom majors
        summary,
        source: MajorSource.COUNSELOR_ADDED,
        liked: null,
        disliked: null,
        deleted: false,
        deleted_by: null,
      },
    });

    // Create edit history record for new major creation
    await createEditHistory(
      newMajor.id,
      EditAction.ADDED,
      null,
      majorName,
      currentUser.id,
      { action_type: "custom_major_creation", summary },
    );

    revalidatePath("/students");

    return { success: true };
  } catch (error) {
    console.error("Error creating custom major:", error);
    return { success: false, error: "Failed to create major" };
  }
}

/**
 * Soft delete a major (counselor can only delete COUNSELOR_ADDED majors)
 */
export async function deleteMajor(majorId: string) {
  const currentUser = await getCurrentUser();
  if (!currentUser) {
    return { success: false, error: "Unauthorized" };
  }

  try {
    // Get the major to check if it can be deleted
    const major = await prisma.studentMajorMatches.findUnique({
      where: { id: majorId },
      select: { source: true, major_name: true },
    });

    if (!major) {
      return { success: false, error: "Major not found" };
    }

    // Only allow deletion of COUNSELOR_ADDED majors
    if (major.source === MajorSource.ADDIE) {
      return {
        success: false,
        error: "ADDIE recommended majors cannot be deleted",
      };
    }

    await prisma.studentMajorMatches.update({
      where: { id: majorId },
      data: {
        deleted: true,
        deleted_by: currentUser.id,
      },
    });

    // Create edit history record for deletion
    await createEditHistory(
      majorId,
      EditAction.REMOVED,
      major.major_name,
      null,
      currentUser.id,
      { action_type: "major_deletion", source: major.source },
    );

    revalidatePath("/students");

    return { success: true };
  } catch (error) {
    console.error("Error deleting major:", error);
    return { success: false, error: "Failed to delete major" };
  }
}

/**
 * Update major like/dislike status with proper toggle and mutual exclusion logic
 */
export async function updateMajorLikeStatus(
  majorId: string,
  action: "like" | "dislike",
) {
  const currentUser = await getCurrentUser();
  if (!currentUser) {
    return { success: false, error: "Unauthorized" };
  }

  try {
    // Get current major status
    const currentMajor = await prisma.studentMajorMatches.findUnique({
      where: { id: majorId },
      select: { liked: true, disliked: true, major_name: true },
    });

    if (!currentMajor) {
      return { success: false, error: "Major not found" };
    }

    let newLiked: boolean | null = null;
    let newDisliked: boolean | null = null;
    let editAction: EditAction;
    let oldValue: string | null = null;
    let newValue: string | null = null;

    if (action === "like") {
      // If already liked, toggle off (unlike)
      if (currentMajor.liked === true) {
        newLiked = null;
        newDisliked = null;
        editAction = EditAction.LIKED;
        oldValue = "liked";
        newValue = null;
      } else {
        // Like and remove dislike if present
        newLiked = true;
        newDisliked = null;
        editAction = EditAction.LIKED;
        oldValue = currentMajor.disliked ? "disliked" : null;
        newValue = "liked";
      }
    } else if (action === "dislike") {
      // If already disliked, toggle off (un-dislike)
      if (currentMajor.disliked === true) {
        newLiked = null;
        newDisliked = null;
        editAction = EditAction.DISLIKED;
        oldValue = "disliked";
        newValue = null;
      } else {
        // Dislike and remove like if present
        newLiked = null;
        newDisliked = true;
        editAction = EditAction.DISLIKED;
        oldValue = currentMajor.liked ? "liked" : null;
        newValue = "disliked";
      }
    }

    await prisma.studentMajorMatches.update({
      where: { id: majorId },
      data: {
        liked: newLiked,
        disliked: newDisliked,
      },
    });

    // Create edit history record for like/dislike action
    await createEditHistory(
      majorId,
      editAction!,
      oldValue,
      newValue,
      currentUser.id,
      {
        action_type: "like_dislike_toggle",
        major_name: currentMajor.major_name,
      },
    );

    revalidatePath("/students");

    // Return appropriate message based on final state
    let message = "";
    if (newLiked === true) {
      message = `${currentMajor.major_name} liked!`;
    } else if (newDisliked === true) {
      message = `${currentMajor.major_name} disliked!`;
    } else {
      // Both are null, so we removed the previous state
      const previousAction = currentMajor.liked ? "like" : "dislike";
      message = `${currentMajor.major_name} ${previousAction} removed!`;
    }

    return { success: true, message };
  } catch (error) {
    console.error("Error updating major like status:", error);
    return { success: false, error: "Failed to update major status" };
  }
}
