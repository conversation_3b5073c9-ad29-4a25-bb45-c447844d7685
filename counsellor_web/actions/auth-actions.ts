"use server";

import { auth } from "@/auth";
import { ensureCounselorRecord } from "@/common/user";

/**
 * Server action to perform post-authentication tasks like ensuring counselor record exists
 * Called after successful login
 */
export async function handlePostSignIn() {
  const session = await auth();
  
  if (!session?.user?.id) {
    return { success: false, error: "Unauthorized" };
  }
  
  try {
    // Create/ensure counselor record if needed
    await ensureCounselorRecord(session.user.id);
    return { success: true };
  } catch (error) {
    console.error("Error in handlePostSignIn server action:", error);
    return { success: false, error: "Failed to process sign-in" };
  }
}
