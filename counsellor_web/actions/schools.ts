"use server";

import { auth } from "@/auth";
import { prisma } from "@/common/db";

/**
 * Get all schools
 */
export async function getAllSchools() {
  try {
    const session = await auth();
    
    if (!session || !session.user) {
      return { error: "Unauthorized" };
    }

    // Only ADMIN users can access the list of all schools
    if (session.user.role !== "ADMIN") {
      return { error: "Unauthorized: Admin access required" };
    }
    
    const schools = await prisma.school.findMany({
      orderBy: { name: 'asc' }
    });
    
    return { schools };
  } catch (error) {
    console.error("Error fetching schools:", error);
    return { error: "Failed to fetch schools" };
  }
}

/**
 * Get counselor's school
 */
export async function getCounselorSchool() {
  try {
    const session = await auth();
    
    if (!session || !session.user) {
      return { error: "Unauthorized" };
    }
    
    // Find the counselor record for this user
    const counselor = await prisma.counselor.findUnique({
      where: { user_id: session.user.id },
      include: { school: true }
    });

    return { school: counselor?.school || null };
  } catch (error) {
    console.error("Error fetching counselor school:", error);
    return { error: "Failed to fetch school information" };
  }
}
