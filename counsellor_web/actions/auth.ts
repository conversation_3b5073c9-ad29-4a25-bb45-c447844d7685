"use server";

import { auth } from "@/auth";

// Note: Server actions can't directly use client-side functions like next-auth's signOut
// This function will handle server-side redirects instead

/**
 * Check if the current user's email matches the provided email
 * @param email - Email to compare with current user
 * @returns Object indicating if user is logged in and if emails match
 */
export async function checkUserEmail(email: string | null) {
  if (!email) return { isLoggedIn: false, emailsMatch: false };
  
  try {
    const session = await auth();
    
    if (!session || !session.user) {
      return { isLoggedIn: false, emailsMatch: false };
    }
    
    return { 
      isLoggedIn: true, 
      emailsMatch: session.user.email?.toLowerCase() === email.toLowerCase() 
    };
  } catch (error) {
    console.error("Error checking user email:", error);
    return { isLoggedIn: false, emailsMatch: false };
  }
}

/**
 * Sign out the current user by redirecting to the logout page
 * This also clears localStorage items including the active school selection
 */
export async function signOutUser() {
  // The server action returns a special URL that will trigger client-side cleanup
  // before redirecting to the login page
  return { redirect: "/logout?callbackUrl=/login" };
}
