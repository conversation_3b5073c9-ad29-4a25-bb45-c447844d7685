"use server";

import { revalidatePath } from "next/cache";
import { auth } from "@/auth";
import { prisma } from "@/common/db";
import { UserRole } from "@prisma/client";
import log from "@/common/logger";

/**
 * Reset user profile data - for testing purposes only
 * This should only be used by admin users
 */
export async function resetUserProfile(userId: string) {
  try {
    const session = await auth();
    
    if (!session) {
      throw new Error("Unauthorized");
    }
    
    // Ensure only admin users can perform this action
    if (session.user?.role !== UserRole.ADMIN) {
      throw new Error("Permission denied: Admin access required");
    }
    
    log.info(`Admin user ${session.user.id} is resetting profile for user ${userId}`);
    
    // Reset the user's profile data
    await prisma.user.update({
      where: { id: userId },
      data: {
        // Clear personal info
        first_name: "", // Clear first name
        last_name: "", // Clear last name
        phone_number: null,
        gender: null,
        pronouns: null,
        timezone: null,
        isProfileComplete: false,
      }
    });
    
    // Delete all preferred availability entries
    await prisma.preferredAvailability.deleteMany({
      where: { user_id: userId }
    });
    
    // Revalidate relevant paths
    revalidatePath("/profile");
    revalidatePath("/dashboard");
    revalidatePath("/onboarding");
    
    return { success: true };
  } catch (error) {
    log.error("Error resetting user profile:", error);
    return { error: "Failed to reset profile" };
  }
}
