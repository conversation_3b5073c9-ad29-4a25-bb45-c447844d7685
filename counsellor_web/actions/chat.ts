"use server";

import { revalidatePath } from "next/cache";
import { cookies } from "next/headers";
import { UserRole } from "@prisma/client";

import { baseApiUrl } from "@/common/api";
import { prisma } from "@/common/db";
import log from "@/common/logger";
import { getUserByEmail } from "@/common/user";

export async function setTzOffset() {
  const cookieStore = await cookies();
  const tzOffset = new Date().getTimezoneOffset();
  cookieStore.set("tz-offset", tzOffset.toString());
}

interface SubmitChatMessageParams {
  message: string;
  userId: string;
  counselorId: string;
  studentId: string;
  lastMsgId: number;
  messageType?: string;
}

export async function submitChatMessage(params: SubmitChatMessageParams) {
  const {
    message,
    counselorId,
    userId,
    studentId,
    lastMsgId,
    messageType = "human",
  } = params;

  const url = `${baseApiUrl}/counselor_chat`;
  log.debug(url);
  const res = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "x-api-key": process.env.ADDIE_API_KEY!,
    },
    body: JSON.stringify({
      message,
      user_id: userId,
      counselor_id: counselorId,
      student_id: studentId,
      last_msg_id: lastMsgId,
      message_type: messageType,
    }),
  });

  // Revalidate the student page to update the messages so it persist the new message
  revalidatePath(`/students/${studentId}`);
  return res.json();
}

export async function getStudentChatContext(params: { studentId: string }) {
  const { studentId } = params;
  const url = `${baseApiUrl}/student_context/${studentId}`;

  const res = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "x-api-key": process.env.ADDIE_API_KEY!,
    },
  });

  return res.json();
}

export interface KeyPrompt {
  key: string;
  prompt: string;
}

export async function getMixedPromptSuggestions(params: {
  studentId: string;
}): Promise<KeyPrompt[]> {
  const { studentId } = params;

  const fixedPromptSuggestions = await prisma.promptSuggestion.findMany({
    include: {
      prompt: {
        orderBy: {
          created_at: "desc",
        },
        take: 1,
      },
    },
  });

  const keyPrompts = fixedPromptSuggestions.map((p) => {
    return {
      key: p.name,
      prompt: p.prompt[0].content,
    };
  });

  const url = `${baseApiUrl}/student/prompt_suggestions`;

  const res = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "x-api-key": process.env.ADDIE_API_KEY!,
    },
    body: JSON.stringify({ student_id: studentId }),
  });

  const suggestions = await res.json();

  return [
    ...keyPrompts,
    ...suggestions.suggestions.map((s) => ({ key: s, prompt: s })),
  ];
}

export async function getStudentChatPromptSuggestions(): Promise<KeyPrompt[]> {
  const fixedPromptSuggestions = await prisma.promptSuggestion.findMany({
    include: {
      prompt: {
        orderBy: {
          created_at: "desc",
        },
        take: 1,
      },
    },
  });

  return fixedPromptSuggestions.map((p) => {
    // Check if prompt array exists and has at least one item before trying to access its content
    const promptContent =
      p.prompt && p.prompt.length > 0 && p.prompt[0].content
        ? p.prompt[0].content
        : "";

    return {
      key: p.name,
      prompt: promptContent,
    };
  });
}

export async function userIsAuthorized(email: string) {
  const user = await getUserByEmail(email.toLowerCase());

  const allowedRoles: UserRole[] = [UserRole.ADMIN, UserRole.COUNSELOR];

  if (!allowedRoles.includes(user?.role as UserRole)) {
    return false;
  }
  return true;
}

export async function clearChatHistory(sessionId: string) {
  try {
    // Extract the user ID and student ID from the session ID (format: userId-studentId)
    const counselorId = sessionId.split("-")[0];
    const studentId = sessionId.split("-")[1];
    const userId = counselorId;

    // Create a welcome message from Addie
    const welcomeMessage = {
      content: "I've reset our conversation. How can I help you today?",
      type: "ai", // Use 'Addie' instead of 'ai' to match what the UI expects
      id: Date.now().toString(), // Add an ID for the message
      created_at: new Date().toISOString(), // Add a timestamp
    };

    // Add the welcome message to the database
    const newMessage = await prisma.messages.create({
      data: {
        session_id: sessionId,
        message: { data: welcomeMessage },
      },
    });

    // Set the lastMsgId cookie to the new message ID
    const cookieStore = await cookies();
    cookieStore.set(sessionId, newMessage.id.toString());

    log.debug(
      `Created new welcome message with ID ${newMessage.id} for session ${sessionId}`,
    );
    log.debug(`Set lastMsgId cookie for ${sessionId} to ${newMessage.id}`);

    return { success: true, welcomeMessage, newMessageId: newMessage.id };
  } catch (error) {
    log.error(`Error resetting chat history: ${error}`);
    return { success: false, error: String(error) };
  }
}
