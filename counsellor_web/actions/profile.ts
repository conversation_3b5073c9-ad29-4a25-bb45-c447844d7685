"use server";

import { randomUUID } from "crypto";
import { revalidatePath } from "next/cache";
import { auth } from "@/auth";
import { AvailabilityBlock, AvailabilityDay } from "@prisma/client";
import { Resend } from "resend";
import { z } from "zod";

import { env } from "@/env.mjs";
import { baseApiUrl } from "@/common/api";
import { prisma } from "@/common/db";
import log from "@/common/logger";

// Initialize the Resend client for sending emails
const resend = new Resend(env.RESEND_API_KEY);

// Define a subset of fields that we allow to be updated on a user profile
const UserProfileSchema = z.object({
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  middle_name: z.string().nullable().optional(),
  email: z.string().email().optional(),
  phone_number: z.string().optional(),
  gender: z.string().optional(),
  pronouns: z.string().nullable().optional(),
  isProfileComplete: z.boolean().optional(),
  timezone: z.string().optional(),
  // Use string enums for day and block to match Prisma's enum types
  preferred_availability: z
    .array(
      z.object({
        day: z.enum(["WEEKDAY", "WEEKEND"]),
        block: z.enum(["MORNING", "AFTERNOON", "EVENING"]),
      }),
    )
    .optional(),
});

type UserProfileUpdateData = z.infer<typeof UserProfileSchema>;

/**
 * Update user profile
 *
 * @param userId - ID of the user to update
 * @param data - User profile data to update
 * @returns Success or error response
 */
export async function updateProfile(userId: string, data: Record<string, any>) {
  try {
    const session = await auth();

    if (!session) {
      throw new Error("Unauthorized");
    }

    // Parse and validate the data with our schema
    const validationResult = UserProfileSchema.safeParse(data);

    if (!validationResult.success) {
      log.error("Profile validation error:", validationResult.error);
      return {
        error: "Invalid profile data",
        details: validationResult.error.flatten(),
      };
    }

    const validData = validationResult.data;

    // Extract preferred_availability for separate handling
    const { preferred_availability, ...directUpdateData } = validData;

    // Log the validated data for debugging
    log.debug("Updating user profile with validated data:", directUpdateData);
    if (preferred_availability) {
      log.debug(
        "Will also update preferred availability:",
        preferred_availability,
      );
    }

    // Prepare update data
    const updateData: any = {
      ...directUpdateData,
      updated_at: new Date(),
    };

    // Handle preferred_availability separately if it exists
    if (preferred_availability && preferred_availability.length > 0) {
      // First, delete existing availabilities for this user
      await prisma.preferredAvailability.deleteMany({
        where: { user_id: userId },
      });

      // Then create new availabilities with proper enum types
      if (preferred_availability.length > 0) {
        await prisma.preferredAvailability.createMany({
          data: preferred_availability.map((item) => ({
            user_id: userId,
            day: item.day as AvailabilityDay,
            block: item.block as AvailabilityBlock,
          })),
        });
      }
    }

    // Update the user record with the basic data (excluding relations)
    await prisma.user.update({
      where: { id: userId },
      data: updateData,
    });

    // Revalidate the profile page to reflect changes
    revalidatePath("/profile");
    revalidatePath("/dashboard");

    return { success: true };
  } catch (error) {
    log.error("Error updating profile:", error);
    return { error: "Failed to update profile" };
  }
}

/**
 * Send a verification code to the provided phone number
 */
export async function sendPhoneVerificationCode(phoneNumber: string): Promise<{
  success: boolean;
  message: string;
}> {
  "use server";

  console.log(`${baseApiUrl}/verify-phone-send`);

  try {
    const response = await fetch(`${baseApiUrl}/verify-phone-send`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": env.ADDIE_API_KEY,
      },
      body: JSON.stringify({ phone_number: phoneNumber }),
    });

    if (!response.ok) {
      throw new Error("Failed to send verification code");
    }

    const result = await response.json();

    return result;
  } catch (error) {
    console.error("Error sending verification code:", error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Verify a phone number with a code
 */
/**
 * Verify the code sent to the phone number
 */
export async function verifyPhoneCode(
  phoneNumber: string,
  code: string,
): Promise<{
  success: boolean;
  message: string;
}> {
  "use server";

  try {
    // Get API URL from environment variable

    const response = await fetch(`${baseApiUrl}/verify-phone-check`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": env.ADDIE_API_KEY,
      },
      body: JSON.stringify({
        phone_number: phoneNumber,
        code: code,
      }),
    });

    if (!response.ok) {
      throw new Error("Failed to verify code");
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("Error verifying code:", error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Update the user's phone number
 */
export async function updatePhoneNumber(userId: string, phoneNumber: string) {
  try {
    const session = await auth();

    if (!session) {
      throw new Error("Unauthorized");
    }

    // Update the user's phone number
    await prisma.user.update({
      where: { id: userId },
      data: {
        phone_number: phoneNumber,
      },
    });

    return { success: true };
  } catch (error) {
    log.error("Error updating phone number:", error);
    return { error: "Failed to update phone number" };
  }
}
