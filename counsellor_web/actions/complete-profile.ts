"use server";

import { auth } from "@/auth";
import { prisma } from "@/common/db";
import log from "@/common/logger";
import { revalidatePath } from "next/cache";

/**
 * Server action to mark a user's profile as complete in the database
 */
export async function completeProfile() {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return { error: "Unauthorized", success: false };
    }
    
    const userId = session.user.id;
    
    // First check if the user has all required fields
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        first_name: true,
        last_name: true,
        phone_number: true,
        gender: true,
      }
    });
    
    if (!user) {
      return { error: "User not found", success: false };
    }
    
    // Verify all required fields are present
    const hasAllFields = !!user.first_name && 
                        !!user.last_name && 
                        !!user.phone_number && 
                        !!user.gender;
    
    if (!hasAllFields) {
      return { 
        error: "Profile incomplete", 
        success: false,
        missingFields: {
          first_name: !user.first_name,
          last_name: !user.last_name,
          phone_number: !user.phone_number,
          gender: !user.gender,
        }
      };
    }
    
    // Update the isProfileComplete flag in the database
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        isProfileComplete: true,
      },
    });
    
    log.debug('Profile marked as complete', { userId, updatedUser });
    
    // Revalidate relevant paths
    revalidatePath('/');
    revalidatePath('/onboarding');
    
    return { success: true };
  } catch (error) {
    log.error('Error completing profile', error);
    return { error: "Server error", success: false };
  }
}
