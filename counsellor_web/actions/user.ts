"use server";

import { auth } from "@/auth";
import { prisma } from "@/common/db";
import log from "@/common/logger";

/**
 * Get a user's complete profile including profile completion status
 * This is a server action that can be called from client components
 */
export async function getUserProfile(userId: string) {
  try {
    // Verify the current user has permission to access this profile
    const session = await auth();
    
    if (!session) {
      throw new Error("Unauthorized");
    }
    
    // Only allow users to access their own profile (or admins to access any profile)
    if (session.user?.id !== userId && session.user?.role !== "ADMIN") {
      throw new Error("Access denied");
    }
    
    // Fetch the complete user profile
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        first_name: true,
        last_name: true,
        middle_name: true,
        email: true,
        phone_number: true,
        gender: true,
        pronouns: true,
        isProfileComplete: true,
        timezone: true,
        preferred_availability: {
          select: {
            day: true,
            block: true
          }
        }
      }
    });
    
    if (!user) {
      throw new Error("User not found");
    }
    
    return user;
  } catch (error) {
    log.error("Error fetching user profile:", error);
    return null;
  }
}
