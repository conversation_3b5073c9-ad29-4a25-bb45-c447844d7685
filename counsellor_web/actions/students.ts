"use server";

import { getStudentAnswers } from "@/actions/conversations/student-conversation";
import { UserRole } from "@prisma/client";

import { prisma } from "@/common/db";
import { StudentWithWorkflows } from "@/common/types";

interface FetchStudentsParams {
  grade?: string | null | undefined;
  counselorId?: string | null | undefined;
  search?: string | null | undefined;
  user: { role: UserRole; id: string };
  schoolId?: string | null | undefined;
}

/**
 * Checks if a user's email domain is valid
 * @param email - The email to check
 * @param user - The user object containing role information
 * @returns boolean indicating if the domain is valid
 */
const isValidEmailDomain = (
  email: string,
  user: { role: UserRole },
): boolean => {
  const domain = email.split("@")[1];
  if (user?.role === UserRole.ADMIN) {
    return domain !== "example.com";
  }
  const domains = [
    "gmail.com",
    "getaddie.com",
    "example.com",
    "futuresight.ventures",
    "brewery.agency",
  ];
  return !domains.includes(domain);
};

/**
 * Fetches filtered students from the database
 * @param params - Filter parameters including grade, counselorId, search, and user role
 * @returns Promise containing array of student records
 */
const getFilteredStudents = async (params: FetchStudentsParams) => {
  // Get counselor's school if not provided and user is a counselor
  let schoolId = params.schoolId;
  if (!schoolId && params.user?.role === UserRole.COUNSELOR && params.user?.id) {
    const counselor = await prisma.counselor.findUnique({
      where: { user_id: params.user.id },
      select: { school_id: true }
    });
    if (counselor?.school_id) {
      schoolId = counselor.school_id;
    }
  }

  return prisma.student.findMany({
    where: {
      AND: [
        params.grade
          ? { grade: parseInt(params.grade.replace("Grade ", "")) }
          : {},

        params.counselorId && params.user?.role == UserRole.COUNSELOR
          ? { counselors: { some: { id: params.counselorId } } }
          : {},

        // Filter by school_id if available
        schoolId ? { school_id: schoolId } : {},

        params.search
          ? {
              OR: [
                { id: { contains: params.search } },
                {
                  users: {
                    some: {
                      OR: [
                        {
                          first_name: {
                            contains: params.search,
                            mode: "insensitive",
                          },
                        },
                        {
                          last_name: {
                            contains: params.search,
                            mode: "insensitive",
                          },
                        },
                        // Support multi-word search by concatenating first and last name
                        {
                          AND: [
                            {
                              first_name: {
                                contains: params.search.split(" ")[0] || "",
                                mode: "insensitive",
                              },
                            },
                            {
                              last_name: {
                                contains: params.search.split(" ").slice(1).join(" ") || "",
                                mode: "insensitive",
                              },
                            },
                          ],
                        },
                      ],
                    },
                  },
                },
              ],
            }
          : {},
        {
          st_agents: {
            none: {},
          },
        },
      ],
    },
    include: {
      st_agents: true,
      users: {
        select: {
          id: true,
          email: true,
          first_name: true,
          last_name: true,
          enabled: true,
        },
      },
      workflow_steps: true,
      student_workflow: {
        include: {
          workflow: {
            include: {
              steps: true,
            },
          },
        },
      },
    },
    orderBy: {
      updated_at: "desc",
    },
  });
};

/**
 * Fetches students with their workflow information
 * @param params - Object containing filter parameters
 * @returns Array of StudentWithWorkflows objects
 */
export const fetchStudents = async (
  params: FetchStudentsParams,
): Promise<StudentWithWorkflows[]> => {
  // get the "Onboarding Conversation" workflow
  const onboardingWorkflow = await prisma.workflow.findFirst({
    where: {
      name: "Onboarding Conversation",
    },
    select: { id: true },
  });

  const onboardingWorkflowId = onboardingWorkflow?.id;

  let students = await getFilteredStudents(params);

  students = students.filter((student) => {
    if (student.users.length === 0) return false;
    const user = student.users[0];
    return (
      user.first_name !== "none" &&
      user.email &&
      user.enabled !== false && // Only include enabled users (allow null/undefined for legacy users)
      isValidEmailDomain(user.email, params.user)
    );
  });
  if (!onboardingWorkflowId) {
    return students.map(
      ({
        id,
        updated_at,
        grade,
        users,
        st_agents,
        workflow_steps,
        student_workflow,
      }) => {
        const user = users[0];
        const agent = st_agents[0] || {};
        return {
          id,
          updated_at,
          user_id: user.id || "",
          config_id: agent.config_id || null,
          student_agent_id: agent.id || null,
          grade,
          first_name: user.first_name || "",
          last_name: user.last_name || "",
          email: user.email || null,
          workflow_steps,
          student_workflow,
          onboardingAnsweredCount: 0,
          onboardingTotalQuestions: 0,
        };
      },
    );
  }
  // For each student, find if they have onboarding StudentWorkflow
  const BATCH_SIZE = 10;
  for (let i = 0; i < students.length; i += BATCH_SIZE) {
    const slice = students.slice(i, i + BATCH_SIZE);

    // Run these 10 calls in parallel
    await Promise.all(
      slice.map(async (student) => {
        const onboardingSW = student.student_workflow.find(
          (sw) => sw.workflow_id === onboardingWorkflowId,
        );
        if (!onboardingSW) {
          // Student doesn't have the onboarding workflow
          (student as any).onboardingAnsweredCount = 0;
          (student as any).onboardingTotalQuestions = 0;
          return;
        }

        const { answerMap } = await getStudentAnswers(
          student.id,
          onboardingWorkflowId,
        );

        const answeredCount = Object.values(answerMap).filter(
          (ans) =>
            ans &&
            ans.trim() !== "" &&
            ans.trim() !== "No answer" &&
            ans.trim() !== "N/A",
        ).length;

        const totalQuestions = onboardingSW.workflow.steps.length;

        (student as any).onboardingAnsweredCount = answeredCount;
        (student as any).onboardingTotalQuestions = totalQuestions;
      }),
    );
  }

  return students.map((raw) => {
    const {
      id,
      updated_at,
      grade,
      users,
      st_agents,
      workflow_steps,
      student_workflow,
    } = raw;
    const user = users[0] || {};
    const agent = st_agents[0] || {};

    // Read the 2 fields we attached above
    const answered = (raw as any).onboardingAnsweredCount || 0;
    const total = (raw as any).onboardingTotalQuestions || 0;

    // Return the final object matching StudentWithWorkflows
    return {
      id,
      updated_at,
      grade,
      user_id: user.id || "",
      first_name: user.first_name || "",
      last_name: user.last_name || "",
      email: user.email || null,
      student_agent_id: agent.id || null,
      config_id: agent.config_id || null,
      workflow_steps,
      student_workflow,
      onboardingAnsweredCount: answered,
      onboardingTotalQuestions: total,
    };
  });
};

/**
 * Fetches students with basic information
 * @param params - Object containing filter parameters
 * @returns Array of StudentWithWorkflows objects
 */
export const fetchStudentsBasicInfo = async (
  params: FetchStudentsParams,
): Promise<StudentWithWorkflows[]> => {
  // Determine school filter based on user role and provided schoolId
  let schoolId = params.schoolId; // Use provided schoolId first (for admin active school)

  // If no schoolId provided and user is a counselor, get counselor's school
  if (!schoolId && params.user?.role === UserRole.COUNSELOR && params.user?.id) {
    const counselor = await prisma.counselor.findUnique({
      where: { user_id: params.user.id },
      select: { school_id: true }
    });
    if (counselor?.school_id) {
      schoolId = counselor.school_id;
    }
  }

  // For admin users in counselor_web, if activeSchool is provided, use it
  // If admin user and no schoolId provided, don't filter by school (show all students)

  // Use a simplified query that doesn't include the expensive workflow data
  const students = await prisma.student.findMany({
    where: {
      AND: [
        params.grade
          ? { grade: parseInt(params.grade.replace("Grade ", "")) }
          : {},

        params.counselorId && params.user?.role == UserRole.COUNSELOR
          ? { counselors: { some: { id: params.counselorId } } }
          : {},

        // Filter by school_id if available
        schoolId ? { school_id: schoolId } : {},

        params.search
          ? {
              OR: [
                { id: { contains: params.search } },
                {
                  users: {
                    some: {
                      OR: [
                        {
                          first_name: {
                            contains: params.search,
                            mode: "insensitive",
                          },
                        },
                        {
                          last_name: {
                            contains: params.search,
                            mode: "insensitive",
                          },
                        },
                        // Support multi-word search by concatenating first and last name
                        {
                          AND: [
                            {
                              first_name: {
                                contains: params.search.split(" ")[0] || "",
                                mode: "insensitive",
                              },
                            },
                            {
                              last_name: {
                                contains: params.search.split(" ").slice(1).join(" ") || "",
                                mode: "insensitive",
                              },
                            },
                          ],
                        },
                      ],
                    },
                  },
                },
              ],
            }
          : {},
        {
          st_agents: {
            none: {},
          },
        },
      ],
    },
    include: {
      st_agents: true,
      users: {
        select: {
          id: true,
          email: true,
          first_name: true,
          last_name: true,
          enabled: true,
        },
      },
      // Do NOT include workflow_steps or student_workflow here
      // as they trigger expensive operations
    },
    orderBy: {
      updated_at: "desc",
    },
  });

  // Filter students similarly to fetchStudents but without the expensive operations
  const filteredStudents = students.filter((student) => {
    if (student.users.length === 0) return false;
    const user = student.users[0];
    return (
      user.first_name !== "none" &&
      user.email &&
      user.enabled !== false && // Only include enabled users (allow null/undefined for legacy users)
      isValidEmailDomain(user.email, params.user)
    );
  });

  // Map to the expected return type but with placeholder values for onboarding data
  return filteredStudents.map(
    ({
      id,
      updated_at,
      grade,
      users,
      st_agents,
    }) => {
      const user = users[0] || {};
      const agent = st_agents[0] || {};

      return {
        id,
        updated_at,
        grade,
        user_id: user.id || "",
        first_name: user.first_name || "",
        last_name: user.last_name || "",
        email: user.email || null,
        student_agent_id: agent.id || null,
        config_id: agent.config_id || null,
        workflow_steps: [], // Empty placeholder
        student_workflow: [], // Empty placeholder
        onboardingAnsweredCount: 0, // Placeholder
        onboardingTotalQuestions: 0, // Placeholder
      };
    },
  );
};

/**
 * Fetches students with basic information
 * @param params - Object containing filter parameters
 * @returns Array of StudentWithWorkflows objects
 */
export const getUserByStudentId = async (
  student_id: string,
): Promise<any | null> => {
  try {
    const student = await prisma.student.findFirst({
      where: {
        id: student_id,
      },
      include: {
        users: true,
      },
    });

    return student?.users[0] || null;
  } catch (error) {
    console.error("Error fetching user by student ID:", error);
    return null;
  }
};
