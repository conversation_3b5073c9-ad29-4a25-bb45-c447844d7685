"use server";

import { cookies } from "next/headers";

import { prisma } from "@/common/db";
import { baseApiUrl } from "@/common/api";
import log from "@/common/logger";

export async function queueExperiment(configId: string) {
  const res = await fetch(`${baseApiUrl}/experiment`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "x-api-key": process.env.ADDIE_API_KEY!,
    },
    body: JSON.stringify({ config_id: configId }),
  });

  const data = await res.json();

  return data;
}

export async function deleteStudentAgentConfig(id: string) {
  await prisma.studentAgentConfig.delete({
    where: { id: id },
  });
}

export const delay = async (ms: number) =>
  new Promise((res) => setTimeout(res, ms));

export async function getExperimentProgress() {
  const res = await fetch(`${baseApiUrl}/experiment`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "x-api-key": process.env.ADDIE_API_KEY!,
    },
  });

  const data = await res.json();

  log.debug(data)

  return { remaining: data.active };
}

export async function setTzOffset() {
  const cookieStore = await cookies();
  const tzOffset = new Date().getTimezoneOffset();
  cookieStore.set("tz-offset", tzOffset.toString());
}

export async function setLastMsgIdForConversationId({
  conversationId,
}: {
  conversationId: string;
}) {
  const lastMsg = await prisma.messages.findMany({
    where: {
      session_id: conversationId,
    },
    orderBy: {
      created_at: "desc",
    },
    take: 10,
  });

  log.debug(lastMsg.map((msg) => msg.id));

  const lastMsgId = lastMsg[0]?.id.toString();
  if (lastMsgId) {
    log.debug(`## lst-msg-id: ${lastMsgId} ##`);

    const cookieStore = await cookies();
    cookieStore.set(conversationId, lastMsgId);
  } else {
    throw new Error("Last message not found");
  }
}
