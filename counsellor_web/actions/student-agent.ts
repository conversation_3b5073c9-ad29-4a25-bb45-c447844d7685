"use server";

import { queueExperiment } from "@/actions/queue";

import { env } from "@/env.mjs";
import { prisma } from "@/common/db";
import log from "@/common/logger";
import { Config, StudentAgentConfig } from "@/common/model";

interface SaveWorkflowStepGoalArgs {
  workflow_step_id: string;
  goal: string;
}

export async function saveWorkflowStepGoal(args: SaveWorkflowStepGoalArgs) {
  const { workflow_step_id, goal } = args;
  const res = await prisma.workflowStep.update({
    where: { id: workflow_step_id },
    data: { goal },
  });
  return res;
}

export async function saveConfig(config: StudentAgentConfig) {
  // create new prompt
  const prompt = await prisma.prompt.create({
    data: {
      content: config.prompt.content,
    },
  });

  // update config
  const data = { id: config.id, prompt_id: prompt.id };

  const res = await prisma.studentAgentConfig.update({
    where: { id: config.id },
    data,
    include: { prompt: true },
  });

  await queueExperiment(config.id);

  // await queueTask();
  return res;
}

export async function saveAddieConfig(config: Config) {
  // create new prompt
  const prompt = await prisma.prompt.create({
    data: {
      content: config.prompt.content,
    },
  });

  // update config
  const data = { id: config.id, prompt_id: prompt.id };

  const res = await prisma.addieConfig.update({
    where: { id: config.id },
    data,
    include: { prompt: true },
  });
  // get all studentAgentConfigs
  const studentAgentConfigs = await prisma.studentAgentConfig.findMany();

  // loop through studentAgentConfigs
  for (const config of studentAgentConfigs) {
    // iterate x number of times
    for (let i = 0; i < 1; i++) {
      await queueExperiment(config.id);
    }
  }

  return res;
}

// queue experiments for given studentagentconfig ids
export async function queueExperiments(studentAgentConfigIds: string[]) {
  return await Promise.all(
    studentAgentConfigIds.map(async (id) => {
      return queueExperiment(id);
    }),
  );
}
