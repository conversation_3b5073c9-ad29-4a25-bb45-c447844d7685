"use server";

import { Goal } from "@/actions/conversations/conversation-workflow";
import { Prisma, WorkflowType } from "@prisma/client";

import { prisma } from "@/common/db";
import log from "@/common/logger";

/**
 * Upserts (creates or updates) workflow steps for each goal in an assignment conversation.
 * Each goal is stored as a separate workflow step with a step name of "assignment-goal-{index}"
 * The early end message is stored in a separate step named "early-end-message"
 *
 * @param conversationId - The ID of the Workflow (unstructured conversation)
 * @param goals - Array of goals from the front end ( eg. {"id":"z23kqnnlv","mode":"assignment","goalText":"this is an assignment","followUpUnit":"days","exampleAnswer":"this is assignment answer","followUpNumber":20,"acceptanceCriteria":"This is assignment AC"})
 * @param earlyEndMessage - The counselor's early-end message
 */
export const upsertAssignmentGoals = async (
  conversationId: string,
  goals: Goal[],
  earlyEndMessage: string = "Thanks for chatting! We are going to move on to other conversation. Stay tuned until I reach back out!",
  disableSystemPrompt: boolean = false,
) => {
  const assignmentConversation = await prisma.workflow.findUnique({
    where: {
      id: conversationId,
    },
    include: { steps: true },
  });

  if (!assignmentConversation) {
    throw new Error("No Workflow found with this Id");
  }

  if (assignmentConversation.workflow_type !== WorkflowType.ASSIGNMENT) {
    throw new Error(
      "This is not an assignment conversation, please check again!",
    );
  }

  // Get all existing goal steps
  const existingGoalSteps = assignmentConversation.steps.filter((step) =>
    step.name.startsWith("assignment-goal-"),
  );

  // Delete any existing goal steps that are no longer needed
  if (existingGoalSteps.length > goals.length) {
    const stepsToDelete = existingGoalSteps.slice(goals.length);
    for (const step of stepsToDelete) {
      await prisma.workflowStep.delete({
        where: { id: step.id },
      });
    }
  }

  // Upsert each goal as a separate step
  for (let i = 0; i < goals.length; i++) {
    const goal = goals[i];
    const stepName = `assignment-goal-${i}`;
    const existingStep = existingGoalSteps[i];

    console.log("######");
    log.debug(goal.goalText);
    console.log("######");

    if (existingStep) {
      // Don't update the name if it already exists to avoid unique constraint error
      // Only update the other fields
      await prisma.workflowStep.update({
        where: { id: existingStep.id },
        data: {
          // Keep the existing name to avoid unique constraint violations
          goal: goal.goalText.substring(0, 100), // Use the goal text as the step goal (truncated)
          data: goal as unknown as Prisma.JsonObject,
          index: i,
        },
      });
    } else {
      // Create new step
      await prisma.workflowStep.create({
        data: {
          name: stepName,
          goal: goal.goalText.substring(0, 100), // Use the goal text as the step goal (truncated)
          parent_workflow: { connect: { id: assignmentConversation.id } },
          data: goal as unknown as Prisma.JsonObject,
          index: i,
        },
      });
    }
  }

  // Update workflow's updated_at and disable_system_prompt
  await prisma.workflow.update({
    where: {
      id: conversationId,
    },
    data: {
      data: { earlyEndMessage },
      disable_system_prompt: disableSystemPrompt,
      updated_at: new Date(),
    },
  });
};
