"use server";

import { Goal } from "@/actions/conversations/conversation-workflow";
import { Prisma, WorkflowType } from "@prisma/client";
import { Simulate } from "react-dom/test-utils";

import { prisma } from "@/common/db";
import log from "@/common/logger";
import { getBaseApiUrl } from "@/common/api";
import { Message } from "@/components/Conversations/Student-ConversationTab/StudentUnstructuredConvo";

import error = Simulate.error;

export interface SummaryInsight {
  id: string;
  summary_id: string;
  goal: string;
  insight: string;
  created_at: string;
  updated_at: string;
}

export interface UnstructuredConversationSummary {
  id: string;
  student_id: string;
  workflow_id: string;
  content: string;
  created_at: string;
  updated_at: string;
  insights: SummaryInsight[];
}

/**
 * Upserts (creates or updates) workflow steps for each goal in an unstructured conversation.
 * Each goal is stored as a separate workflow step with a step name of "unstructured-goal-{index}"
 * The early end message is stored in a separate step named "early-end-message"
 *
 * @param conversationId - The ID of the Workflow (unstructured conversation)
 * @param goals - Array of goals from the front end
 * @param earlyEndMessage - The counselor's early-end message
 */
export const upsertUnstructuredGoals = async (
  conversationId: string,
  goals: Goal[],
  earlyEndMessage: string = "Thanks for chatting! We are going to move on to other conversation. Stay tuned until I reach back out!",
  disableSystemPrompt: boolean = false,
) => {
  const unstructuredConversation = await prisma.workflow.findUnique({
    where: {
      id: conversationId,
    },
    include: { steps: true },
  });

  if (!unstructuredConversation) {
    throw new Error("No Workflow found with this Id");
  }

  if (unstructuredConversation.workflow_type !== WorkflowType.UNSTRUCTURED) {
    throw new Error(
      "This is not an unstructured conversation, please check again!",
    );
  }

  // Get all existing goal steps
  const existingGoalSteps = unstructuredConversation.steps.filter((step) =>
    step.name.startsWith("unstructured-goal-"),
  );

  // Delete any existing goal steps that are no longer needed
  if (existingGoalSteps.length > goals.length) {
    const stepsToDelete = existingGoalSteps.slice(goals.length);
    for (const step of stepsToDelete) {
      await prisma.workflowStep.delete({
        where: { id: step.id },
      });
    }
  }

  // Upsert each goal as a separate step
  for (let i = 0; i < goals.length; i++) {
    const goal = goals[i];
    const stepName = `unstructured-goal-${i}`;
    const existingStep = existingGoalSteps[i];

    if (existingStep) {
      // Don't update the name if it already exists to avoid unique constraint error
      // Only update the other fields
      await prisma.workflowStep.update({
        where: { id: existingStep.id },
        data: {
          // Keep the existing name to avoid unique constraint violations
          goal: goal.goalText.substring(0, 100), // Use the goal text as the step goal (truncated)
          data: goal as unknown as Prisma.JsonObject,
          index: i,
        },
      });
    } else {
      // Create new step
      await prisma.workflowStep.create({
        data: {
          name: stepName,
          goal: goal.goalText.substring(0, 100), // Use the goal text as the step goal (truncated)
          parent_workflow: { connect: { id: unstructuredConversation.id } },
          data: goal as unknown as Prisma.JsonObject,
          index: i,
        },
      });
    }
  }

  // Update workflow's updated_at and disable_system_prompt
  await prisma.workflow.update({
    where: {
      id: conversationId,
    },
    data: {
      data: { earlyEndMessage },
      updated_at: new Date(),
      disable_system_prompt: disableSystemPrompt,
    },
  });
};

/**
 * Migrates an unstructured conversation from the old format (single step with goals array)
 * to the new format (one step per goal).
 *
 * @param conversationId - The ID of the unstructured conversation to migrate
 * @returns A boolean indicating whether the migration was successful
 */
export const migrateUnstructuredConversation = async (
  conversationId: string,
): Promise<boolean> => {
  try {
    const conversation = await prisma.workflow.findUnique({
      where: { id: conversationId },
      include: { steps: true },
    });

    if (
      !conversation ||
      conversation.workflow_type !== WorkflowType.UNSTRUCTURED
    ) {
      return false;
    }

    // Find the legacy step that contains all goals
    const legacyStep = conversation.steps.find(
      (s) => s.name === "unstructured-goals",
    );
    if (!legacyStep) {
      // No legacy step found, nothing to migrate
      return false;
    }

    // Extract goals and earlyEndMessage from the legacy step
    const stepData = legacyStep.data as any;
    const goals = stepData?.goals || [];
    const earlyEndMessage = stepData?.earlyEndMessage || "";

    // Create new steps for each goal
    await upsertUnstructuredGoals(conversationId, goals, earlyEndMessage);

    // Delete the legacy step
    await prisma.workflowStep.delete({
      where: { id: legacyStep.id },
    });

    return true;
  } catch (error) {
    console.error("Error migrating unstructured conversation:", error);
    return false;
  }
};

/**
 * Migrates all unstructured conversations from the old format to the new format.
 *
 * @returns The number of successfully migrated conversations
 */
export const migrateAllUnstructuredConversations =
  async (): Promise<number> => {
    try {
      const unstructuredWorkflows = await prisma.workflow.findMany({
        where: { workflow_type: WorkflowType.UNSTRUCTURED },
        select: { id: true },
      });

      let migratedCount = 0;
      for (const workflow of unstructuredWorkflows) {
        const success = await migrateUnstructuredConversation(workflow.id);
        if (success) {
          migratedCount++;
        }
      }

      return migratedCount;
    } catch (error) {
      console.error("Error migrating all unstructured conversations:", error);
      return 0;
    }
  };

export const getUnstructuredConvoSummary = async (
  studentId: string,
  workflowId: string,
): Promise<UnstructuredConversationSummary | null> => {
  if (!studentId || !workflowId) {
    throw new Error("Missing student ID or Workflow ID");
  }
  const summary =
    (await prisma.generatedUnstructuredConversationSummary.findFirst({
      where: {
        student_id: studentId,
        workflow_id: workflowId,
      },
      orderBy: {
        created_at: "desc",
      },
      include: {
        insights: true,
      },
    })) as UnstructuredConversationSummary | null;
  if (!summary) return null;

  return summary;
};

export const regenerateUnstructuredConvoSummary = async (
  studentId: string,
  workflowId: string,
): Promise<UnstructuredConversationSummary> => {
  if (!studentId || !workflowId) {
    throw new Error("Missing student ID or Workflow ID");
  }

  try {
    const apiKey = process.env.ADDIE_API_KEY;
    if (!apiKey) {
      throw new Error("ADDIE_API_KEY not configured");
    }

    const apiBaseUrl = getBaseApiUrl();

    log.debug(`API Base URL: ${apiBaseUrl}`);

    const response = await fetch(`${apiBaseUrl}/conversation/${workflowId}/student/${studentId}/summary/regenerate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": apiKey,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to regenerate summary: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();

    // Convert Date strings back to the expected format
    return {
      ...data,
      created_at: data.created_at,
      updated_at: data.updated_at,
      insights: data.insights.map((insight: any) => ({
        ...insight,
        created_at: insight.created_at,
        updated_at: insight.updated_at,
      })),
    };
  } catch (error) {
    log.error("Failed to regenerate conversation summary:", error);
    throw error;
  }
};

export const getUnstructuredConvoMessagesBySessionId = async (
  sessionId: string,
): Promise<Message[]> => {
  try {
    const messages = await prisma.messages.findMany({
      where: {
        session_id: sessionId,
      },
      orderBy: {
        id: "asc",
      },
    });

    const minMessages = messages
      .map((msg) => {
        const message = msg.message as {
          data?: {
            type?: string;
            content?: string;
            additional_kwargs?: {
              source?: string;
            };
          };
          additional_kwargs?: {
            source?: string;
          };
        };
        const msgData = message?.data;

        // Try to get source from data.additional_kwargs first, then from top-level additional_kwargs
        const source = msgData?.additional_kwargs?.source || message?.additional_kwargs?.source || "web";

        return {
          id: msg.id, // Include message ID
          type: msgData?.type || "unknown",
          content: msgData?.content || "",
          source: source,
        };
      })
      .filter((msg) => msg.content.length > 0 && msg.type !== "tool"); // Filter out empty and tool messages

    return minMessages;
  } catch (error) {
    console.error("Error fetching unstructured conversation messages:", error);
    return [];
  }
};
