"use server";

import { StudentWorkflowStatus, WorkflowStatus } from "@prisma/client";

import { prisma } from "@/common/db";
import log from "@/common/logger";

// get published conversations
export const getPublishedStudentConversations = async (studentId: string) => {
  const conversations = await prisma.studentWorkflow.findMany({
    where: {
      student_id: studentId,
      workflow: {
        status: WorkflowStatus.PUBLISHED,
        NOT: {
          id: {
            // exlcude predefined onboarding workflows
            in: ["cm4khj7sv00009okyrti419zf", "cm7z6p9ee04w32s97z6u05ahj"],
          },
        },
      },
    },
    include: {
      steps: true,
      workflow: {
        include: {
          steps: true,
        },
      },
    },
  });
  // exclude predefined workflows that are duplicated
  const filteredConversation = conversations.filter(
    (convo) => convo.workflow?.parent_step_id !== "cm4khj7ww00019okynqz1t9i5",
  );

  filteredConversation.sort(
    (a, b) =>
      new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime(),
  );

  return filteredConversation;
};

//get individual StudentWorkflow by workflowId

export const getStudentWorkflow = async (
  studentId: string,
  workflowId: string,
) => {
  return prisma.studentWorkflow.findUniqueOrThrow({
    where: {
      student_id_workflow_id: {
        student_id: studentId,
        workflow_id: workflowId,
      },
    },
    include: {
      steps: {
        include: {
          step: true,
        },
        orderBy: {
          step: {
            index: "asc",
          },
        },
      },
      workflow: true,
    },
  });
};

export const getStudentAnswers = async (
  studentId: string,
  workflowId: string,
) => {
  const studentWorkflow = await getStudentWorkflow(studentId, workflowId);

  // Extract all question IDs from the workflow steps
  // Each step.data contains information about the question including its type and ID
  const questionData = studentWorkflow.steps
    .map((step) => {
      const data = step.step.data as any;

      // Return an object with the question ID and table (question type)
      return {
        questionId: data.questionId,
        table: data.table,
      };
    })
    .filter((item) => !!item.questionId);

  // Create arrays for each question type
  const regularQuestionIds: string[] = [];
  const mcQuestionIds: string[] = [];
  const binaryQuestionIds: string[] = [];

  // Sort question IDs by their type
  questionData.forEach((item) => {
    if (item.table === "Question") {
      regularQuestionIds.push(item.questionId);
    } else if (item.table === "MultipleChoiceQuestion") {
      mcQuestionIds.push(item.questionId);
    } else if (item.table === "BinaryQuestion") {
      binaryQuestionIds.push(item.questionId);
    } else if (item.table === "LikertQuestion") {
      mcQuestionIds.push(item.questionId);
    } else {
      // Default to regular question if type is unknown
      regularQuestionIds.push(item.questionId);
    }
  });

  // Query for all question responses for this student that match any of the question IDs
  const questionResponses = await prisma.questionResponse.findMany({
    where: {
      student_id: studentId,
      OR: [
        // Only include non-empty arrays in the query
        ...(regularQuestionIds.length > 0
          ? [
              {
                question_id: { in: regularQuestionIds },
              },
            ]
          : []),
        ...(mcQuestionIds.length > 0
          ? [
              {
                mc_question_id: { in: mcQuestionIds },
              },
            ]
          : []),
        ...(binaryQuestionIds.length > 0
          ? [
              {
                binary_question_id: { in: binaryQuestionIds },
              },
            ]
          : []),
      ],
    },
  });

  const answerMap: Record<string, string> = {};

  questionResponses.forEach((qr) => {
    // Determine which ID to use based on the question type
    const id =
      qr.question_id || qr.mc_question_id || qr.binary_question_id || "no-id";

    // Always use response_data if available
    if (qr.response_data) {
      const responseData = qr.response_data as any;
      // Handle different question types
      if (typeof responseData.value === "boolean") {
        // Binary question
        answerMap[id] = responseData.value.toString();
      } else {
        // Text or multiple choice question
        answerMap[id] = responseData.value;
      }
    } else {
      // DEPRECATED: Using response field is deprecated
      // log.warn(
      //   `DEPRECATED: Using response field for question ${id} is deprecated. Please migrate to response_data.`,
      // );

      // Only use response as fallback for legacy data
      // This branch should eventually be removed once all data is migrated
      answerMap[id] = qr.response || "";
    }
  });

  return {
    studentWorkflow,
    answerMap,
  };
};

export const beginConversation = async (
  studentId: string,
  workflowId: string,
) => {
  await prisma.studentWorkflow.update({
    where: {
      student_id_workflow_id: {
        student_id: studentId,
        workflow_id: workflowId,
      },
    },
    data: {
      status: StudentWorkflowStatus.IN_PROGRESS,
    },
  });
};

export const upsertQuestionResponse = async (
  studentId: string,
  question: {
    questionId: string;
    question: string;
    table: string;
    options?: string[];
  },
  response: string,
) => {
  try {
    const questionId = question.questionId;
    const questionType = question.table;

    // Create response_data JSON object based on the question type
    let responseData: any = {};

    if (questionType === "BinaryQuestion") {
      // For binary questions, convert string "true"/"false" to boolean
      responseData.value = response.toLowerCase() === "true";
    } else if (questionType === "MultipleChoiceQuestion") {
      // For multiple choice, store the selected option ID
      responseData.value = response;
    } else {
      // For text questions, store the text response
      responseData.value = response;
    }

    // Determine which question type field to use based on the table
    let updateData: any = {
      student_id: studentId,
      response_data: responseData,
      // DEPRECATED: response field is deprecated and will be removed in a future version
      // Only keeping it for now for backward compatibility
      response: "DEPRECATED: Use response_data.value instead",
    };

    // Set the appropriate question ID field based on the question type
    if (questionType === "Question") {
      updateData.question_id = questionId;
    } else if (questionType === "MultipleChoiceQuestion") {
      updateData.mc_question_id = questionId;
    } else if (questionType === "BinaryQuestion") {
      updateData.binary_question_id = questionId;
    } else {
      // Default to question_id if type is unknown
      updateData.question_id = questionId;
    }

    // Create a unique identifier for the upsert operation
    const whereCondition: any = {
      student_id: studentId,
    };

    // Set the appropriate question ID field in the where condition
    if (questionType === "Question") {
      whereCondition.question_id = questionId;
    } else if (questionType === "MultipleChoiceQuestion") {
      whereCondition.mc_question_id = questionId;
    } else if (questionType === "BinaryQuestion") {
      whereCondition.binary_question_id = questionId;
    } else {
      whereCondition.question_id = questionId;
    }

    // Find existing response with these conditions
    const existingResponse = await prisma.questionResponse.findFirst({
      where: whereCondition,
    });

    let newResponse;

    if (existingResponse) {
      // Update existing response
      newResponse = await prisma.questionResponse.update({
        where: { id: existingResponse.id },
        data: {
          response_data: responseData,
        },
      });
    } else {
      // Create new response
      newResponse = await prisma.questionResponse.create({
        data: updateData,
      });
    }

    // log response object
    log.debug(newResponse);

    return { success: true };
  } catch (error) {
    log.error("Error upserting question response:", error);
    return { success: false, error };
  }
};

/**
 * Mark a specific student's conversation (StudentWorkflow) as COMPLETED.
 */
export async function completeStudentWorkflow(
  studentId: string,
  workflowId: string,
): Promise<{ success: boolean; error?: string }> {
  try {
    await prisma.studentWorkflow.update({
      where: {
        student_id_workflow_id: {
          student_id: studentId,
          workflow_id: workflowId,
        },
      },
      data: {
        status: StudentWorkflowStatus.COMPLETED,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Error completing StudentWorkflow:", error);
    return { success: false, error: "Failed to complete conversation." };
  }
}
