"use server";

import { sendEmail } from "@/common/send-email";

export async function sendSupportEmail({
  message,
  userEmail,
  userName,
  userId,
}) {
  try {
    if (!message) {
      throw new Error("Message content is required.");
    }

    await sendEmail(
      // "<EMAIL>",
      "<EMAIL>",
      `Help Request from Counselor ${userName}(${userEmail}`,
      userEmail,
      `
        <p><strong>Counselor Name:</strong> ${userName}</p>
        <p><strong>Counselor Email:</strong> ${userEmail}</p>
        <p><strong>User Id:</strong> ${userId}</p>
        <hr />
        <hr />
        <hr />
        <p><strong>Message:</strong> ${message}</p>

      `,
    );

    return { success: true, message: "Email sent successfully." };
  } catch (error) {
    console.error("Error sending support email:", error);
    return { success: false, message: "Failed to send email." };
  }
}
