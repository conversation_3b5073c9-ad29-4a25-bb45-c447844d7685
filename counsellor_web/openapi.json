{"openapi": "3.1.0", "info": {"title": "Prefect Prefect REST API", "version": "3.0.0rc13", "x-logo": {"url": "static/prefect-logo-mark-gradient.png"}}, "paths": {"/api/health": {"get": {"tags": ["Root"], "summary": "Health Check", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/version": {"get": {"tags": ["Root"], "summary": "Server Version", "operationId": "server_version_version_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/flows/": {"post": {"tags": ["Flows"], "summary": "Create Flow", "description": "Gracefully creates a new flow from the provided schema. If a flow with the\nsame name already exists, the existing flow is returned.", "operationId": "create_flow_flows__post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Flow"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flows/{id}": {"patch": {"tags": ["Flows"], "summary": "Update Flow", "description": "Updates a flow.", "operationId": "update_flow_flows__id__patch", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow id", "title": "Id"}, "description": "The flow id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowUpdate"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Flows"], "summary": "Read Flow", "description": "Get a flow by id.", "operationId": "read_flow_flows__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow id", "title": "Id"}, "description": "The flow id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Flow"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Flows"], "summary": "Delete Flow", "description": "Delete a flow by id.", "operationId": "delete_flow_flows__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow id", "title": "Id"}, "description": "The flow id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flows/count": {"post": {"tags": ["Flows"], "summary": "Count Flows", "description": "Count flows.", "operationId": "count_flows_flows_count_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_count_flows_flows_count_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "integer", "title": "Response Count Flows Flows Count Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flows/name/{name}": {"get": {"tags": ["Flows"], "summary": "Read Flow By Name", "description": "Get a flow by name.", "operationId": "read_flow_by_name_flows_name__name__get", "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string", "description": "The name of the flow", "title": "Name"}, "description": "The name of the flow"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Flow"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flows/filter": {"post": {"tags": ["Flows"], "summary": "Read Flows", "description": "Query for flows.", "operationId": "read_flows_flows_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_flows_flows_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Flow"}, "title": "Response Read Flows Flows Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flows/paginate": {"post": {"tags": ["Flows"], "summary": "Paginate Flows", "description": "Pagination query for flows.", "operationId": "paginate_flows_flows_paginate_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_paginate_flows_flows_paginate_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowPaginationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_runs/": {"post": {"tags": ["Flow Runs"], "summary": "Create Flow Run", "description": "Create a flow run. If a flow run with the same flow_id and\nidempotency key already exists, the existing flow run will be returned.\n\nIf no state is provided, the flow run will be created in a PENDING state.", "operationId": "create_flow_run_flow_runs__post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowRunCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowRunResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_runs/{id}": {"patch": {"tags": ["Flow Runs"], "summary": "Update Flow Run", "description": "Updates a flow run.", "operationId": "update_flow_run_flow_runs__id__patch", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow run id", "title": "Id"}, "description": "The flow run id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowRunUpdate"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Flow Runs"], "summary": "Read Flow Run", "description": "Get a flow run by id.", "operationId": "read_flow_run_flow_runs__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow run id", "title": "Id"}, "description": "The flow run id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowRunResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Flow Runs"], "summary": "Delete Flow Run", "description": "Delete a flow run by id.", "operationId": "delete_flow_run_flow_runs__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow run id", "title": "Id"}, "description": "The flow run id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_runs/count": {"post": {"tags": ["Flow Runs"], "summary": "Count Flow Runs", "description": "Query for flow runs.", "operationId": "count_flow_runs_flow_runs_count_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_count_flow_runs_flow_runs_count_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "integer", "title": "Response Count Flow Runs Flow Runs Count Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_runs/lateness": {"post": {"tags": ["Flow Runs"], "summary": "Average Flow Run Lateness", "description": "Query for average flow-run lateness in seconds.", "operationId": "average_flow_run_lateness_flow_runs_lateness_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_average_flow_run_lateness_flow_runs_lateness_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Response Average Flow Run Lateness Flow Runs Lateness Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_runs/history": {"post": {"tags": ["Flow Runs"], "summary": "Flow Run History", "description": "Query for flow run history data across a given range and interval.", "operationId": "flow_run_history_flow_runs_history_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_flow_run_history_flow_runs_history_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HistoryResponse"}, "title": "Response Flow Run History Flow Runs History Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_runs/{id}/graph": {"get": {"tags": ["Flow Runs"], "summary": "Read Flow Run Graph V1", "description": "Get a task run dependency map for a given flow run.", "operationId": "read_flow_run_graph_v1_flow_runs__id__graph_get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow run id", "title": "Id"}, "description": "The flow run id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DependencyResult"}, "title": "Response Read Flow Run Graph V1 Flow Runs  Id  Graph Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_runs/{id}/graph-v2": {"get": {"tags": ["Flow Runs"], "summary": "Read Flow Run Graph V2", "description": "Get a graph of the tasks and subflow runs for the given flow run", "operationId": "read_flow_run_graph_v2_flow_runs__id__graph_v2_get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow run id", "title": "Id"}, "description": "The flow run id"}, {"name": "since", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time", "description": "Only include runs that start or end after this time.", "default": "0001-01-01T00:00:00", "title": "Since"}, "description": "Only include runs that start or end after this time."}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Graph"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_runs/{id}/resume": {"post": {"tags": ["Flow Runs"], "summary": "Resume Flow Run", "description": "Resume a paused flow run.", "operationId": "resume_flow_run_flow_runs__id__resume_post", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow run id", "title": "Id"}, "description": "The flow run id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_resume_flow_run_flow_runs__id__resume_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrchestrationResult"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_runs/filter": {"post": {"tags": ["Flow Runs"], "summary": "Read Flow Runs", "description": "Query for flow runs.", "operationId": "read_flow_runs_flow_runs_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_flow_runs_flow_runs_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FlowRunResponse"}, "title": "Response Read Flow Runs Flow Runs Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_runs/{id}/set_state": {"post": {"tags": ["Flow Runs"], "summary": "Set Flow Run State", "description": "Set a flow run state, invoking any orchestration rules.", "operationId": "set_flow_run_state_flow_runs__id__set_state_post", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow run id", "title": "Id"}, "description": "The flow run id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_set_flow_run_state_flow_runs__id__set_state_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrchestrationResult"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_runs/{id}/input": {"post": {"tags": ["Flow Runs"], "summary": "Create Flow Run Input", "description": "Create a key/value input for a flow run.", "operationId": "create_flow_run_input_flow_runs__id__input_post", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow run id", "title": "Id"}, "description": "The flow run id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_create_flow_run_input_flow_runs__id__input_post"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_runs/{id}/input/filter": {"post": {"tags": ["Flow Runs"], "summary": "Filter Flow Run Input", "description": "Filter flow run inputs by key prefix", "operationId": "filter_flow_run_input_flow_runs__id__input_filter_post", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow run id", "title": "Id"}, "description": "The flow run id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_filter_flow_run_input_flow_runs__id__input_filter_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FlowRunInput"}, "title": "Response Filter Flow Run Input Flow Runs  Id  Input Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_runs/{id}/input/{key}": {"get": {"tags": ["Flow Runs"], "summary": "Read Flow Run Input", "description": "Create a value from a flow run input", "operationId": "read_flow_run_input_flow_runs__id__input__key__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow run id", "title": "Id"}, "description": "The flow run id"}, {"name": "key", "in": "path", "required": true, "schema": {"type": "string", "description": "The input key", "title": "Key"}, "description": "The input key"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Flow Runs"], "summary": "Delete Flow Run Input", "description": "Delete a flow run input", "operationId": "delete_flow_run_input_flow_runs__id__input__key__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow run id", "title": "Id"}, "description": "The flow run id"}, {"name": "key", "in": "path", "required": true, "schema": {"type": "string", "description": "The input key", "title": "Key"}, "description": "The input key"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_runs/paginate": {"post": {"tags": ["Flow Runs"], "summary": "Paginate Flow Runs", "description": "Pagination query for flow runs.", "operationId": "paginate_flow_runs_flow_runs_paginate_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_paginate_flow_runs_flow_runs_paginate_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowRunPaginationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/task_runs/": {"post": {"tags": ["Task Runs"], "summary": "Create Task Run", "description": "Create a task run. If a task run with the same flow_run_id,\ntask_key, and dynamic_key already exists, the existing task\nrun will be returned.\n\nIf no state is provided, the task run will be created in a PENDING state.", "operationId": "create_task_run_task_runs__post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRunCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRun"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/task_runs/{id}": {"patch": {"tags": ["Task Runs"], "summary": "Update Task Run", "description": "Updates a task run.", "operationId": "update_task_run_task_runs__id__patch", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The task run id", "title": "Id"}, "description": "The task run id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRunUpdate"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Task Runs"], "summary": "Read Task Run", "description": "Get a task run by id.", "operationId": "read_task_run_task_runs__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The task run id", "title": "Id"}, "description": "The task run id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRun"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Task Runs"], "summary": "Delete Task Run", "description": "Delete a task run by id.", "operationId": "delete_task_run_task_runs__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The task run id", "title": "Id"}, "description": "The task run id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/task_runs/count": {"post": {"tags": ["Task Runs"], "summary": "Count Task Runs", "description": "Count task runs.", "operationId": "count_task_runs_task_runs_count_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_count_task_runs_task_runs_count_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "integer", "title": "Response Count Task Runs Task Runs Count Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/task_runs/history": {"post": {"tags": ["Task Runs"], "summary": "Task Run History", "description": "Query for task run history data across a given range and interval.", "operationId": "task_run_history_task_runs_history_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_task_run_history_task_runs_history_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HistoryResponse"}, "title": "Response Task Run History Task Runs History Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/task_runs/filter": {"post": {"tags": ["Task Runs"], "summary": "Read Task Runs", "description": "Query for task runs.", "operationId": "read_task_runs_task_runs_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_task_runs_task_runs_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TaskRun"}, "title": "Response Read Task Runs Task Runs Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/task_runs/{id}/set_state": {"post": {"tags": ["Task Runs"], "summary": "Set Task Run State", "description": "Set a task run state, invoking any orchestration rules.", "operationId": "set_task_run_state_task_runs__id__set_state_post", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The task run id", "title": "Id"}, "description": "The task run id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_set_task_run_state_task_runs__id__set_state_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrchestrationResult"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_run_states/{id}": {"get": {"tags": ["Flow Run States"], "summary": "Read Flow Run State", "description": "Get a flow run state by id.", "operationId": "read_flow_run_state_flow_run_states__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow run state id", "title": "Id"}, "description": "The flow run state id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/State"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_run_states/": {"get": {"tags": ["Flow Run States"], "summary": "Read Flow Run States", "description": "Get states associated with a flow run.", "operationId": "read_flow_run_states_flow_run_states__get", "parameters": [{"name": "flow_run_id", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Flow Run Id"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/State"}, "title": "Response Read Flow Run States Flow Run States  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/task_run_states/{id}": {"get": {"tags": ["Task Run States"], "summary": "Read Task Run State", "description": "Get a task run state by id.", "operationId": "read_task_run_state_task_run_states__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The task run state id", "title": "Id"}, "description": "The task run state id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/State"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/task_run_states/": {"get": {"tags": ["Task Run States"], "summary": "Read Task Run States", "description": "Get states associated with a task run.", "operationId": "read_task_run_states_task_run_states__get", "parameters": [{"name": "task_run_id", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Task Run Id"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/State"}, "title": "Response Read Task Run States Task Run States  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_run_notification_policies/": {"post": {"tags": ["Flow Run Notification Policies"], "summary": "Create Flow Run Notification Policy", "description": "Creates a new flow run notification policy.", "operationId": "create_flow_run_notification_policy_flow_run_notification_policies__post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowRunNotificationPolicyCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowRunNotificationPolicy"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_run_notification_policies/{id}": {"patch": {"tags": ["Flow Run Notification Policies"], "summary": "Update Flow Run Notification Policy", "description": "Updates an existing flow run notification policy.", "operationId": "update_flow_run_notification_policy_flow_run_notification_policies__id__patch", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow run notification policy id", "title": "Id"}, "description": "The flow run notification policy id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowRunNotificationPolicyUpdate"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Flow Run Notification Policies"], "summary": "Read Flow Run Notification Policy", "description": "Get a flow run notification policy by id.", "operationId": "read_flow_run_notification_policy_flow_run_notification_policies__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow run notification policy id", "title": "Id"}, "description": "The flow run notification policy id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowRunNotificationPolicy"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Flow Run Notification Policies"], "summary": "Delete Flow Run Notification Policy", "description": "Delete a flow run notification policy by id.", "operationId": "delete_flow_run_notification_policy_flow_run_notification_policies__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The flow run notification policy id", "title": "Id"}, "description": "The flow run notification policy id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/flow_run_notification_policies/filter": {"post": {"tags": ["Flow Run Notification Policies"], "summary": "Read Flow Run Notification Policies", "description": "Query for flow run notification policies.", "operationId": "read_flow_run_notification_policies_flow_run_notification_policies_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_flow_run_notification_policies_flow_run_notification_policies_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FlowRunNotificationPolicy"}, "title": "Response Read Flow Run Notification Policies Flow Run Notification Policies Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/": {"post": {"tags": ["Deployments"], "summary": "Create Deployment", "description": "Gracefully creates a new deployment from the provided schema. If a deployment with\nthe same name and flow_id already exists, the deployment is updated.\n\nIf the deployment has an active schedule, flow runs will be scheduled.\nWhen upserting, any scheduled runs from the existing deployment will be deleted.", "operationId": "create_deployment_deployments__post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeploymentCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeploymentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/{id}": {"patch": {"tags": ["Deployments"], "summary": "Update Deployment", "operationId": "update_deployment_deployments__id__patch", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The deployment id", "title": "Id"}, "description": "The deployment id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeploymentUpdate"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Deployments"], "summary": "Read Deployment", "description": "Get a deployment by id.", "operationId": "read_deployment_deployments__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The deployment id", "title": "Id"}, "description": "The deployment id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeploymentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Deployments"], "summary": "Delete Deployment", "description": "Delete a deployment by id.", "operationId": "delete_deployment_deployments__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The deployment id", "title": "Id"}, "description": "The deployment id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/name/{flow_name}/{deployment_name}": {"get": {"tags": ["Deployments"], "summary": "Read Deployment By Name", "description": "Get a deployment using the name of the flow and the deployment.", "operationId": "read_deployment_by_name_deployments_name__flow_name___deployment_name__get", "parameters": [{"name": "flow_name", "in": "path", "required": true, "schema": {"type": "string", "description": "The name of the flow", "title": "Flow Name"}, "description": "The name of the flow"}, {"name": "deployment_name", "in": "path", "required": true, "schema": {"type": "string", "description": "The name of the deployment", "title": "Deployment Name"}, "description": "The name of the deployment"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeploymentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/filter": {"post": {"tags": ["Deployments"], "summary": "Read Deployments", "description": "Query for deployments.", "operationId": "read_deployments_deployments_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_deployments_deployments_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeploymentResponse"}, "title": "Response Read Deployments Deployments Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/paginate": {"post": {"tags": ["Deployments"], "summary": "Paginate Deployments", "description": "Pagination query for flow runs.", "operationId": "paginate_deployments_deployments_paginate_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_paginate_deployments_deployments_paginate_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeploymentPaginationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/get_scheduled_flow_runs": {"post": {"tags": ["Deployments"], "summary": "Get Scheduled Flow Runs For Deployments", "description": "Get scheduled runs for a set of deployments. Used by a runner to poll for work.", "operationId": "get_scheduled_flow_runs_for_deployments_deployments_get_scheduled_flow_runs_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_get_scheduled_flow_runs_for_deployments_deployments_get_scheduled_flow_runs_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FlowRunResponse"}, "title": "Response Get Scheduled Flow Runs For Deployments Deployments Get Scheduled Flow Runs Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/count": {"post": {"tags": ["Deployments"], "summary": "Count Deployments", "description": "Count deployments.", "operationId": "count_deployments_deployments_count_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_count_deployments_deployments_count_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "integer", "title": "Response Count Deployments Deployments Count Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/{id}/schedule": {"post": {"tags": ["Deployments"], "summary": "Schedule Deployment", "description": "Schedule runs for a deployment. For backfills, provide start/end times in the past.\n\nThis function will generate the minimum number of runs that satisfy the min\nand max times, and the min and max counts. Specifically, the following order\nwill be respected.\n\n    - Runs will be generated starting on or after the `start_time`\n    - No more than `max_runs` runs will be generated\n    - No runs will be generated after `end_time` is reached\n    - At least `min_runs` runs will be generated\n    - Runs will be generated until at least `start_time + min_time` is reached", "operationId": "schedule_deployment_deployments__id__schedule_post", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The deployment id", "title": "Id"}, "description": "The deployment id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_schedule_deployment_deployments__id__schedule_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/{id}/resume_deployment": {"post": {"tags": ["Deployments"], "summary": "Resume Deployment", "description": "Set a deployment schedule to active. Runs will be scheduled immediately.", "operationId": "resume_deployment_deployments__id__resume_deployment_post", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The deployment id", "title": "Id"}, "description": "The deployment id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/{id}/set_schedule_active": {"post": {"tags": ["Deployments"], "summary": "Resume Deployment", "description": "Set a deployment schedule to active. Runs will be scheduled immediately.", "operationId": "resume_deployment_deployments__id__set_schedule_active_post", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The deployment id", "title": "Id"}, "description": "The deployment id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/{id}/pause_deployment": {"post": {"tags": ["Deployments"], "summary": "Pause Deployment", "description": "Set a deployment schedule to inactive. Any auto-scheduled runs still in a Scheduled\nstate will be deleted.", "operationId": "pause_deployment_deployments__id__pause_deployment_post", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The deployment id", "title": "Id"}, "description": "The deployment id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/{id}/set_schedule_inactive": {"post": {"tags": ["Deployments"], "summary": "Pause Deployment", "description": "Set a deployment schedule to inactive. Any auto-scheduled runs still in a Scheduled\nstate will be deleted.", "operationId": "pause_deployment_deployments__id__set_schedule_inactive_post", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The deployment id", "title": "Id"}, "description": "The deployment id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/{id}/create_flow_run": {"post": {"tags": ["Deployments"], "summary": "Create Flow Run From Deployment", "description": "Create a flow run from a deployment.\n\nAny parameters not provided will be inferred from the deployment's parameters.\nIf tags are not provided, the deployment's tags will be used.\n\nIf no state is provided, the flow run will be created in a SCHEDULED state.", "operationId": "create_flow_run_from_deployment_deployments__id__create_flow_run_post", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The deployment id", "title": "Id"}, "description": "The deployment id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeploymentFlowRunCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowRunResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/{id}/work_queue_check": {"get": {"tags": ["Deployments"], "summary": "Work Queue Check For Deployment", "description": "Get list of work-queues that are able to pick up the specified deployment.\n\nThis endpoint is intended to be used by the UI to provide users warnings\nabout deployments that are unable to be executed because there are no work\nqueues that will pick up their runs, based on existing filter criteria. It\nmay be deprecated in the future because there is not a strict relationship\nbetween work queues and deployments.", "operationId": "work_queue_check_for_deployment_deployments__id__work_queue_check_get", "deprecated": true, "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The deployment id", "title": "Id"}, "description": "The deployment id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkQueue"}, "title": "Response Work Queue Check For Deployment Deployments  Id  Work Queue Check Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/{id}/schedules": {"get": {"tags": ["Deployments"], "summary": "Read Deployment Schedules", "operationId": "read_deployment_schedules_deployments__id__schedules_get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The deployment id", "title": "Id"}, "description": "The deployment id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeploymentSchedule"}, "title": "Response Read Deployment Schedules Deployments  Id  Schedules Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Deployments"], "summary": "Create Deployment Schedules", "operationId": "create_deployment_schedules_deployments__id__schedules_post", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The deployment id", "title": "Id"}, "description": "The deployment id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeploymentScheduleCreate"}, "description": "The schedules to create", "title": "Schedules"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeploymentSchedule"}, "title": "Response Create Deployment Schedules Deployments  Id  Schedules Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/{id}/schedules/{schedule_id}": {"patch": {"tags": ["Deployments"], "summary": "Update Deployment Schedule", "operationId": "update_deployment_schedule_deployments__id__schedules__schedule_id__patch", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The deployment id", "title": "Id"}, "description": "The deployment id"}, {"name": "schedule_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The schedule id", "title": "Schedule Id"}, "description": "The schedule id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/DeploymentScheduleUpdate"}], "description": "The updated schedule", "title": "Schedule"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Deployments"], "summary": "Delete Deployment Schedule", "operationId": "delete_deployment_schedule_deployments__id__schedules__schedule_id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The deployment id", "title": "Id"}, "description": "The deployment id"}, {"name": "schedule_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The schedule id", "title": "Schedule Id"}, "description": "The schedule id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/saved_searches/": {"put": {"tags": ["SavedSearches"], "summary": "Create Saved Search", "description": "Gracefully creates a new saved search from the provided schema.\n\nIf a saved search with the same name already exists, the saved search's fields are\nreplaced.", "operationId": "create_saved_search_saved_searches__put", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavedSearchCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavedSearch"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/saved_searches/{id}": {"get": {"tags": ["SavedSearches"], "summary": "Read Saved Search", "description": "Get a saved search by id.", "operationId": "read_saved_search_saved_searches__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The saved search id", "title": "Id"}, "description": "The saved search id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavedSearch"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["SavedSearches"], "summary": "Delete Saved Search", "description": "Delete a saved search by id.", "operationId": "delete_saved_search_saved_searches__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The saved search id", "title": "Id"}, "description": "The saved search id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/saved_searches/filter": {"post": {"tags": ["SavedSearches"], "summary": "Read Saved Searches", "description": "Query for saved searches.", "operationId": "read_saved_searches_saved_searches_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_saved_searches_saved_searches_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SavedSearch"}, "title": "Response Read Saved Searches Saved Searches Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/logs/": {"post": {"tags": ["Logs"], "summary": "Create Logs", "description": "Create new logs from the provided schema.", "operationId": "create_logs_logs__post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LogCreate"}, "title": "Logs"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/logs/filter": {"post": {"tags": ["Logs"], "summary": "Read Logs", "description": "Query for logs.", "operationId": "read_logs_logs_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_logs_logs_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Log"}, "title": "Response Read Logs Logs Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/concurrency_limits/": {"post": {"tags": ["Concurrency Limits"], "summary": "Create Concurrency Limit", "operationId": "create_concurrency_limit_concurrency_limits__post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConcurrencyLimitCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConcurrencyLimit"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/concurrency_limits/{id}": {"get": {"tags": ["Concurrency Limits"], "summary": "Read Concurrency Limit", "description": "Get a concurrency limit by id.\n\nThe `active slots` field contains a list of TaskRun IDs currently using a\nconcurrency slot for the specified tag.", "operationId": "read_concurrency_limit_concurrency_limits__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The concurrency limit id", "title": "Id"}, "description": "The concurrency limit id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConcurrencyLimit"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Concurrency Limits"], "summary": "Delete Concurrency Limit", "operationId": "delete_concurrency_limit_concurrency_limits__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The concurrency limit id", "title": "Id"}, "description": "The concurrency limit id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/concurrency_limits/tag/{tag}": {"get": {"tags": ["Concurrency Limits"], "summary": "Read Concurrency Limit By Tag", "description": "Get a concurrency limit by tag.\n\nThe `active slots` field contains a list of TaskRun IDs currently using a\nconcurrency slot for the specified tag.", "operationId": "read_concurrency_limit_by_tag_concurrency_limits_tag__tag__get", "parameters": [{"name": "tag", "in": "path", "required": true, "schema": {"type": "string", "description": "The tag name", "title": "Tag"}, "description": "The tag name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConcurrencyLimit"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Concurrency Limits"], "summary": "Delete Concurrency Limit By Tag", "operationId": "delete_concurrency_limit_by_tag_concurrency_limits_tag__tag__delete", "parameters": [{"name": "tag", "in": "path", "required": true, "schema": {"type": "string", "description": "The tag name", "title": "Tag"}, "description": "The tag name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/concurrency_limits/filter": {"post": {"tags": ["Concurrency Limits"], "summary": "Read Concurrency Limits", "description": "Query for concurrency limits.\n\nFor each concurrency limit the `active slots` field contains a list of TaskRun IDs\ncurrently using a concurrency slot for the specified tag.", "operationId": "read_concurrency_limits_concurrency_limits_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_concurrency_limits_concurrency_limits_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConcurrencyLimit"}, "title": "Response Read Concurrency Limits Concurrency Limits Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/concurrency_limits/tag/{tag}/reset": {"post": {"tags": ["Concurrency Limits"], "summary": "Reset Concurrency Limit By Tag", "operationId": "reset_concurrency_limit_by_tag_concurrency_limits_tag__tag__reset_post", "parameters": [{"name": "tag", "in": "path", "required": true, "schema": {"type": "string", "description": "The tag name", "title": "Tag"}, "description": "The tag name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_reset_concurrency_limit_by_tag_concurrency_limits_tag__tag__reset_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v2/concurrency_limits/": {"post": {"tags": ["Concurrency Limits V2"], "summary": "Create Concurrency Limit V2", "operationId": "create_concurrency_limit_v2_v2_concurrency_limits__post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConcurrencyLimitV2Create"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConcurrencyLimitV2"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v2/concurrency_limits/{id_or_name}": {"get": {"tags": ["Concurrency Limits V2"], "summary": "Read Concurrency Limit V2", "operationId": "read_concurrency_limit_v2_v2_concurrency_limits__id_or_name__get", "parameters": [{"name": "id_or_name", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "string"}], "description": "The ID or name of the concurrency limit", "title": "Id Or Name"}, "description": "The ID or name of the concurrency limit"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalConcurrencyLimitResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["Concurrency Limits V2"], "summary": "Update Concurrency Limit V2", "operationId": "update_concurrency_limit_v2_v2_concurrency_limits__id_or_name__patch", "parameters": [{"name": "id_or_name", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "string"}], "description": "The ID or name of the concurrency limit", "title": "Id Or Name"}, "description": "The ID or name of the concurrency limit"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConcurrencyLimitV2Update"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Concurrency Limits V2"], "summary": "Delete Concurrency Limit V2", "operationId": "delete_concurrency_limit_v2_v2_concurrency_limits__id_or_name__delete", "parameters": [{"name": "id_or_name", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "string"}], "description": "The ID or name of the concurrency limit", "title": "Id Or Name"}, "description": "The ID or name of the concurrency limit"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v2/concurrency_limits/filter": {"post": {"tags": ["Concurrency Limits V2"], "summary": "Read All Concurrency Limits V2", "operationId": "read_all_concurrency_limits_v2_v2_concurrency_limits_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_all_concurrency_limits_v2_v2_concurrency_limits_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GlobalConcurrencyLimitResponse"}, "title": "Response Read All Concurrency Limits V2 V2 Concurrency Limits Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v2/concurrency_limits/increment": {"post": {"tags": ["Concurrency Limits V2"], "summary": "Bulk Increment Active Slots", "operationId": "bulk_increment_active_slots_v2_concurrency_limits_increment_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_bulk_increment_active_slots_v2_concurrency_limits_increment_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MinimalConcurrencyLimitResponse"}, "title": "Response Bulk Increment Active Slots V2 Concurrency Limits Increment Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v2/concurrency_limits/decrement": {"post": {"tags": ["Concurrency Limits V2"], "summary": "Bulk Decrement Active Slots", "operationId": "bulk_decrement_active_slots_v2_concurrency_limits_decrement_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_bulk_decrement_active_slots_v2_concurrency_limits_decrement_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MinimalConcurrencyLimitResponse"}, "title": "Response Bulk Decrement Active Slots V2 Concurrency Limits Decrement Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/block_types/": {"post": {"tags": ["Block types"], "summary": "Create Block Type", "description": "Create a new block type", "operationId": "create_block_type_block_types__post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockTypeCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockType"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/block_types/{id}": {"get": {"tags": ["Block types"], "summary": "Read Block Type By Id", "description": "Get a block type by ID.", "operationId": "read_block_type_by_id_block_types__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The block type ID", "title": "Id"}, "description": "The block type ID"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockType"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["Block types"], "summary": "Update Block Type", "description": "Update a block type.", "operationId": "update_block_type_block_types__id__patch", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The block type ID", "title": "Id"}, "description": "The block type ID"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockTypeUpdate"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Block types"], "summary": "Delete Block Type", "operationId": "delete_block_type_block_types__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The block type ID", "title": "Id"}, "description": "The block type ID"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/block_types/slug/{slug}": {"get": {"tags": ["Block types"], "summary": "Read Block Type By Slug", "description": "Get a block type by name.", "operationId": "read_block_type_by_slug_block_types_slug__slug__get", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string", "description": "The block type name", "title": "Slug"}, "description": "The block type name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockType"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/block_types/filter": {"post": {"tags": ["Block types"], "summary": "Read Block Types", "description": "Gets all block types. Optionally limit return with limit and offset.", "operationId": "read_block_types_block_types_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_block_types_block_types_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlockType"}, "title": "Response Read Block Types Block Types Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/block_types/slug/{slug}/block_documents": {"get": {"tags": ["Block types", "Block types", "Block documents"], "summary": "Read Block Documents For Block Type", "operationId": "read_block_documents_for_block_type_block_types_slug__slug__block_documents_get", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string", "description": "The block type name", "title": "Slug"}, "description": "The block type name"}, {"name": "include_secrets", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Whether to include sensitive values in the block document.", "default": false, "title": "Include Secrets"}, "description": "Whether to include sensitive values in the block document."}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlockDocument"}, "title": "Response Read Block Documents For Block Type Block Types Slug  Slug  Block Documents Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/block_types/slug/{slug}/block_documents/name/{block_document_name}": {"get": {"tags": ["Block types", "Block types", "Block documents"], "summary": "Read Block Document By Name For Block Type", "operationId": "read_block_document_by_name_for_block_type_block_types_slug__slug__block_documents_name__block_document_name__get", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string", "description": "The block type name", "title": "Slug"}, "description": "The block type name"}, {"name": "block_document_name", "in": "path", "required": true, "schema": {"type": "string", "description": "The block type name", "title": "Block Document Name"}, "description": "The block type name"}, {"name": "include_secrets", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Whether to include sensitive values in the block document.", "default": false, "title": "Include Secrets"}, "description": "Whether to include sensitive values in the block document."}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockDocument"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/block_types/install_system_block_types": {"post": {"tags": ["Block types"], "summary": "Install System Block Types", "operationId": "install_system_block_types_block_types_install_system_block_types_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/block_documents/": {"post": {"tags": ["Block documents"], "summary": "Create Block Document", "description": "Create a new block document.", "operationId": "create_block_document_block_documents__post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockDocumentCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockDocument"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/block_documents/filter": {"post": {"tags": ["Block documents"], "summary": "Read Block Documents", "description": "Query for block documents.", "operationId": "read_block_documents_block_documents_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_block_documents_block_documents_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlockDocument"}, "title": "Response Read Block Documents Block Documents Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/block_documents/count": {"post": {"tags": ["Block documents"], "summary": "Count Block Documents", "description": "Count block documents.", "operationId": "count_block_documents_block_documents_count_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_count_block_documents_block_documents_count_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "integer", "title": "Response Count Block Documents Block Documents Count Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/block_documents/{id}": {"get": {"tags": ["Block documents"], "summary": "Read Block Document By Id", "operationId": "read_block_document_by_id_block_documents__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The block document id", "title": "Id"}, "description": "The block document id"}, {"name": "include_secrets", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Whether to include sensitive values in the block document.", "default": false, "title": "Include Secrets"}, "description": "Whether to include sensitive values in the block document."}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockDocument"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Block documents"], "summary": "Delete Block Document", "operationId": "delete_block_document_block_documents__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The block document id", "title": "Id"}, "description": "The block document id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["Block documents"], "summary": "Update Block Document Data", "operationId": "update_block_document_data_block_documents__id__patch", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The block document id", "title": "Id"}, "description": "The block document id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockDocumentUpdate"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_pools/": {"post": {"tags": ["Work Pools"], "summary": "Create Work Pool", "description": "Creates a new work pool. If a work pool with the same\nname already exists, an error will be raised.", "operationId": "create_work_pool_work_pools__post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkPoolCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkPool"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_pools/{name}": {"get": {"tags": ["Work Pools"], "summary": "Read Work Pool", "description": "Read a work pool by name", "operationId": "read_work_pool_work_pools__name__get", "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work pool name", "title": "Name"}, "description": "The work pool name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkPool"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["Work Pools"], "summary": "Update Work Pool", "description": "Update a work pool", "operationId": "update_work_pool_work_pools__name__patch", "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work pool name", "title": "Name"}, "description": "The work pool name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkPoolUpdate"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Work Pools"], "summary": "Delete Work Pool", "description": "Delete a work pool", "operationId": "delete_work_pool_work_pools__name__delete", "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work pool name", "title": "Name"}, "description": "The work pool name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_pools/filter": {"post": {"tags": ["Work Pools"], "summary": "Read Work Pools", "description": "Read multiple work pools", "operationId": "read_work_pools_work_pools_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_work_pools_work_pools_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkPool"}, "title": "Response Read Work Pools Work Pools Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_pools/count": {"post": {"tags": ["Work Pools"], "summary": "Count Work Pools", "description": "Count work pools", "operationId": "count_work_pools_work_pools_count_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_count_work_pools_work_pools_count_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "integer", "title": "Response Count Work Pools Work Pools Count Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_pools/{name}/get_scheduled_flow_runs": {"post": {"tags": ["Work Pools"], "summary": "Get Scheduled Flow Runs", "description": "Load scheduled runs for a worker", "operationId": "get_scheduled_flow_runs_work_pools__name__get_scheduled_flow_runs_post", "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work pool name", "title": "Name"}, "description": "The work pool name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_get_scheduled_flow_runs_work_pools__name__get_scheduled_flow_runs_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkerFlowRunResponse"}, "title": "Response Get Scheduled Flow Runs Work Pools  Name  Get Scheduled Flow Runs Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_pools/{work_pool_name}/queues": {"post": {"tags": ["Work Pools"], "summary": "Create Work Queue", "description": "Creates a new work pool queue. If a work pool queue with the same\nname already exists, an error will be raised.", "operationId": "create_work_queue_work_pools__work_pool_name__queues_post", "parameters": [{"name": "work_pool_name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work pool name", "title": "Work Pool Name"}, "description": "The work pool name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkQueueCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkQueueResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_pools/{work_pool_name}/queues/{name}": {"get": {"tags": ["Work Pools"], "summary": "Read Work Queue", "description": "Read a work pool queue", "operationId": "read_work_queue_work_pools__work_pool_name__queues__name__get", "parameters": [{"name": "work_pool_name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work pool name", "title": "Work Pool Name"}, "description": "The work pool name"}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work pool queue name", "title": "Name"}, "description": "The work pool queue name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkQueueResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["Work Pools"], "summary": "Update Work Queue", "description": "Update a work pool queue", "operationId": "update_work_queue_work_pools__work_pool_name__queues__name__patch", "parameters": [{"name": "work_pool_name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work pool name", "title": "Work Pool Name"}, "description": "The work pool name"}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work pool queue name", "title": "Name"}, "description": "The work pool queue name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkQueueUpdate"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Work Pools"], "summary": "Delete Work Queue", "description": "Delete a work pool queue", "operationId": "delete_work_queue_work_pools__work_pool_name__queues__name__delete", "parameters": [{"name": "work_pool_name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work pool name", "title": "Work Pool Name"}, "description": "The work pool name"}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work pool queue name", "title": "Name"}, "description": "The work pool queue name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_pools/{work_pool_name}/queues/filter": {"post": {"tags": ["Work Pools"], "summary": "Read Work Queues", "description": "Read all work pool queues", "operationId": "read_work_queues_work_pools__work_pool_name__queues_filter_post", "parameters": [{"name": "work_pool_name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work pool name", "title": "Work Pool Name"}, "description": "The work pool name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_work_queues_work_pools__work_pool_name__queues_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkQueueResponse"}, "title": "Response Read Work Queues Work Pools  Work Pool Name  Queues Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_pools/{work_pool_name}/workers/heartbeat": {"post": {"tags": ["Work Pools"], "summary": "Worker Heartbeat", "operationId": "worker_heartbeat_work_pools__work_pool_name__workers_heartbeat_post", "parameters": [{"name": "work_pool_name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work pool name", "title": "Work Pool Name"}, "description": "The work pool name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_worker_heartbeat_work_pools__work_pool_name__workers_heartbeat_post"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_pools/{work_pool_name}/workers/filter": {"post": {"tags": ["Work Pools"], "summary": "Read Workers", "description": "Read all worker processes", "operationId": "read_workers_work_pools__work_pool_name__workers_filter_post", "parameters": [{"name": "work_pool_name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work pool name", "title": "Work Pool Name"}, "description": "The work pool name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_workers_work_pools__work_pool_name__workers_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkerResponse"}, "title": "Response Read Workers Work Pools  Work Pool Name  Workers Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_pools/{work_pool_name}/workers/{name}": {"delete": {"tags": ["Work Pools"], "summary": "Delete Worker", "description": "Delete a work pool's worker", "operationId": "delete_worker_work_pools__work_pool_name__workers__name__delete", "parameters": [{"name": "work_pool_name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work pool name", "title": "Work Pool Name"}, "description": "The work pool name"}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work pool's worker name", "title": "Name"}, "description": "The work pool's worker name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/task_workers/filter": {"post": {"tags": ["Task Workers"], "summary": "Read Task Workers", "description": "Read active task workers. Optionally filter by task keys.", "operationId": "read_task_workers_task_workers_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_task_workers_task_workers_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TaskWorkerResponse"}, "title": "Response Read Task Workers Task Workers Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_queues/": {"post": {"tags": ["Work Queues"], "summary": "Create Work Queue", "description": "Creates a new work queue.\n\nIf a work queue with the same name already exists, an error\nwill be raised.", "operationId": "create_work_queue_work_queues__post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkQueueCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkQueueResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_queues/{id}": {"patch": {"tags": ["Work Queues"], "summary": "Update Work Queue", "description": "Updates an existing work queue.", "operationId": "update_work_queue_work_queues__id__patch", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The work queue id", "title": "Id"}, "description": "The work queue id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkQueueUpdate"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Work Queues"], "summary": "Read Work Queue", "description": "Get a work queue by id.", "operationId": "read_work_queue_work_queues__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The work queue id", "title": "Id"}, "description": "The work queue id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkQueueResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Work Queues"], "summary": "Delete Work Queue", "description": "Delete a work queue by id.", "operationId": "delete_work_queue_work_queues__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The work queue id", "title": "Id"}, "description": "The work queue id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_queues/name/{name}": {"get": {"tags": ["Work Queues"], "summary": "Read Work Queue By Name", "description": "Get a work queue by id.", "operationId": "read_work_queue_by_name_work_queues_name__name__get", "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string", "description": "The work queue name", "title": "Name"}, "description": "The work queue name"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkQueueResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_queues/{id}/get_runs": {"post": {"tags": ["Work Queues"], "summary": "Read Work Queue Runs", "description": "Get flow runs from the work queue.", "operationId": "read_work_queue_runs_work_queues__id__get_runs_post", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The work queue id", "title": "Id"}, "description": "The work queue id"}, {"name": "x-prefect-ui", "in": "header", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "A header to indicate this request came from the Prefect UI.", "default": false, "title": "X-Prefect-Ui"}, "description": "A header to indicate this request came from the Prefect UI."}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_work_queue_runs_work_queues__id__get_runs_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FlowRunResponse"}, "title": "Response Read Work Queue Runs Work Queues  Id  Get Runs Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_queues/filter": {"post": {"tags": ["Work Queues"], "summary": "Read Work Queues", "description": "Query for work queues.", "operationId": "read_work_queues_work_queues_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_work_queues_work_queues_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkQueueResponse"}, "title": "Response Read Work Queues Work Queues Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/work_queues/{id}/status": {"get": {"tags": ["Work Queues"], "summary": "Read Work Queue Status", "description": "Get the status of a work queue.", "operationId": "read_work_queue_status_work_queues__id__status_get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The work queue id", "title": "Id"}, "description": "The work queue id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkQueueStatusDetail"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/artifacts/": {"post": {"tags": ["Artifacts"], "summary": "Create Artifact", "operationId": "create_artifact_artifacts__post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ArtifactCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Artifact"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/artifacts/{id}": {"get": {"tags": ["Artifacts"], "summary": "Read Artifact", "description": "Retrieve an artifact from the database.", "operationId": "read_artifact_artifacts__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The ID of the artifact to retrieve.", "title": "Id"}, "description": "The ID of the artifact to retrieve."}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Artifact"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["Artifacts"], "summary": "Update Artifact", "description": "Update an artifact in the database.", "operationId": "update_artifact_artifacts__id__patch", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The ID of the artifact to update.", "title": "Id"}, "description": "The ID of the artifact to update."}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ArtifactUpdate"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Artifacts"], "summary": "Delete Artifact", "description": "Delete an artifact from the database.", "operationId": "delete_artifact_artifacts__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The ID of the artifact to delete.", "title": "Id"}, "description": "The ID of the artifact to delete."}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/artifacts/{key}/latest": {"get": {"tags": ["Artifacts"], "summary": "Read Latest Artifact", "description": "Retrieve the latest artifact from the artifact table.", "operationId": "read_latest_artifact_artifacts__key__latest_get", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "description": "The key of the artifact to retrieve.", "title": "Key"}, "description": "The key of the artifact to retrieve."}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Artifact"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/artifacts/filter": {"post": {"tags": ["Artifacts"], "summary": "Read Artifacts", "description": "Retrieve artifacts from the database.", "operationId": "read_artifacts_artifacts_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_artifacts_artifacts_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Artifact"}, "title": "Response Read Artifacts Artifacts Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/artifacts/latest/filter": {"post": {"tags": ["Artifacts"], "summary": "Read Latest Artifacts", "description": "Retrieve artifacts from the database.", "operationId": "read_latest_artifacts_artifacts_latest_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_latest_artifacts_artifacts_latest_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ArtifactCollection"}, "title": "Response Read Latest Artifacts Artifacts Latest Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/artifacts/count": {"post": {"tags": ["Artifacts"], "summary": "Count Artifacts", "description": "Count artifacts from the database.", "operationId": "count_artifacts_artifacts_count_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_count_artifacts_artifacts_count_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "integer", "title": "Response Count Artifacts Artifacts Count Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/artifacts/latest/count": {"post": {"tags": ["Artifacts"], "summary": "Count Latest Artifacts", "description": "Count artifacts from the database.", "operationId": "count_latest_artifacts_artifacts_latest_count_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_count_latest_artifacts_artifacts_latest_count_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "integer", "title": "Response Count Latest Artifacts Artifacts Latest Count Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/block_schemas/": {"post": {"tags": ["Block schemas"], "summary": "Create Block Schema", "operationId": "create_block_schema_block_schemas__post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockSchemaCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/block_schemas/{id}": {"delete": {"tags": ["Block schemas"], "summary": "Delete Block Schema", "description": "Delete a block schema by id.", "operationId": "delete_block_schema_block_schemas__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The block schema id", "title": "Id"}, "description": "The block schema id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Block schemas"], "summary": "Read Block Schema By Id", "description": "Get a block schema by id.", "operationId": "read_block_schema_by_id_block_schemas__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "The block schema id", "title": "Id"}, "description": "The block schema id"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/block_schemas/filter": {"post": {"tags": ["Block schemas"], "summary": "Read Block Schemas", "description": "Read all block schemas, optionally filtered by type", "operationId": "read_block_schemas_block_schemas_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_block_schemas_block_schemas_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlockSchema"}, "title": "Response Read Block Schemas Block Schemas Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/block_schemas/checksum/{checksum}": {"get": {"tags": ["Block schemas"], "summary": "Read Block Schema By Checksum", "operationId": "read_block_schema_by_checksum_block_schemas_checksum__checksum__get", "parameters": [{"name": "checksum", "in": "path", "required": true, "schema": {"type": "string", "description": "The block schema checksum", "title": "Checksum"}, "description": "The block schema checksum"}, {"name": "version", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Version of block schema. If not provided the most recently created block schema with the matching checksum will be returned.", "title": "Version"}, "description": "Version of block schema. If not provided the most recently created block schema with the matching checksum will be returned."}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/block_capabilities/": {"get": {"tags": ["Block capabilities"], "summary": "Read Available Block Capabilities", "operationId": "read_available_block_capabilities_block_capabilities__get", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "title": "Response Read Available Block Capabilities Block Capabilities  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/collections/views/{view}": {"get": {"tags": ["Collections"], "summary": "Read View Content", "description": "Reads the content of a view from the prefect-collection-registry.", "operationId": "read_view_content_collections_views__view__get", "parameters": [{"name": "view", "in": "path", "required": true, "schema": {"type": "string", "title": "View"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Read View Content Collections Views  View  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/variables/": {"post": {"tags": ["Variables"], "summary": "Create Variable", "operationId": "create_variable_variables__post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VariableCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Variable"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/variables/{id}": {"get": {"tags": ["Variables"], "summary": "Read Variable", "operationId": "read_variable_variables__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Variable"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["Variables"], "summary": "Update Variable", "operationId": "update_variable_variables__id__patch", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VariableUpdate"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Variables"], "summary": "Delete Variable", "operationId": "delete_variable_variables__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/variables/name/{name}": {"get": {"tags": ["Variables"], "summary": "Read Variable By Name", "operationId": "read_variable_by_name_variables_name__name__get", "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string", "title": "Name"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Variable"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["Variables"], "summary": "Update Variable By Name", "operationId": "update_variable_by_name_variables_name__name__patch", "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string", "title": "Name"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VariableUpdate"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Variables"], "summary": "Delete Variable By Name", "operationId": "delete_variable_by_name_variables_name__name__delete", "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string", "title": "Name"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/variables/filter": {"post": {"tags": ["Variables"], "summary": "Read Variables", "operationId": "read_variables_variables_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_variables_variables_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Variable"}, "title": "Response Read Variables Variables Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/variables/count": {"post": {"tags": ["Variables"], "summary": "Count Variables", "operationId": "count_variables_variables_count_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_count_variables_variables_count_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "integer", "title": "Response Count Variables Variables Count Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/csrf-token": {"get": {"summary": "Create Csrf Token", "description": "Create or update a CSRF token for a client", "operationId": "create_csrf_token_csrf_token_get", "parameters": [{"name": "client", "in": "query", "required": true, "schema": {"type": "string", "description": "The client to create a CSRF token for", "title": "Client"}, "description": "The client to create a CSRF token for"}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CsrfToken"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/events": {"post": {"tags": ["Events"], "summary": "Create Events", "description": "Record a batch of Events", "operationId": "create_events_events_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Event"}, "title": "Events"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/events/filter": {"post": {"tags": ["Events"], "summary": "Read Events", "description": "Queries for Events matching the given filter criteria in the given Account.  Returns\nthe first page of results, and the URL to request the next page (if there are more\nresults).", "operationId": "read_events_events_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_events_events_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventPage"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/events/filter/next": {"get": {"tags": ["Events"], "summary": "Read Account Events Page", "description": "Returns the next page of Events for a previous query against the given Account, and\nthe URL to request the next page (if there are more results).", "operationId": "read_account_events_page_events_filter_next_get", "parameters": [{"name": "page-token", "in": "query", "required": true, "schema": {"type": "string", "title": "Page-<PERSON><PERSON>"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventPage"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/events/count-by/{countable}": {"post": {"tags": ["Events"], "summary": "Count Account Events", "description": "Returns distinct objects and the count of events associated with them.  Objects\nthat can be counted include the day the event occurred, the type of event, or\nthe IDs of the resources associated with the event.", "operationId": "count_account_events_events_count_by__countable__post", "parameters": [{"name": "countable", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Countable"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_count_account_events_events_count_by__countable__post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EventCount"}, "title": "Response Count Account Events Events Count By  Countable  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/automations/": {"post": {"tags": ["Automations"], "summary": "Create Automation", "operationId": "create_automation_automations__post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AutomationCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Automation"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/automations/{id}": {"put": {"tags": ["Automations"], "summary": "Update Automation", "operationId": "update_automation_automations__id__put", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AutomationUpdate"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["Automations"], "summary": "Patch Automation", "operationId": "patch_automation_automations__id__patch", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AutomationPartialUpdate"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Automations"], "summary": "Delete Automation", "operationId": "delete_automation_automations__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Automations"], "summary": "Read Automation", "operationId": "read_automation_automations__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Automation"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/automations/filter": {"post": {"tags": ["Automations"], "summary": "Read Automations", "operationId": "read_automations_automations_filter_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_automations_automations_filter_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Automation"}, "title": "Response Read Automations Automations Filter Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/automations/count": {"post": {"tags": ["Automations"], "summary": "Count Automations", "operationId": "count_automations_automations_count_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "integer", "title": "Response Count Automations Automations Count Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/automations/related-to/{resource_id}": {"get": {"tags": ["Automations"], "summary": "Read Automations Related To Resource", "operationId": "read_automations_related_to_resource_automations_related_to__resource_id__get", "parameters": [{"name": "resource_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Resource Id"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Automation"}, "title": "Response Read Automations Related To Resource Automations Related To  Resource Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/automations/owned-by/{resource_id}": {"delete": {"tags": ["Automations"], "summary": "Delete Automations Owned By Resource", "operationId": "delete_automations_owned_by_resource_automations_owned_by__resource_id__delete", "parameters": [{"name": "resource_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Resource Id"}}, {"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/templates/validate": {"post": {"tags": ["Automations"], "summary": "Validate Template", "operationId": "validate_template_templates_validate_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string", "default": "", "title": "Template"}}}}, "responses": {"200": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/ui/flows/count-deployments": {"post": {"tags": ["Flows", "UI"], "summary": "Count Deployments By Flow", "description": "Get deployment counts by flow id.", "operationId": "count_deployments_by_flow_ui_flows_count_deployments_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_count_deployments_by_flow_ui_flows_count_deployments_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "integer"}, "title": "Response Count Deployments By Flow Ui Flows Count Deployments Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/ui/flows/next-runs": {"post": {"tags": ["Flows", "UI"], "summary": "Next Runs By Flow", "description": "Get the next flow run by flow id.", "operationId": "next_runs_by_flow_ui_flows_next_runs_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_next_runs_by_flow_ui_flows_next_runs_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"anyOf": [{"$ref": "#/components/schemas/SimpleNextFlowRun"}, {"type": "null"}]}, "title": "Response Next Runs By Flow Ui Flows Next Runs Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/ui/flow_runs/history": {"post": {"tags": ["Flow Runs", "UI"], "summary": "Read Flow Run History", "operationId": "read_flow_run_history_ui_flow_runs_history_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_flow_run_history_ui_flow_runs_history_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SimpleFlowRun"}, "title": "Response Read Flow Run History Ui Flow Runs History Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/ui/flow_runs/count-task-runs": {"post": {"tags": ["Flow Runs", "UI"], "summary": "Count Task Runs By Flow Run", "description": "Get task run counts by flow run id.", "operationId": "count_task_runs_by_flow_run_ui_flow_runs_count_task_runs_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_count_task_runs_by_flow_run_ui_flow_runs_count_task_runs_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "integer"}, "title": "Response Count Task Runs By Flow Run Ui Flow Runs Count Task Runs Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/ui/schemas/validate": {"post": {"tags": ["UI", "<PERSON><PERSON><PERSON>"], "summary": "Validate Obj", "operationId": "validate_obj_ui_schemas_validate_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_validate_obj_ui_schemas_validate_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/ui/task_runs/dashboard/counts": {"post": {"tags": ["Task Runs", "UI"], "summary": "Read Dashboard Task Run Counts", "operationId": "read_dashboard_task_run_counts_ui_task_runs_dashboard_counts_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_read_dashboard_task_run_counts_ui_task_runs_dashboard_counts_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TaskRunCount"}, "title": "Response Read Dashboard Task Run Counts Ui Task Runs Dashboard Counts Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/ui/task_runs/count": {"post": {"tags": ["Task Runs", "UI"], "summary": "Read Task Run Counts By State", "operationId": "read_task_run_counts_by_state_ui_task_runs_count_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_read_task_run_counts_by_state_ui_task_runs_count_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CountByState"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/admin/settings": {"get": {"tags": ["Admin"], "summary": "Read Settings", "description": "Get the current Prefect REST API settings.\n\nSecret setting values will be obfuscated.", "operationId": "read_settings_admin_settings_get", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Settings"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/admin/version": {"get": {"tags": ["Admin"], "summary": "Read Version", "description": "Returns the Prefect version number", "operationId": "read_version_admin_version_get", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "string", "title": "Response Read Version Admin Version Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/admin/database/clear": {"post": {"tags": ["Admin"], "summary": "Clear Database", "description": "Clear all database tables without dropping them.", "operationId": "clear_database_admin_database_clear_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_clear_database_admin_database_clear_post"}], "title": "Body"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/admin/database/drop": {"post": {"tags": ["Admin"], "summary": "Drop Database", "description": "Drop all database objects.", "operationId": "drop_database_admin_database_drop_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_drop_database_admin_database_drop_post"}], "title": "Body"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/admin/database/create": {"post": {"tags": ["Admin"], "summary": "Create Database", "description": "Create all database objects.", "operationId": "create_database_admin_database_create_post", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_create_database_admin_database_create_post"}], "title": "Body"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/hello": {"get": {"tags": ["Root"], "summary": "Hello", "description": "Say hello!", "operationId": "hello_hello_get", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/ready": {"get": {"tags": ["Root"], "summary": "Perform Readiness Check", "operationId": "perform_readiness_check_ready_get", "parameters": [{"name": "x-prefect-api-version", "in": "header", "required": false, "schema": {"type": "string", "title": "X-Prefect-Api-Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"Artifact": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Key", "description": "An optional unique reference key for this artifact."}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type", "description": "An identifier that describes the shape of the data field. e.g. 'result', 'table', 'markdown'"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "A markdown-enabled description of the artifact."}, "data": {"anyOf": [{"type": "object"}, {}, {"type": "null"}], "title": "Data", "description": "Data associated with the artifact, e.g. a result.; structure depends on the artifact type."}, "metadata_": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> ", "description": "User-defined artifact metadata. Content must be string key and value pairs."}, "flow_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Flow Run Id", "description": "The flow run associated with the artifact."}, "task_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Task Run Id", "description": "The task run associated with the artifact."}}, "type": "object", "title": "Artifact"}, "ArtifactCollection": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "key": {"type": "string", "title": "Key", "description": "An optional unique reference key for this artifact."}, "latest_id": {"type": "string", "format": "uuid", "title": "Latest Id", "description": "The latest artifact ID associated with the key."}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type", "description": "An identifier that describes the shape of the data field. e.g. 'result', 'table', 'markdown'"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "A markdown-enabled description of the artifact."}, "data": {"anyOf": [{"type": "object"}, {}, {"type": "null"}], "title": "Data", "description": "Data associated with the artifact, e.g. a result.; structure depends on the artifact type."}, "metadata_": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> ", "description": "User-defined artifact metadata. Content must be string key and value pairs."}, "flow_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Flow Run Id", "description": "The flow run associated with the artifact."}, "task_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Task Run Id", "description": "The task run associated with the artifact."}}, "type": "object", "required": ["key", "latest_id"], "title": "ArtifactCollection"}, "ArtifactCollectionFilter": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "latest_id": {"anyOf": [{"$ref": "#/components/schemas/ArtifactCollectionFilterLatestId"}, {"type": "null"}], "description": "Filter criteria for `Artifact.id`"}, "key": {"anyOf": [{"$ref": "#/components/schemas/ArtifactCollectionFilterKey"}, {"type": "null"}], "description": "Filter criteria for `Artifact.key`"}, "flow_run_id": {"anyOf": [{"$ref": "#/components/schemas/ArtifactCollectionFilterFlowRunId"}, {"type": "null"}], "description": "Filter criteria for `Artifact.flow_run_id`"}, "task_run_id": {"anyOf": [{"$ref": "#/components/schemas/ArtifactCollectionFilterTaskRunId"}, {"type": "null"}], "description": "Filter criteria for `Artifact.task_run_id`"}, "type": {"anyOf": [{"$ref": "#/components/schemas/ArtifactCollectionFilterType"}, {"type": "null"}], "description": "Filter criteria for `Artifact.type`"}}, "additionalProperties": false, "type": "object", "title": "ArtifactCollectionFilter", "description": "Filter artifact collections. Only artifact collections matching all criteria will be returned"}, "ArtifactCollectionFilterFlowRunId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of flow run IDs to include"}}, "additionalProperties": false, "type": "object", "title": "ArtifactCollectionFilterFlowRunId", "description": "Filter by `ArtifactCollection.flow_run_id`."}, "ArtifactCollectionFilterKey": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of artifact keys to include"}, "like_": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Like ", "description": "A string to match artifact keys against. This can include SQL wildcard characters like `%` and `_`.", "examples": ["my-artifact-%"]}, "exists_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Exists ", "description": "If `true`, only include artifacts with a non-null key. If `false`, only include artifacts with a null key. Should return all rows in the ArtifactCollection table if specified."}}, "additionalProperties": false, "type": "object", "title": "ArtifactCollectionFilterKey", "description": "Filter by `ArtifactCollection.key`."}, "ArtifactCollectionFilterLatestId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of artifact ids to include"}}, "additionalProperties": false, "type": "object", "title": "ArtifactCollectionFilterLatestId", "description": "Filter by `ArtifactCollection.latest_id`."}, "ArtifactCollectionFilterTaskRunId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of task run IDs to include"}}, "additionalProperties": false, "type": "object", "title": "ArtifactCollectionFilterTaskRunId", "description": "Filter by `ArtifactCollection.task_run_id`."}, "ArtifactCollectionFilterType": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of artifact types to include"}, "not_any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Not Any ", "description": "A list of artifact types to exclude"}}, "additionalProperties": false, "type": "object", "title": "ArtifactCollectionFilterType", "description": "Filter by `ArtifactCollection.type`."}, "ArtifactCollectionSort": {"type": "string", "enum": ["CREATED_DESC", "UPDATED_DESC", "ID_DESC", "KEY_DESC", "KEY_ASC"], "title": "ArtifactCollectionSort", "description": "Defines artifact collection sorting options."}, "ArtifactCreate": {"properties": {"key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Key", "description": "An optional unique reference key for this artifact."}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type", "description": "An identifier that describes the shape of the data field. e.g. 'result', 'table', 'markdown'"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "A markdown-enabled description of the artifact."}, "data": {"anyOf": [{"type": "object"}, {}, {"type": "null"}], "title": "Data", "description": "Data associated with the artifact, e.g. a result.; structure depends on the artifact type."}, "metadata_": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> ", "description": "User-defined artifact metadata. Content must be string key and value pairs."}, "flow_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Flow Run Id", "description": "The flow run associated with the artifact."}, "task_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Task Run Id", "description": "The task run associated with the artifact."}}, "additionalProperties": false, "type": "object", "title": "ArtifactCreate", "description": "Data used by the Prefect REST API to create an artifact."}, "ArtifactFilter": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "id": {"anyOf": [{"$ref": "#/components/schemas/ArtifactFilterId"}, {"type": "null"}], "description": "Filter criteria for `Artifact.id`"}, "key": {"anyOf": [{"$ref": "#/components/schemas/ArtifactFilterKey"}, {"type": "null"}], "description": "Filter criteria for `Artifact.key`"}, "flow_run_id": {"anyOf": [{"$ref": "#/components/schemas/ArtifactFilterFlowRunId"}, {"type": "null"}], "description": "Filter criteria for `Artifact.flow_run_id`"}, "task_run_id": {"anyOf": [{"$ref": "#/components/schemas/ArtifactFilterTaskRunId"}, {"type": "null"}], "description": "Filter criteria for `Artifact.task_run_id`"}, "type": {"anyOf": [{"$ref": "#/components/schemas/ArtifactFilterType"}, {"type": "null"}], "description": "Filter criteria for `Artifact.type`"}}, "additionalProperties": false, "type": "object", "title": "ArtifactFilter", "description": "Filter artifacts. Only artifacts matching all criteria will be returned"}, "ArtifactFilterFlowRunId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of flow run IDs to include"}}, "additionalProperties": false, "type": "object", "title": "ArtifactFilterFlowRunId", "description": "Filter by `Artifact.flow_run_id`."}, "ArtifactFilterId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of artifact ids to include"}}, "additionalProperties": false, "type": "object", "title": "ArtifactFilterId", "description": "Filter by `Artifact.id`."}, "ArtifactFilterKey": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of artifact keys to include"}, "like_": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Like ", "description": "A string to match artifact keys against. This can include SQL wildcard characters like `%` and `_`.", "examples": ["my-artifact-%"]}, "exists_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Exists ", "description": "If `true`, only include artifacts with a non-null key. If `false`, only include artifacts with a null key."}}, "additionalProperties": false, "type": "object", "title": "ArtifactFilterKey", "description": "Filter by `Artifact.key`."}, "ArtifactFilterTaskRunId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of task run IDs to include"}}, "additionalProperties": false, "type": "object", "title": "ArtifactFilterTaskRunId", "description": "Filter by `Artifact.task_run_id`."}, "ArtifactFilterType": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of artifact types to include"}, "not_any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Not Any ", "description": "A list of artifact types to exclude"}}, "additionalProperties": false, "type": "object", "title": "ArtifactFilterType", "description": "Filter by `Artifact.type`."}, "ArtifactSort": {"type": "string", "enum": ["CREATED_DESC", "UPDATED_DESC", "ID_DESC", "KEY_DESC", "KEY_ASC"], "title": "ArtifactSort", "description": "Defines artifact sorting options."}, "ArtifactUpdate": {"properties": {"data": {"anyOf": [{"type": "object"}, {}, {"type": "null"}], "title": "Data"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "metadata_": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> "}}, "additionalProperties": false, "type": "object", "title": "ArtifactUpdate", "description": "Data used by the Prefect REST API to update an artifact."}, "Automation": {"properties": {"name": {"type": "string", "title": "Name", "description": "The name of this automation"}, "description": {"type": "string", "title": "Description", "description": "A longer description of this automation", "default": ""}, "enabled": {"type": "boolean", "title": "Enabled", "description": "Whether this automation will be evaluated", "default": true}, "trigger": {"anyOf": [{"$ref": "#/components/schemas/prefect__server__events__schemas__automations__EventTrigger"}, {"$ref": "#/components/schemas/prefect__server__events__schemas__automations__CompoundTrigger-Output"}, {"$ref": "#/components/schemas/prefect__server__events__schemas__automations__SequenceTrigger-Output"}], "title": "<PERSON><PERSON>", "description": "The criteria for which events this Automation covers and how it will respond to the presence or absence of those events"}, "actions": {"items": {"anyOf": [{"$ref": "#/components/schemas/DoNothing"}, {"$ref": "#/components/schemas/RunDeployment"}, {"$ref": "#/components/schemas/PauseDeployment"}, {"$ref": "#/components/schemas/ResumeDeployment"}, {"$ref": "#/components/schemas/CancelFlowRun"}, {"$ref": "#/components/schemas/ChangeFlowRunState"}, {"$ref": "#/components/schemas/PauseWorkQueue"}, {"$ref": "#/components/schemas/ResumeWorkQueue"}, {"$ref": "#/components/schemas/SendNotification"}, {"$ref": "#/components/schemas/CallWebhook"}, {"$ref": "#/components/schemas/PauseAutomation"}, {"$ref": "#/components/schemas/ResumeAutomation"}, {"$ref": "#/components/schemas/SuspendFlowRun"}, {"$ref": "#/components/schemas/PauseWorkPool"}, {"$ref": "#/components/schemas/ResumeWorkPool"}]}, "type": "array", "title": "Actions", "description": "The actions to perform when this Automation triggers"}, "actions_on_trigger": {"items": {"anyOf": [{"$ref": "#/components/schemas/DoNothing"}, {"$ref": "#/components/schemas/RunDeployment"}, {"$ref": "#/components/schemas/PauseDeployment"}, {"$ref": "#/components/schemas/ResumeDeployment"}, {"$ref": "#/components/schemas/CancelFlowRun"}, {"$ref": "#/components/schemas/ChangeFlowRunState"}, {"$ref": "#/components/schemas/PauseWorkQueue"}, {"$ref": "#/components/schemas/ResumeWorkQueue"}, {"$ref": "#/components/schemas/SendNotification"}, {"$ref": "#/components/schemas/CallWebhook"}, {"$ref": "#/components/schemas/PauseAutomation"}, {"$ref": "#/components/schemas/ResumeAutomation"}, {"$ref": "#/components/schemas/SuspendFlowRun"}, {"$ref": "#/components/schemas/PauseWorkPool"}, {"$ref": "#/components/schemas/ResumeWorkPool"}]}, "type": "array", "title": "Actions On Trigger", "description": "The actions to perform when an Automation goes into a triggered state"}, "actions_on_resolve": {"items": {"anyOf": [{"$ref": "#/components/schemas/DoNothing"}, {"$ref": "#/components/schemas/RunDeployment"}, {"$ref": "#/components/schemas/PauseDeployment"}, {"$ref": "#/components/schemas/ResumeDeployment"}, {"$ref": "#/components/schemas/CancelFlowRun"}, {"$ref": "#/components/schemas/ChangeFlowRunState"}, {"$ref": "#/components/schemas/PauseWorkQueue"}, {"$ref": "#/components/schemas/ResumeWorkQueue"}, {"$ref": "#/components/schemas/SendNotification"}, {"$ref": "#/components/schemas/CallWebhook"}, {"$ref": "#/components/schemas/PauseAutomation"}, {"$ref": "#/components/schemas/ResumeAutomation"}, {"$ref": "#/components/schemas/SuspendFlowRun"}, {"$ref": "#/components/schemas/PauseWorkPool"}, {"$ref": "#/components/schemas/ResumeWorkPool"}]}, "type": "array", "title": "Actions On Resolve", "description": "The actions to perform when an Automation goes into a resolving state"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}}, "type": "object", "required": ["name", "trigger", "actions"], "title": "Automation"}, "AutomationCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "The name of this automation"}, "description": {"type": "string", "title": "Description", "description": "A longer description of this automation", "default": ""}, "enabled": {"type": "boolean", "title": "Enabled", "description": "Whether this automation will be evaluated", "default": true}, "trigger": {"anyOf": [{"$ref": "#/components/schemas/prefect__server__events__schemas__automations__EventTrigger"}, {"$ref": "#/components/schemas/prefect__server__events__schemas__automations__CompoundTrigger-Input"}, {"$ref": "#/components/schemas/prefect__server__events__schemas__automations__SequenceTrigger-Input"}], "title": "<PERSON><PERSON>", "description": "The criteria for which events this Automation covers and how it will respond to the presence or absence of those events"}, "actions": {"items": {"anyOf": [{"$ref": "#/components/schemas/DoNothing"}, {"$ref": "#/components/schemas/RunDeployment"}, {"$ref": "#/components/schemas/PauseDeployment"}, {"$ref": "#/components/schemas/ResumeDeployment"}, {"$ref": "#/components/schemas/CancelFlowRun"}, {"$ref": "#/components/schemas/ChangeFlowRunState"}, {"$ref": "#/components/schemas/PauseWorkQueue"}, {"$ref": "#/components/schemas/ResumeWorkQueue"}, {"$ref": "#/components/schemas/SendNotification"}, {"$ref": "#/components/schemas/CallWebhook"}, {"$ref": "#/components/schemas/PauseAutomation"}, {"$ref": "#/components/schemas/ResumeAutomation"}, {"$ref": "#/components/schemas/SuspendFlowRun"}, {"$ref": "#/components/schemas/PauseWorkPool"}, {"$ref": "#/components/schemas/ResumeWorkPool"}]}, "type": "array", "title": "Actions", "description": "The actions to perform when this Automation triggers"}, "actions_on_trigger": {"items": {"anyOf": [{"$ref": "#/components/schemas/DoNothing"}, {"$ref": "#/components/schemas/RunDeployment"}, {"$ref": "#/components/schemas/PauseDeployment"}, {"$ref": "#/components/schemas/ResumeDeployment"}, {"$ref": "#/components/schemas/CancelFlowRun"}, {"$ref": "#/components/schemas/ChangeFlowRunState"}, {"$ref": "#/components/schemas/PauseWorkQueue"}, {"$ref": "#/components/schemas/ResumeWorkQueue"}, {"$ref": "#/components/schemas/SendNotification"}, {"$ref": "#/components/schemas/CallWebhook"}, {"$ref": "#/components/schemas/PauseAutomation"}, {"$ref": "#/components/schemas/ResumeAutomation"}, {"$ref": "#/components/schemas/SuspendFlowRun"}, {"$ref": "#/components/schemas/PauseWorkPool"}, {"$ref": "#/components/schemas/ResumeWorkPool"}]}, "type": "array", "title": "Actions On Trigger", "description": "The actions to perform when an Automation goes into a triggered state"}, "actions_on_resolve": {"items": {"anyOf": [{"$ref": "#/components/schemas/DoNothing"}, {"$ref": "#/components/schemas/RunDeployment"}, {"$ref": "#/components/schemas/PauseDeployment"}, {"$ref": "#/components/schemas/ResumeDeployment"}, {"$ref": "#/components/schemas/CancelFlowRun"}, {"$ref": "#/components/schemas/ChangeFlowRunState"}, {"$ref": "#/components/schemas/PauseWorkQueue"}, {"$ref": "#/components/schemas/ResumeWorkQueue"}, {"$ref": "#/components/schemas/SendNotification"}, {"$ref": "#/components/schemas/CallWebhook"}, {"$ref": "#/components/schemas/PauseAutomation"}, {"$ref": "#/components/schemas/ResumeAutomation"}, {"$ref": "#/components/schemas/SuspendFlowRun"}, {"$ref": "#/components/schemas/PauseWorkPool"}, {"$ref": "#/components/schemas/ResumeWorkPool"}]}, "type": "array", "title": "Actions On Resolve", "description": "The actions to perform when an Automation goes into a resolving state"}, "owner_resource": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Owner Resource", "description": "The resource to which this automation belongs"}}, "additionalProperties": false, "type": "object", "required": ["name", "trigger", "actions"], "title": "AutomationCreate"}, "AutomationFilter": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "name": {"anyOf": [{"$ref": "#/components/schemas/AutomationFilterName"}, {"type": "null"}], "description": "Filter criteria for `Automation.name`"}, "created": {"anyOf": [{"$ref": "#/components/schemas/AutomationFilterCreated"}, {"type": "null"}], "description": "Filter criteria for `Automation.created`"}}, "additionalProperties": false, "type": "object", "title": "AutomationFilter"}, "AutomationFilterCreated": {"properties": {"before_": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Before ", "description": "Only include automations created before this datetime"}}, "additionalProperties": false, "type": "object", "title": "AutomationFilterCreated", "description": "Filter by `Automation.created`."}, "AutomationFilterName": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "Only include automations with names that match any of these strings"}}, "additionalProperties": false, "type": "object", "title": "AutomationFilterName", "description": "Filter by `Automation.created`."}, "AutomationPartialUpdate": {"properties": {"enabled": {"type": "boolean", "title": "Enabled", "description": "Whether this automation will be evaluated", "default": true}}, "additionalProperties": false, "type": "object", "title": "AutomationPartialUpdate"}, "AutomationSort": {"type": "string", "enum": ["CREATED_DESC", "UPDATED_DESC", "NAME_ASC", "NAME_DESC"], "title": "AutomationSort", "description": "Defines automations sorting options."}, "AutomationUpdate": {"properties": {"name": {"type": "string", "title": "Name", "description": "The name of this automation"}, "description": {"type": "string", "title": "Description", "description": "A longer description of this automation", "default": ""}, "enabled": {"type": "boolean", "title": "Enabled", "description": "Whether this automation will be evaluated", "default": true}, "trigger": {"anyOf": [{"$ref": "#/components/schemas/prefect__server__events__schemas__automations__EventTrigger"}, {"$ref": "#/components/schemas/prefect__server__events__schemas__automations__CompoundTrigger-Input"}, {"$ref": "#/components/schemas/prefect__server__events__schemas__automations__SequenceTrigger-Input"}], "title": "<PERSON><PERSON>", "description": "The criteria for which events this Automation covers and how it will respond to the presence or absence of those events"}, "actions": {"items": {"anyOf": [{"$ref": "#/components/schemas/DoNothing"}, {"$ref": "#/components/schemas/RunDeployment"}, {"$ref": "#/components/schemas/PauseDeployment"}, {"$ref": "#/components/schemas/ResumeDeployment"}, {"$ref": "#/components/schemas/CancelFlowRun"}, {"$ref": "#/components/schemas/ChangeFlowRunState"}, {"$ref": "#/components/schemas/PauseWorkQueue"}, {"$ref": "#/components/schemas/ResumeWorkQueue"}, {"$ref": "#/components/schemas/SendNotification"}, {"$ref": "#/components/schemas/CallWebhook"}, {"$ref": "#/components/schemas/PauseAutomation"}, {"$ref": "#/components/schemas/ResumeAutomation"}, {"$ref": "#/components/schemas/SuspendFlowRun"}, {"$ref": "#/components/schemas/PauseWorkPool"}, {"$ref": "#/components/schemas/ResumeWorkPool"}]}, "type": "array", "title": "Actions", "description": "The actions to perform when this Automation triggers"}, "actions_on_trigger": {"items": {"anyOf": [{"$ref": "#/components/schemas/DoNothing"}, {"$ref": "#/components/schemas/RunDeployment"}, {"$ref": "#/components/schemas/PauseDeployment"}, {"$ref": "#/components/schemas/ResumeDeployment"}, {"$ref": "#/components/schemas/CancelFlowRun"}, {"$ref": "#/components/schemas/ChangeFlowRunState"}, {"$ref": "#/components/schemas/PauseWorkQueue"}, {"$ref": "#/components/schemas/ResumeWorkQueue"}, {"$ref": "#/components/schemas/SendNotification"}, {"$ref": "#/components/schemas/CallWebhook"}, {"$ref": "#/components/schemas/PauseAutomation"}, {"$ref": "#/components/schemas/ResumeAutomation"}, {"$ref": "#/components/schemas/SuspendFlowRun"}, {"$ref": "#/components/schemas/PauseWorkPool"}, {"$ref": "#/components/schemas/ResumeWorkPool"}]}, "type": "array", "title": "Actions On Trigger", "description": "The actions to perform when an Automation goes into a triggered state"}, "actions_on_resolve": {"items": {"anyOf": [{"$ref": "#/components/schemas/DoNothing"}, {"$ref": "#/components/schemas/RunDeployment"}, {"$ref": "#/components/schemas/PauseDeployment"}, {"$ref": "#/components/schemas/ResumeDeployment"}, {"$ref": "#/components/schemas/CancelFlowRun"}, {"$ref": "#/components/schemas/ChangeFlowRunState"}, {"$ref": "#/components/schemas/PauseWorkQueue"}, {"$ref": "#/components/schemas/ResumeWorkQueue"}, {"$ref": "#/components/schemas/SendNotification"}, {"$ref": "#/components/schemas/CallWebhook"}, {"$ref": "#/components/schemas/PauseAutomation"}, {"$ref": "#/components/schemas/ResumeAutomation"}, {"$ref": "#/components/schemas/SuspendFlowRun"}, {"$ref": "#/components/schemas/PauseWorkPool"}, {"$ref": "#/components/schemas/ResumeWorkPool"}]}, "type": "array", "title": "Actions On Resolve", "description": "The actions to perform when an Automation goes into a resolving state"}}, "additionalProperties": false, "type": "object", "required": ["name", "trigger", "actions"], "title": "AutomationUpdate"}, "BlockDocument": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "name": {"anyOf": [{"type": "string", "pattern": "^[^/%&><]+$"}, {"type": "null"}], "title": "Name", "description": "The block document's name. Not required for anonymous block documents."}, "data": {"type": "object", "title": "Data", "description": "The block document's data"}, "block_schema_id": {"type": "string", "format": "uuid", "title": "Block Schema Id", "description": "A block schema ID"}, "block_schema": {"anyOf": [{"$ref": "#/components/schemas/BlockSchema"}, {"type": "null"}], "description": "The associated block schema"}, "block_type_id": {"type": "string", "format": "uuid", "title": "Block Type Id", "description": "A block type ID"}, "block_type_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Block Type Name", "description": "The associated block type's name"}, "block_type": {"anyOf": [{"$ref": "#/components/schemas/BlockType"}, {"type": "null"}], "description": "The associated block type"}, "block_document_references": {"additionalProperties": {"type": "object"}, "type": "object", "title": "Block Document References", "description": "Record of the block document's references"}, "is_anonymous": {"type": "boolean", "title": "Is Anonymous", "description": "Whether the block is anonymous (anonymous blocks are usually created by Prefect automatically)", "default": false}}, "type": "object", "required": ["block_schema_id", "block_type_id"], "title": "BlockDocument", "description": "An ORM representation of a block document."}, "BlockDocumentCreate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "The block document's name. Not required for anonymous block documents."}, "data": {"type": "object", "title": "Data", "description": "The block document's data"}, "block_schema_id": {"type": "string", "format": "uuid", "title": "Block Schema Id", "description": "A block schema ID"}, "block_type_id": {"type": "string", "format": "uuid", "title": "Block Type Id", "description": "A block type ID"}, "is_anonymous": {"type": "boolean", "title": "Is Anonymous", "description": "Whether the block is anonymous (anonymous blocks are usually created by Prefect automatically)", "default": false}}, "additionalProperties": false, "type": "object", "required": ["block_schema_id", "block_type_id"], "title": "BlockDocumentCreate", "description": "Data used by the Prefect REST API to create a block document."}, "BlockDocumentFilter": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "id": {"anyOf": [{"$ref": "#/components/schemas/BlockDocumentFilterId"}, {"type": "null"}], "description": "Filter criteria for `BlockDocument.id`"}, "is_anonymous": {"anyOf": [{"$ref": "#/components/schemas/BlockDocumentFilterIsAnonymous"}, {"type": "null"}], "description": "Filter criteria for `BlockDocument.is_anonymous`. Defaults to excluding anonymous blocks.", "default": {"eq_": false}}, "block_type_id": {"anyOf": [{"$ref": "#/components/schemas/BlockDocumentFilterBlockTypeId"}, {"type": "null"}], "description": "Filter criteria for `BlockDocument.block_type_id`"}, "name": {"anyOf": [{"$ref": "#/components/schemas/BlockDocumentFilterName"}, {"type": "null"}], "description": "Filter criteria for `BlockDocument.name`"}}, "additionalProperties": false, "type": "object", "title": "BlockDocumentFilter", "description": "Filter BlockDocuments. Only BlockDocuments matching all criteria will be returned"}, "BlockDocumentFilterBlockTypeId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of block type ids to include"}}, "additionalProperties": false, "type": "object", "title": "BlockDocumentFilterBlockTypeId", "description": "Filter by `BlockDocument.block_type_id`."}, "BlockDocumentFilterId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of block ids to include"}}, "additionalProperties": false, "type": "object", "title": "BlockDocumentFilterId", "description": "Filter by `BlockDocument.id`."}, "BlockDocumentFilterIsAnonymous": {"properties": {"eq_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Eq ", "description": "Filter block documents for only those that are or are not anonymous."}}, "additionalProperties": false, "type": "object", "title": "BlockDocumentFilterIsAnonymous", "description": "Filter by `BlockDocument.is_anonymous`."}, "BlockDocumentFilterName": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of block names to include"}, "like_": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Like ", "description": "A string to match block names against. This can include SQL wildcard characters like `%` and `_`.", "examples": ["my-block%"]}}, "additionalProperties": false, "type": "object", "title": "BlockDocumentFilterName", "description": "Filter by `BlockDocument.name`."}, "BlockDocumentSort": {"type": "string", "enum": ["NAME_DESC", "NAME_ASC", "BLOCK_TYPE_AND_NAME_ASC"], "title": "BlockDocumentSort", "description": "Defines block document sorting options."}, "BlockDocumentUpdate": {"properties": {"block_schema_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Block Schema Id", "description": "A block schema ID"}, "data": {"type": "object", "title": "Data", "description": "The block document's data"}, "merge_existing_data": {"type": "boolean", "title": "Merge Existing Data", "default": true}}, "additionalProperties": false, "type": "object", "title": "BlockDocumentUpdate", "description": "Data used by the Prefect REST API to update a block document."}, "BlockSchema": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "checksum": {"type": "string", "title": "Checksum", "description": "The block schema's unique checksum"}, "fields": {"type": "object", "title": "Fields", "description": "The block schema's field schema"}, "block_type_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Block Type Id", "description": "A block type ID"}, "block_type": {"anyOf": [{"$ref": "#/components/schemas/BlockType"}, {"type": "null"}], "description": "The associated block type"}, "capabilities": {"items": {"type": "string"}, "type": "array", "title": "Capabilities", "description": "A list of Block capabilities"}, "version": {"type": "string", "title": "Version", "description": "Human readable identifier for the block schema", "default": "non-versioned"}}, "type": "object", "required": ["checksum", "block_type_id"], "title": "BlockSchema", "description": "An ORM representation of a block schema."}, "BlockSchemaCreate": {"properties": {"fields": {"type": "object", "title": "Fields", "description": "The block schema's field schema"}, "block_type_id": {"type": "string", "format": "uuid", "title": "Block Type Id", "description": "A block type ID"}, "capabilities": {"items": {"type": "string"}, "type": "array", "title": "Capabilities", "description": "A list of Block capabilities"}, "version": {"type": "string", "title": "Version", "description": "Human readable identifier for the block schema", "default": "non-versioned"}}, "additionalProperties": false, "type": "object", "required": ["block_type_id"], "title": "BlockSchemaCreate", "description": "Data used by the Prefect REST API to create a block schema."}, "BlockSchemaFilter": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "block_type_id": {"anyOf": [{"$ref": "#/components/schemas/BlockSchemaFilterBlockTypeId"}, {"type": "null"}], "description": "Filter criteria for `BlockSchema.block_type_id`"}, "block_capabilities": {"anyOf": [{"$ref": "#/components/schemas/BlockSchemaFilterCapabilities"}, {"type": "null"}], "description": "Filter criteria for `BlockSchema.capabilities`"}, "id": {"anyOf": [{"$ref": "#/components/schemas/BlockSchemaFilterId"}, {"type": "null"}], "description": "Filter criteria for `BlockSchema.id`"}, "version": {"anyOf": [{"$ref": "#/components/schemas/BlockSchemaFilterVersion"}, {"type": "null"}], "description": "Filter criteria for `BlockSchema.version`"}}, "additionalProperties": false, "type": "object", "title": "BlockSchemaFilter", "description": "Filter BlockSchemas"}, "BlockSchemaFilterBlockTypeId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of block type ids to include"}}, "additionalProperties": false, "type": "object", "title": "BlockSchemaFilterBlockTypeId", "description": "Filter by `BlockSchema.block_type_id`."}, "BlockSchemaFilterCapabilities": {"properties": {"all_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "All ", "description": "A list of block capabilities. Block entities will be returned only if an associated block schema has a superset of the defined capabilities.", "examples": [["write-storage", "read-storage"]]}}, "additionalProperties": false, "type": "object", "title": "BlockSchemaFilterCapabilities", "description": "Filter by `BlockSchema.capabilities`"}, "BlockSchemaFilterId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of IDs to include"}}, "additionalProperties": false, "type": "object", "title": "BlockSchemaFilterId", "description": "Filter by BlockSchema.id"}, "BlockSchemaFilterVersion": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of block schema versions.", "examples": [["2.0.0", "2.1.0"]]}}, "additionalProperties": false, "type": "object", "title": "BlockSchemaFilterVersion", "description": "Filter by `BlockSchema.capabilities`"}, "BlockType": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "name": {"type": "string", "pattern": "^[^/%&><]+$", "title": "Name", "description": "A block type's name"}, "slug": {"type": "string", "title": "Slug", "description": "A block type's slug"}, "logo_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Logo Url", "description": "Web URL for the block type's logo"}, "documentation_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Documentation Url", "description": "Web URL for the block type's documentation"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "A short blurb about the corresponding block's intended use"}, "code_example": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code Example", "description": "A code snippet demonstrating use of the corresponding block"}, "is_protected": {"type": "boolean", "title": "Is Protected", "description": "Protected block types cannot be modified via API.", "default": false}}, "type": "object", "required": ["name", "slug"], "title": "BlockType", "description": "An ORM representation of a block type"}, "BlockTypeCreate": {"properties": {"name": {"type": "string", "pattern": "^[^/%&><]+$", "title": "Name", "description": "A block type's name"}, "slug": {"type": "string", "title": "Slug", "description": "A block type's slug"}, "logo_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Logo Url", "description": "Web URL for the block type's logo"}, "documentation_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Documentation Url", "description": "Web URL for the block type's documentation"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "A short blurb about the corresponding block's intended use"}, "code_example": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code Example", "description": "A code snippet demonstrating use of the corresponding block"}}, "additionalProperties": false, "type": "object", "required": ["name", "slug"], "title": "BlockTypeCreate", "description": "Data used by the Prefect REST API to create a block type."}, "BlockTypeFilter": {"properties": {"name": {"anyOf": [{"$ref": "#/components/schemas/BlockTypeFilterName"}, {"type": "null"}], "description": "Filter criteria for `BlockType.name`"}, "slug": {"anyOf": [{"$ref": "#/components/schemas/BlockTypeFilterSlug"}, {"type": "null"}], "description": "Filter criteria for `BlockType.slug`"}}, "additionalProperties": false, "type": "object", "title": "BlockTypeFilter", "description": "Filter BlockTypes"}, "BlockTypeFilterName": {"properties": {"like_": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Like ", "description": "A case-insensitive partial match. For example,  passing 'marvin' will match 'marvin', 'sad-<PERSON>', and 'marvin-robot'.", "examples": ["marvin"]}}, "additionalProperties": false, "type": "object", "title": "BlockTypeFilterName", "description": "Filter by `BlockType.name`"}, "BlockTypeFilterSlug": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of slugs to match"}}, "additionalProperties": false, "type": "object", "title": "BlockTypeFilterSlug", "description": "Filter by `BlockType.slug`"}, "BlockTypeUpdate": {"properties": {"logo_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Logo Url"}, "documentation_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Documentation Url"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "code_example": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code Example"}}, "additionalProperties": false, "type": "object", "title": "BlockTypeUpdate", "description": "Data used by the Prefect REST API to update a block type."}, "Body_average_flow_run_lateness_flow_runs_lateness_post": {"properties": {"flows": {"anyOf": [{"$ref": "#/components/schemas/FlowFilter"}, {"type": "null"}]}, "flow_runs": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilter"}, {"type": "null"}]}, "task_runs": {"anyOf": [{"$ref": "#/components/schemas/TaskRunFilter"}, {"type": "null"}]}, "deployments": {"anyOf": [{"$ref": "#/components/schemas/DeploymentFilter"}, {"type": "null"}]}, "work_pools": {"anyOf": [{"$ref": "#/components/schemas/WorkPoolFilter"}, {"type": "null"}]}, "work_pool_queues": {"anyOf": [{"$ref": "#/components/schemas/WorkQueueFilter"}, {"type": "null"}]}}, "type": "object", "title": "Body_average_flow_run_lateness_flow_runs_lateness_post"}, "Body_bulk_decrement_active_slots_v2_concurrency_limits_decrement_post": {"properties": {"slots": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Slots"}, "names": {"items": {"type": "string"}, "type": "array", "title": "Names", "min_items": 1}, "occupancy_seconds": {"anyOf": [{"type": "number", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Occupancy Seconds"}}, "type": "object", "required": ["slots", "names"], "title": "Body_bulk_decrement_active_slots_v2_concurrency_limits_decrement_post"}, "Body_bulk_increment_active_slots_v2_concurrency_limits_increment_post": {"properties": {"slots": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Slots"}, "names": {"items": {"type": "string"}, "type": "array", "title": "Names", "min_items": 1}, "mode": {"type": "string", "enum": ["concurrency", "rate_limit"], "title": "Mode", "default": "concurrency"}}, "type": "object", "required": ["slots", "names"], "title": "Body_bulk_increment_active_slots_v2_concurrency_limits_increment_post"}, "Body_clear_database_admin_database_clear_post": {"properties": {"confirm": {"type": "boolean", "title": "Confirm", "description": "Pass confirm=True to confirm you want to modify the database.", "default": false}}, "type": "object", "title": "Body_clear_database_admin_database_clear_post"}, "Body_count_account_events_events_count_by__countable__post": {"properties": {"filter": {"$ref": "#/components/schemas/EventFilter"}, "time_unit": {"allOf": [{"$ref": "#/components/schemas/TimeUnit"}], "default": "day"}, "time_interval": {"type": "number", "minimum": 0.01, "title": "Time Interval", "default": 1.0}}, "type": "object", "required": ["filter"], "title": "Body_count_account_events_events_count_by__countable__post"}, "Body_count_artifacts_artifacts_count_post": {"properties": {"artifacts": {"allOf": [{"$ref": "#/components/schemas/ArtifactFilter"}]}, "flow_runs": {"allOf": [{"$ref": "#/components/schemas/FlowRunFilter"}]}, "task_runs": {"allOf": [{"$ref": "#/components/schemas/TaskRunFilter"}]}, "flows": {"allOf": [{"$ref": "#/components/schemas/FlowFilter"}]}, "deployments": {"allOf": [{"$ref": "#/components/schemas/DeploymentFilter"}]}}, "type": "object", "title": "Body_count_artifacts_artifacts_count_post"}, "Body_count_block_documents_block_documents_count_post": {"properties": {"block_documents": {"anyOf": [{"$ref": "#/components/schemas/BlockDocumentFilter"}, {"type": "null"}]}, "block_types": {"anyOf": [{"$ref": "#/components/schemas/BlockTypeFilter"}, {"type": "null"}]}, "block_schemas": {"anyOf": [{"$ref": "#/components/schemas/BlockSchemaFilter"}, {"type": "null"}]}}, "type": "object", "title": "Body_count_block_documents_block_documents_count_post"}, "Body_count_deployments_by_flow_ui_flows_count_deployments_post": {"properties": {"flow_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "Flow Ids", "max_items": 200}}, "type": "object", "required": ["flow_ids"], "title": "Body_count_deployments_by_flow_ui_flows_count_deployments_post"}, "Body_count_deployments_deployments_count_post": {"properties": {"flows": {"allOf": [{"$ref": "#/components/schemas/FlowFilter"}]}, "flow_runs": {"allOf": [{"$ref": "#/components/schemas/FlowRunFilter"}]}, "task_runs": {"allOf": [{"$ref": "#/components/schemas/TaskRunFilter"}]}, "deployments": {"allOf": [{"$ref": "#/components/schemas/DeploymentFilter"}]}, "work_pools": {"allOf": [{"$ref": "#/components/schemas/WorkPoolFilter"}]}, "work_pool_queues": {"allOf": [{"$ref": "#/components/schemas/WorkQueueFilter"}]}}, "type": "object", "title": "Body_count_deployments_deployments_count_post"}, "Body_count_flow_runs_flow_runs_count_post": {"properties": {"flows": {"allOf": [{"$ref": "#/components/schemas/FlowFilter"}]}, "flow_runs": {"allOf": [{"$ref": "#/components/schemas/FlowRunFilter"}]}, "task_runs": {"allOf": [{"$ref": "#/components/schemas/TaskRunFilter"}]}, "deployments": {"allOf": [{"$ref": "#/components/schemas/DeploymentFilter"}]}, "work_pools": {"allOf": [{"$ref": "#/components/schemas/WorkPoolFilter"}]}, "work_pool_queues": {"allOf": [{"$ref": "#/components/schemas/WorkQueueFilter"}]}}, "type": "object", "title": "Body_count_flow_runs_flow_runs_count_post"}, "Body_count_flows_flows_count_post": {"properties": {"flows": {"allOf": [{"$ref": "#/components/schemas/FlowFilter"}]}, "flow_runs": {"allOf": [{"$ref": "#/components/schemas/FlowRunFilter"}]}, "task_runs": {"allOf": [{"$ref": "#/components/schemas/TaskRunFilter"}]}, "deployments": {"allOf": [{"$ref": "#/components/schemas/DeploymentFilter"}]}, "work_pools": {"allOf": [{"$ref": "#/components/schemas/WorkPoolFilter"}]}}, "type": "object", "title": "Body_count_flows_flows_count_post"}, "Body_count_latest_artifacts_artifacts_latest_count_post": {"properties": {"artifacts": {"allOf": [{"$ref": "#/components/schemas/ArtifactCollectionFilter"}]}, "flow_runs": {"allOf": [{"$ref": "#/components/schemas/FlowRunFilter"}]}, "task_runs": {"allOf": [{"$ref": "#/components/schemas/TaskRunFilter"}]}, "flows": {"allOf": [{"$ref": "#/components/schemas/FlowFilter"}]}, "deployments": {"allOf": [{"$ref": "#/components/schemas/DeploymentFilter"}]}}, "type": "object", "title": "Body_count_latest_artifacts_artifacts_latest_count_post"}, "Body_count_task_runs_by_flow_run_ui_flow_runs_count_task_runs_post": {"properties": {"flow_run_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "Flow Run Ids", "max_items": 200}}, "type": "object", "required": ["flow_run_ids"], "title": "Body_count_task_runs_by_flow_run_ui_flow_runs_count_task_runs_post"}, "Body_count_task_runs_task_runs_count_post": {"properties": {"flows": {"allOf": [{"$ref": "#/components/schemas/FlowFilter"}]}, "flow_runs": {"allOf": [{"$ref": "#/components/schemas/FlowRunFilter"}]}, "task_runs": {"allOf": [{"$ref": "#/components/schemas/TaskRunFilter"}]}, "deployments": {"allOf": [{"$ref": "#/components/schemas/DeploymentFilter"}]}}, "type": "object", "title": "Body_count_task_runs_task_runs_count_post"}, "Body_count_variables_variables_count_post": {"properties": {"variables": {"anyOf": [{"$ref": "#/components/schemas/VariableFilter"}, {"type": "null"}]}}, "type": "object", "title": "Body_count_variables_variables_count_post"}, "Body_count_work_pools_work_pools_count_post": {"properties": {"work_pools": {"anyOf": [{"$ref": "#/components/schemas/WorkPoolFilter"}, {"type": "null"}]}}, "type": "object", "title": "Body_count_work_pools_work_pools_count_post"}, "Body_create_database_admin_database_create_post": {"properties": {"confirm": {"type": "boolean", "title": "Confirm", "description": "Pass confirm=True to confirm you want to modify the database.", "default": false}}, "type": "object", "title": "Body_create_database_admin_database_create_post"}, "Body_create_flow_run_input_flow_runs__id__input_post": {"properties": {"key": {"type": "string", "title": "Key", "description": "The input key"}, "value": {"type": "string", "format": "binary", "title": "Value", "description": "The value of the input"}, "sender": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sender", "description": "The sender of the input"}}, "type": "object", "required": ["key", "value"], "title": "Body_create_flow_run_input_flow_runs__id__input_post"}, "Body_drop_database_admin_database_drop_post": {"properties": {"confirm": {"type": "boolean", "title": "Confirm", "description": "Pass confirm=True to confirm you want to modify the database.", "default": false}}, "type": "object", "title": "Body_drop_database_admin_database_drop_post"}, "Body_filter_flow_run_input_flow_runs__id__input_filter_post": {"properties": {"prefix": {"type": "string", "title": "Prefix", "description": "The input key prefix"}, "limit": {"type": "integer", "title": "Limit", "description": "The maximum number of results to return", "default": 1}, "exclude_keys": {"items": {"type": "string"}, "type": "array", "title": "Exclude Keys", "description": "Exclude inputs with these keys", "default": []}}, "type": "object", "required": ["prefix"], "title": "Body_filter_flow_run_input_flow_runs__id__input_filter_post"}, "Body_flow_run_history_flow_runs_history_post": {"properties": {"history_start": {"type": "string", "format": "date-time", "title": "History Start", "description": "The history's start time."}, "history_end": {"type": "string", "format": "date-time", "title": "History End", "description": "The history's end time."}, "history_interval": {"type": "number", "format": "time-delta", "title": "History Interval", "description": "The size of each history interval, in seconds. Must be at least 1 second."}, "flows": {"allOf": [{"$ref": "#/components/schemas/FlowFilter"}]}, "flow_runs": {"allOf": [{"$ref": "#/components/schemas/FlowRunFilter"}]}, "task_runs": {"allOf": [{"$ref": "#/components/schemas/TaskRunFilter"}]}, "deployments": {"allOf": [{"$ref": "#/components/schemas/DeploymentFilter"}]}, "work_pools": {"allOf": [{"$ref": "#/components/schemas/WorkPoolFilter"}]}, "work_queues": {"allOf": [{"$ref": "#/components/schemas/WorkQueueFilter"}]}}, "type": "object", "required": ["history_start", "history_end", "history_interval"], "title": "Body_flow_run_history_flow_runs_history_post"}, "Body_get_scheduled_flow_runs_for_deployments_deployments_get_scheduled_flow_runs_post": {"properties": {"deployment_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "Deployment Ids", "description": "The deployment IDs to get scheduled runs for"}, "scheduled_before": {"type": "string", "format": "date-time", "title": "Scheduled Before", "description": "The maximum time to look for scheduled flow runs"}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "required": ["deployment_ids"], "title": "Body_get_scheduled_flow_runs_for_deployments_deployments_get_scheduled_flow_runs_post"}, "Body_get_scheduled_flow_runs_work_pools__name__get_scheduled_flow_runs_post": {"properties": {"work_queue_names": {"items": {"type": "string"}, "type": "array", "title": "Work Queue Names", "description": "The names of work pool queues"}, "scheduled_before": {"type": "string", "format": "date-time", "title": "Scheduled Before", "description": "The maximum time to look for scheduled flow runs"}, "scheduled_after": {"type": "string", "format": "date-time", "title": "Scheduled After", "description": "The minimum time to look for scheduled flow runs"}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_get_scheduled_flow_runs_work_pools__name__get_scheduled_flow_runs_post"}, "Body_next_runs_by_flow_ui_flows_next_runs_post": {"properties": {"flow_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "Flow Ids", "max_items": 200}}, "type": "object", "required": ["flow_ids"], "title": "Body_next_runs_by_flow_ui_flows_next_runs_post"}, "Body_paginate_deployments_deployments_paginate_post": {"properties": {"page": {"type": "integer", "minimum": 1.0, "title": "Page", "default": 1}, "flows": {"allOf": [{"$ref": "#/components/schemas/FlowFilter"}]}, "flow_runs": {"allOf": [{"$ref": "#/components/schemas/FlowRunFilter"}]}, "task_runs": {"allOf": [{"$ref": "#/components/schemas/TaskRunFilter"}]}, "deployments": {"allOf": [{"$ref": "#/components/schemas/DeploymentFilter"}]}, "work_pools": {"allOf": [{"$ref": "#/components/schemas/WorkPoolFilter"}]}, "work_pool_queues": {"allOf": [{"$ref": "#/components/schemas/WorkQueueFilter"}]}, "sort": {"allOf": [{"$ref": "#/components/schemas/DeploymentSort"}], "default": "NAME_ASC"}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_paginate_deployments_deployments_paginate_post"}, "Body_paginate_flow_runs_flow_runs_paginate_post": {"properties": {"sort": {"allOf": [{"$ref": "#/components/schemas/FlowRunSort"}], "default": "ID_DESC"}, "page": {"type": "integer", "minimum": 1.0, "title": "Page", "default": 1}, "flows": {"anyOf": [{"$ref": "#/components/schemas/FlowFilter"}, {"type": "null"}]}, "flow_runs": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilter"}, {"type": "null"}]}, "task_runs": {"anyOf": [{"$ref": "#/components/schemas/TaskRunFilter"}, {"type": "null"}]}, "deployments": {"anyOf": [{"$ref": "#/components/schemas/DeploymentFilter"}, {"type": "null"}]}, "work_pools": {"anyOf": [{"$ref": "#/components/schemas/WorkPoolFilter"}, {"type": "null"}]}, "work_pool_queues": {"anyOf": [{"$ref": "#/components/schemas/WorkQueueFilter"}, {"type": "null"}]}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_paginate_flow_runs_flow_runs_paginate_post"}, "Body_paginate_flows_flows_paginate_post": {"properties": {"page": {"type": "integer", "minimum": 1.0, "title": "Page", "default": 1}, "flows": {"anyOf": [{"$ref": "#/components/schemas/FlowFilter"}, {"type": "null"}]}, "flow_runs": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilter"}, {"type": "null"}]}, "task_runs": {"anyOf": [{"$ref": "#/components/schemas/TaskRunFilter"}, {"type": "null"}]}, "deployments": {"anyOf": [{"$ref": "#/components/schemas/DeploymentFilter"}, {"type": "null"}]}, "work_pools": {"anyOf": [{"$ref": "#/components/schemas/WorkPoolFilter"}, {"type": "null"}]}, "sort": {"allOf": [{"$ref": "#/components/schemas/FlowSort"}], "default": "NAME_ASC"}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_paginate_flows_flows_paginate_post"}, "Body_read_all_concurrency_limits_v2_v2_concurrency_limits_filter_post": {"properties": {"offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_all_concurrency_limits_v2_v2_concurrency_limits_filter_post"}, "Body_read_artifacts_artifacts_filter_post": {"properties": {"sort": {"allOf": [{"$ref": "#/components/schemas/ArtifactSort"}], "default": "ID_DESC"}, "offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "artifacts": {"allOf": [{"$ref": "#/components/schemas/ArtifactFilter"}]}, "flow_runs": {"allOf": [{"$ref": "#/components/schemas/FlowRunFilter"}]}, "task_runs": {"allOf": [{"$ref": "#/components/schemas/TaskRunFilter"}]}, "flows": {"allOf": [{"$ref": "#/components/schemas/FlowFilter"}]}, "deployments": {"allOf": [{"$ref": "#/components/schemas/DeploymentFilter"}]}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_artifacts_artifacts_filter_post"}, "Body_read_automations_automations_filter_post": {"properties": {"sort": {"allOf": [{"$ref": "#/components/schemas/AutomationSort"}], "default": "NAME_ASC"}, "offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "automations": {"anyOf": [{"$ref": "#/components/schemas/AutomationFilter"}, {"type": "null"}]}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_automations_automations_filter_post"}, "Body_read_block_documents_block_documents_filter_post": {"properties": {"block_documents": {"anyOf": [{"$ref": "#/components/schemas/BlockDocumentFilter"}, {"type": "null"}]}, "block_types": {"anyOf": [{"$ref": "#/components/schemas/BlockTypeFilter"}, {"type": "null"}]}, "block_schemas": {"anyOf": [{"$ref": "#/components/schemas/BlockSchemaFilter"}, {"type": "null"}]}, "include_secrets": {"type": "boolean", "title": "Include Secrets", "description": "Whether to include sensitive values in the block document.", "default": false}, "sort": {"anyOf": [{"$ref": "#/components/schemas/BlockDocumentSort"}, {"type": "null"}], "default": "NAME_ASC"}, "offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_block_documents_block_documents_filter_post"}, "Body_read_block_schemas_block_schemas_filter_post": {"properties": {"block_schemas": {"anyOf": [{"$ref": "#/components/schemas/BlockSchemaFilter"}, {"type": "null"}]}, "offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_block_schemas_block_schemas_filter_post"}, "Body_read_block_types_block_types_filter_post": {"properties": {"block_types": {"anyOf": [{"$ref": "#/components/schemas/BlockTypeFilter"}, {"type": "null"}]}, "block_schemas": {"anyOf": [{"$ref": "#/components/schemas/BlockSchemaFilter"}, {"type": "null"}]}, "offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_block_types_block_types_filter_post"}, "Body_read_concurrency_limits_concurrency_limits_filter_post": {"properties": {"offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_concurrency_limits_concurrency_limits_filter_post"}, "Body_read_dashboard_task_run_counts_ui_task_runs_dashboard_counts_post": {"properties": {"task_runs": {"$ref": "#/components/schemas/TaskRunFilter"}, "flows": {"anyOf": [{"$ref": "#/components/schemas/FlowFilter"}, {"type": "null"}]}, "flow_runs": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilter"}, {"type": "null"}]}, "deployments": {"anyOf": [{"$ref": "#/components/schemas/DeploymentFilter"}, {"type": "null"}]}, "work_pools": {"anyOf": [{"$ref": "#/components/schemas/WorkPoolFilter"}, {"type": "null"}]}, "work_queues": {"anyOf": [{"$ref": "#/components/schemas/WorkQueueFilter"}, {"type": "null"}]}}, "type": "object", "required": ["task_runs"], "title": "Body_read_dashboard_task_run_counts_ui_task_runs_dashboard_counts_post"}, "Body_read_deployments_deployments_filter_post": {"properties": {"offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "flows": {"allOf": [{"$ref": "#/components/schemas/FlowFilter"}]}, "flow_runs": {"allOf": [{"$ref": "#/components/schemas/FlowRunFilter"}]}, "task_runs": {"allOf": [{"$ref": "#/components/schemas/TaskRunFilter"}]}, "deployments": {"allOf": [{"$ref": "#/components/schemas/DeploymentFilter"}]}, "work_pools": {"allOf": [{"$ref": "#/components/schemas/WorkPoolFilter"}]}, "work_pool_queues": {"allOf": [{"$ref": "#/components/schemas/WorkQueueFilter"}]}, "sort": {"allOf": [{"$ref": "#/components/schemas/DeploymentSort"}], "default": "NAME_ASC"}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_deployments_deployments_filter_post"}, "Body_read_events_events_filter_post": {"properties": {"filter": {"anyOf": [{"$ref": "#/components/schemas/EventFilter"}, {"type": "null"}], "description": "Additional optional filter criteria to narrow down the set of Events"}, "limit": {"type": "integer", "maximum": 50.0, "minimum": 0.0, "title": "Limit", "description": "The number of events to return with each page", "default": 50}}, "type": "object", "title": "Body_read_events_events_filter_post"}, "Body_read_flow_run_history_ui_flow_runs_history_post": {"properties": {"sort": {"allOf": [{"$ref": "#/components/schemas/FlowRunSort"}], "default": "EXPECTED_START_TIME_DESC"}, "limit": {"type": "integer", "maximum": 1000.0, "title": "Limit", "default": 1000}, "offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "flows": {"allOf": [{"$ref": "#/components/schemas/FlowFilter"}]}, "flow_runs": {"allOf": [{"$ref": "#/components/schemas/FlowRunFilter"}]}, "task_runs": {"allOf": [{"$ref": "#/components/schemas/TaskRunFilter"}]}, "deployments": {"allOf": [{"$ref": "#/components/schemas/DeploymentFilter"}]}, "work_pools": {"allOf": [{"$ref": "#/components/schemas/WorkPoolFilter"}]}}, "type": "object", "title": "Body_read_flow_run_history_ui_flow_runs_history_post"}, "Body_read_flow_run_notification_policies_flow_run_notification_policies_filter_post": {"properties": {"flow_run_notification_policy_filter": {"allOf": [{"$ref": "#/components/schemas/FlowRunNotificationPolicyFilter"}]}, "offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_flow_run_notification_policies_flow_run_notification_policies_filter_post"}, "Body_read_flow_runs_flow_runs_filter_post": {"properties": {"sort": {"allOf": [{"$ref": "#/components/schemas/FlowRunSort"}], "default": "ID_DESC"}, "offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "flows": {"anyOf": [{"$ref": "#/components/schemas/FlowFilter"}, {"type": "null"}]}, "flow_runs": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilter"}, {"type": "null"}]}, "task_runs": {"anyOf": [{"$ref": "#/components/schemas/TaskRunFilter"}, {"type": "null"}]}, "deployments": {"anyOf": [{"$ref": "#/components/schemas/DeploymentFilter"}, {"type": "null"}]}, "work_pools": {"anyOf": [{"$ref": "#/components/schemas/WorkPoolFilter"}, {"type": "null"}]}, "work_pool_queues": {"anyOf": [{"$ref": "#/components/schemas/WorkQueueFilter"}, {"type": "null"}]}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_flow_runs_flow_runs_filter_post"}, "Body_read_flows_flows_filter_post": {"properties": {"offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "flows": {"allOf": [{"$ref": "#/components/schemas/FlowFilter"}]}, "flow_runs": {"allOf": [{"$ref": "#/components/schemas/FlowRunFilter"}]}, "task_runs": {"allOf": [{"$ref": "#/components/schemas/TaskRunFilter"}]}, "deployments": {"allOf": [{"$ref": "#/components/schemas/DeploymentFilter"}]}, "work_pools": {"allOf": [{"$ref": "#/components/schemas/WorkPoolFilter"}]}, "sort": {"allOf": [{"$ref": "#/components/schemas/FlowSort"}], "default": "NAME_ASC"}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_flows_flows_filter_post"}, "Body_read_latest_artifacts_artifacts_latest_filter_post": {"properties": {"sort": {"allOf": [{"$ref": "#/components/schemas/ArtifactCollectionSort"}], "default": "ID_DESC"}, "offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "artifacts": {"allOf": [{"$ref": "#/components/schemas/ArtifactCollectionFilter"}]}, "flow_runs": {"allOf": [{"$ref": "#/components/schemas/FlowRunFilter"}]}, "task_runs": {"allOf": [{"$ref": "#/components/schemas/TaskRunFilter"}]}, "flows": {"allOf": [{"$ref": "#/components/schemas/FlowFilter"}]}, "deployments": {"allOf": [{"$ref": "#/components/schemas/DeploymentFilter"}]}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_latest_artifacts_artifacts_latest_filter_post"}, "Body_read_logs_logs_filter_post": {"properties": {"offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "logs": {"allOf": [{"$ref": "#/components/schemas/LogFilter"}]}, "sort": {"allOf": [{"$ref": "#/components/schemas/LogSort"}], "default": "TIMESTAMP_ASC"}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_logs_logs_filter_post"}, "Body_read_saved_searches_saved_searches_filter_post": {"properties": {"offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_saved_searches_saved_searches_filter_post"}, "Body_read_task_run_counts_by_state_ui_task_runs_count_post": {"properties": {"flows": {"anyOf": [{"$ref": "#/components/schemas/FlowFilter"}, {"type": "null"}]}, "flow_runs": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilter"}, {"type": "null"}]}, "task_runs": {"anyOf": [{"$ref": "#/components/schemas/TaskRunFilter"}, {"type": "null"}]}, "deployments": {"anyOf": [{"$ref": "#/components/schemas/DeploymentFilter"}, {"type": "null"}]}}, "type": "object", "title": "Body_read_task_run_counts_by_state_ui_task_runs_count_post"}, "Body_read_task_runs_task_runs_filter_post": {"properties": {"sort": {"allOf": [{"$ref": "#/components/schemas/TaskRunSort"}], "default": "ID_DESC"}, "offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "flows": {"anyOf": [{"$ref": "#/components/schemas/FlowFilter"}, {"type": "null"}]}, "flow_runs": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilter"}, {"type": "null"}]}, "task_runs": {"anyOf": [{"$ref": "#/components/schemas/TaskRunFilter"}, {"type": "null"}]}, "deployments": {"anyOf": [{"$ref": "#/components/schemas/DeploymentFilter"}, {"type": "null"}]}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_task_runs_task_runs_filter_post"}, "Body_read_task_workers_task_workers_filter_post": {"properties": {"task_worker_filter": {"anyOf": [{"$ref": "#/components/schemas/TaskWorkerFilter"}, {"type": "null"}], "description": "The task worker filter"}}, "type": "object", "title": "Body_read_task_workers_task_workers_filter_post"}, "Body_read_variables_variables_filter_post": {"properties": {"offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "variables": {"anyOf": [{"$ref": "#/components/schemas/VariableFilter"}, {"type": "null"}]}, "sort": {"allOf": [{"$ref": "#/components/schemas/VariableSort"}], "default": "NAME_ASC"}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_variables_variables_filter_post"}, "Body_read_work_pools_work_pools_filter_post": {"properties": {"work_pools": {"anyOf": [{"$ref": "#/components/schemas/WorkPoolFilter"}, {"type": "null"}]}, "offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_work_pools_work_pools_filter_post"}, "Body_read_work_queue_runs_work_queues__id__get_runs_post": {"properties": {"scheduled_before": {"type": "string", "format": "date-time", "title": "Scheduled Before", "description": "Only flow runs scheduled to start before this time will be returned."}, "agent_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Agent Id", "description": "An optional unique identifier for the agent making this query. If provided, the Prefect REST API will track the last time this agent polled the work queue."}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_work_queue_runs_work_queues__id__get_runs_post"}, "Body_read_work_queues_work_pools__work_pool_name__queues_filter_post": {"properties": {"work_queues": {"allOf": [{"$ref": "#/components/schemas/WorkQueueFilter"}]}, "offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_work_queues_work_pools__work_pool_name__queues_filter_post"}, "Body_read_work_queues_work_queues_filter_post": {"properties": {"offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "work_queues": {"allOf": [{"$ref": "#/components/schemas/WorkQueueFilter"}]}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_work_queues_work_queues_filter_post"}, "Body_read_workers_work_pools__work_pool_name__workers_filter_post": {"properties": {"workers": {"allOf": [{"$ref": "#/components/schemas/WorkerFilter"}]}, "offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "default": 0}, "limit": {"type": "integer", "title": "Limit", "description": "Defaults to PREFECT_API_DEFAULT_LIMIT if not provided."}}, "type": "object", "title": "Body_read_workers_work_pools__work_pool_name__workers_filter_post"}, "Body_reset_concurrency_limit_by_tag_concurrency_limits_tag__tag__reset_post": {"properties": {"slot_override": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Slot Override", "description": "Manual override for active concurrency limit slots."}}, "type": "object", "title": "Body_reset_concurrency_limit_by_tag_concurrency_limits_tag__tag__reset_post"}, "Body_resume_flow_run_flow_runs__id__resume_post": {"properties": {"run_input": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Run Input"}}, "type": "object", "title": "Body_resume_flow_run_flow_runs__id__resume_post"}, "Body_schedule_deployment_deployments__id__schedule_post": {"properties": {"start_time": {"type": "string", "format": "date-time", "title": "Start Time", "description": "The earliest date to schedule"}, "end_time": {"type": "string", "format": "date-time", "title": "End Time", "description": "The latest date to schedule"}, "min_time": {"type": "number", "format": "time-delta", "title": "Min Time", "description": "Runs will be scheduled until at least this long after the `start_time`"}, "min_runs": {"type": "integer", "title": "Min Runs", "description": "The minimum number of runs to schedule"}, "max_runs": {"type": "integer", "title": "Max Runs", "description": "The maximum number of runs to schedule"}}, "type": "object", "title": "Body_schedule_deployment_deployments__id__schedule_post"}, "Body_set_flow_run_state_flow_runs__id__set_state_post": {"properties": {"state": {"allOf": [{"$ref": "#/components/schemas/StateCreate"}], "description": "The intended state."}, "force": {"type": "boolean", "title": "Force", "description": "If false, orchestration rules will be applied that may alter or prevent the state transition. If True, orchestration rules are not applied.", "default": false}}, "type": "object", "required": ["state"], "title": "Body_set_flow_run_state_flow_runs__id__set_state_post"}, "Body_set_task_run_state_task_runs__id__set_state_post": {"properties": {"state": {"allOf": [{"$ref": "#/components/schemas/StateCreate"}], "description": "The intended state."}, "force": {"type": "boolean", "title": "Force", "description": "If false, orchestration rules will be applied that may alter or prevent the state transition. If True, orchestration rules are not applied.", "default": false}}, "type": "object", "required": ["state"], "title": "Body_set_task_run_state_task_runs__id__set_state_post"}, "Body_task_run_history_task_runs_history_post": {"properties": {"history_start": {"type": "string", "format": "date-time", "title": "History Start", "description": "The history's start time."}, "history_end": {"type": "string", "format": "date-time", "title": "History End", "description": "The history's end time."}, "history_interval": {"type": "number", "format": "time-delta", "title": "History Interval", "description": "The size of each history interval, in seconds. Must be at least 1 second."}, "flows": {"allOf": [{"$ref": "#/components/schemas/FlowFilter"}]}, "flow_runs": {"allOf": [{"$ref": "#/components/schemas/FlowRunFilter"}]}, "task_runs": {"allOf": [{"$ref": "#/components/schemas/TaskRunFilter"}]}, "deployments": {"allOf": [{"$ref": "#/components/schemas/DeploymentFilter"}]}}, "type": "object", "required": ["history_start", "history_end", "history_interval"], "title": "Body_task_run_history_task_runs_history_post"}, "Body_validate_obj_ui_schemas_validate_post": {"properties": {"json_schema": {"type": "object", "title": "<PERSON><PERSON>"}, "values": {"type": "object", "title": "Values"}}, "type": "object", "required": ["json_schema", "values"], "title": "Body_validate_obj_ui_schemas_validate_post"}, "Body_worker_heartbeat_work_pools__work_pool_name__workers_heartbeat_post": {"properties": {"name": {"type": "string", "title": "Name", "description": "The worker process name"}, "heartbeat_interval_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Heartbeat Interval Seconds", "description": "The worker's heartbeat interval in seconds"}}, "type": "object", "required": ["name"], "title": "Body_worker_heartbeat_work_pools__work_pool_name__workers_heartbeat_post"}, "CallWebhook": {"properties": {"type": {"type": "string", "enum": ["call-webhook"], "const": "call-webhook", "title": "Type", "default": "call-webhook"}, "block_document_id": {"type": "string", "format": "uuid", "title": "Block Document Id", "description": "The identifier of the webhook block to use"}, "payload": {"type": "string", "title": "Payload", "description": "An optional templatable payload to send when calling the webhook.", "default": ""}}, "type": "object", "required": ["block_document_id"], "title": "CallWebhook", "description": "Call a webhook when an Automation is triggered."}, "CancelFlowRun": {"properties": {"type": {"type": "string", "enum": ["cancel-flow-run"], "const": "cancel-flow-run", "title": "Type", "default": "cancel-flow-run"}}, "type": "object", "title": "CancelFlowRun", "description": "Cancels a flow run associated with the trigger"}, "ChangeFlowRunState": {"properties": {"type": {"type": "string", "enum": ["change-flow-run-state"], "const": "change-flow-run-state", "title": "Type", "default": "change-flow-run-state"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "The name of the state to change the flow run to"}, "state": {"allOf": [{"$ref": "#/components/schemas/StateType"}], "description": "The type of the state to change the flow run to"}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message", "description": "An optional message to associate with the state change"}}, "type": "object", "required": ["state"], "title": "ChangeFlowRunState", "description": "Changes the state of a flow run associated with the trigger"}, "ConcurrencyLimit": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "tag": {"type": "string", "title": "Tag", "description": "A tag the concurrency limit is applied to."}, "concurrency_limit": {"type": "integer", "title": "Concurrency Limit", "description": "The concurrency limit."}, "active_slots": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "Active Slots", "description": "A list of active run ids using a concurrency slot"}}, "type": "object", "required": ["tag", "concurrency_limit"], "title": "ConcurrencyLimit", "description": "An ORM representation of a concurrency limit."}, "ConcurrencyLimitCreate": {"properties": {"tag": {"type": "string", "title": "Tag", "description": "A tag the concurrency limit is applied to."}, "concurrency_limit": {"type": "integer", "title": "Concurrency Limit", "description": "The concurrency limit."}}, "additionalProperties": false, "type": "object", "required": ["tag", "concurrency_limit"], "title": "ConcurrencyLimitCreate", "description": "Data used by the Prefect REST API to create a concurrency limit."}, "ConcurrencyLimitV2": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "active": {"type": "boolean", "title": "Active", "description": "Whether the concurrency limit is active.", "default": true}, "name": {"type": "string", "pattern": "^[^/%&><]+$", "title": "Name", "description": "The name of the concurrency limit."}, "limit": {"type": "integer", "title": "Limit", "description": "The concurrency limit."}, "active_slots": {"type": "integer", "title": "Active Slots", "description": "The number of active slots.", "default": 0}, "denied_slots": {"type": "integer", "title": "Denied <PERSON>lots", "description": "The number of denied slots.", "default": 0}, "slot_decay_per_second": {"type": "number", "title": "Slot Decay Per Second", "description": "The decay rate for active slots when used as a rate limit.", "default": 0}, "avg_slot_occupancy_seconds": {"type": "number", "title": "Avg Slot Occupancy Seconds", "description": "The average amount of time a slot is occupied.", "default": 2.0}}, "type": "object", "required": ["name", "limit"], "title": "ConcurrencyLimitV2", "description": "An ORM representation of a v2 concurrency limit."}, "ConcurrencyLimitV2Create": {"properties": {"active": {"type": "boolean", "title": "Active", "description": "Whether the concurrency limit is active.", "default": true}, "name": {"type": "string", "pattern": "^[^/%&><]+$", "title": "Name", "description": "The name of the concurrency limit."}, "limit": {"type": "integer", "minimum": 0.0, "title": "Limit", "description": "The concurrency limit."}, "active_slots": {"type": "integer", "minimum": 0.0, "title": "Active Slots", "description": "The number of active slots.", "default": 0}, "denied_slots": {"type": "integer", "minimum": 0.0, "title": "Denied <PERSON>lots", "description": "The number of denied slots.", "default": 0}, "slot_decay_per_second": {"type": "number", "minimum": 0.0, "title": "Slot Decay Per Second", "description": "The decay rate for active slots when used as a rate limit.", "default": 0}}, "additionalProperties": false, "type": "object", "required": ["name", "limit"], "title": "ConcurrencyLimitV2Create", "description": "Data used by the Prefect REST API to create a v2 concurrency limit."}, "ConcurrencyLimitV2Update": {"properties": {"active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Active"}, "name": {"anyOf": [{"type": "string", "pattern": "^[^/%&><]+$"}, {"type": "null"}], "title": "Name"}, "limit": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Limit"}, "active_slots": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Active Slots"}, "denied_slots": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Denied <PERSON>lots"}, "slot_decay_per_second": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Slot Decay Per Second"}}, "additionalProperties": false, "type": "object", "title": "ConcurrencyLimitV2Update", "description": "Data used by the Prefect REST API to update a v2 concurrency limit."}, "Constant": {"properties": {"input_type": {"type": "string", "enum": ["constant"], "const": "constant", "title": "Input Type", "default": "constant"}, "type": {"type": "string", "title": "Type"}}, "type": "object", "required": ["type"], "title": "Constant", "description": "Represents constant input value to a task run."}, "CountByState": {"properties": {"COMPLETED": {"type": "integer", "title": "Completed", "default": 0}, "PENDING": {"type": "integer", "title": "Pending", "default": 0}, "RUNNING": {"type": "integer", "title": "Running", "default": 0}, "FAILED": {"type": "integer", "title": "Failed", "default": 0}, "CANCELLED": {"type": "integer", "title": "Cancelled", "default": 0}, "CRASHED": {"type": "integer", "title": "Crashed", "default": 0}, "PAUSED": {"type": "integer", "title": "Paused", "default": 0}, "CANCELLING": {"type": "integer", "title": "Cancelling", "default": 0}, "SCHEDULED": {"type": "integer", "title": "Scheduled", "default": 0}}, "type": "object", "title": "CountByState"}, "Countable": {"type": "string", "enum": ["day", "time", "event", "resource"], "title": "Countable"}, "CreatedBy": {"properties": {"id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Id", "description": "The id of the creator of the object."}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type", "description": "The type of the creator of the object."}, "display_value": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Display Value", "description": "The display value for the creator."}}, "type": "object", "title": "CreatedBy"}, "CronSchedule": {"properties": {"cron": {"type": "string", "title": "<PERSON><PERSON>", "examples": ["0 0 * * *"]}, "timezone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timezone", "examples": ["America/New_York"]}, "day_or": {"type": "boolean", "title": "Day Or", "description": "Control croniter behavior for handling day and day_of_week entries.", "default": true}}, "additionalProperties": false, "type": "object", "required": ["cron"], "title": "CronSchedule", "description": "Cron schedule\n\nNOTE: If the timezone is a DST-observing one, then the schedule will adjust\nitself appropriately. Cron's rules for DST are based on schedule times, not\nintervals. This means that an hourly cron schedule will fire on every new\nschedule hour, not every elapsed hour; for example, when clocks are set back\nthis will result in a two-hour pause as the schedule will fire *the first\ntime* 1am is reached and *the first time* 2am is reached, 120 minutes later.\nLonger schedules, such as one that fires at 9am every morning, will\nautomatically adjust for DST.\n\nArgs:\n    cron (str): a valid cron string\n    timezone (str): a valid timezone string in IANA tzdata format (for example,\n        America/New_York).\n    day_or (bool, optional): Control how croniter handles `day` and `day_of_week`\n        entries. Defaults to True, matching cron which connects those values using\n        OR. If the switch is set to False, the values are connected using AND. This\n        behaves like fcron and enables you to e.g. define a job that executes each\n        2nd friday of a month by setting the days of month and the weekday."}, "CsrfToken": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "token": {"type": "string", "title": "Token", "description": "The CSRF token"}, "client": {"type": "string", "title": "Client", "description": "The client id associated with the CSRF token"}, "expiration": {"type": "string", "format": "date-time", "title": "Expiration", "description": "The expiration time of the CSRF token"}}, "type": "object", "required": ["token", "client", "expiration"], "title": "CsrfToken"}, "DependencyResult": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "upstream_dependencies": {"items": {"$ref": "#/components/schemas/TaskRunResult"}, "type": "array", "title": "Upstream Dependencies"}, "state": {"$ref": "#/components/schemas/State"}, "expected_start_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expected Start Time"}, "start_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Time"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "total_run_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Total Run Time"}, "estimated_run_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Estimated Run Time"}, "untrackable_result": {"type": "boolean", "title": "Untrackable Result"}}, "type": "object", "required": ["id", "name", "upstream_dependencies", "state", "expected_start_time", "start_time", "end_time", "total_run_time", "estimated_run_time", "untrackable_result"], "title": "DependencyResult"}, "DeploymentCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "The name of the deployment.", "examples": ["my-deployment"]}, "flow_id": {"type": "string", "format": "uuid", "title": "Flow Id", "description": "The ID of the flow associated with the deployment."}, "is_schedule_active": {"type": "boolean", "title": "Is Schedule Active", "description": "Whether the schedule is active.", "default": true}, "paused": {"type": "boolean", "title": "Paused", "description": "Whether or not the deployment is paused.", "default": false}, "schedules": {"items": {"$ref": "#/components/schemas/DeploymentScheduleCreate"}, "type": "array", "title": "Schedules", "description": "A list of schedules for the deployment."}, "enforce_parameter_schema": {"type": "boolean", "title": "Enforce Parameter Schema", "description": "Whether or not the deployment should enforce the parameter schema.", "default": true}, "parameter_openapi_schema": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Parameter Openapi Schema", "description": "The parameter schema of the flow, including defaults."}, "parameters": {"type": "object", "title": "Parameters", "description": "Parameters for flow runs scheduled by the deployment."}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "A list of deployment tags.", "examples": [["tag-1", "tag-2"]]}, "pull_steps": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "null"}], "title": "Pull Steps"}, "work_queue_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Work Queue Name"}, "work_pool_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Work Pool Name", "description": "The name of the deployment's work pool.", "examples": ["my-work-pool"]}, "storage_document_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Storage Document Id"}, "infrastructure_document_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Infrastructure Document Id"}, "schedule": {"anyOf": [{"$ref": "#/components/schemas/IntervalSchedule"}, {"$ref": "#/components/schemas/CronSchedule"}, {"$ref": "#/components/schemas/RRuleSchedule"}, {"type": "null"}], "title": "Schedule", "description": "The schedule for the deployment."}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Path"}, "version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Version"}, "entrypoint": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Entrypoint"}, "job_variables": {"type": "object", "title": "Job Variables", "description": "Overrides for the flow's infrastructure configuration."}}, "additionalProperties": false, "type": "object", "required": ["name", "flow_id"], "title": "DeploymentCreate", "description": "Data used by the Prefect REST API to create a deployment."}, "DeploymentFilter": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "id": {"anyOf": [{"$ref": "#/components/schemas/DeploymentFilterId"}, {"type": "null"}], "description": "Filter criteria for `Deployment.id`"}, "name": {"anyOf": [{"$ref": "#/components/schemas/DeploymentFilterName"}, {"type": "null"}], "description": "Filter criteria for `Deployment.name`"}, "flow_or_deployment_name": {"anyOf": [{"$ref": "#/components/schemas/DeploymentOrFlowNameFilter"}, {"type": "null"}], "description": "Filter criteria for `Deployment.name` or `Flow.name`"}, "paused": {"anyOf": [{"$ref": "#/components/schemas/DeploymentFilterPaused"}, {"type": "null"}], "description": "Filter criteria for `Deployment.paused`"}, "is_schedule_active": {"anyOf": [{"$ref": "#/components/schemas/DeploymentFilterIsScheduleActive"}, {"type": "null"}], "description": "Filter criteria for `Deployment.is_schedule_active`"}, "tags": {"anyOf": [{"$ref": "#/components/schemas/DeploymentFilterTags"}, {"type": "null"}], "description": "Filter criteria for `Deployment.tags`"}, "work_queue_name": {"anyOf": [{"$ref": "#/components/schemas/DeploymentFilterWorkQueueName"}, {"type": "null"}], "description": "Filter criteria for `Deployment.work_queue_name`"}}, "additionalProperties": false, "type": "object", "title": "DeploymentFilter", "description": "Filter for deployments. Only deployments matching all criteria will be returned."}, "DeploymentFilterId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of deployment ids to include"}}, "additionalProperties": false, "type": "object", "title": "DeploymentFilterId", "description": "Filter by `Deployment.id`."}, "DeploymentFilterIsScheduleActive": {"properties": {"eq_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Eq ", "description": "Only returns where deployment schedule is/is not active"}}, "additionalProperties": false, "type": "object", "title": "DeploymentFilterIsScheduleActive", "description": "Legacy filter to filter by `Deployment.is_schedule_active` which\nis always the opposite of `Deployment.paused`."}, "DeploymentFilterName": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of deployment names to include", "examples": [["my-deployment-1", "my-deployment-2"]]}, "like_": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Like ", "description": "A case-insensitive partial match. For example,  passing 'marvin' will match 'marvin', 'sad-<PERSON>', and 'marvin-robot'.", "examples": ["marvin"]}}, "additionalProperties": false, "type": "object", "title": "DeploymentFilterName", "description": "Filter by `Deployment.name`."}, "DeploymentFilterPaused": {"properties": {"eq_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Eq ", "description": "Only returns where deployment is/is not paused"}}, "additionalProperties": false, "type": "object", "title": "DeploymentFilterPaused", "description": "Filter by `Deployment.paused`."}, "DeploymentFilterTags": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "all_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "All ", "description": "A list of tags. Deployments will be returned only if their tags are a superset of the list", "examples": [["tag-1", "tag-2"]]}, "is_null_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON> ", "description": "If true, only include deployments without tags"}}, "additionalProperties": false, "type": "object", "title": "DeploymentFilterTags", "description": "Filter by `Deployment.tags`."}, "DeploymentFilterWorkQueueName": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of work queue names to include", "examples": [["work_queue_1", "work_queue_2"]]}}, "additionalProperties": false, "type": "object", "title": "DeploymentFilterWorkQueueName", "description": "Filter by `Deployment.work_queue_name`."}, "DeploymentFlowRunCreate": {"properties": {"state": {"anyOf": [{"$ref": "#/components/schemas/StateCreate"}, {"type": "null"}], "description": "The state of the flow run to create"}, "name": {"type": "string", "title": "Name", "description": "The name of the flow run. Defaults to a random slug if not specified.", "examples": ["my-flow-run"]}, "parameters": {"type": "object", "title": "Parameters"}, "enforce_parameter_schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Enforce Parameter Schema", "description": "Whether or not to enforce the parameter schema on this run."}, "context": {"type": "object", "title": "Context"}, "infrastructure_document_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Infrastructure Document Id"}, "empirical_policy": {"allOf": [{"$ref": "#/components/schemas/FlowRunPolicy"}], "description": "The empirical policy for the flow run."}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "A list of tags for the flow run.", "examples": [["tag-1", "tag-2"]]}, "idempotency_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Idempotency Key", "description": "An optional idempotency key. If a flow run with the same idempotency key has already been created, the existing flow run will be returned."}, "parent_task_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Parent Task Run Id"}, "work_queue_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Work Queue Name"}, "job_variables": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Job Variables"}}, "additionalProperties": false, "type": "object", "title": "DeploymentFlowRunCreate", "description": "Data used by the Prefect REST API to create a flow run from a deployment."}, "DeploymentOrFlowNameFilter": {"properties": {"like_": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Like ", "description": "A case-insensitive partial match on deployment or flow names. For example, passing 'example' might match deployments or flows with 'example' in their names."}}, "additionalProperties": false, "type": "object", "title": "DeploymentOrFlowNameFilter", "description": "Filter by `Deployment.name` or `Flow.name` with a single input string for ilike filtering."}, "DeploymentPaginationResponse": {"properties": {"results": {"items": {"$ref": "#/components/schemas/DeploymentResponse"}, "type": "array", "title": "Results"}, "count": {"type": "integer", "title": "Count"}, "limit": {"type": "integer", "title": "Limit"}, "pages": {"type": "integer", "title": "Pages"}, "page": {"type": "integer", "title": "Page"}}, "type": "object", "required": ["results", "count", "limit", "pages", "page"], "title": "DeploymentPaginationResponse"}, "DeploymentResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "name": {"type": "string", "title": "Name", "description": "The name of the deployment."}, "version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Version", "description": "An optional version for the deployment."}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "A description for the deployment."}, "flow_id": {"type": "string", "format": "uuid", "title": "Flow Id", "description": "The flow id associated with the deployment."}, "schedule": {"anyOf": [{"$ref": "#/components/schemas/IntervalSchedule"}, {"$ref": "#/components/schemas/CronSchedule"}, {"$ref": "#/components/schemas/RRuleSchedule"}, {"type": "null"}], "title": "Schedule", "description": "A schedule for the deployment."}, "is_schedule_active": {"type": "boolean", "title": "Is Schedule Active", "description": "Whether or not the deployment schedule is active.", "default": true}, "paused": {"type": "boolean", "title": "Paused", "description": "Whether or not the deployment is paused.", "default": false}, "schedules": {"items": {"$ref": "#/components/schemas/DeploymentSchedule"}, "type": "array", "title": "Schedules", "description": "A list of schedules for the deployment."}, "job_variables": {"type": "object", "title": "Job Variables", "description": "Overrides to apply to the base infrastructure block at runtime."}, "parameters": {"type": "object", "title": "Parameters", "description": "Parameters for flow runs scheduled by the deployment."}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "A list of tags for the deployment", "examples": [["tag-1", "tag-2"]]}, "work_queue_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Work Queue Name", "description": "The work queue for the deployment. If no work queue is set, work will not be scheduled."}, "last_polled": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Polled", "description": "The last time the deployment was polled for status updates."}, "parameter_openapi_schema": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Parameter Openapi Schema", "description": "The parameter schema of the flow, including defaults."}, "path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Path", "description": "The path to the working directory for the workflow, relative to remote storage or an absolute path."}, "pull_steps": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "null"}], "title": "Pull Steps", "description": "Pull steps for cloning and running this deployment."}, "entrypoint": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Entrypoint", "description": "The path to the entrypoint for the workflow, relative to the `path`."}, "storage_document_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Storage Document Id", "description": "The block document defining storage used for this flow."}, "infrastructure_document_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Infrastructure Document Id", "description": "The block document defining infrastructure to use for flow runs."}, "created_by": {"anyOf": [{"$ref": "#/components/schemas/CreatedBy"}, {"type": "null"}], "description": "Optional information about the creator of this deployment."}, "updated_by": {"anyOf": [{"$ref": "#/components/schemas/UpdatedBy"}, {"type": "null"}], "description": "Optional information about the updater of this deployment."}, "work_pool_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Work Pool Name", "description": "The name of the deployment's work pool."}, "status": {"anyOf": [{"$ref": "#/components/schemas/DeploymentStatus"}, {"type": "null"}], "description": "Whether the deployment is ready to run flows.", "default": "NOT_READY"}, "enforce_parameter_schema": {"type": "boolean", "title": "Enforce Parameter Schema", "description": "Whether or not the deployment should enforce the parameter schema.", "default": true}}, "type": "object", "required": ["name", "flow_id"], "title": "DeploymentResponse"}, "DeploymentSchedule": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "deployment_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Deployment Id", "description": "The deployment id associated with this schedule."}, "schedule": {"anyOf": [{"$ref": "#/components/schemas/IntervalSchedule"}, {"$ref": "#/components/schemas/CronSchedule"}, {"$ref": "#/components/schemas/RRuleSchedule"}], "title": "Schedule", "description": "The schedule for the deployment."}, "active": {"type": "boolean", "title": "Active", "description": "Whether or not the schedule is active.", "default": true}, "max_active_runs": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Max Active Runs", "description": "The maximum number of active runs for the schedule."}, "max_scheduled_runs": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Max Scheduled Runs", "description": "The maximum number of scheduled runs for the schedule."}, "catchup": {"type": "boolean", "title": "Catchup", "description": "Whether or not a worker should catch up on Late runs for the schedule.", "default": false}}, "type": "object", "required": ["schedule"], "title": "DeploymentSchedule"}, "DeploymentScheduleCreate": {"properties": {"active": {"type": "boolean", "title": "Active", "description": "Whether or not the schedule is active.", "default": true}, "schedule": {"anyOf": [{"$ref": "#/components/schemas/IntervalSchedule"}, {"$ref": "#/components/schemas/CronSchedule"}, {"$ref": "#/components/schemas/RRuleSchedule"}], "title": "Schedule", "description": "The schedule for the deployment."}, "max_active_runs": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Max Active Runs", "description": "The maximum number of active runs for the schedule."}, "max_scheduled_runs": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Max Scheduled Runs", "description": "The maximum number of scheduled runs for the schedule."}, "catchup": {"type": "boolean", "title": "Catchup", "description": "Whether or not a worker should catch up on Late runs for the schedule.", "default": false}}, "additionalProperties": false, "type": "object", "required": ["schedule"], "title": "DeploymentScheduleCreate"}, "DeploymentScheduleUpdate": {"properties": {"active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Active", "description": "Whether or not the schedule is active."}, "schedule": {"anyOf": [{"$ref": "#/components/schemas/IntervalSchedule"}, {"$ref": "#/components/schemas/CronSchedule"}, {"$ref": "#/components/schemas/RRuleSchedule"}, {"type": "null"}], "title": "Schedule", "description": "The schedule for the deployment."}, "max_active_runs": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Max Active Runs", "description": "The maximum number of active runs for the schedule."}, "max_scheduled_runs": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Max Scheduled Runs", "description": "The maximum number of scheduled runs for the schedule."}, "catchup": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Catchup", "description": "Whether or not a worker should catch up on Late runs for the schedule."}}, "additionalProperties": false, "type": "object", "title": "DeploymentScheduleUpdate"}, "DeploymentSort": {"type": "string", "enum": ["CREATED_DESC", "UPDATED_DESC", "NAME_ASC", "NAME_DESC"], "title": "DeploymentSort", "description": "Defines deployment sorting options."}, "DeploymentStatus": {"type": "string", "enum": ["READY", "NOT_READY"], "title": "DeploymentStatus", "description": "Enumeration of deployment statuses."}, "DeploymentUpdate": {"properties": {"version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Version"}, "schedule": {"anyOf": [{"$ref": "#/components/schemas/IntervalSchedule"}, {"$ref": "#/components/schemas/CronSchedule"}, {"$ref": "#/components/schemas/RRuleSchedule"}, {"type": "null"}], "title": "Schedule", "description": "The schedule for the deployment."}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "is_schedule_active": {"type": "boolean", "title": "Is Schedule Active", "description": "Whether the schedule is active.", "default": true}, "paused": {"type": "boolean", "title": "Paused", "description": "Whether or not the deployment is paused.", "default": false}, "schedules": {"items": {"$ref": "#/components/schemas/DeploymentScheduleCreate"}, "type": "array", "title": "Schedules", "description": "A list of schedules for the deployment."}, "parameters": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Parameters", "description": "Parameters for flow runs scheduled by the deployment."}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "A list of deployment tags.", "examples": [["tag-1", "tag-2"]]}, "work_queue_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Work Queue Name"}, "work_pool_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Work Pool Name", "description": "The name of the deployment's work pool.", "examples": ["my-work-pool"]}, "path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Path"}, "job_variables": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Job Variables", "description": "Overrides for the flow's infrastructure configuration."}, "entrypoint": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Entrypoint"}, "storage_document_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Storage Document Id"}, "infrastructure_document_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Infrastructure Document Id"}, "enforce_parameter_schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Enforce Parameter Schema", "description": "Whether or not the deployment should enforce the parameter schema."}}, "additionalProperties": false, "type": "object", "title": "DeploymentUpdate", "description": "Data used by the Prefect REST API to update a deployment."}, "DoNothing": {"properties": {"type": {"type": "string", "enum": ["do-nothing"], "const": "do-nothing", "title": "Type", "default": "do-nothing"}}, "type": "object", "title": "DoNothing", "description": "Do nothing when an Automation is triggered"}, "Edge": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}}, "type": "object", "required": ["id"], "title": "Edge"}, "Event": {"properties": {"occurred": {"type": "string", "format": "date-time", "title": "Occurred", "description": "When the event happened from the sender's perspective"}, "event": {"type": "string", "title": "Event", "description": "The name of the event that happened"}, "resource": {"allOf": [{"$ref": "#/components/schemas/Resource"}], "description": "The primary Resource this event concerns"}, "related": {"items": {"$ref": "#/components/schemas/RelatedResource"}, "type": "array", "title": "Related", "description": "A list of additional Resources involved in this event"}, "payload": {"type": "object", "title": "Payload", "description": "An open-ended set of data describing what happened"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "The client-provided identifier of this event"}, "follows": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Follows", "description": "The ID of an event that is known to have occurred prior to this one. If set, this may be used to establish a more precise ordering of causally-related events when they occur close enough together in time that the system may receive them out-of-order."}}, "type": "object", "required": ["occurred", "event", "resource", "id"], "title": "Event", "description": "The client-side view of an event that has happened to a Resource"}, "EventAnyResourceFilter": {"properties": {"id": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Id", "description": "Only include events for resources with these IDs"}, "id_prefix": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Id Prefix", "description": "Only include events for resources with IDs starting with these prefixes"}, "labels": {"anyOf": [{"$ref": "#/components/schemas/ResourceSpecification"}, {"type": "null"}], "description": "Only include events for related resources with these labels"}}, "additionalProperties": false, "type": "object", "title": "EventAnyResourceFilter"}, "EventCount": {"properties": {"value": {"type": "string", "title": "Value", "description": "The value to use for filtering"}, "label": {"type": "string", "title": "Label", "description": "The value to display for this count"}, "count": {"type": "integer", "title": "Count", "description": "The count of matching events"}, "start_time": {"type": "string", "format": "date-time", "title": "Start Time", "description": "The start time of this group of events"}, "end_time": {"type": "string", "format": "date-time", "title": "End Time", "description": "The end time of this group of events"}}, "type": "object", "required": ["value", "label", "count", "start_time", "end_time"], "title": "EventCount", "description": "The count of events with the given filter value"}, "EventFilter": {"properties": {"occurred": {"allOf": [{"$ref": "#/components/schemas/EventOccurredFilter"}], "description": "Filter criteria for when the events occurred"}, "event": {"anyOf": [{"$ref": "#/components/schemas/EventNameFilter"}, {"type": "null"}], "description": "Filter criteria for the event name"}, "any_resource": {"anyOf": [{"$ref": "#/components/schemas/EventAnyResourceFilter"}, {"type": "null"}], "description": "Filter criteria for any resource involved in the event"}, "resource": {"anyOf": [{"$ref": "#/components/schemas/EventResourceFilter"}, {"type": "null"}], "description": "Filter criteria for the resource of the event"}, "related": {"anyOf": [{"$ref": "#/components/schemas/EventRelatedFilter"}, {"type": "null"}], "description": "Filter criteria for the related resources of the event"}, "id": {"allOf": [{"$ref": "#/components/schemas/EventIDFilter"}], "description": "Filter criteria for the events' ID"}, "order": {"allOf": [{"$ref": "#/components/schemas/EventOrder"}], "description": "The order to return filtered events", "default": "DESC"}}, "additionalProperties": false, "type": "object", "title": "EventFilter"}, "EventIDFilter": {"properties": {"id": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Id", "description": "Only include events with one of these IDs"}}, "additionalProperties": false, "type": "object", "title": "EventIDFilter"}, "EventNameFilter": {"properties": {"prefix": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Prefix", "description": "Only include events matching one of these prefixes"}, "exclude_prefix": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Exclude Prefix", "description": "Exclude events matching one of these prefixes"}, "name": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Name", "description": "Only include events matching one of these names exactly"}, "exclude_name": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Exclude Name", "description": "Exclude events matching one of these names exactly"}}, "additionalProperties": false, "type": "object", "title": "EventNameFilter"}, "EventOccurredFilter": {"properties": {"since": {"type": "string", "format": "date-time", "title": "Since", "description": "Only include events after this time (inclusive)"}, "until": {"type": "string", "format": "date-time", "title": "Until", "description": "Only include events prior to this time (inclusive)"}}, "additionalProperties": false, "type": "object", "title": "EventOccur<PERSON><PERSON><PERSON><PERSON>"}, "EventOrder": {"type": "string", "enum": ["ASC", "DESC"], "title": "EventOrder"}, "EventPage": {"properties": {"events": {"items": {"$ref": "#/components/schemas/ReceivedEvent"}, "type": "array", "title": "Events", "description": "The Events matching the query"}, "total": {"type": "integer", "title": "Total", "description": "The total number of matching Events"}, "next_page": {"anyOf": [{"type": "string", "minLength": 1, "format": "uri"}, {"type": "null"}], "title": "Next Page", "description": "The URL for the next page of results, if there are more"}}, "type": "object", "required": ["events", "total", "next_page"], "title": "EventPage", "description": "A single page of events returned from the API, with an optional link to the\nnext page of results"}, "EventRelatedFilter": {"properties": {"id": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Id", "description": "Only include events for related resources with these IDs"}, "role": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Role", "description": "Only include events for related resources in these roles"}, "resources_in_roles": {"anyOf": [{"items": {"prefixItems": [{"type": "string"}, {"type": "string"}], "type": "array", "maxItems": 2, "minItems": 2}, "type": "array"}, {"type": "null"}], "title": "Resources In Roles", "description": "Only include events with specific related resources in specific roles"}, "labels": {"anyOf": [{"$ref": "#/components/schemas/ResourceSpecification"}, {"type": "null"}], "description": "Only include events for related resources with these labels"}}, "additionalProperties": false, "type": "object", "title": "EventRelatedFilter"}, "EventResourceFilter": {"properties": {"id": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Id", "description": "Only include events for resources with these IDs"}, "id_prefix": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Id Prefix", "description": "Only include events for resources with IDs starting with these prefixes."}, "labels": {"anyOf": [{"$ref": "#/components/schemas/ResourceSpecification"}, {"type": "null"}], "description": "Only include events for resources with these labels"}, "distinct": {"type": "boolean", "title": "Distinct", "description": "Only include events for distinct resources", "default": false}}, "additionalProperties": false, "type": "object", "title": "EventResourceFilter"}, "Flow": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "name": {"type": "string", "pattern": "^[^/%&><]+$", "title": "Name", "description": "The name of the flow", "examples": ["my-flow"]}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "A list of flow tags", "examples": [["tag-1", "tag-2"]]}}, "type": "object", "required": ["name"], "title": "Flow", "description": "An ORM representation of flow data."}, "FlowCreate": {"properties": {"name": {"type": "string", "pattern": "^[^/%&><]+$", "title": "Name", "description": "The name of the flow", "examples": ["my-flow"]}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "A list of flow tags", "examples": [["tag-1", "tag-2"]]}}, "additionalProperties": false, "type": "object", "required": ["name"], "title": "FlowCreate", "description": "Data used by the Prefect REST API to create a flow."}, "FlowFilter": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "id": {"anyOf": [{"$ref": "#/components/schemas/FlowFilterId"}, {"type": "null"}], "description": "Filter criteria for `Flow.id`"}, "deployment": {"anyOf": [{"$ref": "#/components/schemas/FlowFilterDeployment"}, {"type": "null"}], "description": "Filter criteria for Flow deployments"}, "name": {"anyOf": [{"$ref": "#/components/schemas/FlowFilterName"}, {"type": "null"}], "description": "Filter criteria for `Flow.name`"}, "tags": {"anyOf": [{"$ref": "#/components/schemas/FlowFilterTags"}, {"type": "null"}], "description": "Filter criteria for `Flow.tags`"}}, "additionalProperties": false, "type": "object", "title": "FlowFilter", "description": "Filter for flows. Only flows matching all criteria will be returned."}, "FlowFilterDeployment": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "is_null_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON> ", "description": "If true, only include flows without deployments"}}, "additionalProperties": false, "type": "object", "title": "FlowFilterDeployment", "description": "Filter by flows by deployment"}, "FlowFilterId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of flow ids to include"}}, "additionalProperties": false, "type": "object", "title": "FlowFilterId", "description": "Filter by `Flow.id`."}, "FlowFilterName": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of flow names to include", "examples": [["my-flow-1", "my-flow-2"]]}, "like_": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Like ", "description": "A case-insensitive partial match. For example,  passing 'marvin' will match 'marvin', 'sad-<PERSON>', and 'marvin-robot'.", "examples": ["marvin"]}}, "additionalProperties": false, "type": "object", "title": "FlowFilterName", "description": "Filter by `Flow.name`."}, "FlowFilterTags": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "all_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "All ", "description": "A list of tags. Flows will be returned only if their tags are a superset of the list", "examples": [["tag-1", "tag-2"]]}, "is_null_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON> ", "description": "If true, only include flows without tags"}}, "additionalProperties": false, "type": "object", "title": "FlowFilterTags", "description": "Filter by `Flow.tags`."}, "FlowPaginationResponse": {"properties": {"results": {"items": {"$ref": "#/components/schemas/Flow"}, "type": "array", "title": "Results"}, "count": {"type": "integer", "title": "Count"}, "limit": {"type": "integer", "title": "Limit"}, "pages": {"type": "integer", "title": "Pages"}, "page": {"type": "integer", "title": "Page"}}, "type": "object", "required": ["results", "count", "limit", "pages", "page"], "title": "FlowPaginationResponse"}, "FlowRun": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "name": {"type": "string", "title": "Name", "description": "The name of the flow run. Defaults to a random slug if not specified.", "examples": ["my-flow-run"]}, "flow_id": {"type": "string", "format": "uuid", "title": "Flow Id", "description": "The id of the flow being run."}, "state_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "State Id", "description": "The id of the flow run's current state."}, "deployment_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Deployment Id", "description": "The id of the deployment associated with this flow run, if available."}, "deployment_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deployment Version", "description": "The version of the deployment associated with this flow run.", "examples": ["1.0"]}, "work_queue_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Work Queue Name", "description": "The work queue that handled this flow run."}, "flow_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Flow Version", "description": "The version of the flow executed in this flow run.", "examples": ["1.0"]}, "parameters": {"type": "object", "title": "Parameters", "description": "Parameters for the flow run."}, "idempotency_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Idempotency Key", "description": "An optional idempotency key for the flow run. Used to ensure the same flow run is not created multiple times."}, "context": {"type": "object", "title": "Context", "description": "Additional context for the flow run.", "examples": [{"my_var": "my_value"}]}, "empirical_policy": {"$ref": "#/components/schemas/FlowRunPolicy"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "A list of tags on the flow run", "examples": [["tag-1", "tag-2"]]}, "parent_task_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Parent Task Run Id", "description": "If the flow run is a subflow, the id of the 'dummy' task in the parent flow used to track subflow state."}, "state_type": {"anyOf": [{"$ref": "#/components/schemas/StateType"}, {"type": "null"}], "description": "The type of the current flow run state."}, "state_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "State Name", "description": "The name of the current flow run state."}, "run_count": {"type": "integer", "title": "Run Count", "description": "The number of times the flow run was executed.", "default": 0}, "expected_start_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expected Start Time", "description": "The flow run's expected start time."}, "next_scheduled_start_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Next Scheduled Start Time", "description": "The next time the flow run is scheduled to start."}, "start_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Time", "description": "The actual start time."}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time", "description": "The actual end time."}, "total_run_time": {"type": "number", "title": "Total Run Time", "description": "Total run time. If the flow run was executed multiple times, the time of each run will be summed.", "default": 0.0}, "estimated_run_time": {"type": "number", "title": "Estimated Run Time", "description": "A real-time estimate of the total run time.", "default": 0.0}, "estimated_start_time_delta": {"type": "number", "title": "Estimated Start Time Delta", "description": "The difference between actual and expected start time.", "default": 0.0}, "auto_scheduled": {"type": "boolean", "title": "Auto Scheduled", "description": "Whether or not the flow run was automatically scheduled.", "default": false}, "infrastructure_document_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Infrastructure Document Id", "description": "The block document defining infrastructure to use this flow run."}, "infrastructure_pid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Infrastructure Pid", "description": "The id of the flow run as returned by an infrastructure block."}, "created_by": {"anyOf": [{"$ref": "#/components/schemas/CreatedBy"}, {"type": "null"}], "description": "Optional information about the creator of this flow run."}, "work_queue_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Work Queue Id", "description": "The id of the run's work pool queue."}, "state": {"anyOf": [{"$ref": "#/components/schemas/State"}, {"type": "null"}], "description": "The current state of the flow run."}, "job_variables": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Job Variables", "description": "Variables used as overrides in the base job template"}}, "type": "object", "required": ["flow_id"], "title": "FlowRun", "description": "An ORM representation of flow run data."}, "FlowRunCreate": {"properties": {"state": {"anyOf": [{"$ref": "#/components/schemas/StateCreate"}, {"type": "null"}], "description": "The state of the flow run to create"}, "name": {"type": "string", "title": "Name", "description": "The name of the flow run. Defaults to a random slug if not specified.", "examples": ["my-flow-run"]}, "flow_id": {"type": "string", "format": "uuid", "title": "Flow Id", "description": "The id of the flow being run."}, "flow_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Flow Version", "description": "The version of the flow being run."}, "parameters": {"type": "object", "title": "Parameters"}, "context": {"type": "object", "title": "Context", "description": "The context of the flow run."}, "parent_task_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Parent Task Run Id"}, "infrastructure_document_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Infrastructure Document Id"}, "empirical_policy": {"allOf": [{"$ref": "#/components/schemas/FlowRunPolicy"}], "description": "The empirical policy for the flow run."}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "A list of tags for the flow run.", "examples": [["tag-1", "tag-2"]]}, "idempotency_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Idempotency Key", "description": "An optional idempotency key. If a flow run with the same idempotency key has already been created, the existing flow run will be returned."}, "deployment_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Deployment Id", "description": "DEPRECATED: The id of the deployment associated with this flow run, if available.", "deprecated": true}}, "additionalProperties": false, "type": "object", "required": ["flow_id"], "title": "FlowRunCreate", "description": "Data used by the Prefect REST API to create a flow run."}, "FlowRunFilter": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "id": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilterId"}, {"type": "null"}], "description": "Filter criteria for `FlowRun.id`"}, "name": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilterName"}, {"type": "null"}], "description": "Filter criteria for `FlowRun.name`"}, "tags": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilterTags"}, {"type": "null"}], "description": "Filter criteria for `FlowRun.tags`"}, "deployment_id": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilterDeploymentId"}, {"type": "null"}], "description": "Filter criteria for `FlowRun.deployment_id`"}, "work_queue_name": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilterWorkQueueName"}, {"type": "null"}], "description": "Filter criteria for `FlowRun.work_queue_name"}, "state": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilterState"}, {"type": "null"}], "description": "Filter criteria for `FlowRun.state`"}, "flow_version": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilterFlowVersion"}, {"type": "null"}], "description": "Filter criteria for `FlowRun.flow_version`"}, "start_time": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilterStartTime"}, {"type": "null"}], "description": "Filter criteria for `FlowRun.start_time`"}, "expected_start_time": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilterExpectedStartTime"}, {"type": "null"}], "description": "Filter criteria for `FlowRun.expected_start_time`"}, "next_scheduled_start_time": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilterNextScheduledStartTime"}, {"type": "null"}], "description": "Filter criteria for `FlowRun.next_scheduled_start_time`"}, "parent_flow_run_id": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilterParentFlowRunId"}, {"type": "null"}], "description": "Filter criteria for subflows of the given flow runs"}, "parent_task_run_id": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilterParentTaskRunId"}, {"type": "null"}], "description": "Filter criteria for `FlowRun.parent_task_run_id`"}, "idempotency_key": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilterIdempotencyKey"}, {"type": "null"}], "description": "Filter criteria for `FlowRun.idempotency_key`"}}, "additionalProperties": false, "type": "object", "title": "FlowRunFilter", "description": "Filter flow runs. Only flow runs matching all criteria will be returned"}, "FlowRunFilterDeploymentId": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of flow run deployment ids to include"}, "is_null_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON> ", "description": "If true, only include flow runs without deployment ids"}}, "additionalProperties": false, "type": "object", "title": "FlowRunFilterDeploymentId", "description": "Filter by `FlowRun.deployment_id`."}, "FlowRunFilterExpectedStartTime": {"properties": {"before_": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Before ", "description": "Only include flow runs scheduled to start at or before this time"}, "after_": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "After ", "description": "Only include flow runs scheduled to start at or after this time"}}, "additionalProperties": false, "type": "object", "title": "FlowRunFilterExpectedStartTime", "description": "Filter by `FlowRun.expected_start_time`."}, "FlowRunFilterFlowVersion": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of flow run flow_versions to include"}}, "additionalProperties": false, "type": "object", "title": "FlowRunFilterFlowVersion", "description": "Filter by `FlowRun.flow_version`."}, "FlowRunFilterId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of flow run ids to include"}, "not_any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Not Any ", "description": "A list of flow run ids to exclude"}}, "additionalProperties": false, "type": "object", "title": "FlowRunFilterId", "description": "Filter by `FlowRun.id`."}, "FlowRunFilterIdempotencyKey": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of flow run idempotency keys to include"}, "not_any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Not Any ", "description": "A list of flow run idempotency keys to exclude"}}, "additionalProperties": false, "type": "object", "title": "FlowRunFilterIdempotencyKey", "description": "Filter by FlowRun.idempotency_key."}, "FlowRunFilterName": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of flow run names to include", "examples": [["my-flow-run-1", "my-flow-run-2"]]}, "like_": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Like ", "description": "A case-insensitive partial match. For example,  passing 'marvin' will match 'marvin', 'sad-<PERSON>', and 'marvin-robot'.", "examples": ["marvin"]}}, "additionalProperties": false, "type": "object", "title": "FlowRunFilterName", "description": "Filter by `FlowRun.name`."}, "FlowRunFilterNextScheduledStartTime": {"properties": {"before_": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Before ", "description": "Only include flow runs with a next_scheduled_start_time or before this time"}, "after_": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "After ", "description": "Only include flow runs with a next_scheduled_start_time at or after this time"}}, "additionalProperties": false, "type": "object", "title": "FlowRunFilterNextScheduledStartTime", "description": "Filter by `FlowRun.next_scheduled_start_time`."}, "FlowRunFilterParentFlowRunId": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of parent flow run ids to include"}}, "additionalProperties": false, "type": "object", "title": "FlowRunFilterParentFlowRunId", "description": "Filter for subflows of a given flow run"}, "FlowRunFilterParentTaskRunId": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of flow run parent_task_run_ids to include"}, "is_null_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON> ", "description": "If true, only include flow runs without parent_task_run_id"}}, "additionalProperties": false, "type": "object", "title": "FlowRunFilterParentTaskRunId", "description": "Filter by `FlowRun.parent_task_run_id`."}, "FlowRunFilterStartTime": {"properties": {"before_": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Before ", "description": "Only include flow runs starting at or before this time"}, "after_": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "After ", "description": "Only include flow runs starting at or after this time"}, "is_null_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON> ", "description": "If true, only return flow runs without a start time"}}, "additionalProperties": false, "type": "object", "title": "FlowRunFilterStartTime", "description": "Filter by `FlowRun.start_time`."}, "FlowRunFilterState": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "type": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilterStateType"}, {"type": "null"}], "description": "Filter criteria for `FlowRun.state_type`"}, "name": {"anyOf": [{"$ref": "#/components/schemas/FlowRunFilterStateName"}, {"type": "null"}], "description": "Filter criteria for `FlowRun.state_name`"}}, "additionalProperties": false, "type": "object", "title": "FlowRunFilterState", "description": "Filter by `FlowRun.state_type` and `FlowRun.state_name`."}, "FlowRunFilterStateName": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of flow run state names to include"}, "not_any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Not Any ", "description": "A list of flow run state names to exclude"}}, "additionalProperties": false, "type": "object", "title": "FlowRunFilterStateName", "description": "Filter by `FlowRun.state_name`."}, "FlowRunFilterStateType": {"properties": {"any_": {"anyOf": [{"items": {"$ref": "#/components/schemas/StateType"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of flow run state types to include"}, "not_any_": {"anyOf": [{"items": {"$ref": "#/components/schemas/StateType"}, "type": "array"}, {"type": "null"}], "title": "Not Any ", "description": "A list of flow run state types to exclude"}}, "additionalProperties": false, "type": "object", "title": "FlowRunFilterStateType", "description": "Filter by `FlowRun.state_type`."}, "FlowRunFilterTags": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "all_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "All ", "description": "A list of tags. Flow runs will be returned only if their tags are a superset of the list", "examples": [["tag-1", "tag-2"]]}, "is_null_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON> ", "description": "If true, only include flow runs without tags"}}, "additionalProperties": false, "type": "object", "title": "FlowRunFilterTags", "description": "Filter by `FlowRun.tags`."}, "FlowRunFilterWorkQueueName": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of work queue names to include", "examples": [["work_queue_1", "work_queue_2"]]}, "is_null_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON> ", "description": "If true, only include flow runs without work queue names"}}, "additionalProperties": false, "type": "object", "title": "FlowRunFilterWorkQueueName", "description": "Filter by `FlowRun.work_queue_name`."}, "FlowRunInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "flow_run_id": {"type": "string", "format": "uuid", "title": "Flow Run Id", "description": "The flow run ID associated with the input."}, "key": {"type": "string", "title": "Key", "description": "The key of the input."}, "value": {"type": "string", "title": "Value", "description": "The value of the input."}, "sender": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sender", "description": "The sender of the input."}}, "type": "object", "required": ["flow_run_id", "key", "value"], "title": "FlowRunInput"}, "FlowRunNotificationPolicy": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether the policy is currently active", "default": true}, "state_names": {"items": {"type": "string"}, "type": "array", "title": "State Names", "description": "The flow run states that trigger notifications"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "The flow run tags that trigger notifications (set [] to disable)"}, "block_document_id": {"type": "string", "format": "uuid", "title": "Block Document Id", "description": "The block document ID used for sending notifications"}, "message_template": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message Template", "description": "A templatable notification message. Use {braces} to add variables. Valid variables include: 'flow_id', 'flow_name', 'flow_run_id', 'flow_run_name', 'flow_run_notification_policy_id', 'flow_run_parameters', 'flow_run_state_message', 'flow_run_state_name', 'flow_run_state_timestamp', 'flow_run_state_type', 'flow_run_url'", "examples": ["Flow run {flow_run_name} with id {flow_run_id} entered state {flow_run_state_name}."]}}, "type": "object", "required": ["state_names", "tags", "block_document_id"], "title": "FlowRunNotificationPolicy", "description": "An ORM representation of a flow run notification."}, "FlowRunNotificationPolicyCreate": {"properties": {"is_active": {"type": "boolean", "title": "Is Active", "description": "Whether the policy is currently active", "default": true}, "state_names": {"items": {"type": "string"}, "type": "array", "title": "State Names", "description": "The flow run states that trigger notifications"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "The flow run tags that trigger notifications (set [] to disable)"}, "block_document_id": {"type": "string", "format": "uuid", "title": "Block Document Id", "description": "The block document ID used for sending notifications"}, "message_template": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message Template", "description": "A templatable notification message. Use {braces} to add variables. Valid variables include: 'flow_id', 'flow_name', 'flow_run_id', 'flow_run_name', 'flow_run_notification_policy_id', 'flow_run_parameters', 'flow_run_state_message', 'flow_run_state_name', 'flow_run_state_timestamp', 'flow_run_state_type', 'flow_run_url'", "examples": ["Flow run {flow_run_name} with id {flow_run_id} entered state {flow_run_state_name}."]}}, "additionalProperties": false, "type": "object", "required": ["state_names", "tags", "block_document_id"], "title": "FlowRunNotificationPolicyCreate", "description": "Data used by the Prefect REST API to create a flow run notification policy."}, "FlowRunNotificationPolicyFilter": {"properties": {"is_active": {"anyOf": [{"$ref": "#/components/schemas/FlowRunNotificationPolicyFilterIsActive"}, {"type": "null"}], "description": "Filter criteria for `FlowRunNotificationPolicy.is_active`. ", "default": {"eq_": false}}}, "additionalProperties": false, "type": "object", "title": "FlowRunNotificationPolicyFilter", "description": "Filter FlowRunNotificationPolicies."}, "FlowRunNotificationPolicyFilterIsActive": {"properties": {"eq_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Eq ", "description": "Filter notification policies for only those that are or are not active."}}, "additionalProperties": false, "type": "object", "title": "FlowRunNotificationPolicyFilterIsActive", "description": "Filter by `FlowRunNotificationPolicy.is_active`."}, "FlowRunNotificationPolicyUpdate": {"properties": {"is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}, "state_names": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "State Names"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags"}, "block_document_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Block Document Id"}, "message_template": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message Template"}}, "additionalProperties": false, "type": "object", "title": "FlowRunNotificationPolicyUpdate", "description": "Data used by the Prefect REST API to update a flow run notification policy."}, "FlowRunPaginationResponse": {"properties": {"results": {"items": {"$ref": "#/components/schemas/FlowRunResponse"}, "type": "array", "title": "Results"}, "count": {"type": "integer", "title": "Count"}, "limit": {"type": "integer", "title": "Limit"}, "pages": {"type": "integer", "title": "Pages"}, "page": {"type": "integer", "title": "Page"}}, "type": "object", "required": ["results", "count", "limit", "pages", "page"], "title": "FlowRunPaginationResponse"}, "FlowRunPolicy": {"properties": {"max_retries": {"type": "integer", "title": "Max Retries", "description": "The maximum number of retries. Field is not used. Please use `retries` instead.", "default": 0, "deprecated": true}, "retry_delay_seconds": {"type": "number", "title": "Retry Delay Seconds", "description": "The delay between retries. Field is not used. Please use `retry_delay` instead.", "default": 0, "deprecated": true}, "retries": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Retries", "description": "The number of retries."}, "retry_delay": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Retry Delay", "description": "The delay time between retries, in seconds."}, "pause_keys": {"anyOf": [{"items": {}, "type": "array", "uniqueItems": true}, {"type": "null"}], "title": "Pause Keys", "description": "Tracks pauses this run has observed."}, "resuming": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Resuming", "description": "Indicates if this run is resuming from a pause.", "default": false}}, "type": "object", "title": "FlowRunPolicy", "description": "Defines of how a flow run should retry."}, "FlowRunResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "name": {"type": "string", "title": "Name", "description": "The name of the flow run. Defaults to a random slug if not specified.", "examples": ["my-flow-run"]}, "flow_id": {"type": "string", "format": "uuid", "title": "Flow Id", "description": "The id of the flow being run."}, "state_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "State Id", "description": "The id of the flow run's current state."}, "deployment_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Deployment Id", "description": "The id of the deployment associated with this flow run, if available."}, "deployment_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deployment Version", "description": "The version of the deployment associated with this flow run.", "examples": ["1.0"]}, "work_queue_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Work Queue Id", "description": "The id of the run's work pool queue."}, "work_queue_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Work Queue Name", "description": "The work queue that handled this flow run."}, "flow_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Flow Version", "description": "The version of the flow executed in this flow run.", "examples": ["1.0"]}, "parameters": {"type": "object", "title": "Parameters", "description": "Parameters for the flow run."}, "idempotency_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Idempotency Key", "description": "An optional idempotency key for the flow run. Used to ensure the same flow run is not created multiple times."}, "context": {"type": "object", "title": "Context", "description": "Additional context for the flow run.", "examples": [{"my_var": "my_val"}]}, "empirical_policy": {"$ref": "#/components/schemas/FlowRunPolicy"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "A list of tags on the flow run", "examples": [["tag-1", "tag-2"]]}, "parent_task_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Parent Task Run Id", "description": "If the flow run is a subflow, the id of the 'dummy' task in the parent flow used to track subflow state."}, "state_type": {"anyOf": [{"$ref": "#/components/schemas/StateType"}, {"type": "null"}], "description": "The type of the current flow run state."}, "state_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "State Name", "description": "The name of the current flow run state."}, "run_count": {"type": "integer", "title": "Run Count", "description": "The number of times the flow run was executed.", "default": 0}, "expected_start_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expected Start Time", "description": "The flow run's expected start time."}, "next_scheduled_start_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Next Scheduled Start Time", "description": "The next time the flow run is scheduled to start."}, "start_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Time", "description": "The actual start time."}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time", "description": "The actual end time."}, "total_run_time": {"type": "number", "title": "Total Run Time", "description": "Total run time. If the flow run was executed multiple times, the time of each run will be summed.", "default": 0.0}, "estimated_run_time": {"type": "number", "title": "Estimated Run Time", "description": "A real-time estimate of the total run time.", "default": 0.0}, "estimated_start_time_delta": {"type": "number", "title": "Estimated Start Time Delta", "description": "The difference between actual and expected start time.", "default": 0.0}, "auto_scheduled": {"type": "boolean", "title": "Auto Scheduled", "description": "Whether or not the flow run was automatically scheduled.", "default": false}, "infrastructure_document_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Infrastructure Document Id", "description": "The block document defining infrastructure to use this flow run."}, "infrastructure_pid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Infrastructure Pid", "description": "The id of the flow run as returned by an infrastructure block."}, "created_by": {"anyOf": [{"$ref": "#/components/schemas/CreatedBy"}, {"type": "null"}], "description": "Optional information about the creator of this flow run."}, "work_pool_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Work Pool Id", "description": "The id of the flow run's work pool."}, "work_pool_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Work Pool Name", "description": "The name of the flow run's work pool.", "examples": ["my-work-pool"]}, "state": {"anyOf": [{"$ref": "#/components/schemas/State"}, {"type": "null"}], "description": "The current state of the flow run."}, "job_variables": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Job Variables", "description": "Variables used as overrides in the base job template"}}, "type": "object", "required": ["flow_id"], "title": "FlowRunResponse"}, "FlowRunSort": {"type": "string", "enum": ["ID_DESC", "START_TIME_ASC", "START_TIME_DESC", "EXPECTED_START_TIME_ASC", "EXPECTED_START_TIME_DESC", "NAME_ASC", "NAME_DESC", "NEXT_SCHEDULED_START_TIME_ASC", "END_TIME_DESC"], "title": "FlowRunSort", "description": "Defines flow run sorting options."}, "FlowRunUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "flow_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Flow Version"}, "parameters": {"type": "object", "title": "Parameters"}, "empirical_policy": {"$ref": "#/components/schemas/FlowRunPolicy"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags"}, "infrastructure_pid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Infrastructure Pid"}, "job_variables": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Job Variables"}}, "additionalProperties": false, "type": "object", "title": "FlowRunUpdate", "description": "Data used by the Prefect REST API to update a flow run."}, "FlowSort": {"type": "string", "enum": ["CREATED_DESC", "UPDATED_DESC", "NAME_ASC", "NAME_DESC"], "title": "FlowSort", "description": "Defines flow sorting options."}, "FlowUpdate": {"properties": {"tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "A list of flow tags", "examples": [["tag-1", "tag-2"]]}}, "additionalProperties": false, "type": "object", "title": "FlowUpdate", "description": "Data used by the Prefect REST API to update a flow."}, "GlobalConcurrencyLimitResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "active": {"type": "boolean", "title": "Active", "description": "Whether the global concurrency limit is active.", "default": true}, "name": {"type": "string", "title": "Name", "description": "The name of the global concurrency limit."}, "limit": {"type": "integer", "title": "Limit", "description": "The concurrency limit."}, "active_slots": {"type": "integer", "title": "Active Slots", "description": "The number of active slots."}, "slot_decay_per_second": {"type": "number", "title": "Slot Decay Per Second", "description": "The decay rate for active slots when used as a rate limit.", "default": 2.0}}, "type": "object", "required": ["name", "limit", "active_slots"], "title": "GlobalConcurrencyLimitResponse", "description": "A response object for global concurrency limits."}, "Graph": {"properties": {"start_time": {"type": "string", "format": "date-time", "title": "Start Time"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "root_node_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "Root Node Ids"}, "nodes": {"items": {"prefixItems": [{"type": "string", "format": "uuid"}, {"$ref": "#/components/schemas/Node"}], "type": "array", "maxItems": 2, "minItems": 2}, "type": "array", "title": "Nodes"}, "artifacts": {"items": {"$ref": "#/components/schemas/GraphArtifact"}, "type": "array", "title": "Artifacts"}, "states": {"items": {"$ref": "#/components/schemas/GraphState"}, "type": "array", "title": "States"}}, "type": "object", "required": ["start_time", "end_time", "root_node_ids", "nodes", "artifacts", "states"], "title": "Graph"}, "GraphArtifact": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"type": "string", "format": "date-time", "title": "Created"}, "key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Key"}, "type": {"type": "string", "title": "Type"}, "is_latest": {"type": "boolean", "title": "Is Latest"}, "data": {"anyOf": [{}, {"type": "null"}], "title": "Data"}}, "type": "object", "required": ["id", "created", "key", "type", "is_latest", "data"], "title": "GraphArtifact"}, "GraphState": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "type": {"$ref": "#/components/schemas/StateType"}, "name": {"type": "string", "title": "Name"}}, "type": "object", "required": ["id", "timestamp", "type", "name"], "title": "GraphState"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "HistoryResponse": {"properties": {"interval_start": {"type": "string", "format": "date-time", "title": "Interval Start", "description": "The start date of the interval."}, "interval_end": {"type": "string", "format": "date-time", "title": "Interval End", "description": "The end date of the interval."}, "states": {"items": {"$ref": "#/components/schemas/HistoryResponseState"}, "type": "array", "title": "States", "description": "A list of state histories during the interval."}}, "type": "object", "required": ["interval_start", "interval_end", "states"], "title": "HistoryResponse", "description": "Represents a history of aggregation states over an interval"}, "HistoryResponseState": {"properties": {"state_type": {"allOf": [{"$ref": "#/components/schemas/StateType"}], "description": "The state type."}, "state_name": {"type": "string", "title": "State Name", "description": "The state name."}, "count_runs": {"type": "integer", "title": "Count Runs", "description": "The number of runs in the specified state during the interval."}, "sum_estimated_run_time": {"type": "number", "title": "Sum Estimated Run Time", "description": "The total estimated run time of all runs during the interval."}, "sum_estimated_lateness": {"type": "number", "title": "Sum Estimated Lateness", "description": "The sum of differences between actual and expected start time during the interval."}}, "type": "object", "required": ["state_type", "state_name", "count_runs", "sum_estimated_run_time", "sum_estimated_lateness"], "title": "HistoryResponseState", "description": "Represents a single state's history over an interval."}, "IntervalSchedule": {"properties": {"interval": {"type": "number", "title": "Interval"}, "anchor_date": {"type": "string", "format": "date-time", "title": "Anchor Date", "examples": ["2020-01-01T00:00:00Z"]}, "timezone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timezone", "examples": ["America/New_York"]}}, "additionalProperties": false, "type": "object", "required": ["interval"], "title": "IntervalSchedule", "description": "A schedule formed by adding `interval` increments to an `anchor_date`. If no\n`anchor_date` is supplied, the current UTC time is used.  If a\ntimezone-naive datetime is provided for `anchor_date`, it is assumed to be\nin the schedule's timezone (or UTC). Even if supplied with an IANA timezone,\nanchor dates are always stored as UTC offsets, so a `timezone` can be\nprovided to determine localization behaviors like DST boundary handling. If\nnone is provided it will be inferred from the anchor date.\n\nNOTE: If the `IntervalSchedule` `anchor_date` or `timezone` is provided in a\nDST-observing timezone, then the schedule will adjust itself appropriately.\nIntervals greater than 24 hours will follow DST conventions, while intervals\nof less than 24 hours will follow UTC intervals. For example, an hourly\nschedule will fire every UTC hour, even across DST boundaries. When clocks\nare set back, this will result in two runs that *appear* to both be\nscheduled for 1am local time, even though they are an hour apart in UTC\ntime. For longer intervals, like a daily schedule, the interval schedule\nwill adjust for DST boundaries so that the clock-hour remains constant. This\nmeans that a daily schedule that always fires at 9am will observe DST and\ncontinue to fire at 9am in the local time zone.\n\nArgs:\n    interval (datetime.timedelta): an interval to schedule on.\n    anchor_date (DateTime, optional): an anchor date to schedule increments against;\n        if not provided, the current timestamp will be used.\n    timezone (str, optional): a valid timezone string."}, "Log": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "name": {"type": "string", "title": "Name", "description": "The logger name."}, "level": {"type": "integer", "title": "Level", "description": "The log level."}, "message": {"type": "string", "title": "Message", "description": "The log message."}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp", "description": "The log timestamp."}, "flow_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Flow Run Id", "description": "The flow run ID associated with the log."}, "task_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Task Run Id", "description": "The task run ID associated with the log."}}, "type": "object", "required": ["name", "level", "message", "timestamp"], "title": "Log", "description": "An ORM representation of log data."}, "LogCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "The logger name."}, "level": {"type": "integer", "title": "Level", "description": "The log level."}, "message": {"type": "string", "title": "Message", "description": "The log message."}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp", "description": "The log timestamp."}, "flow_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Flow Run Id"}, "task_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Task Run Id"}}, "additionalProperties": false, "type": "object", "required": ["name", "level", "message", "timestamp"], "title": "LogCreate", "description": "Data used by the Prefect REST API to create a log."}, "LogFilter": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "level": {"anyOf": [{"$ref": "#/components/schemas/LogFilterLevel"}, {"type": "null"}], "description": "Filter criteria for `Log.level`"}, "timestamp": {"anyOf": [{"$ref": "#/components/schemas/LogFilterTimestamp"}, {"type": "null"}], "description": "Filter criteria for `Log.timestamp`"}, "flow_run_id": {"anyOf": [{"$ref": "#/components/schemas/LogFilterFlowRunId"}, {"type": "null"}], "description": "Filter criteria for `Log.flow_run_id`"}, "task_run_id": {"anyOf": [{"$ref": "#/components/schemas/LogFilterTaskRunId"}, {"type": "null"}], "description": "Filter criteria for `Log.task_run_id`"}}, "additionalProperties": false, "type": "object", "title": "Log<PERSON><PERSON><PERSON>", "description": "Filter logs. Only logs matching all criteria will be returned"}, "LogFilterFlowRunId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of flow run IDs to include"}}, "additionalProperties": false, "type": "object", "title": "LogFilterFlowRunId", "description": "Filter by `Log.flow_run_id`."}, "LogFilterLevel": {"properties": {"ge_": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Ge ", "description": "Include logs with a level greater than or equal to this level", "examples": [20]}, "le_": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Le ", "description": "Include logs with a level less than or equal to this level", "examples": [50]}}, "additionalProperties": false, "type": "object", "title": "LogFilterLevel", "description": "Filter by `Log.level`."}, "LogFilterTaskRunId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of task run IDs to include"}}, "additionalProperties": false, "type": "object", "title": "LogFilterTaskRunId", "description": "Filter by `Log.task_run_id`."}, "LogFilterTimestamp": {"properties": {"before_": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Before ", "description": "Only include logs with a timestamp at or before this time"}, "after_": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "After ", "description": "Only include logs with a timestamp at or after this time"}}, "additionalProperties": false, "type": "object", "title": "LogFilterTimestamp", "description": "Filter by `Log.timestamp`."}, "LogSort": {"type": "string", "enum": ["TIMESTAMP_ASC", "TIMESTAMP_DESC"], "title": "LogSort", "description": "Defines log sorting options."}, "MetricTrigger-Input": {"properties": {"type": {"type": "string", "enum": ["metric"], "const": "metric", "title": "Type", "default": "metric"}, "match": {"allOf": [{"$ref": "#/components/schemas/ResourceSpecification"}], "description": "Labels for resources which this trigger will match."}, "match_related": {"allOf": [{"$ref": "#/components/schemas/ResourceSpecification"}], "description": "Labels for related resources which this trigger will match."}, "posture": {"type": "string", "enum": ["Metric"], "const": "Metric", "title": "Posture", "description": "Periodically evaluate the configured metric query.", "default": "Metric"}, "metric": {"allOf": [{"$ref": "#/components/schemas/MetricTriggerQuery"}], "description": "The metric query to evaluate for this trigger. "}}, "type": "object", "required": ["metric"], "title": "<PERSON>ric<PERSON><PERSON>ger", "description": "A trigger that fires based on the results of a metric query."}, "MetricTrigger-Output": {"properties": {"type": {"type": "string", "enum": ["metric"], "const": "metric", "title": "Type", "default": "metric"}, "match": {"allOf": [{"$ref": "#/components/schemas/ResourceSpecification"}], "description": "Labels for resources which this trigger will match."}, "match_related": {"allOf": [{"$ref": "#/components/schemas/ResourceSpecification"}], "description": "Labels for related resources which this trigger will match."}, "posture": {"type": "string", "enum": ["Metric"], "const": "Metric", "title": "Posture", "description": "Periodically evaluate the configured metric query.", "default": "Metric"}, "metric": {"allOf": [{"$ref": "#/components/schemas/MetricTriggerQuery"}], "description": "The metric query to evaluate for this trigger. "}}, "type": "object", "required": ["metric"], "title": "<PERSON>ric<PERSON><PERSON>ger", "description": "A trigger that fires based on the results of a metric query."}, "MetricTriggerOperator": {"type": "string", "enum": ["<", "<=", ">", ">="], "title": "MetricTriggerOperator"}, "MetricTriggerQuery": {"properties": {"name": {"allOf": [{"$ref": "#/components/schemas/PrefectMetric"}], "description": "The name of the metric to query."}, "threshold": {"type": "number", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "The threshold value against which we'll compare the query result."}, "operator": {"allOf": [{"$ref": "#/components/schemas/MetricTriggerOperator"}], "description": "The comparative operator (LT / LTE / GT / GTE) used to compare the query result against the threshold value."}, "range": {"type": "number", "title": "Range", "description": "The lookback duration (seconds) for a metric query. This duration is used to determine the time range over which the query will be executed. The minimum value is 300 seconds (5 minutes).", "default": 300.0}, "firing_for": {"type": "number", "title": "Firing For", "description": "The duration (seconds) for which the metric query must breach or resolve continuously before the state is updated and the automation is triggered. The minimum value is 300 seconds (5 minutes).", "default": 300.0}}, "type": "object", "required": ["name", "threshold", "operator"], "title": "<PERSON>ric<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Defines a subset of the Trigger subclass, which is specific\nto Metric automations, that specify the query configurations\nand breaching conditions for the Automation"}, "MinimalConcurrencyLimitResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "limit": {"type": "integer", "title": "Limit"}}, "type": "object", "required": ["id", "name", "limit"], "title": "MinimalConcurrencyLimitResponse"}, "Node": {"properties": {"kind": {"type": "string", "enum": ["flow-run", "task-run"], "title": "Kind"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "label": {"type": "string", "title": "Label"}, "state_type": {"$ref": "#/components/schemas/StateType"}, "start_time": {"type": "string", "format": "date-time", "title": "Start Time"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "parents": {"items": {"$ref": "#/components/schemas/Edge"}, "type": "array", "title": "Parents"}, "children": {"items": {"$ref": "#/components/schemas/Edge"}, "type": "array", "title": "Children"}, "encapsulating": {"items": {"$ref": "#/components/schemas/Edge"}, "type": "array", "title": "Encapsulating"}, "artifacts": {"items": {"$ref": "#/components/schemas/GraphArtifact"}, "type": "array", "title": "Artifacts"}}, "type": "object", "required": ["kind", "id", "label", "state_type", "start_time", "end_time", "parents", "children", "encapsulating", "artifacts"], "title": "Node"}, "Operator": {"type": "string", "enum": ["and_", "or_"], "title": "Operator", "description": "Operators for combining filter criteria."}, "OrchestrationResult": {"properties": {"state": {"anyOf": [{"$ref": "#/components/schemas/State"}, {"type": "null"}]}, "status": {"$ref": "#/components/schemas/SetStateStatus"}, "details": {"anyOf": [{"$ref": "#/components/schemas/StateAcceptDetails"}, {"$ref": "#/components/schemas/StateWaitDetails"}, {"$ref": "#/components/schemas/StateRejectDetails"}, {"$ref": "#/components/schemas/StateAbortDetails"}], "title": "Details"}}, "type": "object", "required": ["state", "status", "details"], "title": "OrchestrationResult", "description": "A container for the output of state orchestration."}, "Parameter": {"properties": {"input_type": {"type": "string", "enum": ["parameter"], "const": "parameter", "title": "Input Type", "default": "parameter"}, "name": {"type": "string", "title": "Name"}}, "type": "object", "required": ["name"], "title": "Parameter", "description": "Represents a parameter input to a task run."}, "PauseAutomation": {"properties": {"type": {"type": "string", "enum": ["pause-automation"], "const": "pause-automation", "title": "Type", "default": "pause-automation"}, "source": {"type": "string", "enum": ["selected", "inferred"], "title": "Source", "description": "Whether this Action applies to a specific selected automation (given by `automation_id`), or to an automation that is inferred from the triggering event.  If the source is 'inferred', the `automation_id` may not be set.  If the source is 'selected', the `automation_id` must be set.", "default": "selected"}, "automation_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Automation Id", "description": "The identifier of the automation to act on"}}, "type": "object", "title": "PauseAutomation", "description": "Pauses a Work Queue"}, "PauseDeployment": {"properties": {"type": {"type": "string", "enum": ["pause-deployment"], "const": "pause-deployment", "title": "Type", "default": "pause-deployment"}, "source": {"type": "string", "enum": ["selected", "inferred"], "title": "Source", "description": "Whether this Action applies to a specific selected deployment (given by `deployment_id`), or to a deployment that is inferred from the triggering event.  If the source is 'inferred', the `deployment_id` may not be set.  If the source is 'selected', the `deployment_id` must be set.", "default": "selected"}, "deployment_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Deployment Id", "description": "The identifier of the deployment"}}, "type": "object", "title": "PauseDeployment", "description": "Pauses the given Deployment"}, "PauseWorkPool": {"properties": {"type": {"type": "string", "enum": ["pause-work-pool"], "const": "pause-work-pool", "title": "Type", "default": "pause-work-pool"}, "source": {"type": "string", "enum": ["selected", "inferred"], "title": "Source", "description": "Whether this Action applies to a specific selected work pool (given by `work_pool_id`), or to a work pool that is inferred from the triggering event.  If the source is 'inferred', the `work_pool_id` may not be set.  If the source is 'selected', the `work_pool_id` must be set.", "default": "selected"}, "work_pool_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Work Pool Id", "description": "The identifier of the work pool to pause"}}, "type": "object", "title": "PauseWorkPool", "description": "Pauses a Work Pool"}, "PauseWorkQueue": {"properties": {"type": {"type": "string", "enum": ["pause-work-queue"], "const": "pause-work-queue", "title": "Type", "default": "pause-work-queue"}, "source": {"type": "string", "enum": ["selected", "inferred"], "title": "Source", "description": "Whether this Action applies to a specific selected work queue (given by `work_queue_id`), or to a work queue that is inferred from the triggering event.  If the source is 'inferred', the `work_queue_id` may not be set.  If the source is 'selected', the `work_queue_id` must be set.", "default": "selected"}, "work_queue_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Work Queue Id", "description": "The identifier of the work queue to pause"}}, "type": "object", "title": "PauseWorkQueue", "description": "Pauses a Work Queue"}, "PrefectMetric": {"type": "string", "enum": ["lateness", "duration", "successes"], "title": "PrefectMetric"}, "QueueFilter": {"properties": {"tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags", "description": "Only include flow runs with these tags in the work queue."}, "deployment_ids": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Deployment Ids", "description": "Only include flow runs from these deployments in the work queue."}}, "type": "object", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Filter criteria definition for a work queue."}, "RRuleSchedule": {"properties": {"rrule": {"type": "string", "title": "<PERSON><PERSON><PERSON>"}, "timezone": {"anyOf": [{"type": "string", "pattern": "Africa/Abidjan|Africa/Accra|Africa/Addis_Ababa|Africa/Algiers|Africa/Asmara|Africa/Asmera|Africa/Bamako|Africa/Bangui|Africa/Banjul|Africa/Bissau|Africa/Blantyre|Africa/Brazzaville|Africa/Bujumbura|Africa/Cairo|Africa/Casablanca|Africa/Ceuta|Africa/Conakry|Africa/Dakar|Africa/Dar_es_Salaam|Africa/Djibouti|Africa/Douala|Africa/El_Aaiun|Africa/Freetown|Africa/Gaborone|Africa/Harare|Africa/Johannesburg|Africa/Juba|Africa/Kampala|Africa/Khartoum|Africa/Kigali|Africa/Kinshasa|Africa/Lagos|Africa/Libreville|Africa/Lome|Africa/Luanda|Africa/Lubumbashi|Africa/Lusaka|Africa/Malabo|Africa/Maputo|Africa/Maseru|Africa/Mbabane|Africa/Mogadishu|Africa/Monrovia|Africa/Nairobi|Africa/Ndjamena|Africa/Niamey|Africa/Nouakchott|Africa/Ouagadougou|Africa/Porto-Novo|Africa/Sao_Tome|Africa/Timbuktu|Africa/Tripoli|Africa/Tunis|Africa/Windhoek|America/Adak|America/Anchorage|America/Anguilla|America/Antigua|America/Araguaina|America/Argentina/Buenos_Aires|America/Argentina/Catamarca|America/Argentina/ComodRivadavia|America/Argentina/Cordoba|America/Argentina/Jujuy|America/Argentina/La_Rioja|America/Argentina/Mendoza|America/Argentina/Rio_Gallegos|America/Argentina/Salta|America/Argentina/San_Juan|America/Argentina/San_Luis|America/Argentina/Tucuman|America/Argentina/Ushuaia|America/Aruba|America/Asuncion|America/Atikokan|America/Atka|America/Bahia|America/Bahia_Banderas|America/Barbados|America/Belem|America/Belize|America/Blanc-Sablon|America/Boa_Vista|America/Bogota|America/Boise|America/Buenos_Aires|America/Cambridge_Bay|America/Campo_Grande|America/Cancun|America/Caracas|America/Catamarca|America/Cayenne|America/Cayman|America/Chicago|America/Chihuahua|America/Ciudad_Juarez|America/Coral_Harbour|America/Cordoba|America/Costa_Rica|America/Creston|America/Cuiaba|America/Curacao|America/Danmarkshavn|America/Dawson|America/Dawson_Creek|America/Denver|America/Detroit|America/Dominica|America/Edmonton|America/Eirunepe|America/El_Salvador|America/Ensenada|America/Fort_Nelson|America/Fort_Wayne|America/Fortaleza|America/Glace_Bay|America/Godthab|America/Goose_Bay|America/Grand_Turk|America/Grenada|America/Guadeloupe|America/Guatemala|America/Guayaquil|America/Guyana|America/Halifax|America/Havana|America/Hermosillo|America/Indiana/Indianapolis|America/Indiana/Knox|America/Indiana/Marengo|America/Indiana/Petersburg|America/Indiana/Tell_City|America/Indiana/Vevay|America/Indiana/Vincennes|America/Indiana/Winamac|America/Indianapolis|America/Inuvik|America/Iqaluit|America/Jamaica|America/Jujuy|America/Juneau|America/Kentucky/Louisville|America/Kentucky/Monticello|America/Knox_IN|America/Kralendijk|America/La_Paz|America/Lima|America/Los_Angeles|America/Louisville|America/Lower_Princes|America/Maceio|America/Managua|America/Manaus|America/Marigot|America/Martinique|America/Matamoros|America/Mazatlan|America/Mendoza|America/Menominee|America/Merida|America/Metlakatla|America/Mexico_City|America/Miquelon|America/Moncton|America/Monterrey|America/Montevideo|America/Montreal|America/Montserrat|America/Nassau|America/New_York|America/Nipigon|America/Nome|America/Noronha|America/North_Dakota/Beulah|America/North_Dakota/Center|America/North_Dakota/New_Salem|America/Nuuk|America/Ojinaga|America/Panama|America/Pangnirtung|America/Paramaribo|America/Phoenix|America/Port-au-Prince|America/Port_of_Spain|America/Porto_Acre|America/Porto_Velho|America/Puerto_Rico|America/Punta_Arenas|America/Rainy_River|America/Rankin_Inlet|America/Recife|America/Regina|America/Resolute|America/Rio_Branco|America/Rosario|America/Santa_Isabel|America/Santarem|America/Santiago|America/Santo_Domingo|America/Sao_Paulo|America/Scoresbysund|America/Shiprock|America/Sitka|America/St_Barthelemy|America/St_Johns|America/St_Kitts|America/St_Lucia|America/St_Thomas|America/St_Vincent|America/Swift_Current|America/Tegucigalpa|America/Thule|America/Thunder_Bay|America/Tijuana|America/Toronto|America/Tortola|America/Vancouver|America/Virgin|America/Whitehorse|America/Winnipeg|America/Yakutat|America/Yellowknife|Antarctica/Casey|Antarctica/Davis|Antarctica/DumontDUrville|Antarctica/Macquarie|Antarctica/Mawson|Antarctica/McMurdo|Antarctica/Palmer|Antarctica/Rothera|Antarctica/South_Pole|Antarctica/Syowa|Antarctica/Troll|Antarctica/Vostok|Arctic/Longyearbyen|Asia/Aden|Asia/Almaty|Asia/Amman|Asia/Anadyr|Asia/Aqtau|Asia/Aqtobe|Asia/Ashgabat|Asia/Ashkhabad|Asia/Atyrau|Asia/Baghdad|Asia/Bahrain|Asia/Baku|Asia/Bangkok|Asia/Barnaul|Asia/Beirut|Asia/Bishkek|Asia/Brunei|Asia/Calcutta|Asia/Chita|Asia/Choibalsan|Asia/Chongqing|Asia/Chungking|Asia/Colombo|Asia/Dacca|Asia/Damascus|Asia/Dhaka|Asia/Dili|Asia/Dubai|Asia/Dushanbe|Asia/Famagusta|Asia/Gaza|Asia/Harbin|Asia/Hebron|Asia/Ho_Chi_Minh|Asia/Hong_Kong|Asia/Hovd|Asia/Irkutsk|Asia/Istanbul|Asia/Jakarta|Asia/Jayapura|Asia/Jerusalem|Asia/Kabul|Asia/Kamchatka|Asia/Karachi|Asia/Kashgar|Asia/Kathmandu|Asia/Katmandu|Asia/Khandyga|Asia/Kolkata|Asia/Krasnoyarsk|Asia/Kuala_Lumpur|Asia/Kuching|Asia/Kuwait|Asia/Macao|Asia/Macau|Asia/Magadan|Asia/Makassar|Asia/Manila|Asia/Muscat|Asia/Nicosia|Asia/Novokuznetsk|Asia/Novosibirsk|Asia/Omsk|Asia/Oral|Asia/Phnom_Penh|Asia/Pontianak|Asia/Pyongyang|Asia/Qatar|Asia/Qostanay|Asia/Qyzylorda|Asia/Rangoon|Asia/Riyadh|Asia/Saigon|Asia/Sakhalin|Asia/Samarkand|Asia/Seoul|Asia/Shanghai|Asia/Singapore|Asia/Srednekolymsk|Asia/Taipei|Asia/Tashkent|Asia/Tbilisi|Asia/Tehran|Asia/Tel_Aviv|Asia/Thimbu|Asia/Thimphu|Asia/Tokyo|Asia/Tomsk|Asia/Ujung_Pandang|Asia/Ulaanbaatar|Asia/Ulan_Bator|Asia/Urumqi|Asia/Ust-Nera|Asia/Vientiane|Asia/Vladivostok|Asia/Yakutsk|Asia/Yangon|Asia/Yekaterinburg|Asia/Yerevan|Atlantic/Azores|Atlantic/Bermuda|Atlantic/Canary|Atlantic/Cape_Verde|Atlantic/Faeroe|Atlantic/Faroe|Atlantic/Jan_Mayen|Atlantic/Madeira|Atlantic/Reykjavik|Atlantic/South_Georgia|Atlantic/St_Helena|Atlantic/Stanley|Australia/ACT|Australia/Adelaide|Australia/Brisbane|Australia/Broken_Hill|Australia/Canberra|Australia/Currie|Australia/Darwin|Australia/Eucla|Australia/Hobart|Australia/LHI|Australia/Lindeman|Australia/Lord_Howe|Australia/Melbourne|Australia/NSW|Australia/North|Australia/Perth|Australia/Queensland|Australia/South|Australia/Sydney|Australia/Tasmania|Australia/Victoria|Australia/West|Australia/Yancowinna|Brazil/Acre|Brazil/DeNoronha|Brazil/East|Brazil/West|CET|CST6CDT|Canada/Atlantic|Canada/Central|Canada/Eastern|Canada/Mountain|Canada/Newfoundland|Canada/Pacific|Canada/Saskatchewan|Canada/Yukon|Chile/Continental|Chile/EasterIsland|Cuba|EET|EST|EST5EDT|Egypt|Eire|Etc/GMT|Etc/GMT+0|Etc/GMT+1|Etc/GMT+10|Etc/GMT+11|Etc/GMT+12|Etc/GMT+2|Etc/GMT+3|Etc/GMT+4|Etc/GMT+5|Etc/GMT+6|Etc/GMT+7|Etc/GMT+8|Etc/GMT+9|Etc/GMT-0|Etc/GMT-1|Etc/GMT-10|Etc/GMT-11|Etc/GMT-12|Etc/GMT-13|Etc/GMT-14|Etc/GMT-2|Etc/GMT-3|Etc/GMT-4|Etc/GMT-5|Etc/GMT-6|Etc/GMT-7|Etc/GMT-8|Etc/GMT-9|Etc/GMT0|Etc/Greenwich|Etc/UCT|Etc/UTC|Etc/Universal|Etc/Zulu|Europe/Amsterdam|Europe/Andorra|Europe/Astrakhan|Europe/Athens|Europe/Belfast|Europe/Belgrade|Europe/Berlin|Europe/Bratislava|Europe/Brussels|Europe/Bucharest|Europe/Budapest|Europe/Busingen|Europe/Chisinau|Europe/Copenhagen|Europe/Dublin|Europe/Gibraltar|Europe/Guernsey|Europe/Helsinki|Europe/Isle_of_Man|Europe/Istanbul|Europe/Jersey|Europe/Kaliningrad|Europe/Kiev|Europe/Kirov|Europe/Kyiv|Europe/Lisbon|Europe/Ljubljana|Europe/London|Europe/Luxembourg|Europe/Madrid|Europe/Malta|Europe/Mariehamn|Europe/Minsk|Europe/Monaco|Europe/Moscow|Europe/Nicosia|Europe/Oslo|Europe/Paris|Europe/Podgorica|Europe/Prague|Europe/Riga|Europe/Rome|Europe/Samara|Europe/San_Marino|Europe/Sarajevo|Europe/Saratov|Europe/Simferopol|Europe/Skopje|Europe/Sofia|Europe/Stockholm|Europe/Tallinn|Europe/Tirane|Europe/Tiraspol|Europe/Ulyanovsk|Europe/Uzhgorod|Europe/Vaduz|Europe/Vatican|Europe/Vienna|Europe/Vilnius|Europe/Volgograd|Europe/Warsaw|Europe/Zagreb|Europe/Zaporozhye|Europe/Zurich|Factory|GB|GB-Eire|GMT|GMT+0|GMT-0|GMT0|Greenwich|HST|Hongkong|Iceland|Indian/Antananarivo|Indian/Chagos|Indian/Christmas|Indian/Cocos|Indian/Comoro|Indian/Kerguelen|Indian/Mahe|Indian/Maldives|Indian/Mauritius|Indian/Mayotte|Indian/Reunion|Iran|Israel|Jamaica|Japan|Kwajalein|Libya|MET|MST|MST7MDT|Mexico/BajaNorte|Mexico/BajaSur|Mexico/General|NZ|NZ-CHAT|Navajo|PRC|PST8PDT|Pacific/Apia|Pacific/Auckland|Pacific/Bougainville|Pacific/Chatham|Pacific/Chuuk|Pacific/Easter|Pacific/Efate|Pacific/Enderbury|Pacific/Fakaofo|Pacific/Fiji|Pacific/Funafuti|Pacific/Galapagos|Pacific/Gambier|Pacific/Guadalcanal|Pacific/Guam|Pacific/Honolulu|Pacific/Johnston|Pacific/Kanton|Pacific/Kiritimati|Pacific/Kosrae|Pacific/Kwajalein|Pacific/Majuro|Pacific/Marquesas|Pacific/Midway|Pacific/Nauru|Pacific/Niue|Pacific/Norfolk|Pacific/Noumea|Pacific/Pago_Pago|Pacific/Palau|Pacific/Pitcairn|Pacific/Pohnpei|Pacific/Ponape|Pacific/Port_Moresby|Pacific/Rarotonga|Pacific/Saipan|Pacific/Samoa|Pacific/Tahiti|Pacific/Tarawa|Pacific/Tongatapu|Pacific/Truk|Pacific/Wake|Pacific/Wallis|Pacific/Yap|Poland|Portugal|ROC|ROK|Singapore|SystemV/AST4|SystemV/AST4ADT|SystemV/CST6|SystemV/CST6CDT|SystemV/EST5|SystemV/EST5EDT|SystemV/HST10|SystemV/MST7|SystemV/MST7MDT|SystemV/PST8|SystemV/PST8PDT|SystemV/YST9|SystemV/YST9YDT|Turkey|UCT|US/Alaska|US/Aleutian|US/Arizona|US/Central|US/East-Indiana|US/Eastern|US/Hawaii|US/Indiana-Starke|US/Michigan|US/Mountain|US/Pacific|US/Pacific-New|US/Samoa|UTC|Universal|W-SU|WET|Zulu", "default": "UTC"}, {"type": "null"}], "title": "Timezone", "default": "UTC", "examples": ["America/New_York"]}}, "additionalProperties": false, "type": "object", "required": ["r<PERSON>le"], "title": "RRuleSchedule", "description": "RRule schedule, based on the iCalendar standard\n([RFC 5545](https://datatracker.ietf.org/doc/html/rfc5545)) as\nimplemented in `dateutils.rrule`.\n\nRRules are appropriate for any kind of calendar-date manipulation, including\nirregular intervals, repetition, exclusions, week day or day-of-month\nadjustments, and more.\n\nNote that as a calendar-oriented standard, `RRuleSchedules` are sensitive to\nto the initial timezone provided. A 9am daily schedule with a daylight saving\ntime-aware start date will maintain a local 9am time through DST boundaries;\na 9am daily schedule with a UTC start date will maintain a 9am UTC time.\n\nArgs:\n    rrule (str): a valid RRule string\n    timezone (str, optional): a valid timezone string"}, "ReceivedEvent": {"properties": {"occurred": {"type": "string", "format": "date-time", "title": "Occurred", "description": "When the event happened from the sender's perspective"}, "event": {"type": "string", "title": "Event", "description": "The name of the event that happened"}, "resource": {"allOf": [{"$ref": "#/components/schemas/Resource"}], "description": "The primary Resource this event concerns"}, "related": {"items": {"$ref": "#/components/schemas/RelatedResource"}, "type": "array", "title": "Related", "description": "A list of additional Resources involved in this event"}, "payload": {"type": "object", "title": "Payload", "description": "An open-ended set of data describing what happened"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "The client-provided identifier of this event"}, "follows": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Follows", "description": "The ID of an event that is known to have occurred prior to this one. If set, this may be used to establish a more precise ordering of causally-related events when they occur close enough together in time that the system may receive them out-of-order."}, "received": {"type": "string", "format": "date-time", "title": "Received", "description": "When the event was received by Prefect Cloud"}}, "type": "object", "required": ["occurred", "event", "resource", "id"], "title": "ReceivedEvent", "description": "The server-side view of an event that has happened to a Resource after it has\nbeen received by the server"}, "RelatedResource": {"additionalProperties": {"type": "string"}, "type": "object", "title": "RelatedResource", "description": "A Resource with a specific role in an Event"}, "Resource": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Resource", "description": "An observable business object of interest to the user"}, "ResourceSpecification": {"additionalProperties": {"anyOf": [{"type": "string"}, {"items": {"type": "string"}, "type": "array"}]}, "type": "object", "title": "ResourceSpecification"}, "ResumeAutomation": {"properties": {"type": {"type": "string", "enum": ["resume-automation"], "const": "resume-automation", "title": "Type", "default": "resume-automation"}, "source": {"type": "string", "enum": ["selected", "inferred"], "title": "Source", "description": "Whether this Action applies to a specific selected automation (given by `automation_id`), or to an automation that is inferred from the triggering event.  If the source is 'inferred', the `automation_id` may not be set.  If the source is 'selected', the `automation_id` must be set.", "default": "selected"}, "automation_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Automation Id", "description": "The identifier of the automation to act on"}}, "type": "object", "title": "ResumeAutomation", "description": "Resumes a Work Queue"}, "ResumeDeployment": {"properties": {"type": {"type": "string", "enum": ["resume-deployment"], "const": "resume-deployment", "title": "Type", "default": "resume-deployment"}, "source": {"type": "string", "enum": ["selected", "inferred"], "title": "Source", "description": "Whether this Action applies to a specific selected deployment (given by `deployment_id`), or to a deployment that is inferred from the triggering event.  If the source is 'inferred', the `deployment_id` may not be set.  If the source is 'selected', the `deployment_id` must be set.", "default": "selected"}, "deployment_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Deployment Id", "description": "The identifier of the deployment"}}, "type": "object", "title": "ResumeDeployment", "description": "Resumes the given Deployment"}, "ResumeWorkPool": {"properties": {"type": {"type": "string", "enum": ["resume-work-pool"], "const": "resume-work-pool", "title": "Type", "default": "resume-work-pool"}, "source": {"type": "string", "enum": ["selected", "inferred"], "title": "Source", "description": "Whether this Action applies to a specific selected work pool (given by `work_pool_id`), or to a work pool that is inferred from the triggering event.  If the source is 'inferred', the `work_pool_id` may not be set.  If the source is 'selected', the `work_pool_id` must be set.", "default": "selected"}, "work_pool_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Work Pool Id", "description": "The identifier of the work pool to pause"}}, "type": "object", "title": "ResumeWorkPool", "description": "Resumes a Work Pool"}, "ResumeWorkQueue": {"properties": {"type": {"type": "string", "enum": ["resume-work-queue"], "const": "resume-work-queue", "title": "Type", "default": "resume-work-queue"}, "source": {"type": "string", "enum": ["selected", "inferred"], "title": "Source", "description": "Whether this Action applies to a specific selected work queue (given by `work_queue_id`), or to a work queue that is inferred from the triggering event.  If the source is 'inferred', the `work_queue_id` may not be set.  If the source is 'selected', the `work_queue_id` must be set.", "default": "selected"}, "work_queue_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Work Queue Id", "description": "The identifier of the work queue to pause"}}, "type": "object", "title": "ResumeWorkQueue", "description": "Resumes a Work Queue"}, "RunDeployment": {"properties": {"type": {"type": "string", "enum": ["run-deployment"], "const": "run-deployment", "title": "Type", "default": "run-deployment"}, "source": {"type": "string", "enum": ["selected", "inferred"], "title": "Source", "description": "Whether this Action applies to a specific selected deployment (given by `deployment_id`), or to a deployment that is inferred from the triggering event.  If the source is 'inferred', the `deployment_id` may not be set.  If the source is 'selected', the `deployment_id` must be set.", "default": "selected"}, "deployment_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Deployment Id", "description": "The identifier of the deployment"}, "parameters": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Parameters", "description": "The parameters to pass to the deployment, or None to use the deployment's default parameters"}, "job_variables": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Job Variables", "description": "The job variables to pass to the created flow run, or None to use the deployment's default job variables"}}, "type": "object", "title": "RunDeployment", "description": "Runs the given deployment with the given parameters"}, "SavedSearch": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "name": {"type": "string", "title": "Name", "description": "The name of the saved search."}, "filters": {"items": {"$ref": "#/components/schemas/SavedSearchFilter"}, "type": "array", "title": "Filters", "description": "The filter set for the saved search."}}, "type": "object", "required": ["name"], "title": "SavedSearch", "description": "An ORM representation of saved search data. Represents a set of filter criteria."}, "SavedSearchCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "The name of the saved search."}, "filters": {"items": {"$ref": "#/components/schemas/SavedSearchFilter"}, "type": "array", "title": "Filters", "description": "The filter set for the saved search."}}, "additionalProperties": false, "type": "object", "required": ["name"], "title": "SavedSearchCreate", "description": "Data used by the Prefect REST API to create a saved search."}, "SavedSearchFilter": {"properties": {"object": {"type": "string", "title": "Object", "description": "The object over which to filter."}, "property": {"type": "string", "title": "Property", "description": "The property of the object on which to filter."}, "type": {"type": "string", "title": "Type", "description": "The type of the property."}, "operation": {"type": "string", "title": "Operation", "description": "The operator to apply to the object. For example, `equals`."}, "value": {"title": "Value", "description": "A JSON-compatible value for the filter."}}, "type": "object", "required": ["object", "property", "type", "operation", "value"], "title": "Saved<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "A filter for a saved search model. Intended for use by the Prefect UI."}, "SendNotification": {"properties": {"type": {"type": "string", "enum": ["send-notification"], "const": "send-notification", "title": "Type", "default": "send-notification"}, "block_document_id": {"type": "string", "format": "uuid", "title": "Block Document Id", "description": "The identifier of the notification block to use"}, "subject": {"type": "string", "title": "Subject", "default": "Prefect automated notification"}, "body": {"type": "string", "title": "Body", "description": "The text of the notification to send"}}, "type": "object", "required": ["block_document_id", "body"], "title": "SendNotification", "description": "Send a notification when an Automation is triggered"}, "SetStateStatus": {"type": "string", "enum": ["ACCEPT", "REJECT", "ABORT", "WAIT"], "title": "SetStateStatus", "description": "Enumerates return statuses for setting run states."}, "Settings": {"properties": {"PREFECT_HOME": {"type": "string", "format": "path", "title": "Prefect Home", "default": "~/.prefect"}, "PREFECT_DEBUG_MODE": {"type": "boolean", "title": "Prefect Debug Mode", "default": false}, "PREFECT_CLI_COLORS": {"type": "boolean", "title": "Prefect Cli Colors", "default": true}, "PREFECT_CLI_PROMPT": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Prefect Cli Prompt"}, "PREFECT_CLI_WRAP_LINES": {"type": "boolean", "title": "Prefect <PERSON><PERSON> Wrap Lines", "default": true}, "PREFECT_TEST_MODE": {"type": "boolean", "title": "Prefect Test Mode", "default": false}, "PREFECT_UNIT_TEST_MODE": {"type": "boolean", "title": "Prefect Unit Test Mode", "default": false}, "PREFECT_UNIT_TEST_LOOP_DEBUG": {"type": "boolean", "title": "Prefect Unit Test Loop Debug", "default": true}, "PREFECT_TEST_SETTING": {"title": "Prefect Test Setting"}, "PREFECT_API_TLS_INSECURE_SKIP_VERIFY": {"type": "boolean", "title": "Prefect Api Tls Insecure Skip Verify", "default": false}, "PREFECT_API_SSL_CERT_FILE": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Prefect Api Ssl Cert File"}, "PREFECT_API_URL": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Prefect Api Url"}, "PREFECT_SILENCE_API_URL_MISCONFIGURATION": {"type": "boolean", "title": "Prefect Silence Api Url Misconfiguration", "default": false}, "PREFECT_API_KEY": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Prefect Api Key"}, "PREFECT_API_ENABLE_HTTP2": {"type": "boolean", "title": "Prefect Api Enable Http2", "default": true}, "PREFECT_CLIENT_MAX_RETRIES": {"type": "integer", "title": "Prefect Client Max Retries", "default": 5}, "PREFECT_CLIENT_RETRY_JITTER_FACTOR": {"type": "number", "title": "Prefect Client Retry Jitter Factor", "default": 0.2}, "PREFECT_CLIENT_RETRY_EXTRA_CODES": {"type": "string", "title": "Prefect Client Retry Extra Codes", "default": ""}, "PREFECT_CLIENT_CSRF_SUPPORT_ENABLED": {"type": "boolean", "title": "Prefect Client Csrf Support Enabled", "default": true}, "PREFECT_CLOUD_API_URL": {"type": "string", "title": "Prefect Cloud Api Url", "default": "https://api.prefect.cloud/api"}, "PREFECT_UI_URL": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Prefect <PERSON><PERSON> Url"}, "PREFECT_CLOUD_UI_URL": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Prefect Cloud Ui Url"}, "PREFECT_API_REQUEST_TIMEOUT": {"type": "number", "title": "Prefect Api Request Timeout", "default": 60.0}, "PREFECT_EXPERIMENTAL_WARN": {"type": "boolean", "title": "Prefect Experimental Warn", "default": true}, "PREFECT_PROFILES_PATH": {"type": "string", "format": "path", "title": "Prefect Profiles Path", "default": "${PREFECT_HOME}/profiles.toml"}, "PREFECT_RESULTS_DEFAULT_SERIALIZER": {"type": "string", "title": "Prefect Results Default Serializer", "default": "pickle"}, "PREFECT_RESULTS_PERSIST_BY_DEFAULT": {"type": "boolean", "title": "Prefect Results Persist By Default", "default": false}, "PREFECT_TASKS_REFRESH_CACHE": {"type": "boolean", "title": "Prefect Tasks Refresh <PERSON><PERSON>", "default": false}, "PREFECT_TASK_DEFAULT_RETRIES": {"type": "integer", "title": "Prefect Task Default Retries", "default": 0}, "PREFECT_FLOW_DEFAULT_RETRIES": {"type": "integer", "title": "Prefect Flow Default Retries", "default": 0}, "PREFECT_FLOW_DEFAULT_RETRY_DELAY_SECONDS": {"anyOf": [{"type": "integer"}, {"type": "number"}], "title": "Prefect Flow Default Retry Delay Seconds", "default": 0}, "PREFECT_TASK_DEFAULT_RETRY_DELAY_SECONDS": {"anyOf": [{"type": "number"}, {"type": "integer"}, {"items": {"type": "number"}, "type": "array"}], "title": "Prefect Task Default Retry Delay Seconds", "default": 0}, "PREFECT_TASK_RUN_TAG_CONCURRENCY_SLOT_WAIT_SECONDS": {"type": "integer", "title": "Prefect Task Run Tag Concurrency Slot Wait Seconds", "default": 30}, "PREFECT_LOCAL_STORAGE_PATH": {"type": "string", "format": "path", "title": "Prefect Local Storage Path", "default": "${PREFECT_HOME}/storage"}, "PREFECT_MEMO_STORE_PATH": {"type": "string", "format": "path", "title": "Prefect Memo Store Path", "default": "${PREFECT_HOME}/memo_store.toml"}, "PREFECT_MEMOIZE_BLOCK_AUTO_REGISTRATION": {"type": "boolean", "title": "Prefect Memoize Block Auto Registration", "default": true}, "PREFECT_LOGGING_LEVEL": {"type": "string", "title": "Prefect Logging Level", "default": "INFO"}, "PREFECT_LOGGING_INTERNAL_LEVEL": {"type": "string", "title": "Prefect Logging Internal Level", "default": "ERROR"}, "PREFECT_LOGGING_SERVER_LEVEL": {"type": "string", "title": "Prefect Logging Server Level", "default": "WARNING"}, "PREFECT_LOGGING_SETTINGS_PATH": {"type": "string", "format": "path", "title": "Prefect Logging Settings Path", "default": "${PREFECT_HOME}/logging.yml"}, "PREFECT_LOGGING_EXTRA_LOGGERS": {"type": "string", "title": "Prefect Logging Extra Loggers", "default": ""}, "PREFECT_LOGGING_LOG_PRINTS": {"type": "boolean", "title": "Prefect Logging Log Prints", "default": false}, "PREFECT_LOGGING_TO_API_ENABLED": {"type": "boolean", "title": "Prefect Logging To Api Enabled", "default": true}, "PREFECT_LOGGING_TO_API_BATCH_INTERVAL": {"type": "number", "title": "Prefect Logging To Api Batch Interval", "default": 2.0}, "PREFECT_LOGGING_TO_API_BATCH_SIZE": {"type": "integer", "title": "Prefect Logging To Api Batch Size", "default": 4000000}, "PREFECT_LOGGING_TO_API_MAX_LOG_SIZE": {"type": "integer", "title": "Prefect Logging To Api Max Log Size", "default": 1000000}, "PREFECT_LOGGING_TO_API_WHEN_MISSING_FLOW": {"type": "string", "enum": ["warn", "error", "ignore"], "title": "Prefect Logging To Api When Missing Flow", "default": "warn"}, "PREFECT_SQLALCHEMY_POOL_SIZE": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Prefect Sqlalchemy Pool Size"}, "PREFECT_SQLALCHEMY_MAX_OVERFLOW": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Prefect Sqlalchemy Max Overflow"}, "PREFECT_LOGGING_COLORS": {"type": "boolean", "title": "Prefect Logging Colors", "default": true}, "PREFECT_LOGGING_MARKUP": {"type": "boolean", "title": "Prefect Logging Markup", "default": false}, "PREFECT_ASYNC_FETCH_STATE_RESULT": {"type": "boolean", "title": "Prefect Async Fetch State Result", "default": false}, "PREFECT_API_BLOCKS_REGISTER_ON_START": {"type": "boolean", "title": "Prefect Api Blocks Register On Start", "default": true}, "PREFECT_API_DATABASE_PASSWORD": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Prefect Api Database Password"}, "PREFECT_API_DATABASE_CONNECTION_URL": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Prefect Api Database Connection Url"}, "PREFECT_API_DATABASE_ECHO": {"type": "boolean", "title": "Prefect Api Database Echo", "default": false}, "PREFECT_API_DATABASE_MIGRATE_ON_START": {"type": "boolean", "title": "Prefect Api Database Migrate On Start", "default": true}, "PREFECT_API_DATABASE_TIMEOUT": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Prefect Api Database Timeout", "default": 10.0}, "PREFECT_API_DATABASE_CONNECTION_TIMEOUT": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Prefect Api Database Connection Timeout", "default": 5}, "PREFECT_API_SERVICES_SCHEDULER_LOOP_SECONDS": {"type": "number", "title": "Prefect Api Services Scheduler Loop Seconds", "default": 60}, "PREFECT_API_SERVICES_SCHEDULER_DEPLOYMENT_BATCH_SIZE": {"type": "integer", "title": "Prefect Api Services Scheduler Deployment Batch Size", "default": 100}, "PREFECT_API_SERVICES_SCHEDULER_MAX_RUNS": {"type": "integer", "title": "Prefect Api Services Scheduler Max Runs", "default": 100}, "PREFECT_API_SERVICES_SCHEDULER_MIN_RUNS": {"type": "integer", "title": "Prefect Api Services Scheduler Min Runs", "default": 3}, "PREFECT_API_SERVICES_SCHEDULER_MAX_SCHEDULED_TIME": {"type": "string", "format": "duration", "title": "Prefect Api Services Scheduler Max Scheduled Time", "default": "P100D"}, "PREFECT_API_SERVICES_SCHEDULER_MIN_SCHEDULED_TIME": {"type": "string", "format": "duration", "title": "Prefect Api Services Scheduler Min Scheduled Time", "default": "PT1H"}, "PREFECT_API_SERVICES_SCHEDULER_INSERT_BATCH_SIZE": {"type": "integer", "title": "Prefect Api Services Scheduler Insert Batch Size", "default": 500}, "PREFECT_API_SERVICES_LATE_RUNS_LOOP_SECONDS": {"type": "number", "title": "Prefect Api Services Late Runs Loop Seconds", "default": 5}, "PREFECT_API_SERVICES_LATE_RUNS_AFTER_SECONDS": {"type": "string", "format": "duration", "title": "Prefect Api Services Late Runs After Seconds", "default": "PT15S"}, "PREFECT_API_SERVICES_PAUSE_EXPIRATIONS_LOOP_SECONDS": {"type": "number", "title": "Prefect Api Services Pause Expirations Loop Seconds", "default": 5}, "PREFECT_API_SERVICES_CANCELLATION_CLEANUP_LOOP_SECONDS": {"type": "number", "title": "Prefect Api Services Cancellation Cleanup Loop Seconds", "default": 20}, "PREFECT_API_SERVICES_FOREMAN_ENABLED": {"type": "boolean", "title": "Prefect Api Services Foreman Enabled", "default": true}, "PREFECT_API_SERVICES_FOREMAN_LOOP_SECONDS": {"type": "number", "title": "Prefect Api Services Foreman Loop Seconds", "default": 15}, "PREFECT_API_SERVICES_FOREMAN_INACTIVITY_HEARTBEAT_MULTIPLE": {"type": "integer", "title": "Prefect Api Services Foreman Inactivity Heartbeat Multiple", "default": 3}, "PREFECT_API_SERVICES_FOREMAN_FALLBACK_HEARTBEAT_INTERVAL_SECONDS": {"type": "integer", "title": "Prefect Api Services Foreman Fallback Heartbeat Interval Seconds", "default": 30}, "PREFECT_API_SERVICES_FOREMAN_DEPLOYMENT_LAST_POLLED_TIMEOUT_SECONDS": {"type": "integer", "title": "Prefect Api Services Foreman Deployment Last Polled Timeout Seconds", "default": 60}, "PREFECT_API_SERVICES_FOREMAN_WORK_QUEUE_LAST_POLLED_TIMEOUT_SECONDS": {"type": "integer", "title": "Prefect Api Services Foreman Work Queue Last Polled Timeout Seconds", "default": 60}, "PREFECT_API_LOG_RETRYABLE_ERRORS": {"type": "boolean", "title": "Prefect Api Log Retryable Errors", "default": false}, "PREFECT_API_SERVICES_TASK_RUN_RECORDER_ENABLED": {"type": "boolean", "title": "Prefect Api Services Task Run Recorder Enabled", "default": true}, "PREFECT_API_DEFAULT_LIMIT": {"type": "integer", "title": "Prefect <PERSON><PERSON> Default Limit", "default": 200}, "PREFECT_SERVER_API_HOST": {"type": "string", "title": "Prefect Server Api Host", "default": "127.0.0.1"}, "PREFECT_SERVER_API_PORT": {"type": "integer", "title": "Prefect Server Api Port", "default": 4200}, "PREFECT_SERVER_API_KEEPALIVE_TIMEOUT": {"type": "integer", "title": "Prefect Server <PERSON><PERSON> Keepalive Timeout", "default": 5}, "PREFECT_SERVER_CSRF_PROTECTION_ENABLED": {"type": "boolean", "title": "Prefect Server Csrf Protection Enabled", "default": false}, "PREFECT_SERVER_CSRF_TOKEN_EXPIRATION": {"type": "string", "format": "duration", "title": "Prefect Server Csrf Token Expiration", "default": "PT1H"}, "PREFECT_UI_ENABLED": {"type": "boolean", "title": "Prefect Ui Enabled", "default": true}, "PREFECT_UI_API_URL": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Prefect Ui Api Url"}, "PREFECT_SERVER_ANALYTICS_ENABLED": {"type": "boolean", "title": "Prefect Server Analytics Enabled", "default": true}, "PREFECT_API_SERVICES_SCHEDULER_ENABLED": {"type": "boolean", "title": "Prefect Api Services Scheduler Enabled", "default": true}, "PREFECT_API_SERVICES_LATE_RUNS_ENABLED": {"type": "boolean", "title": "Prefect Api Services Late Runs Enabled", "default": true}, "PREFECT_API_SERVICES_FLOW_RUN_NOTIFICATIONS_ENABLED": {"type": "boolean", "title": "Prefect Api Services Flow Run Notifications Enabled", "default": true}, "PREFECT_API_SERVICES_PAUSE_EXPIRATIONS_ENABLED": {"type": "boolean", "title": "Prefect Api Services Pause Expirations Enabled", "default": true}, "PREFECT_API_TASK_CACHE_KEY_MAX_LENGTH": {"type": "integer", "title": "Prefect Api Task Cache Key Max Length", "default": 2000}, "PREFECT_API_SERVICES_CANCELLATION_CLEANUP_ENABLED": {"type": "boolean", "title": "Prefect Api Services Cancellation Cleanup Enabled", "default": true}, "PREFECT_API_MAX_FLOW_RUN_GRAPH_NODES": {"type": "integer", "title": "Prefect Api Max Flow Run Graph Nodes", "default": 10000}, "PREFECT_API_MAX_FLOW_RUN_GRAPH_ARTIFACTS": {"type": "integer", "title": "Prefect Api Max Flow Run Graph Artifacts", "default": 10000}, "PREFECT_EXPERIMENTAL_ENABLE_CLIENT_SIDE_TASK_ORCHESTRATION": {"type": "boolean", "title": "Prefect Experimental Enable Client Side Task Orchestration", "default": false}, "PREFECT_RUNNER_PROCESS_LIMIT": {"type": "integer", "title": "Prefect Runner Process Limit", "default": 5}, "PREFECT_RUNNER_POLL_FREQUENCY": {"type": "integer", "title": "Prefect Runner Poll Frequency", "default": 10}, "PREFECT_RUNNER_SERVER_MISSED_POLLS_TOLERANCE": {"type": "integer", "title": "Prefect Runner Server Missed Polls Tolerance", "default": 2}, "PREFECT_RUNNER_SERVER_HOST": {"type": "string", "title": "Prefect Runner Server Host", "default": "localhost"}, "PREFECT_RUNNER_SERVER_PORT": {"type": "integer", "title": "Prefect Runner Server Port", "default": 8080}, "PREFECT_RUNNER_SERVER_LOG_LEVEL": {"type": "string", "title": "Prefect Runner Server Log Level", "default": "error"}, "PREFECT_RUNNER_SERVER_ENABLE": {"type": "boolean", "title": "Prefect Runner Server Enable", "default": false}, "PREFECT_DEPLOYMENT_SCHEDULE_MAX_SCHEDULED_RUNS": {"type": "integer", "title": "Prefect Deployment Schedule Max Scheduled Runs", "default": 50}, "PREFECT_WORKER_HEARTBEAT_SECONDS": {"type": "number", "title": "Prefect Worker Heartbeat Seconds", "default": 30}, "PREFECT_WORKER_QUERY_SECONDS": {"type": "number", "title": "Prefect Worker Query Seconds", "default": 10}, "PREFECT_WORKER_PREFETCH_SECONDS": {"type": "number", "title": "Prefect Worker Prefetch Seconds", "default": 10}, "PREFECT_WORKER_WEBSERVER_HOST": {"type": "string", "title": "Prefect Worker Webserver Host", "default": "0.0.0.0"}, "PREFECT_WORKER_WEBSERVER_PORT": {"type": "integer", "title": "Prefect Worker Webserver Port", "default": 8080}, "PREFECT_TASK_SCHEDULING_DEFAULT_STORAGE_BLOCK": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Prefect Task Scheduling Default Storage Block"}, "PREFECT_TASK_SCHEDULING_DELETE_FAILED_SUBMISSIONS": {"type": "boolean", "title": "Prefect Task Scheduling Delete Failed Submissions", "default": true}, "PREFECT_TASK_SCHEDULING_MAX_SCHEDULED_QUEUE_SIZE": {"type": "integer", "title": "Prefect Task Scheduling Max Scheduled Queue Size", "default": 1000}, "PREFECT_TASK_SCHEDULING_MAX_RETRY_QUEUE_SIZE": {"type": "integer", "title": "Prefect Task Scheduling Max Retry Queue Size", "default": 100}, "PREFECT_TASK_SCHEDULING_PENDING_TASK_TIMEOUT": {"type": "string", "format": "duration", "title": "Prefect Task Scheduling Pending Task Timeout", "default": "PT0S"}, "PREFECT_EXPERIMENTAL_ENABLE_SCHEDULE_CONCURRENCY": {"type": "boolean", "title": "Prefect Experimental Enable Schedule Concurrency", "default": false}, "PREFECT_DEFAULT_RESULT_STORAGE_BLOCK": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Prefect Default Result Storage Block"}, "PREFECT_DEFAULT_WORK_POOL_NAME": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Prefect Default Work Pool Name"}, "PREFECT_DEFAULT_DOCKER_BUILD_NAMESPACE": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Prefect Default Docker Build Namespace"}, "PREFECT_UI_SERVE_BASE": {"type": "string", "title": "Prefect Ui Serve Base", "default": "/"}, "PREFECT_UI_STATIC_DIRECTORY": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Prefect Ui Static Directory"}, "PREFECT_MESSAGING_BROKER": {"type": "string", "title": "Prefect Messaging Broker", "default": "prefect.server.utilities.messaging.memory"}, "PREFECT_MESSAGING_CACHE": {"type": "string", "title": "Prefect Messaging Cache", "default": "prefect.server.utilities.messaging.memory"}, "PREFECT_EVENTS_MAXIMUM_LABELS_PER_RESOURCE": {"type": "integer", "title": "Prefect Events Maximum Labels Per Resource", "default": 500}, "PREFECT_EVENTS_MAXIMUM_RELATED_RESOURCES": {"type": "integer", "title": "Prefect Events Maximum Related Resources", "default": 500}, "PREFECT_EVENTS_MAXIMUM_SIZE_BYTES": {"type": "integer", "title": "Prefect Events Maximum Size Bytes", "default": 1500000}, "PREFECT_API_SERVICES_TRIGGERS_ENABLED": {"type": "boolean", "title": "Prefect Api Services Triggers Enabled", "default": true}, "PREFECT_EVENTS_EXPIRED_BUCKET_BUFFER": {"type": "string", "format": "duration", "title": "Prefect Events Expired <PERSON><PERSON>er", "default": "PT1M"}, "PREFECT_EVENTS_PROACTIVE_GRANULARITY": {"type": "string", "format": "duration", "title": "Prefect Events Proactive Granularity", "default": "PT5S"}, "PREFECT_API_SERVICES_EVENT_PERSISTER_ENABLED": {"type": "boolean", "title": "Prefect Api Services Event Persister Enabled", "default": true}, "PREFECT_API_SERVICES_EVENT_PERSISTER_BATCH_SIZE": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Prefect Api Services Event Persister Batch Size", "default": 20}, "PREFECT_API_SERVICES_EVENT_PERSISTER_FLUSH_INTERVAL": {"type": "number", "exclusiveMinimum": 0.0, "title": "Prefect Api Services Event Persister Flush Interval", "default": 5}, "PREFECT_EVENTS_RETENTION_PERIOD": {"type": "string", "format": "duration", "title": "Prefect Events Retention Period", "default": "P7D"}, "PREFECT_API_EVENTS_STREAM_OUT_ENABLED": {"type": "boolean", "title": "Prefect Api Events Stream Out Enabled", "default": true}, "PREFECT_API_EVENTS_RELATED_RESOURCE_CACHE_TTL": {"type": "string", "format": "duration", "title": "Prefect Api Events Related Resource Cache Ttl", "default": "PT5M"}, "PREFECT_EVENTS_MAXIMUM_WEBSOCKET_BACKFILL": {"type": "string", "format": "duration", "title": "Prefect Events Maximum Websocket Backfill", "default": "PT15M"}, "PREFECT_EVENTS_WEBSOCKET_BACKFILL_PAGE_SIZE": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Prefect Events Websocket Backfill Page Size", "default": 250}}, "type": "object", "title": "Settings", "description": "Contains validated Prefect settings.\n\nSettings should be accessed using the relevant `Setting` object. For example:\n```python\nfrom prefect.settings import PREFECT_HOME\nPREFECT_HOME.value()\n```\n\nAccessing a setting attribute directly will ignore any `value_callback` mutations.\nThis is not recommended:\n```python\nfrom prefect.settings import Settings\nSettings().PREFECT_PROFILES_PATH  # PosixPath('${PREFECT_HOME}/profiles.toml')\n```"}, "SimpleFlowRun": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id", "description": "The flow run id."}, "state_type": {"allOf": [{"$ref": "#/components/schemas/StateType"}], "description": "The state type."}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp", "description": "The start time of the run, or the expected start time if it hasn't run yet."}, "duration": {"type": "number", "title": "Duration", "description": "The total run time of the run."}, "lateness": {"type": "number", "title": "Lateness", "description": "The delay between the expected and actual start time."}}, "type": "object", "required": ["id", "state_type", "timestamp", "duration", "lateness"], "title": "SimpleFlowRun"}, "SimpleNextFlowRun": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id", "description": "The flow run id."}, "flow_id": {"type": "string", "format": "uuid", "title": "Flow Id", "description": "The flow id."}, "name": {"type": "string", "title": "Name", "description": "The flow run name"}, "state_name": {"type": "string", "title": "State Name", "description": "The state name."}, "state_type": {"allOf": [{"$ref": "#/components/schemas/StateType"}], "description": "The state type."}, "next_scheduled_start_time": {"type": "string", "format": "date-time", "title": "Next Scheduled Start Time", "description": "The next scheduled start time"}}, "type": "object", "required": ["id", "flow_id", "name", "state_name", "state_type", "next_scheduled_start_time"], "title": "SimpleNextFlowRun"}, "State": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "type": {"$ref": "#/components/schemas/StateType"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message", "examples": ["Run started"]}, "data": {"anyOf": [{}, {"type": "null"}], "title": "Data", "description": "Data associated with the state, e.g. a result. Content must be storable as JSON."}, "state_details": {"$ref": "#/components/schemas/StateDetails"}}, "type": "object", "required": ["type"], "title": "State", "description": "Represents the state of a run."}, "StateAbortDetails": {"properties": {"type": {"type": "string", "enum": ["abort_details"], "const": "abort_details", "title": "Type", "description": "The type of state transition detail. Used to ensure pydantic does not coerce into a different type.", "default": "abort_details"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason", "description": "The reason why the state transition was aborted."}}, "type": "object", "title": "StateAbortDetails", "description": "Details associated with an ABORT state transition."}, "StateAcceptDetails": {"properties": {"type": {"type": "string", "enum": ["accept_details"], "const": "accept_details", "title": "Type", "description": "The type of state transition detail. Used to ensure pydantic does not coerce into a different type.", "default": "accept_details"}}, "type": "object", "title": "StateAcceptDetails", "description": "Details associated with an ACCEPT state transition."}, "StateCreate": {"properties": {"type": {"allOf": [{"$ref": "#/components/schemas/StateType"}], "description": "The type of the state to create"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "The name of the state to create"}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message", "description": "The message of the state to create"}, "data": {"anyOf": [{}, {"type": "null"}], "title": "Data", "description": "The data of the state to create"}, "state_details": {"allOf": [{"$ref": "#/components/schemas/StateDetails"}], "description": "The details of the state to create"}}, "additionalProperties": false, "type": "object", "required": ["type"], "title": "StateCreate", "description": "Data used by the Prefect REST API to create a new state."}, "StateDetails": {"properties": {"flow_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Flow Run Id"}, "task_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Task Run Id"}, "child_flow_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Child Flow Run Id"}, "scheduled_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Scheduled Time"}, "cache_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "cache_expiration": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Cache Expiration"}, "deferred": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Deferred", "default": false}, "untrackable_result": {"type": "boolean", "title": "Untrackable Result", "default": false}, "pause_timeout": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Pause Timeout"}, "pause_reschedule": {"type": "boolean", "title": "Pause Reschedule", "default": false}, "pause_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Pause Key"}, "run_input_keyset": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "title": "Run Input Keyset"}, "refresh_cache": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "retriable": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Retriable"}, "transition_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Transition Id"}, "task_parameters_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Task Parameters Id"}}, "type": "object", "title": "StateDetails"}, "StateRejectDetails": {"properties": {"type": {"type": "string", "enum": ["reject_details"], "const": "reject_details", "title": "Type", "description": "The type of state transition detail. Used to ensure pydantic does not coerce into a different type.", "default": "reject_details"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason", "description": "The reason why the state transition was rejected."}}, "type": "object", "title": "StateRejectDetails", "description": "Details associated with a REJECT state transition."}, "StateType": {"type": "string", "enum": ["SCHEDULED", "PENDING", "RUNNING", "COMPLETED", "FAILED", "CANCELLED", "CRASHED", "PAUSED", "CANCELLING"], "title": "StateType", "description": "Enumeration of state types."}, "StateWaitDetails": {"properties": {"type": {"type": "string", "enum": ["wait_details"], "const": "wait_details", "title": "Type", "description": "The type of state transition detail. Used to ensure pydantic does not coerce into a different type.", "default": "wait_details"}, "delay_seconds": {"type": "integer", "title": "Delay Seconds", "description": "The length of time in seconds the client should wait before transitioning states."}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason", "description": "The reason why the state transition should wait."}}, "type": "object", "required": ["delay_seconds"], "title": "StateWaitDetails", "description": "Details associated with a WAIT state transition."}, "SuspendFlowRun": {"properties": {"type": {"type": "string", "enum": ["suspend-flow-run"], "const": "suspend-flow-run", "title": "Type", "default": "suspend-flow-run"}}, "type": "object", "title": "SuspendFlowRun", "description": "Suspends a flow run associated with the trigger"}, "TaskRun": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "name": {"type": "string", "title": "Name", "examples": ["my-task-run"]}, "flow_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Flow Run Id", "description": "The flow run id of the task run."}, "task_key": {"type": "string", "title": "Task Key", "description": "A unique identifier for the task being run."}, "dynamic_key": {"type": "string", "title": "Dynamic Key", "description": "A dynamic key used to differentiate between multiple runs of the same task within the same flow run."}, "cache_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "An optional cache key. If a COMPLETED state associated with this cache key is found, the cached COMPLETED state will be used instead of executing the task run."}, "cache_expiration": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Cache Expiration", "description": "Specifies when the cached state should expire."}, "task_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Task Version", "description": "The version of the task being run."}, "empirical_policy": {"$ref": "#/components/schemas/TaskRunPolicy"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "A list of tags for the task run.", "examples": [["tag-1", "tag-2"]]}, "state_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "State Id", "description": "The id of the current task run state."}, "task_inputs": {"additionalProperties": {"items": {"anyOf": [{"$ref": "#/components/schemas/TaskRunResult"}, {"$ref": "#/components/schemas/Parameter"}, {"$ref": "#/components/schemas/Constant"}]}, "type": "array"}, "type": "object", "title": "Task Inputs", "description": "Tracks the source of inputs to a task run. Used for internal bookkeeping."}, "state_type": {"anyOf": [{"$ref": "#/components/schemas/StateType"}, {"type": "null"}], "description": "The type of the current task run state."}, "state_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "State Name", "description": "The name of the current task run state."}, "run_count": {"type": "integer", "title": "Run Count", "description": "The number of times the task run has been executed.", "default": 0}, "flow_run_run_count": {"type": "integer", "title": "Flow Run Run Count", "description": "If the parent flow has retried, this indicates the flow retry this run is associated with.", "default": 0}, "expected_start_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expected Start Time", "description": "The task run's expected start time."}, "next_scheduled_start_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Next Scheduled Start Time", "description": "The next time the task run is scheduled to start."}, "start_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Time", "description": "The actual start time."}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time", "description": "The actual end time."}, "total_run_time": {"type": "number", "title": "Total Run Time", "description": "Total run time. If the task run was executed multiple times, the time of each run will be summed.", "default": 0.0}, "estimated_run_time": {"type": "number", "title": "Estimated Run Time", "description": "A real-time estimate of total run time.", "default": 0.0}, "estimated_start_time_delta": {"type": "number", "title": "Estimated Start Time Delta", "description": "The difference between actual and expected start time.", "default": 0.0}, "state": {"anyOf": [{"$ref": "#/components/schemas/State"}, {"type": "null"}], "description": "The current task run state."}}, "type": "object", "required": ["task_key", "dynamic_key"], "title": "TaskRun", "description": "An ORM representation of task run data."}, "TaskRunCount": {"type": "object", "title": "TaskRunCount"}, "TaskRunCreate": {"properties": {"id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Id", "description": "The ID to assign to the task run. If not provided, a random UUID will be generated."}, "state": {"anyOf": [{"$ref": "#/components/schemas/StateCreate"}, {"type": "null"}], "description": "The state of the task run to create"}, "name": {"type": "string", "title": "Name", "examples": ["my-task-run"]}, "flow_run_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Flow Run Id", "description": "The flow run id of the task run."}, "task_key": {"type": "string", "title": "Task Key", "description": "A unique identifier for the task being run."}, "dynamic_key": {"type": "string", "title": "Dynamic Key", "description": "A dynamic key used to differentiate between multiple runs of the same task within the same flow run."}, "cache_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "An optional cache key. If a COMPLETED state associated with this cache key is found, the cached COMPLETED state will be used instead of executing the task run."}, "cache_expiration": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Cache Expiration", "description": "Specifies when the cached state should expire."}, "task_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Task Version", "description": "The version of the task being run."}, "empirical_policy": {"$ref": "#/components/schemas/TaskRunPolicy"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "A list of tags for the task run.", "examples": [["tag-1", "tag-2"]]}, "task_inputs": {"additionalProperties": {"items": {"anyOf": [{"$ref": "#/components/schemas/TaskRunResult"}, {"$ref": "#/components/schemas/Parameter"}, {"$ref": "#/components/schemas/Constant"}]}, "type": "array"}, "type": "object", "title": "Task Inputs", "description": "The inputs to the task run."}}, "additionalProperties": false, "type": "object", "required": ["task_key", "dynamic_key"], "title": "TaskRunCreate", "description": "Data used by the Prefect REST API to create a task run"}, "TaskRunFilter": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "id": {"anyOf": [{"$ref": "#/components/schemas/TaskRunFilterId"}, {"type": "null"}], "description": "Filter criteria for `TaskRun.id`"}, "name": {"anyOf": [{"$ref": "#/components/schemas/TaskRunFilterName"}, {"type": "null"}], "description": "Filter criteria for `TaskRun.name`"}, "tags": {"anyOf": [{"$ref": "#/components/schemas/TaskRunFilterTags"}, {"type": "null"}], "description": "Filter criteria for `TaskRun.tags`"}, "state": {"anyOf": [{"$ref": "#/components/schemas/TaskRunFilterState"}, {"type": "null"}], "description": "Filter criteria for `TaskRun.state`"}, "start_time": {"anyOf": [{"$ref": "#/components/schemas/TaskRunFilterStartTime"}, {"type": "null"}], "description": "Filter criteria for `TaskRun.start_time`"}, "expected_start_time": {"anyOf": [{"$ref": "#/components/schemas/TaskRunFilterExpectedStartTime"}, {"type": "null"}], "description": "Filter criteria for `TaskRun.expected_start_time`"}, "subflow_runs": {"anyOf": [{"$ref": "#/components/schemas/TaskRunFilterSubFlowRuns"}, {"type": "null"}], "description": "Filter criteria for `TaskRun.subflow_run`"}, "flow_run_id": {"anyOf": [{"$ref": "#/components/schemas/TaskRunFilterFlowRunId"}, {"type": "null"}], "description": "Filter criteria for `TaskRun.flow_run_id`"}}, "additionalProperties": false, "type": "object", "title": "TaskRunFilter", "description": "Filter task runs. Only task runs matching all criteria will be returned"}, "TaskRunFilterExpectedStartTime": {"properties": {"before_": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Before ", "description": "Only include task runs expected to start at or before this time"}, "after_": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "After ", "description": "Only include task runs expected to start at or after this time"}}, "additionalProperties": false, "type": "object", "title": "TaskRunFilterExpectedStartTime", "description": "Filter by `TaskRun.expected_start_time`."}, "TaskRunFilterFlowRunId": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of task run flow run ids to include"}, "is_null_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON> ", "description": "Filter for task runs with None as their flow run id", "default": false}}, "additionalProperties": false, "type": "object", "title": "TaskRunFilterFlowRunId", "description": "Filter by `TaskRun.flow_run_id`."}, "TaskRunFilterId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of task run ids to include"}}, "additionalProperties": false, "type": "object", "title": "TaskRunFilterId", "description": "Filter by `TaskRun.id`."}, "TaskRunFilterName": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of task run names to include", "examples": [["my-task-run-1", "my-task-run-2"]]}, "like_": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Like ", "description": "A case-insensitive partial match. For example,  passing 'marvin' will match 'marvin', 'sad-<PERSON>', and 'marvin-robot'.", "examples": ["marvin"]}}, "additionalProperties": false, "type": "object", "title": "TaskRunFilterName", "description": "Filter by `TaskRun.name`."}, "TaskRunFilterStartTime": {"properties": {"before_": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Before ", "description": "Only include task runs starting at or before this time"}, "after_": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "After ", "description": "Only include task runs starting at or after this time"}, "is_null_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON> ", "description": "If true, only return task runs without a start time"}}, "additionalProperties": false, "type": "object", "title": "TaskRunFilterStartTime", "description": "Filter by `TaskRun.start_time`."}, "TaskRunFilterState": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "type": {"anyOf": [{"$ref": "#/components/schemas/TaskRunFilterStateType"}, {"type": "null"}], "description": "Filter criteria for `TaskRun.state_type`"}, "name": {"anyOf": [{"$ref": "#/components/schemas/TaskRunFilterStateName"}, {"type": "null"}], "description": "Filter criteria for `TaskRun.state_name`"}}, "additionalProperties": false, "type": "object", "title": "TaskRunFilterState", "description": "Filter by `TaskRun.type` and `TaskRun.name`."}, "TaskRunFilterStateName": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of task run state names to include"}}, "additionalProperties": false, "type": "object", "title": "TaskRunFilterStateName", "description": "Filter by `TaskRun.state_name`."}, "TaskRunFilterStateType": {"properties": {"any_": {"anyOf": [{"items": {"$ref": "#/components/schemas/StateType"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of task run state types to include"}}, "additionalProperties": false, "type": "object", "title": "TaskRunFilterStateType", "description": "Filter by `TaskRun.state_type`."}, "TaskRunFilterSubFlowRuns": {"properties": {"exists_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Exists ", "description": "If true, only include task runs that are subflow run parents; if false, exclude parent task runs"}}, "additionalProperties": false, "type": "object", "title": "TaskRunFilterSubFlowRuns", "description": "Filter by `TaskRun.subflow_run`."}, "TaskRunFilterTags": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "all_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "All ", "description": "A list of tags. Task runs will be returned only if their tags are a superset of the list", "examples": [["tag-1", "tag-2"]]}, "is_null_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON> ", "description": "If true, only include task runs without tags"}}, "additionalProperties": false, "type": "object", "title": "TaskRunFilterTags", "description": "Filter by `TaskRun.tags`."}, "TaskRunPolicy": {"properties": {"max_retries": {"type": "integer", "title": "Max Retries", "description": "The maximum number of retries. Field is not used. Please use `retries` instead.", "default": 0, "deprecated": true}, "retry_delay_seconds": {"type": "number", "title": "Retry Delay Seconds", "description": "The delay between retries. Field is not used. Please use `retry_delay` instead.", "default": 0, "deprecated": true}, "retries": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Retries", "description": "The number of retries."}, "retry_delay": {"anyOf": [{"type": "integer"}, {"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Retry Delay", "description": "A delay time or list of delay times between retries, in seconds."}, "retry_jitter_factor": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Retry Jitter Factor", "description": "Determines the amount a retry should jitter"}}, "type": "object", "title": "TaskRunPolicy", "description": "Defines of how a task run should retry."}, "TaskRunResult": {"properties": {"input_type": {"type": "string", "enum": ["task_run"], "const": "task_run", "title": "Input Type", "default": "task_run"}, "id": {"type": "string", "format": "uuid", "title": "Id"}}, "type": "object", "required": ["id"], "title": "TaskRunResult", "description": "Represents a task run result input to another task run."}, "TaskRunSort": {"type": "string", "enum": ["ID_DESC", "EXPECTED_START_TIME_ASC", "EXPECTED_START_TIME_DESC", "NAME_ASC", "NAME_DESC", "NEXT_SCHEDULED_START_TIME_ASC", "END_TIME_DESC"], "title": "TaskRunSort", "description": "Defines task run sorting options."}, "TaskRunUpdate": {"properties": {"name": {"type": "string", "title": "Name", "examples": ["my-task-run"]}}, "additionalProperties": false, "type": "object", "title": "TaskRunUpdate", "description": "Data used by the Prefect REST API to update a task run"}, "TaskWorkerFilter": {"properties": {"task_keys": {"items": {"type": "string"}, "type": "array", "title": "Task Keys"}}, "type": "object", "required": ["task_keys"], "title": "TaskWorkerFilter"}, "TaskWorkerResponse": {"properties": {"identifier": {"type": "string", "title": "Identifier"}, "task_keys": {"items": {"type": "string"}, "type": "array", "title": "Task Keys"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}}, "type": "object", "required": ["identifier", "task_keys", "timestamp"], "title": "TaskWorkerResponse"}, "TimeUnit": {"type": "string", "enum": ["week", "day", "hour", "minute", "second"], "title": "TimeUnit"}, "UpdatedBy": {"properties": {"id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Id", "description": "The id of the updater of the object."}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type", "description": "The type of the updater of the object."}, "display_value": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Display Value", "description": "The display value for the updater."}}, "type": "object", "title": "UpdatedBy"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "Variable": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "name": {"type": "string", "maxLength": 255, "title": "Name", "description": "The name of the variable", "examples": ["my-variable"]}, "value": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "boolean"}, {"type": "number"}, {"type": "object"}, {"items": {}, "type": "array"}, {"type": "null"}], "title": "Value", "description": "The value of the variable", "examples": ["my-value"]}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "A list of variable tags", "examples": [["tag-1", "tag-2"]]}}, "type": "object", "required": ["name", "value"], "title": "Variable"}, "VariableCreate": {"properties": {"name": {"type": "string", "maxLength": 255, "title": "Name", "description": "The name of the variable", "examples": ["my-variable"]}, "value": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "boolean"}, {"type": "number"}, {"type": "object"}, {"items": {}, "type": "array"}, {"type": "null"}], "title": "Value", "description": "The value of the variable", "examples": ["my-value"]}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "A list of variable tags", "examples": [["tag-1", "tag-2"]]}}, "additionalProperties": false, "type": "object", "required": ["name", "value"], "title": "VariableCreate", "description": "Data used by the Prefect REST API to create a Variable."}, "VariableFilter": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "id": {"anyOf": [{"$ref": "#/components/schemas/VariableFilterId"}, {"type": "null"}], "description": "Filter criteria for `Variable.id`"}, "name": {"anyOf": [{"$ref": "#/components/schemas/VariableFilterName"}, {"type": "null"}], "description": "Filter criteria for `Variable.name`"}, "tags": {"anyOf": [{"$ref": "#/components/schemas/VariableFilterTags"}, {"type": "null"}], "description": "Filter criteria for `Variable.tags`"}}, "additionalProperties": false, "type": "object", "title": "VariableFilter", "description": "Filter variables. Only variables matching all criteria will be returned"}, "VariableFilterId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of variable ids to include"}}, "additionalProperties": false, "type": "object", "title": "VariableFilterId", "description": "Filter by `Variable.id`."}, "VariableFilterName": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of variables names to include"}, "like_": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Like ", "description": "A string to match variable names against. This can include SQL wildcard characters like `%` and `_`.", "examples": ["my_variable_%"]}}, "additionalProperties": false, "type": "object", "title": "VariableFilterName", "description": "Filter by `Variable.name`."}, "VariableFilterTags": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "all_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "All ", "description": "A list of tags. Variables will be returned only if their tags are a superset of the list", "examples": [["tag-1", "tag-2"]]}, "is_null_": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON> ", "description": "If true, only include Variables without tags"}}, "additionalProperties": false, "type": "object", "title": "VariableFilterTags", "description": "Filter by `Variable.tags`."}, "VariableSort": {"type": "string", "enum": ["CREATED_DESC", "UPDATED_DESC", "NAME_DESC", "NAME_ASC"], "title": "VariableSort", "description": "Defines variables sorting options."}, "VariableUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Name", "description": "The name of the variable", "examples": ["my-variable"]}, "value": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "boolean"}, {"type": "number"}, {"type": "object"}, {"items": {}, "type": "array"}, {"type": "null"}], "title": "Value", "description": "The value of the variable", "examples": ["my-value"]}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags", "description": "A list of variable tags", "examples": [["tag-1", "tag-2"]]}}, "additionalProperties": false, "type": "object", "title": "VariableUpdate", "description": "Data used by the Prefect REST API to update a Variable."}, "WorkPool": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "name": {"type": "string", "pattern": "^[^/%&><]+$", "title": "Name", "description": "The name of the work pool."}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "A description of the work pool."}, "type": {"type": "string", "title": "Type", "description": "The work pool type."}, "base_job_template": {"type": "object", "title": "Base Job Template", "description": "The work pool's base job template."}, "is_paused": {"type": "boolean", "title": "Is Paused", "description": "Pausing the work pool stops the delivery of all work.", "default": false}, "concurrency_limit": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Concurrency Limit", "description": "A concurrency limit for the work pool."}, "status": {"anyOf": [{"$ref": "#/components/schemas/WorkPoolStatus"}, {"type": "null"}], "description": "The current status of the work pool."}, "default_queue_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "De<PERSON><PERSON>d", "description": "The id of the pool's default queue."}}, "type": "object", "required": ["name", "type"], "title": "WorkPool", "description": "An ORM representation of a work pool"}, "WorkPoolCreate": {"properties": {"name": {"type": "string", "pattern": "^[^/%&><]+$", "title": "Name", "description": "The name of the work pool."}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "The work pool description."}, "type": {"type": "string", "title": "Type", "description": "The work pool type.", "default": "prefect-agent"}, "base_job_template": {"type": "object", "title": "Base Job Template", "description": "The work pool's base job template."}, "is_paused": {"type": "boolean", "title": "Is Paused", "description": "Pausing the work pool stops the delivery of all work.", "default": false}, "concurrency_limit": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Concurrency Limit", "description": "A concurrency limit for the work pool."}}, "additionalProperties": false, "type": "object", "required": ["name"], "title": "WorkPoolCreate", "description": "Data used by the Prefect REST API to create a work pool."}, "WorkPoolFilter": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "id": {"anyOf": [{"$ref": "#/components/schemas/WorkPoolFilterId"}, {"type": "null"}], "description": "Filter criteria for `WorkPool.id`"}, "name": {"anyOf": [{"$ref": "#/components/schemas/WorkPoolFilterName"}, {"type": "null"}], "description": "Filter criteria for `WorkPool.name`"}, "type": {"anyOf": [{"$ref": "#/components/schemas/WorkPoolFilterType"}, {"type": "null"}], "description": "Filter criteria for `WorkPool.type`"}}, "additionalProperties": false, "type": "object", "title": "WorkPoolFilter", "description": "Filter work pools. Only work pools matching all criteria will be returned"}, "WorkPoolFilterId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of work pool ids to include"}}, "additionalProperties": false, "type": "object", "title": "WorkPoolFilterId", "description": "Filter by `WorkPool.id`."}, "WorkPoolFilterName": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of work pool names to include"}}, "additionalProperties": false, "type": "object", "title": "WorkPoolFilterName", "description": "Filter by `WorkPool.name`."}, "WorkPoolFilterType": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of work pool types to include"}}, "additionalProperties": false, "type": "object", "title": "WorkPoolFilterType", "description": "Filter by `WorkPool.type`."}, "WorkPoolStatus": {"type": "string", "enum": ["READY", "NOT_READY", "PAUSED"], "title": "WorkPoolStatus", "description": "Enumeration of work pool statuses."}, "WorkPoolUpdate": {"properties": {"description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "is_paused": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Paused"}, "base_job_template": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Base Job Template"}, "concurrency_limit": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Concurrency Limit"}}, "additionalProperties": false, "type": "object", "title": "WorkPoolUpdate", "description": "Data used by the Prefect REST API to update a work pool."}, "WorkQueue": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "name": {"type": "string", "pattern": "^[^/%&><]+$", "title": "Name", "description": "The name of the work queue."}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "An optional description for the work queue.", "default": ""}, "is_paused": {"type": "boolean", "title": "Is Paused", "description": "Whether or not the work queue is paused.", "default": false}, "concurrency_limit": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Concurrency Limit", "description": "An optional concurrency limit for the work queue."}, "priority": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Priority", "description": "The queue's priority. Lower values are higher priority (1 is the highest).", "default": 1}, "work_pool_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Work Pool Id", "description": "The work pool with which the queue is associated."}, "filter": {"anyOf": [{"$ref": "#/components/schemas/QueueFilter"}, {"type": "null"}], "description": "DEPRECATED: Filter criteria for the work queue.", "deprecated": true}, "last_polled": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Polled", "description": "The last time an agent polled this queue for work."}}, "type": "object", "required": ["name"], "title": "WorkQueue", "description": "An ORM representation of a work queue"}, "WorkQueueCreate": {"properties": {"name": {"type": "string", "pattern": "^[^/%&><]+$", "title": "Name", "description": "The name of the work queue."}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "An optional description for the work queue.", "default": ""}, "is_paused": {"type": "boolean", "title": "Is Paused", "description": "Whether or not the work queue is paused.", "default": false}, "concurrency_limit": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Concurrency Limit", "description": "The work queue's concurrency limit."}, "priority": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Priority", "description": "The queue's priority. Lower values are higher priority (1 is the highest)."}, "filter": {"anyOf": [{"$ref": "#/components/schemas/QueueFilter"}, {"type": "null"}], "description": "DEPRECATED: Filter criteria for the work queue.", "deprecated": true}}, "additionalProperties": false, "type": "object", "required": ["name"], "title": "WorkQueueCreate", "description": "Data used by the Prefect REST API to create a work queue."}, "WorkQueueFilter": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "id": {"anyOf": [{"$ref": "#/components/schemas/WorkQueueFilterId"}, {"type": "null"}], "description": "Filter criteria for `WorkQueue.id`"}, "name": {"anyOf": [{"$ref": "#/components/schemas/WorkQueueFilterName"}, {"type": "null"}], "description": "Filter criteria for `WorkQueue.name`"}}, "additionalProperties": false, "type": "object", "title": "Work<PERSON>ueue<PERSON>ilter", "description": "Filter work queues. Only work queues matching all criteria will be\nreturned"}, "WorkQueueFilterId": {"properties": {"any_": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of work queue ids to include"}}, "additionalProperties": false, "type": "object", "title": "WorkQueueFilterId", "description": "Filter by `WorkQueue.id`."}, "WorkQueueFilterName": {"properties": {"any_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of work queue names to include", "examples": [["wq-1", "wq-2"]]}, "startswith_": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Startswith ", "description": "A list of case-insensitive starts-with matches. For example,  passing 'marvin' will match 'marvin', and 'Marvin-robot', but not 'sad-marvin'.", "examples": [["marvin", "<PERSON>-<PERSON>"]]}}, "additionalProperties": false, "type": "object", "title": "WorkQueueFilterName", "description": "Filter by `WorkQueue.name`."}, "WorkQueueHealthPolicy": {"properties": {"maximum_late_runs": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Maximum Late Runs", "description": "The maximum number of late runs in the work queue before it is deemed unhealthy. Defaults to `0`.", "default": 0}, "maximum_seconds_since_last_polled": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Maximum Seconds Since Last Polled", "description": "The maximum number of time in seconds elapsed since work queue has been polled before it is deemed unhealthy. Defaults to `60`.", "default": 60}}, "type": "object", "title": "WorkQueueHealthPolicy"}, "WorkQueueResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "name": {"type": "string", "pattern": "^[^/%&><]+$", "title": "Name", "description": "The name of the work queue."}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "An optional description for the work queue.", "default": ""}, "is_paused": {"type": "boolean", "title": "Is Paused", "description": "Whether or not the work queue is paused.", "default": false}, "concurrency_limit": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Concurrency Limit", "description": "An optional concurrency limit for the work queue."}, "priority": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Priority", "description": "The queue's priority. Lower values are higher priority (1 is the highest).", "default": 1}, "work_pool_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Work Pool Id", "description": "The work pool with which the queue is associated."}, "filter": {"anyOf": [{"$ref": "#/components/schemas/QueueFilter"}, {"type": "null"}], "description": "DEPRECATED: Filter criteria for the work queue.", "deprecated": true}, "last_polled": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Polled", "description": "The last time an agent polled this queue for work."}, "work_pool_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Work Pool Name", "description": "The name of the work pool the work pool resides within."}, "status": {"anyOf": [{"$ref": "#/components/schemas/WorkQueueStatus"}, {"type": "null"}], "description": "The queue status."}}, "type": "object", "required": ["name"], "title": "WorkQueueResponse"}, "WorkQueueStatus": {"type": "string", "enum": ["READY", "NOT_READY", "PAUSED"], "title": "WorkQueueStatus", "description": "Enumeration of work queue statuses."}, "WorkQueueStatusDetail": {"properties": {"healthy": {"type": "boolean", "title": "Healthy", "description": "Whether or not the work queue is healthy."}, "late_runs_count": {"type": "integer", "title": "Late Runs Count", "description": "The number of late flow runs in the work queue.", "default": 0}, "last_polled": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Polled", "description": "The last time an agent polled this queue for work."}, "health_check_policy": {"allOf": [{"$ref": "#/components/schemas/WorkQueueHealthPolicy"}], "description": "The policy used to determine whether or not the work queue is healthy."}}, "type": "object", "required": ["healthy", "health_check_policy"], "title": "WorkQueueStatusDetail"}, "WorkQueueUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "is_paused": {"type": "boolean", "title": "Is Paused", "description": "Whether or not the work queue is paused.", "default": false}, "concurrency_limit": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Concurrency Limit"}, "priority": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Priority"}, "last_polled": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Polled"}, "filter": {"anyOf": [{"$ref": "#/components/schemas/QueueFilter"}, {"type": "null"}], "description": "DEPRECATED: Filter criteria for the work queue.", "deprecated": true}}, "additionalProperties": false, "type": "object", "title": "WorkQueueUpdate", "description": "Data used by the Prefect REST API to update a work queue."}, "WorkerFilter": {"properties": {"operator": {"allOf": [{"$ref": "#/components/schemas/Operator"}], "description": "Operator for combining filter criteria. Defaults to 'and_'.", "default": "and_"}, "last_heartbeat_time": {"anyOf": [{"$ref": "#/components/schemas/WorkerFilterLastHeartbeatTime"}, {"type": "null"}], "description": "Filter criteria for `Worker.last_heartbeat_time`"}, "status": {"anyOf": [{"$ref": "#/components/schemas/WorkerFilterStatus"}, {"type": "null"}], "description": "Filter criteria for `Worker.status`"}}, "additionalProperties": false, "type": "object", "title": "<PERSON><PERSON><PERSON>er", "description": "Filter by `Worker.last_heartbeat_time`."}, "WorkerFilterLastHeartbeatTime": {"properties": {"before_": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Before ", "description": "Only include processes whose last heartbeat was at or before this time"}, "after_": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "After ", "description": "Only include processes whose last heartbeat was at or after this time"}}, "additionalProperties": false, "type": "object", "title": "WorkerFilterLastHeartbeatTime", "description": "Filter by `Worker.last_heartbeat_time`."}, "WorkerFilterStatus": {"properties": {"any_": {"anyOf": [{"items": {"$ref": "#/components/schemas/WorkerStatus"}, "type": "array"}, {"type": "null"}], "title": "Any ", "description": "A list of worker statuses to include"}, "not_any_": {"anyOf": [{"items": {"$ref": "#/components/schemas/WorkerStatus"}, "type": "array"}, {"type": "null"}], "title": "Not Any ", "description": "A list of worker statuses to exclude"}}, "additionalProperties": false, "type": "object", "title": "WorkerFilterStatus", "description": "Filter by `Worker.status`."}, "WorkerFlowRunResponse": {"properties": {"work_pool_id": {"type": "string", "format": "uuid", "title": "Work Pool Id"}, "work_queue_id": {"type": "string", "format": "uuid", "title": "Work Queue Id"}, "flow_run": {"$ref": "#/components/schemas/FlowRun"}}, "type": "object", "required": ["work_pool_id", "work_queue_id", "flow_run"], "title": "WorkerFlowRunResponse"}, "WorkerResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "created": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created"}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated"}, "name": {"type": "string", "title": "Name", "description": "The name of the worker."}, "work_pool_id": {"type": "string", "format": "uuid", "title": "Work Pool Id", "description": "The work pool with which the queue is associated."}, "last_heartbeat_time": {"type": "string", "format": "date-time", "title": "Last Heartbeat Time", "description": "The last time the worker process sent a heartbeat."}, "heartbeat_interval_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Heartbeat Interval Seconds", "description": "The number of seconds to expect between heartbeats sent by the worker."}, "status": {"allOf": [{"$ref": "#/components/schemas/WorkerStatus"}], "description": "Current status of the worker.", "default": "OFFLINE"}}, "type": "object", "required": ["name", "work_pool_id"], "title": "WorkerResponse"}, "WorkerStatus": {"type": "string", "enum": ["ONLINE", "OFFLINE"], "title": "WorkerStatus", "description": "Enumeration of worker statuses."}, "prefect__events__schemas__automations__CompoundTrigger-Input": {"properties": {"type": {"type": "string", "enum": ["compound"], "const": "compound", "title": "Type", "default": "compound"}, "triggers": {"items": {"anyOf": [{"$ref": "#/components/schemas/prefect__events__schemas__automations__EventTrigger"}, {"$ref": "#/components/schemas/MetricTrigger-Input"}, {"$ref": "#/components/schemas/prefect__events__schemas__automations__CompoundTrigger-Input"}, {"$ref": "#/components/schemas/prefect__events__schemas__automations__SequenceTrigger-Input"}]}, "type": "array", "title": "Triggers"}, "within": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Within", "description": "The time period over which the events must occur.  For Reactive triggers, this may be as low as 0 seconds, but must be at least 10 seconds for Proactive triggers"}, "require": {"anyOf": [{"type": "integer"}, {"type": "string", "enum": ["any", "all"]}], "title": "Require"}}, "type": "object", "required": ["triggers", "require"], "title": "CompoundTrigger", "description": "A composite trigger that requires some number of triggers to have\nfired within the given time period"}, "prefect__events__schemas__automations__CompoundTrigger-Output": {"properties": {"type": {"type": "string", "enum": ["compound"], "const": "compound", "title": "Type", "default": "compound"}, "triggers": {"items": {"anyOf": [{"$ref": "#/components/schemas/prefect__events__schemas__automations__EventTrigger"}, {"$ref": "#/components/schemas/MetricTrigger-Output"}, {"$ref": "#/components/schemas/prefect__events__schemas__automations__CompoundTrigger-Output"}, {"$ref": "#/components/schemas/prefect__events__schemas__automations__SequenceTrigger-Output"}]}, "type": "array", "title": "Triggers"}, "within": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Within", "description": "The time period over which the events must occur.  For Reactive triggers, this may be as low as 0 seconds, but must be at least 10 seconds for Proactive triggers"}, "require": {"anyOf": [{"type": "integer"}, {"type": "string", "enum": ["any", "all"]}], "title": "Require"}}, "type": "object", "required": ["triggers", "require"], "title": "CompoundTrigger", "description": "A composite trigger that requires some number of triggers to have\nfired within the given time period"}, "prefect__events__schemas__automations__EventTrigger": {"properties": {"type": {"type": "string", "enum": ["event"], "const": "event", "title": "Type", "default": "event"}, "match": {"allOf": [{"$ref": "#/components/schemas/ResourceSpecification"}], "description": "Labels for resources which this trigger will match."}, "match_related": {"allOf": [{"$ref": "#/components/schemas/ResourceSpecification"}], "description": "Labels for related resources which this trigger will match."}, "after": {"items": {"type": "string"}, "type": "array", "uniqueItems": true, "title": "After", "description": "The event(s) which must first been seen to fire this trigger.  If empty, then fire this trigger immediately.  Events may include trailing wildcards, like `prefect.flow-run.*`"}, "expect": {"items": {"type": "string"}, "type": "array", "uniqueItems": true, "title": "Expect", "description": "The event(s) this trigger is expecting to see.  If empty, this trigger will match any event.  Events may include trailing wildcards, like `prefect.flow-run.*`"}, "for_each": {"items": {"type": "string"}, "type": "array", "uniqueItems": true, "title": "For Each", "description": "Evaluate the trigger separately for each distinct value of these labels on the resource.  By default, labels refer to the primary resource of the triggering event.  You may also refer to labels from related resources by specifying `related:<role>:<label>`.  This will use the value of that label for the first related resource in that role.  For example, `\"for_each\": [\"related:flow:prefect.resource.id\"]` would evaluate the trigger for each flow."}, "posture": {"type": "string", "enum": ["Reactive", "Proactive"], "title": "Posture", "description": "The posture of this trigger, either Reactive or Proactive.  Reactive triggers respond to the _presence_ of the expected events, while Proactive triggers respond to the _absence_ of those expected events.", "default": "Reactive"}, "threshold": {"type": "integer", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "The number of events required for this trigger to fire (for Reactive triggers), or the number of events expected (for Proactive triggers)", "default": 1}, "within": {"type": "number", "title": "Within", "description": "The time period over which the events must occur.  For Reactive triggers, this may be as low as 0 seconds, but must be at least 10 seconds for Proactive triggers", "default": 0.0}}, "type": "object", "title": "EventTrigger", "description": "A trigger that fires based on the presence or absence of events within a given\nperiod of time."}, "prefect__events__schemas__automations__SequenceTrigger-Input": {"properties": {"type": {"type": "string", "enum": ["sequence"], "const": "sequence", "title": "Type", "default": "sequence"}, "triggers": {"items": {"anyOf": [{"$ref": "#/components/schemas/prefect__events__schemas__automations__EventTrigger"}, {"$ref": "#/components/schemas/MetricTrigger-Input"}, {"$ref": "#/components/schemas/prefect__events__schemas__automations__CompoundTrigger-Input"}, {"$ref": "#/components/schemas/prefect__events__schemas__automations__SequenceTrigger-Input"}]}, "type": "array", "title": "Triggers"}, "within": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Within", "description": "The time period over which the events must occur.  For Reactive triggers, this may be as low as 0 seconds, but must be at least 10 seconds for Proactive triggers"}}, "type": "object", "required": ["triggers"], "title": "SequenceTrigger", "description": "A composite trigger that requires some number of triggers to have fired\nwithin the given time period in a specific order"}, "prefect__events__schemas__automations__SequenceTrigger-Output": {"properties": {"type": {"type": "string", "enum": ["sequence"], "const": "sequence", "title": "Type", "default": "sequence"}, "triggers": {"items": {"anyOf": [{"$ref": "#/components/schemas/prefect__events__schemas__automations__EventTrigger"}, {"$ref": "#/components/schemas/MetricTrigger-Output"}, {"$ref": "#/components/schemas/prefect__events__schemas__automations__CompoundTrigger-Output"}, {"$ref": "#/components/schemas/prefect__events__schemas__automations__SequenceTrigger-Output"}]}, "type": "array", "title": "Triggers"}, "within": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Within", "description": "The time period over which the events must occur.  For Reactive triggers, this may be as low as 0 seconds, but must be at least 10 seconds for Proactive triggers"}}, "type": "object", "required": ["triggers"], "title": "SequenceTrigger", "description": "A composite trigger that requires some number of triggers to have fired\nwithin the given time period in a specific order"}, "prefect__server__events__schemas__automations__CompoundTrigger-Input": {"properties": {"type": {"type": "string", "enum": ["compound"], "const": "compound", "title": "Type", "default": "compound"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "The unique ID of this trigger"}, "triggers": {"items": {"anyOf": [{"$ref": "#/components/schemas/prefect__events__schemas__automations__EventTrigger"}, {"$ref": "#/components/schemas/MetricTrigger-Input"}, {"$ref": "#/components/schemas/prefect__events__schemas__automations__CompoundTrigger-Input"}, {"$ref": "#/components/schemas/prefect__events__schemas__automations__SequenceTrigger-Input"}]}, "type": "array", "title": "Triggers"}, "within": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Within"}, "require": {"anyOf": [{"type": "integer"}, {"type": "string", "enum": ["any", "all"]}], "title": "Require"}}, "type": "object", "required": ["triggers", "within", "require"], "title": "CompoundTrigger", "description": "A composite trigger that requires some number of triggers to have\nfired within the given time period"}, "prefect__server__events__schemas__automations__CompoundTrigger-Output": {"properties": {"type": {"type": "string", "enum": ["compound"], "const": "compound", "title": "Type", "default": "compound"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "The unique ID of this trigger"}, "triggers": {"items": {"anyOf": [{"$ref": "#/components/schemas/prefect__events__schemas__automations__EventTrigger"}, {"$ref": "#/components/schemas/MetricTrigger-Output"}, {"$ref": "#/components/schemas/prefect__events__schemas__automations__CompoundTrigger-Output"}, {"$ref": "#/components/schemas/prefect__events__schemas__automations__SequenceTrigger-Output"}]}, "type": "array", "title": "Triggers"}, "within": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Within"}, "require": {"anyOf": [{"type": "integer"}, {"type": "string", "enum": ["any", "all"]}], "title": "Require"}}, "type": "object", "required": ["triggers", "within", "require"], "title": "CompoundTrigger", "description": "A composite trigger that requires some number of triggers to have\nfired within the given time period"}, "prefect__server__events__schemas__automations__EventTrigger": {"properties": {"type": {"type": "string", "enum": ["event"], "const": "event", "title": "Type", "default": "event"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "The unique ID of this trigger"}, "match": {"allOf": [{"$ref": "#/components/schemas/ResourceSpecification"}], "description": "Labels for resources which this trigger will match."}, "match_related": {"allOf": [{"$ref": "#/components/schemas/ResourceSpecification"}], "description": "Labels for related resources which this trigger will match."}, "after": {"items": {"type": "string"}, "type": "array", "uniqueItems": true, "title": "After", "description": "The event(s) which must first been seen to fire this trigger.  If empty, then fire this trigger immediately.  Events may include trailing wildcards, like `prefect.flow-run.*`"}, "expect": {"items": {"type": "string"}, "type": "array", "uniqueItems": true, "title": "Expect", "description": "The event(s) this trigger is expecting to see.  If empty, this trigger will match any event.  Events may include trailing wildcards, like `prefect.flow-run.*`"}, "for_each": {"items": {"type": "string"}, "type": "array", "uniqueItems": true, "title": "For Each", "description": "Evaluate the trigger separately for each distinct value of these labels on the resource.  By default, labels refer to the primary resource of the triggering event.  You may also refer to labels from related resources by specifying `related:<role>:<label>`.  This will use the value of that label for the first related resource in that role.  For example, `\"for_each\": [\"related:flow:prefect.resource.id\"]` would evaluate the trigger for each flow."}, "posture": {"type": "string", "enum": ["Reactive", "Proactive"], "title": "Posture", "description": "The posture of this trigger, either Reactive or Proactive.  Reactive triggers respond to the _presence_ of the expected events, while Proactive triggers respond to the _absence_ of those expected events."}, "threshold": {"type": "integer", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "The number of events required for this trigger to fire (for Reactive triggers), or the number of events expected (for Proactive triggers)", "default": 1}, "within": {"type": "number", "title": "Within", "description": "The time period over which the events must occur.  For Reactive triggers, this may be as low as 0 seconds, but must be at least 10 seconds for Proactive triggers", "default": 0.0}}, "type": "object", "required": ["posture"], "title": "EventTrigger", "description": "A trigger that fires based on the presence or absence of events within a given\nperiod of time."}, "prefect__server__events__schemas__automations__SequenceTrigger-Input": {"properties": {"type": {"type": "string", "enum": ["sequence"], "const": "sequence", "title": "Type", "default": "sequence"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "The unique ID of this trigger"}, "triggers": {"items": {"anyOf": [{"$ref": "#/components/schemas/prefect__events__schemas__automations__EventTrigger"}, {"$ref": "#/components/schemas/MetricTrigger-Input"}, {"$ref": "#/components/schemas/prefect__events__schemas__automations__CompoundTrigger-Input"}, {"$ref": "#/components/schemas/prefect__events__schemas__automations__SequenceTrigger-Input"}]}, "type": "array", "title": "Triggers"}, "within": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Within"}}, "type": "object", "required": ["triggers", "within"], "title": "SequenceTrigger", "description": "A composite trigger that requires some number of triggers to have fired\nwithin the given time period in a specific order"}, "prefect__server__events__schemas__automations__SequenceTrigger-Output": {"properties": {"type": {"type": "string", "enum": ["sequence"], "const": "sequence", "title": "Type", "default": "sequence"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "The unique ID of this trigger"}, "triggers": {"items": {"anyOf": [{"$ref": "#/components/schemas/prefect__events__schemas__automations__EventTrigger"}, {"$ref": "#/components/schemas/MetricTrigger-Output"}, {"$ref": "#/components/schemas/prefect__events__schemas__automations__CompoundTrigger-Output"}, {"$ref": "#/components/schemas/prefect__events__schemas__automations__SequenceTrigger-Output"}]}, "type": "array", "title": "Triggers"}, "within": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Within"}}, "type": "object", "required": ["triggers", "within"], "title": "SequenceTrigger", "description": "A composite trigger that requires some number of triggers to have fired\nwithin the given time period in a specific order"}}}}