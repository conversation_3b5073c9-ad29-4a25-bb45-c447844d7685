// import("./env.mjs");

/** @type {import("next").NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "lh3.googleusercontent.com",
      },
      {
        protocol: "https",
        hostname: "randomuser.me",
      },
    ],
  },
  serverExternalPackages: ["@prisma/client"],
  async rewrites() {
    return [
      {
        source: "/api/:path*",
        destination: "/api/:path*",
      },
    ];
  },
  // Fix for tunnelmole / proxied environments
  // This ensures NextAuth works correctly behind proxies
  async headers() {
    return [
      {
        source: "/:path*",
        headers: [
          { key: "X-Forwarded-Host", value: "true" }
        ],
      },
    ];
  },
  // Ensure Next.js trusts the proxy headers
  serverRuntimeConfig: {
    trustProxy: true,
  },
};

module.exports = nextConfig;
