{"$schema": "https://json.schemastore.org/eslintrc", "root": true, "extends": ["next/core-web-vitals", "prettier", "plugin:tailwindcss/recommended"], "plugins": ["tailwindcss"], "rules": {"@next/next/no-html-link-for-pages": "off", "react/jsx-key": "off", "tailwindcss/no-custom-classname": "off", "tailwindcss/classnames-order": "error", "tailwindcss/enforces-shorthand": "off", "quotes": ["error", "double"]}, "settings": {"tailwindcss": {"callees": ["cn"], "config": "tailwind.config.ts"}, "next": {"rootDir": true}}, "overrides": [{"files": ["*.ts", "*.tsx"], "parser": "@typescript-eslint/parser"}]}