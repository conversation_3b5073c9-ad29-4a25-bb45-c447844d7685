"use client";

import { useEffect, useState } from "react";

import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { NoData } from "@/components/ui/no-data";

interface TeacherComment {
  id: string;
  comment: string;
  teacher_name: string | null;
  date: Date;
  context: string | null;
}

interface TeacherCommentsProps {
  comments: TeacherComment[];
}

export function TeacherComments({ comments }: TeacherCommentsProps) {
  if (comments.length === 0) {
    return <NoData message="No teacher comments available" />;
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Teacher Comments</h2>
      <div className="grid gap-4">
        {comments.map((comment) => (
          <div key={comment.id} className="rounded-lg border p-4">
            <div className="mb-2 flex items-start justify-between">
              <div>
                <p className="font-medium">
                  {comment.teacher_name || "Unknown Teacher"}
                </p>
                <p className="text-sm text-muted-foreground">
                  {comment.context || "General Comment"}
                </p>
              </div>
              <span className="text-sm text-muted-foreground">
                {new Date(comment.date).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                })}
              </span>
            </div>
            <p className="text-sm">{comment.comment}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
