"use client";

import { useEffect, useState } from "react";
import { Copy, Mail, Phone } from "lucide-react";

import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

interface StudentDetails {
  name: string;
  gender: string;
  grade: string;
  section: string;
  assignedCounselor: string;
  phone: string;
  email: string;
  parent1Name: string;
  parent2Name: string;
}

// Mock data service
const getMockStudentDetails = async (
  studentId: string,
): Promise<StudentDetails> => {
  await new Promise((resolve) => setTimeout(resolve, 500));

  return {
    name: "Student name",
    gender: "Male",
    grade: "11",
    section: "A",
    assignedCounselor: "Counselor Name",
    phone: "***-***-8897",
    email: "*********@lakeside.edu",
    parent1Name: "Parent Name",
    parent2Name: "Parent Name",
  };
};

interface StudentDetailsContentProps {
  studentId: string;
}

export default function StudentDetailsContent({
  studentId,
}: StudentDetailsContentProps) {
  const [loading, setLoading] = useState(true);
  const [details, setDetails] = useState<StudentDetails | null>(null);

  useEffect(() => {
    const loadDetails = async () => {
      try {
        const data = await getMockStudentDetails(studentId);
        setDetails(data);
      } catch (error) {
        console.error("Failed to load student details:", error);
      } finally {
        setLoading(false);
      }
    };

    loadDetails();
  }, [studentId]);

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (!details) {
    return <div>Failed to load student details.</div>;
  }

  return (
    <div className="max-w-3xl space-y-6">
      <div className="grid grid-cols-2 gap-x-12 gap-y-6">
        <div className="space-y-2">
          <label className="text-sm font-medium">Name</label>
          <Input value={details.name} readOnly />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Phone</label>
          <div className="relative">
            <Input value={details.phone} readOnly />
            <div className="absolute right-3 top-1/2 flex -translate-y-1/2 gap-2">
              <button className="text-muted-foreground hover:text-primary">
                <Phone className="h-4 w-4" />
              </button>
              <button className="text-muted-foreground hover:text-primary">
                <Copy className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Gender</label>
          <Input value={details.gender} readOnly />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Email</label>
          <div className="relative">
            <Input value={details.email} readOnly />
            <div className="absolute right-3 top-1/2 flex -translate-y-1/2 gap-2">
              <button className="text-muted-foreground hover:text-primary">
                <Mail className="h-4 w-4" />
              </button>
              <button className="text-muted-foreground hover:text-primary">
                <Copy className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Grade</label>
          <Input value={details.grade} readOnly />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Parent 1 Name</label>
          <Input value={details.parent1Name} readOnly />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Section</label>
          <Input value={details.section} readOnly />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Parent 2 Name</label>
          <Input value={details.parent2Name} readOnly />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Assigned Counselor</label>
          <Input value={details.assignedCounselor} readOnly />
        </div>
      </div>
    </div>
  );
}
