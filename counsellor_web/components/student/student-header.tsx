"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { X } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Icons } from "@/components/shared/icons";
import { useUI } from "@/app/context/UIContext";
import { StudentWithWorkflows } from "@/common/types";

interface StudentHeaderProps {
  student: StudentWithWorkflows | any;
  onChatClick: () => void;
  className?: string;
}

export function StudentHeader({
  student,
  onChatClick,
  className,
}: StudentHeaderProps) {
  const router = useRouter();
  const { isChatOpen } = useUI();
  return (
    <div className={`${className} border-b`}>
      <div className="container py-4">
        <div className="flex w-full items-center justify-between">
          <div>
            <Button
              variant="ghost"
              className="mb-2"
              onClick={() => router.push("/")}
            >
              <Icons.chevronLeft className="mr-2 size-4" />
              Back to Students
            </Button>
            <h1 className="text-2xl font-bold">
              {student.first_name} {student.last_name}
            </h1>
            <div className="mt-2 space-y-2">
              <p className="text-sm text-muted-foreground">
                Email: {student.email}
              </p>

              <p className="text-sm text-muted-foreground">
                Student ID: {student.student_id}
              </p>
            </div>
          </div>
          <div className="flex items-center">
            <Button
              variant="highlight"
              className={"mx-2"}
              onClick={onChatClick}
            >
              {isChatOpen ? (
                <X className="size-5" />
              ) : (
                <>
                  <Icons.logo className="fill-current text-accent invert" />
                  Chat With Addie About {student.first_name}
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
