import {Input} from "@/components/ui/input";
import {Icons} from "@/components/shared/icons";
import {Markdown} from "@/components/ui/content/markdown";

interface Message {
  type: string;
  content: string;
  created_at: string | Date;
}

interface StudentChatHistoryProps {
  messages: Message[];
}

export function StudentChatHistory({messages}: StudentChatHistoryProps) {
  const formatDate = (dateString: string | Date) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const filteredMessages = messages.filter(
    (message) => message.type !== "tool_agent" && message.type !== "tool",
  );

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold">Conversation History</h2>

      <div className="grid grid-cols-12 gap-4">
        {/* Left Panel - Conversation Summary */}
        <div className="col-span-4 rounded-lg border">
          <div className="border-b p-4">
            <h3 className="font-semibold">Conversation Summary</h3>
          </div>
          <div className="p-4">
            <div className="space-y-4">
              {/* Stats Row */}
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <h4 className="text-sm font-medium">Total</h4>
                  <p className="text-2xl font-bold">
                    {filteredMessages.length}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Student</h4>
                  <p className="text-2xl font-bold">
                    {
                      filteredMessages.filter((m) => m.type === "Student")
                        .length
                    }
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Addie</h4>
                  <p className="text-2xl font-bold">
                    {filteredMessages.filter((m) => m.type === "Addie").length}
                  </p>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium">Summary of Conversation</h4>
                <p className="text-sm text-muted-foreground">
                  TO BE AI GENERATED SUMMARY
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Right Panel - Messages */}
        <div className="col-span-8 rounded-lg border">
          {/* Search Bar */}
          <div className="sticky top-0 border-b bg-background p-4">
            {/* <Input
              type="search"
              placeholder="Search messages"
              className="w-full"
              icon={<Icons.search className="h-4 w-4" />}
            /> */}
            <h3 className="font-semibold">Conversation</h3>
          </div>

          {/* Messages */}
          <div className="h-[680px] space-y-8 overflow-y-auto p-4">
            {filteredMessages.map((message, index) => (
              <div key={index}>
                <div
                  className={`flex gap-2 ${message.type === "Addie" ? "" : "justify-end"}`}
                >
                  {message.type === "Addie" && (
                    <Icons.logo className="h-8 w-8 rounded-full"/>
                  )}
                  <div
                    className={`flex max-w-[80%] flex-col gap-1 ${message.type === "Addie" ? "items-start" : "items-end"}`}
                  >
                    <div
                      className={`rounded-lg p-3 ${
                        message.type === "Addie"
                          ? "bg-muted"
                          : "bg-primary text-primary-foreground"
                      }`}
                    >
                      <Markdown content={message.content}/>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {formatDate(message.created_at)}
                    </span>
                  </div>
                  {message.type === "Student" && (
                    <Icons.user className="h-8 w-8 rounded-full"/>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
