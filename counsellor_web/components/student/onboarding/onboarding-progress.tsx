"use client";

import { Progress } from "@/components/ui/progress";

interface OnboardingProgressProps {
  completed: number;
  total: number;
}

export function OnboardingProgress({
  completed,
  total,
}: OnboardingProgressProps) {
  const percentage = Math.round((completed / total) * 100);

  return (
    <div className="space-y-4 rounded-lg border p-6">
      <h2 className="text-lg font-semibold">Onboarding Progress</h2>
      <div className="flex items-baseline gap-2">
        <span className="text-4xl font-bold">{completed}</span>
        <span className="text-muted-foreground">/{total}</span>
      </div>
      <Progress value={percentage} className="h-2" />
    </div>
  );
}
