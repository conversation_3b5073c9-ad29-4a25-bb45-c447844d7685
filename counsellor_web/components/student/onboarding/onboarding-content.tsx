"use client";

import { useEffect, useState } from "react";

import { LoadingSpinner } from "@/components/ui/loading-spinner";

import { OnboardingProgress } from "./onboarding-progress";
import { OnboardingQA } from "./onboarding-qa";
import { OnboardingSummary } from "./onboarding-summary";

interface OnboardingData {
  progress: {
    completed: number;
    total: number;
  };
  summary: {
    text: string;
    tags: Array<{
      id: string;
      label: string;
    }>;
  };
  qa: Array<{
    id: string;
    question: string;
    answer: string;
  }>;
}

interface OnboardingContentProps {
  data: any[];
}

export default function OnboardingContent({ data }: OnboardingContentProps) {
  // Transform workflow steps data into the expected format
  const onboardingData = {
    progress: {
      completed: data.filter((step) => step.completed).length,
      total: data.length,
    },
    summary: {
      text: "Student onboarding progress summary...",
      tags: [],
    },
    qa: [],
  };

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-2 gap-6">
        <OnboardingProgress
          completed={onboardingData.progress.completed}
          total={onboardingData.progress.total}
        />
        <OnboardingSummary
          summary={onboardingData.summary.text}
          tags={onboardingData.summary.tags}
        />
      </div>
      <OnboardingQA qnaireResponses={onboardingData.qa} />
    </div>
  );
}
