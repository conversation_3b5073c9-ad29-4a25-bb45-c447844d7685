"use client";

import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Icons } from "@/components/shared/icons";

interface Question {
  id: string;
  text: string;
  type: string;
}

interface QnaireResponse {
  id: string;
  question: Question;
  response: string;
  workflow_step_id: string;
  workflow_id: string;
}

interface OnboardingQAProps {
  qnaireResponses: QnaireResponse[];
}

export function OnboardingQA({ qnaireResponses = [] }: OnboardingQAProps) {
  if (!qnaireResponses) return null;
  // console.log("qnaireResponse==>", qnaireResponses);
  return (
    <div className="mt-4 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-bold">Questions and Answers</h2>

          {/*<a*/}
          {/*  href="#"*/}
          {/*  className="flex items-center text-sm text-primary hover:underline"*/}
          {/*>*/}
          {/*  Full Conversation*/}
          {/*  <Icons.arrowUpRight className="ml-1 h-4 w-4" />*/}
          {/*</a>*/}
        </div>
        <Input type="search" placeholder="Search" className="w-[300px]" />
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[400px]">Question</TableHead>
            <TableHead>Response</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {qnaireResponses.map((qa) => (
            <TableRow key={qa.id}>
              <TableCell>
                <strong>{qa.question.text}</strong>
              </TableCell>
              <TableCell>{qa.response || "N/A"}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
