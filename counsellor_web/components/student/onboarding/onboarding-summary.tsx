"use client";

interface Tag {
  id: string;
  label: string;
}

interface OnboardingSummaryProps {
  summary: string;
  tags: Tag[];
}

export function OnboardingSummary({ summary, tags }: OnboardingSummaryProps) {
  return (
    <div className="space-y-4 rounded-lg border p-6">
      <h2 className="text-lg font-semibold">Addie Summary</h2>
      <p className="text-muted-foreground">{summary}</p>
      <div className="flex flex-wrap gap-2">
        {tags.map((tag) => (
          <span
            key={tag.id}
            className="rounded-full bg-secondary px-3 py-1 text-sm text-secondary-foreground"
          >
            {tag.label}
          </span>
        ))}
      </div>
    </div>
  );
}
