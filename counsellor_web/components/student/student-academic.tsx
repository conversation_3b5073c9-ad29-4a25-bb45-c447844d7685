interface StudentAcademicProps {
  student: {
    academic_achievements: any[];
    // Add other required student data types
  };
}

export function StudentAcademic({ student }: StudentAcademicProps) {
  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold">Academic Progress</h2>
      <div className="grid gap-4">
        {student.academic_achievements.map((achievement) => (
          <div key={achievement.id} className="rounded-lg border p-4">
            <h3 className="font-semibold">{achievement.title}</h3>
            <p className="text-muted-foreground">{achievement.description}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
