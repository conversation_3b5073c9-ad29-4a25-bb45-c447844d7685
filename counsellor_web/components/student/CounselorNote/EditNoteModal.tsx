"use client";

import React, { useEffect, useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import TiptapEditor from "@/components/ui/TiptapEditor";
import { useNotes } from "@/app/context/NotesContext";

interface EditNoteModalProps {
  isOpen: boolean;
  onClose: () => void;
  noteId: string;
  initialContent?: string;
}

const EditNoteModal: React.FC<EditNoteModalProps> = ({
  isOpen,
  onClose,
  noteId,
  initialContent = "",
}) => {
  const [noteContent, setNoteContent] = useState(initialContent);

  // get the editing note handler from NotesContext
  const { handleEditNote } = useNotes();

  // handle submit edit note
  const handleSubmit = async () => {
    await handleEditNote(noteId, noteContent);
    onClose();
  };

  useEffect(() => {
    if (isOpen && noteContent !== initialContent) {
      setNoteContent(initialContent);
    }
  }, [isOpen, initialContent, noteContent]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        aria-describedby="editor-dialog-description"
        className="flex h-screen w-full max-w-4xl flex-col"
      >
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Edit Note</DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-auto">
          <TiptapEditor
            initialContent={noteContent}
            onChange={setNoteContent}
          />
        </div>

        <DialogFooter className="flex justify-end gap-3 p-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            className="bg-black text-white hover:bg-gray-800"
            onClick={handleSubmit}
          >
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditNoteModal;
