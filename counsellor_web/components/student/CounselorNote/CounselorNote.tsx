"use client";

import React, { use<PERSON><PERSON>back, useLayoutEffect, useRef, useState } from "react";
import { ChevronDown, ChevronUp, Pencil, Plus } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Markdown } from "@/components/ui/content/markdown";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Skeleton } from "@/components/ui/skeleton";
import DestructiveModal from "@/components/destructive-modal";
import { SkeletonSection } from "@/components/shared/section-skeleton";
import EditNoteModal from "@/components/student/CounselorNote/EditNoteModal";
import NoNotes from "@/components/student/CounselorNote/NoNotes";
import NoteEditor from "@/components/student/CounselorNote/NoteEditor";
import { useNotes } from "@/app/context/NotesContext";

// Define the Note type
interface Note {
  id: string;
  student_id: string;
  counselor_id: string;
  topic: string;
  content: string;
}

// Function to format the date
const formatDate = (isoString: string) => {
  const date = new Date(isoString);
  return date.toLocaleString("en-US", {
    month: "short",
    day: "numeric",
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  });
};

// Main Counselor Note Component

interface CounselorNoteProps {
  studentId: string;
  counselor;
}
function CounselorNote({ studentId, counselor }: CounselorNoteProps) {
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [editingNote, setEditingNote] = useState<Note | null>(null);
  const [expandedNotes, setExpandedNotes] = useState<Record<string, boolean>>(
    {},
  );
  const noteRefs = useRef<Record<string, HTMLDivElement | null>>({});

  const containerRef = useRef<HTMLDivElement | null>(null);
  const notesContainerRef = useRef<HTMLDivElement | null>(null);

  const { handleAddNote, notes, fetchNotes, isFetching, handleDeleteNote } =
    useNotes();

  // Add a function to handle the scroll reset completely
  const resetScrollPosition = useCallback(() => {
    // Only reset window scroll - don't try to scroll to the notes container
    window.scrollTo(0, 0);
    document.body.scrollTop = 0;
    document.documentElement.scrollTop = 0;

    // Reset all scrollable parent containers
    if (containerRef.current) {
      let parent = containerRef.current.parentElement;
      while (parent) {
        if (
          getComputedStyle(parent).overflow === "auto" ||
          getComputedStyle(parent).overflow === "scroll" ||
          getComputedStyle(parent).overflowY === "auto" ||
          getComputedStyle(parent).overflowY === "scroll"
        ) {
          parent.scrollTop = 0;
        }
        parent = parent.parentElement;
      }
    }
  }, []);

  // Combine all scroll management into a single useLayoutEffect
  useLayoutEffect(() => {
    // Fetch notes when student changes
    fetchNotes();

    // Reset expanded notes
    setExpandedNotes({});

    // Schedule multiple scroll attempts with increasing delays
    // This helps ensure scrolling works even if there are race conditions
    const scrollAttempts = [0, 50, 100, 300, 500, 1000];

    // Clear any existing timeouts
    const timeouts: NodeJS.Timeout[] = [];

    scrollAttempts.forEach((delay) => {
      const timeout = setTimeout(() => {
        resetScrollPosition();
      }, delay);
      timeouts.push(timeout);
    });

    // Cleanup function to clear all timeouts
    return () => {
      timeouts.forEach((timeout) => clearTimeout(timeout));
    };
  }, [studentId, resetScrollPosition, fetchNotes]);

  // Add a separate effect that runs when notes are loaded
  useLayoutEffect(() => {
    if (!isFetching) {
      // Try to scroll to the top when notes are loaded
      const timeout = setTimeout(() => {
        resetScrollPosition();
      }, 100);

      return () => clearTimeout(timeout);
    }
  }, [notes, isFetching, resetScrollPosition]);

  const handleEditClick = (note: Note) => {
    setEditingNote(note);
    setIsEditOpen(true);
  };

  const toggleNoteExpansion = (noteId: string) => {
    // Get the current element before toggling
    const noteElement = noteRefs.current[noteId];

    // Check if we're collapsing
    const isExpanding = !expandedNotes[noteId];

    // Update the state
    setExpandedNotes((prev) => ({
      ...prev,
      [noteId]: isExpanding,
    }));

    // If we're collapsing (not expanding), handle scroll position
    if (!isExpanding && noteElement) {
      // Use multiple approaches with different timing to ensure it works
      const ensureNoteVisible = () => {
        if (noteElement) {
          const rect = noteElement.getBoundingClientRect();

          // If note is not visible in viewport
          if (rect.top < 0 || rect.top > window.innerHeight - 100) {
            noteElement.scrollIntoView({
              behavior: "auto",
              block: "start",
            });

            // Add some padding at the top
            window.scrollBy(0, -50);
          }
        }
      };

      // Try multiple times with different timing
      setTimeout(ensureNoteVisible, 0);
      setTimeout(ensureNoteVisible, 50);
      setTimeout(ensureNoteVisible, 100);
    }
  };

  const isLongNote = (content: string): boolean => {
    const lines = content.split("\n");
    return content.length > 300 || lines.length > 5;
  };

  const renderExpandButton = (note: Note) => {
    if (!isLongNote(note.content)) return null;

    const isExpanded = expandedNotes[note.id];
    return (
      <Button
        variant="ghost"
        onClick={() => toggleNoteExpansion(note.id)}
        className={"mb-2 text-blue-600 hover:text-blue-800"}
      >
        {isExpanded ? (
          <>
            <ChevronUp className="mr-1 h-4 w-4" />
            Show less
          </>
        ) : (
          <>
            <ChevronDown className="mr-1 h-4 w-4" />
            Show more
          </>
        )}
      </Button>
    );
  };

  const renderNoteContent = (note: Note) => {
    if (!isLongNote(note.content) || expandedNotes[note.id]) {
      return (
        <>
          <Markdown content={note.content} />
          {renderExpandButton(note)}
        </>
      );
    }

    const previewContent = note.content.slice(0, 300) + "...";
    return (
      <>
        <Markdown content={previewContent} />
        {renderExpandButton(note)}
      </>
    );
  };

  // Add a manual scroll reset button for testing

  return (
    <div className="p-5" ref={containerRef}>
      {/* Header */}
      <div className="flex items-center justify-between py-2">
        <h3 className="text-lg font-semibold text-gray-900">Counselor Notes</h3>
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-2 border-gray-300 px-4 py-2"
            onClick={() => setIsEditorOpen(true)}
          >
            <Plus className="size-5 text-gray-600" />
            <span className="text-sm font-medium text-gray-700">Add Notes</span>
          </Button>
        </div>
      </div>

      {/* Notes Display */}
      <div
        className="rounded-lg border border-gray-300 bg-white p-6 shadow-sm"
        ref={notesContainerRef}
      >
        {isFetching ? (
          <div className="flex items-center justify-center">
            <LoadingSpinner />
          </div>
        ) : notes.length > 0 ? (
          <div className="space-y-4">
            {notes.map((note, index) => (
              <div
                key={note.id}
                className="rounded-lg bg-gray-100 p-4 shadow-sm"
                ref={(el) => {
                  noteRefs.current[note.id] = el;
                  return undefined;
                }}
              >
                <div className="flex items-center justify-between text-sm font-semibold text-gray-700">
                  Counselor notes | {formatDate(note.created_at)}
                  <div className="flex items-center">
                    <Button
                      variant="ghost"
                      onClick={() => handleEditClick(note)}
                    >
                      <Pencil className="size-4" />
                      Edit
                    </Button>

                    <DestructiveModal
                      title="Delete Note"
                      description="Are you sure you want to delete this note?"
                      handler={() => handleDeleteNote(note.id)}
                      className="bg-none"
                      variant="ghost"
                    />
                  </div>
                </div>
                {renderNoteContent(note)}
              </div>
            ))}
          </div>
        ) : (
          <NoNotes onAddNote={() => setIsEditorOpen(true)} />
        )}
      </div>

      {/* Modal for Adding Notes */}
      <NoteEditor
        isOpen={isEditorOpen}
        onClose={() => setIsEditorOpen(false)}
        onSave={handleAddNote}
      />
      {/*Modal for editing notes*/}
      {editingNote && (
        <EditNoteModal
          isOpen={isEditOpen}
          onClose={() => setIsEditOpen(false)}
          initialContent={editingNote.content}
          noteId={editingNote.id}
        />
      )}
    </div>
  );
}

export default CounselorNote;
