import React from "react";
import Image from "next/image";
import { NotepadText } from "lucide-react";

import { Button } from "@/components/ui/button";

interface NoNotesProps {
  onAddNote: () => void;
}
const NoNotes = ({ onAddNote }: NoNotesProps) => {
  return (
    <div className="flex flex-col items-center justify-center gap-4 p-8">
      <Image
        className="mb-4"
        alt="no-image"
        src="/images/no-notes.png"
        width={147}
        height={158}
      />
      <h4 className="text-lg font-semibold text-gray-900">No notes added</h4>
      <p className="max-w-md text-center text-sm text-gray-500">
        You can create your personal notes for each student. (Notes are not
        visible to students)
      </p>
      <Button variant="outline" onClick={onAddNote} className="gap-2">
        <NotepadText className="size-4" />
        <span>Add a note</span>
      </Button>
    </div>
  );
};

export default NoNotes;
