import React, { useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import TiptapEditor from "@/components/ui/TiptapEditor";

interface NoteEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (note: string) => void;
}

const NoteEditor = ({ isOpen, onClose, onSave }: NoteEditorProps) => {
  const [note, setNote] = useState("");

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        aria-describedby="editor-dialog-description"
        className="max-w-lg rounded-lg p-6"
      >
        <DialogHeader>
          <DialogTitle className="text-lg font-bold text-gray-900">
            Add your notes
          </DialogTitle>
        </DialogHeader>
        <div className="flex-1 overflow-auto">
          <TiptapEditor onChange={setNote} />
        </div>
        {/*<Textarea*/}
        {/*  placeholder="Type your notes here..."*/}
        {/*  value={note}*/}
        {/*  onChange={(e) => setNote(e.target.value)}*/}
        {/*  className="h-32 w-full resize-none border border-gray-300 p-3 text-gray-900 focus:ring-2 focus:ring-blue-500"*/}
        {/*/>*/}
        <DialogFooter className="flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            className="bg-black text-white hover:bg-gray-800"
            onClick={() => {
              if (note.trim()) {
                onSave(note);
                setNote("");
              }
              onClose();
            }}
          >
            Add note
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default NoteEditor;
