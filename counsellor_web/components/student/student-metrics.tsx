import { Progress } from "@/components/ui/progress";

interface StudentMetrics {
  academicProgress: number;
  onboardingProgress: number;
  lastInteraction?: string | null;
  completedWFSteps?: number;
  totalSteps?: number;
}

export function StudentMetrics({ metrics }: { metrics: StudentMetrics }) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="container grid grid-cols-3 gap-6 py-6">
      {/* Academic Progress */}
      {/*<div className="rounded-lg border p-4">*/}
      {/*  <h3 className="mb-2 text-sm font-medium text-muted-foreground">*/}
      {/*    Academic*/}
      {/*  </h3>*/}
      {/*  <p className="mb-4 text-2xl font-bold">*/}
      {/*    {metrics.academicProgress}/100 This is not real data*/}
      {/*  </p>*/}
      {/*  <Progress value={metrics.academicProgress} className="h-2" />*/}
      {/*</div>*/}

      {/* Onboarding Progress */}
      <div className="rounded-lg border p-4">
        <h3 className="mb-2 text-sm font-medium text-muted-foreground">
          Onboarding Progress
        </h3>
        <p className="mb-4 text-2xl font-bold">
          {metrics.completedWFSteps !== undefined
            ? `${metrics.completedWFSteps} / ${metrics.totalSteps}`
            : `${metrics.onboardingProgress}/100`}
        </p>
        <Progress value={metrics.onboardingProgress} className="h-2" />
      </div>

      {/* Last Interaction */}
      <div className="rounded-lg border p-4">
        <h3 className="mb-2 text-sm font-medium text-muted-foreground">
          Last Interaction with Addie
        </h3>
        <p className="text-2xl font-bold">
          {metrics.lastInteraction
            ? formatDate(metrics.lastInteraction)
            : "None"}
        </p>
      </div>
    </div>
  );
}
