import React, { useLayoutEffect, useRef } from "react";

import log from "@/common/logger";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { StudentConversationTab } from "@/components/Conversations/Student-ConversationTab/StudentConversationTab";
import { StudentMajorsTab } from "@/components/Majors/StudentMajorsTab";
import CounselorNote from "@/components/student/CounselorNote/CounselorNote";
import { OnboardingQA } from "@/components/student/onboarding/onboarding-qa";
import SummaryText from "@/components/student/shared/summary-text";
import { StudentChatHistory } from "@/components/student/student-chat-history";
import StudentOverview from "@/components/student/student-overview";
import { TeacherComments } from "@/components/student/teacher-comments";

interface Metrics {
  academicProgress: number;
  onboardingProgress: number;
  lastInteraction?: string | null;
  completedWFSteps?: number;
  totalSteps?: number;
}

interface StudentTabsProps {
  student: any;
  messages: any[];
  onboarding: any[];
  teacherComments: any[];
  qnaireResponses: any[];
  metrics: Metrics;
  teacherCommentsSummary: any[];
  studentChatSummary: any[];

  counselor;
}

export function StudentTabs({
  metrics,
  student,
  messages,
  onboarding,
  teacherComments,
  qnaireResponses,
  teacherCommentsSummary,
  studentChatSummary,

  counselor,
}: StudentTabsProps) {
  // Add refs for the container and to track active tab
  const containerRef = useRef<HTMLDivElement>(null);
  const activeTabRef = useRef<string>("overview");

  // Scroll to top when tab changes
  const handleTabChange = (value: string) => {
    activeTabRef.current = value;

    // Force scroll to top immediately when tab changes
    window.scrollTo(0, 0);
    document.body.scrollTop = 0;
    document.documentElement.scrollTop = 0;

    // Also scroll the container to top
    if (containerRef.current) {
      containerRef.current.scrollTop = 0;
    }
  };
  // console.log("Student ==>", student);
  return (
    <div className="container py-6" ref={containerRef}>
      <Tabs defaultValue="overview" onValueChange={handleTabChange}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          {/*<TabsTrigger value="onboarding">Onboarding</TabsTrigger>*/}
          <TabsTrigger value="comments">Teacher Comments</TabsTrigger>
          <TabsTrigger value="conversations">Conversations</TabsTrigger>
          {/*<TabsTrigger value="chat">Chat History</TabsTrigger>*/}
          {/*<TabsTrigger value="academic">Academic</TabsTrigger>*/}
          <TabsTrigger value="majors">Majors</TabsTrigger>
        </TabsList>

        {/* Add fixed height container with independent scroll area */}
        <div className="h-[calc(100vh-200px)] min-h-[600px] overflow-y-auto pr-4">
          <TabsContent value="overview">
            <StudentOverview metrics={metrics} />
            <CounselorNote studentId={student.id} counselor={counselor} />
          </TabsContent>
          {/*<TabsContent value="chat">*/}
          {/*  <StudentChatHistory messages={messages} />*/}
          {/*</TabsContent>*/}
          {/*<TabsContent value="academic">*/}
          {/*  <StudentAcademic student={student} />*/}
          {/*</TabsContent>*/}
          {/*<SummaryText*/}
          {/*  title="Summary"*/}
          {/*  summary={*/}
          {/*    studentChatSummary.length > 0 && studentChatSummary[0].content*/}
          {/*      ? studentChatSummary[0].content*/}
          {/*      : "No onboarding summary is available for this student at the moment."*/}
          {/*  }*/}
          {/*/>*/}

          {/*Hide onboarding for now*/}
          {/*<TabsContent value="onboarding">*/}
          {/*  <OnboardingQA qnaireResponses={qnaireResponses} />*/}
          {/*</TabsContent>*/}
          <TabsContent value="comments">
            <TeacherComments comments={teacherComments} />
            <SummaryText
              title="Summary"
              summary={
                teacherCommentsSummary.length > 0 &&
                teacherCommentsSummary[0].content
                  ? teacherCommentsSummary[0].content
                  : "It looks like this student has not received any teacher feedback yet. Stay tuned!"
              }
            />
          </TabsContent>
          <TabsContent value="conversations">
            <StudentConversationTab studentId={student.id} />
          </TabsContent>
          <TabsContent value="majors">
            <StudentMajorsTab student={student} />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
