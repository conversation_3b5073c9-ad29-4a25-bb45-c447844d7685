import React from "react";

import SummaryText from "@/components/student/shared/summary-text";
import { StudentMetrics } from "@/components/student/student-metrics";

const dummySummary =
  "2-3 pages summary of the overall student profile.Al contrario del\n" +
  "      pensamiento popular, el texto de Lorem Ipsum no es simplemente texto\n" +
  "      aleatorio. Tiene sus raices en una pieza cl´sica de la literatura del\n" +
  "      Latin, que data del año 45 antes de Cristo, haciendo que este adquiera mas\n" +
  "      de 2000 años de antiguedad. <PERSON>, un profesor de Latin de la\n" +
  "      Universidad de Hampden-Sydney en Virginia, encontró una de las palabras\n" +
  "      más oscuras de la lengua del latín, \"consecteur\", en un pasaje de Lorem\n" +
  "      Ipsum, y al seguir leyendo distintos textos del latín, descubrió la fuente\n" +
  "      indudable. Lorem Ipsum viene de las secciones 1.10.32 y 1.10.33 de \"de\n" +
  "      Finnibus Bonorum et Malorum\" (Los Extremos del Bien y El Mal) por Cicero,\n" +
  "      escrito en el año 45 antes de Cristo. Este libro es un tratado de teoría\n" +
  "      de éticas, muy popular durante el Renacimiento. La primera linea del Lorem\n" +
  "      Ipsum, \"Lorem ipsum dolor sit amet..\", viene de una linea en la sección\n" +
  "      1.10.32 El trozo de texto estándar de Lorem Ipsum usado desde el año 1500\n" +
  "      es reproducido debajo para aquellos interesados. Las secciones 1.10.32 y\n" +
  "      1.10.33 de \"de Finibus Bonorum et Malorum\" por Cicero son también\n" +
  "      reproducidas en su forma original exacta, acompañadas por versiones en\n" +
  "      Inglés de la traducción realizada en 1914 por H. Rackham. Al contrario del\n" +
  "      pensamiento popular, el texto de Lorem Ipsum no es simplemente texto\n" +
  "      aleatorio. Tiene sus raices en una pieza cl´sica de la literatura del\n" +
  "      Latin, que data del año 45 antes de Cristo, haciendo que este adquiera mas\n" +
  "      de 2000 años de antiguedad. Richard McClintock, un profesor de Latin de la\n" +
  "      Universidad de Hampden-Sydney en Virginia, encontró una de las palabras\n" +
  "      más oscuras de la lengua del latín, \"consecteur\", en un pasaje de Lorem\n" +
  "      Ipsum, y al seguir leyendo distintos textos del latín, descubrió la fuente\n" +
  "      indudable. Lorem Ipsum viene de las secciones 1.10.32 y 1.10.33 de \"de\n" +
  "      Finnibus Bonorum et Malorum\" (Los Extremos del Bien y El Mal) por Cicero,\n" +
  "      escrito en el año 45 antes de Cristo. Este libro es un tratado de teoría\n" +
  "      de éticas, muy popular durante el Renacimiento. La primera linea del Lorem\n" +
  "      Ipsum, \"Lorem ipsum dolor sit amet..\", viene de una linea en la sección\n" +
  "      1.10.32 El trozo de texto estándar de Lorem Ipsum usado desde el año 1500\n" +
  "      es reproducido debajo para aquellos interesados. Las secciones 1.10.32 y\n" +
  "      1.10.33 de \"de Finibus Bonorum et Malorum\" por Cicero son también\n" +
  "      reproducidas en su forma original exacta, acompañadas por versiones en\n" +
  "      Inglés de la traducción realizada en 1914 por H. Rackham.";
interface Metrics {
  academicProgress: number;
  onboardingProgress: number;
  lastInteraction?: string | null;
  completedWFSteps?: number;
  totalSteps?: number;
}
interface StudentSummaryProp {
  metrics: Metrics;
}
function StudentOverview({ metrics }: StudentSummaryProp) {
  return (
    <>
      <StudentMetrics metrics={metrics} />
      {/*<SummaryText summary={dummySummary} title="Student Summary" />*/}
    </>
  );
}

export default StudentOverview;
