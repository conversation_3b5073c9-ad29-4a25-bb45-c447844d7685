"use client";

import * as React from "react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { CheckCircle, XCircle } from "lucide-react";

import urls from "@/common/config/urls.json";
import {
  calculateProgress,
  DashboardStudent,
  isValidWorkflowStep,
  StudentWithWorkflows,
} from "@/common/types";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Icons } from "@/components/shared/icons";
import { StudentDetailPageSkeleton } from "@/components/StudentDetailPageSkeleton/StudentDetailPageSkeleton";

interface StudentTableProps {
  students: DashboardStudent[];
}

type SortField =
  | "name"
  | "studentId"
  | "academic"
  | "onboarding"
  | "lastUpdate"
  | "grade";
type SortDirection = "asc" | "desc";

const ITEMS_PER_PAGE = 15;

export function StudentTableCounsellorDashboard({
  students,
  loadingOnboardingData = false,
}: StudentTableProps & { loadingOnboardingData?: boolean }) {
  // console.log("students==>", students);
  const router = useRouter();
  const [sortField, setSortField] = React.useState<SortField>("onboarding");
  const [sortDirection, setSortDirection] =
    React.useState<SortDirection>("desc");
  const [currentPage, setCurrentPage] = React.useState(1);
  const [isLoadingStudent, setIsLoadingStudent] = useState(false);
  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const handleClickStudentRow = (studentId: string) => {
    setIsLoadingStudent(true);
    router.push(urls.routes.studentProfile.replace("[id]", studentId));
  };

  const sortedStudents = React.useMemo(() => {
    return [...students].sort((a, b) => {
      const multiplier = sortDirection === "asc" ? 1 : -1;

      switch (sortField) {
        case "name":
          const nameA = `${a.first_name} ${a.last_name}`;
          const nameB = `${b.first_name} ${b.last_name}`;
          return nameA.localeCompare(nameB) * multiplier;

        case "studentId":
          return ((a.id || "") > (b.id || "") ? 1 : -1) * multiplier;

        case "grade":
          return (a.grade - b.grade) * multiplier;

        case "lastUpdate":
          return (
            (new Date(a.updated_at).getTime() -
              new Date(b.updated_at).getTime()) *
            multiplier
          );

        case "onboarding":
          // Sort by onboarded status (phone_number + isProfileComplete)
          const isOnboardedA = !!(a.phone_number && a.isProfileComplete);
          const isOnboardedB = !!(b.phone_number && b.isProfileComplete);

          // Convert boolean to number for comparison (true = 1, false = 0)
          return (Number(isOnboardedA) - Number(isOnboardedB)) * multiplier;

        default:
          return 0;
      }
    });
  }, [students, sortField, sortDirection]);

  // Calculate pagination
  const totalPages = Math.ceil(sortedStudents.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const paginatedStudents = sortedStudents.slice(
    startIndex,
    startIndex + ITEMS_PER_PAGE,
  );

  const SortIcon = ({ field }: { field: SortField }) => {
    if (field !== sortField)
      return <Icons.chevronDown className="ml-2 inline-block h-4 w-4" />;
    return sortDirection === "asc" ? (
      <Icons.chevronUp className="ml-2 inline-block h-4 w-4" />
    ) : (
      <Icons.chevronDown className="ml-2 inline-block h-4 w-4" />
    );
  };

  // added a section skeleton when the student detail page is loading
  if (isLoadingStudent) return <StudentDetailPageSkeleton />;

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead
                className="w-[200px] cursor-pointer"
                onClick={() => handleSort("name")}
              >
                Student
                <SortIcon field="name" />
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSort("studentId")}
              >
                Student ID
                <SortIcon field="studentId" />
              </TableHead>
              {/*<TableHead>Academic</TableHead>*/}
              <TableHead
                className="cursor-pointer text-center"
                onClick={() => handleSort("onboarding")}
              >
                <div className="flex items-center justify-center">
                  Onboarded
                  <SortIcon field="onboarding" />
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSort("lastUpdate")}
              >
                Last Ledger Update
                <SortIcon field="lastUpdate" />
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSort("grade")}
              >
                Grade
                <SortIcon field="grade" />
              </TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedStudents.map((student) => {
              return (
                <TableRow
                  key={student.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleClickStudentRow(student.id)}
                >
                  <TableCell className="font-medium">
                    {student.first_name || student.last_name
                      ? `${student.first_name || ""} ${student.last_name || ""}`.trim()
                      : `Student ${student.student_id || student.id}`}
                  </TableCell>
                  <TableCell>{student.id || "N/A"}</TableCell>
                  {/*<TableCell>HOW DO WE CALCULATE THIS?</TableCell>*/}
                  <TableCell className="text-center">
                    {loadingOnboardingData ? (
                      <div className="flex items-center justify-center">
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-primary"></div>
                        <span className="ml-2">Loading...</span>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        {(() => {
                          const hasPhoneNumber = !!student.phone_number;
                          const isProfileComplete = student.isProfileComplete;
                          const isOnboarded =
                            hasPhoneNumber && isProfileComplete;

                          return isOnboarded ? (
                            <CheckCircle className="h-5 w-5 text-green-600" />
                          ) : (
                            <XCircle className="h-5 w-5 text-red-600" />
                          );
                        })()}
                      </div>
                      // {/* Old progress display - commented out but kept for reference */}
                      // {/* {student.onboardingAnsweredCount ?? 0}/{student.onboardingTotalQuestions ?? 0} */}
                    )}
                  </TableCell>
                  <TableCell>
                    {new Date(student.updated_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell>{student.grade}</TableCell>
                  <TableCell>
                    <Button variant="ghost" size="icon">
                      <Icons.chevronRight className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      <div className="mt-4 flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {startIndex + 1}-
          {Math.min(startIndex + ITEMS_PER_PAGE, sortedStudents.length)} of{" "}
          {sortedStudents.length} records
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="py-2">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
