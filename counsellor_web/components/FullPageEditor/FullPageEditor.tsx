"use client";

import * as React from "react";
import { useEffect, useState } from "react";
import { getStudentChatContext } from "@/actions/chat";
import { Expand } from "lucide-react";

import log from "@/common/logger";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";

interface FullPageEditorProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (value: string) => void;
  isDisabled?: boolean;
  studentId: string;
}

export function FullPageEditor({
  value,
  onChange,
  onSubmit,
  isDisabled,
  studentId,
}: FullPageEditorProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [currentQuestion, setCurrentQuestion] = useState<string | null>(null);

  // Fetch current question when this modal is open

  useEffect(() => {
    const fetchCurrentQuestion = async () => {
      try {
        const res = await getStudentChatContext({ studentId });
        if (res.pending_workflow_steps.length > 0) {
          const currentQ = res.pending_workflow_steps[0].data.question;
          setCurrentQuestion(currentQ);
        } else {
          setCurrentQuestion(null);
        }
      } catch (e) {
        console.error("Failed to fetch current question", e);
        setCurrentQuestion(null);
      }
    };
    if (isOpen) {
      fetchCurrentQuestion();
    }
  }, [isOpen, studentId]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className={`w-full cursor-pointer text-lg ${isDisabled ? "cursor-not-allowed" : "cursor-pointer"}`}
          disabled={isDisabled}
        >
          <Expand className="mr-2 h-4 w-4" />
          Open full-screen editor
        </Button>
      </DialogTrigger>
      <DialogContent className="flex h-[90vh] max-h-[90vh] max-w-4xl flex-col">
        <DialogHeader>
          <DialogTitle>
            {currentQuestion
              ? `Current Question: ${currentQuestion}`
              : "No question available"}
          </DialogTitle>
        </DialogHeader>
        <div className="flex flex-1 flex-col gap-4 overflow-y-auto">
          <Textarea
            value={value}
            onChange={(e) => onChange(e.target.value)}
            className="min-h-[500px] flex-1 p-4 text-base"
            placeholder="Type your message here, use enter key to change lines "
            disabled={isDisabled}
          />
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button
              className={isDisabled ? "cursor-not-allowed" : "cursor-pointer"}
              onClick={() => {
                onSubmit(value);
                setIsOpen(false);
              }}
              disabled={isDisabled}
            >
              Submit
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
