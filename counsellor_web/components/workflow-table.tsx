import Link from "next/link";
import { deleteStudentAgentConfig } from "@/actions/queue";
import { Eye } from "lucide-react";

import { Workflow } from "@/common/model";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface WorkflowTableProps {
  workflows: Workflow[];
}

export function WorkflowTable({ workflows }: WorkflowTableProps) {
  return (
    <div className="w-full overflow-auto p-4">
      <h1 className="text-2xl font-bold">Workflows</h1>
      <div className="mt-4 overflow-hidden rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">Name</TableHead>
              <TableHead>Parent Workflow</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead>Updated At</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {workflows.map((workflow) => (
              <TableRow key={workflow.id}>
                <TableCell className="font-medium">{workflow.name}</TableCell>
                <TableCell>
                  {workflow.parent_step && (
                    <Link
                      href={`/workflow/${workflow.parent_step?.parent_workflow_id}`}
                    >
                      <Button
                        variant="ghost"
                        size="default"
                        aria-label="View Workflow"
                      >
                        <Eye className="mr-2 size-4" />
                        {workflow.parent_step.name}
                      </Button>
                    </Link>
                  )}
                </TableCell>
                <TableCell>
                  {new Date(workflow.created_at).toLocaleString()}
                </TableCell>
                <TableCell>
                  {new Date(workflow.updated_at).toLocaleString()}
                </TableCell>
                <TableCell className="text-right">
                  <Link href={`/workflow/${workflow.id}`}>
                    <Button
                      variant="ghost"
                      size="icon"
                      aria-label="View Workflow"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </Link>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
