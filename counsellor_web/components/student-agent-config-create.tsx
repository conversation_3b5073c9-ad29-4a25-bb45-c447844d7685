"use client";

import React, { useState } from "react";
import { redirect } from "next/navigation";

import log from "@/common/logger";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";

interface StudentAgentConfigParams {
  role: string;
  prompt: {
    content: string;
  };
}

export default function StudentAgentConfigCreate() {
  const [config, setConfig] = useState<StudentAgentConfigParams>({
    role: "",
    prompt: {
      content: "",
    },
  });

  const handleRoleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConfig((prev) => ({ ...prev, role: e.target.value }));
  };

  const handlePromptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setConfig((prev) => ({ ...prev, prompt: { content: e.target.value } }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would typically send the data to your backend
    console.log("Submitting config:", config);
    // call create config api
    const result = await fetch("/api/student_agent_config", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(config),
    });

    // toast({
    //   variant: "default",
    //   title: "Configuration Created",
    //   description: "Your new student agent configuration has been created.",
    // });

    toast({
      title: "Experiment Queued",
      description: "Your student agent configuration has been updated.",
    });

    // Reset form after submission

    setConfig({ role: "", prompt: { content: "" } });
    const configData = await result.json();

    const url = `/student_agent_config/${configData.id}`;

    redirect(url);
  };

  return (
    <div className="w-full">
      <Card className="w-full max-w-4xl border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">
            Create Student Agent Configuration
          </CardTitle>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="role">Role</Label>
              <Input
                id="role"
                value={config.role}
                onChange={handleRoleChange}
                placeholder="Enter role (e.g. teachersPet)"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="prompt">Prompt</Label>
              <Textarea
                id="prompt"
                value={config.prompt.content}
                onChange={handlePromptChange}
                placeholder="Enter the prompt content"
                required
                className="min-h-[200px]"
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" className="w-full">
              Create Configuration
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
