"use client";

import * as React from "react";
import { useEffect } from "react";
import { cookies } from "next/headers";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { setTzOffset } from "@/actions/queue";
import {
  ChevronDown,
  FlaskConical,
  HelpCircle,
  LogOut,
  MessageSquare,
  Settings,
  Workflow,
} from "lucide-react";
import { signIn } from "next-auth/react";
import { useCookies } from "next-client-cookies";

import { siteConfig } from "@/config/site";
import log from "@/common/logger";
import { getCurrentUser } from "@/common/session";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Sidebar,
  SidebarContent,
  <PERSON>barFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarRail,
} from "@/components/ui/sidebar";

interface SideBarProps extends React.HTMLAttributes<HTMLDivElement> {
  user?: any;
}

export default function Component({ user, children }: SideBarProps) {
  const cookies = useCookies();

  useEffect(() => {
    const tzOffset = cookies.get("tz-offset");
    if (!tzOffset) {
      setTzOffset();
    }
  });

  const names = [user.first_name, user.last_name];
  const pathname = usePathname();

  const initials = names
    .filter((name) => name !== undefined)
    .map((name) => name.slice(0, 1)[0].toUpperCase())
    .reverse()
    .join("");

  const [isSettingsOpen, setIsSettingsOpen] = React.useState(false);

  return (
    <SidebarProvider>
      <Sidebar>
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton size="lg" asChild>
                <Link href="/" className="flex items-center gap-2">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src="/placeholder.svg" alt="User Avatar Img" />
                    <AvatarFallback>{initials}</AvatarFallback>
                  </Avatar>
                  <div className="flex flex-col gap-0.5 text-sm">
                    <span className="font-semibold">{siteConfig.name}</span>
                  </div>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton isActive={pathname == "/"} asChild>
                <a href="/" className="flex items-center gap-2">
                  <FlaskConical className="h-4 w-4" />
                  <span>Experiments</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton
                isActive={pathname == "/student_agent_config"}
                asChild
              >
                <a
                  href="/student_agent_config"
                  className="flex items-center gap-2"
                >
                  <Settings className="h-4 w-4" />
                  <span>Student Agent Configs</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton isActive={pathname == "/config"} asChild>
                <a href="/config" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  <span>Addie Config</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton
                isActive={pathname.includes("/workflow")}
                asChild
              >
                <a href="/workflow" className="flex items-center gap-2">
                  <Workflow className="h-4 w-4" />
                  <span>Workflows</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarContent>
        <SidebarFooter className="mt-auto">
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <Link href="/api/auth/signout">
                  <button className="flex w-full items-center gap-2 text-destructive">
                    <LogOut className="h-4 w-4" />
                    <span>Logout</span>
                  </button>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
      {children}
    </SidebarProvider>
  );
}
