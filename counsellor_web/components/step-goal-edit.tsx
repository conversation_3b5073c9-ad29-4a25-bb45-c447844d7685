"use client";

import React, { useState } from "react";
import { redirect } from "next/navigation";
import { saveConfig, saveWorkflowStepGoal } from "@/actions/student-agent";

import { StudentAgentConfig, WorkflowStep } from "@/common/model";
import { ToastQueue } from "@/common/toastQueue";
import { formatDate } from "@/common/utils";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

interface StudentAgentConfigEditProps {
  data: WorkflowStep;
}

const toastQueue = new ToastQueue();

export default function StepGoalEdit({ data }: StudentAgentConfigEditProps) {
  const [config, setConfig] = useState<WorkflowStep>(data);

  const handleGoalChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setConfig((prev) => ({
      ...prev,
      goal: e.target.value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const res = await saveWorkflowStepGoal({
      workflow_step_id: config.id,
      goal: config.goal,
    });

    toastQueue.addToast({
      title: "Goal Updated",
      description: "Your workflow step goal has been updated.",
    });

    toastQueue.renderToasts();

    redirect(`/workflow/${config.parent_workflow_id}/${config.id}`);
  };

  return (
    <div className="w-full p-4">
      <Card className="w-full border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">
            Edit Workflow Step Goal (Prompt)
          </CardTitle>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <Label className="text-sm font-medium">ID</Label>
                <p className="mt-1 text-sm">{config.id}</p>
              </div>
            </div>
            <div>
              <Label htmlFor="goalContent" className="text-sm font-medium">
                Goal
              </Label>
              <Textarea
                id="goalContent"
                value={config.goal}
                onChange={handleGoalChange}
                className="mt-1 h-[400px]"
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" className="w-full">
              Update Goal
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
