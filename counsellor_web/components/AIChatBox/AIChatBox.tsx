"use client";

import React, { use<PERSON><PERSON><PERSON>, useEffect, useRef, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  clearChatHistory,
  getStudentChatPromptSuggestions,
  KeyPrompt,
  submitChatMessage,
} from "@/actions/chat";
import { getStudentAnswers } from "@/actions/conversations/student-conversation";
import {
  BookO<PERSON>,
  Check,
  Co<PERSON>,
  CopyCheck,
  NotebookPen,
  NotebookText,
  Send,
} from "lucide-react";
import { toast } from "sonner";

import log from "@/common/logger";
import { DISCUSS_WITH_ADDIE_PROMPT } from "@/common/prompts/discussWithAddiePrompt";
import { cn } from "@/common/utils";
import { Button } from "@/components/ui/button";
import { Markdown } from "@/components/ui/content/markdown";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Sidebar,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>barHeader,
} from "@/components/ui/sidebar";
import { Textarea } from "@/components/ui/textarea";
import { TypingIndicator } from "@/components/TypingIndicator";
import { useConversation } from "@/app/context/ConversationContext";
import { useNotes } from "@/app/context/NotesContext";
import { useUI } from "@/app/context/UIContext";

import DestructiveModal from "../destructive-modal";
import ChatInput from "./ChatInput";
import { forceScrollToBottom } from "./forceScrollToBottom";

interface AIChatBoxProps {
  isOpen: boolean;
  onClose: () => void;
  studentId: string;
  userId: string;
  messages: any[];
  user;
  counselor;
  lastMsgId: number;
}

export function AIChatBox({
  isOpen,
  onClose,
  studentId,
  userId,
  messages: initialMessages,
  user,
  counselor,
  lastMsgId,
}: AIChatBoxProps) {
  const [messages, setMessages] = useState([]);
  // Track conversation answers in a separate state
  const [conversationAnswers, setConversationAnswers] = useState<
    Record<string, string>
  >({});
  const [chatMessages, setChatMessages] = useState(() =>
    initialMessages.filter((msg: any) => {
      // Filter out system messages but keep everything else
      return !(msg.type === "system" || msg.role === "system");
    }),
  );
  const [input, setInput] = useState("");
  const [isDisabled, setIsDisabled] = useState(false);
  const [showTypingIndicator, setShowTypingIndicator] = useState(false);
  const [triggeredAgent, setTriggeredAgent] = useState(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [promptRequestInProgress, setPromptRequestInProgress] = useState(false);
  const [showPrompts, setShowPrompts] = useState(false);
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null);
  const [hasAddedHiddenContext, setHasAddedHiddenContext] = useState(false);
  const { isChatOpen, setIsChatOpen } = useUI();
  const { chatConversation, setChatConversation } = useConversation();
  const { handleAddNote, checkSavedNotes, notes } = useNotes();
  const messagesId = `${user.id}-${studentId}`;

  const [prompts, setPrompts] = useState<KeyPrompt[]>([]);
  const [savedMessages, setSavedMessages] = useState<any[]>([]);

  const router = useRouter();

  const handleSendMessage = useCallback(
    async (e: React.FormEvent | null, customMessageContent?: string) => {
      if (e) e.preventDefault();

      // Use either the custom message content (from ChatInput) or the input state
      const messageContent = customMessageContent || input.trim();
      if (!messageContent && e !== null) return;

      if (!userId || !user.id || !studentId) {
        console.error("Missing required IDs for chat");
        return;
      }

      // Only clear input state if we're using it (not when using ChatInput)
      if (!customMessageContent) {
        setInput("");
      }

      // Prepare the human message
      const humanMessage = {
        type: "human",
        role: "human",
        content: messageContent,
        id: `temp-${Date.now()}`,
      };

      // Update UI immediately - optimistic update
      setChatMessages((prev) => [...prev, humanMessage]);

      // Scroll to bottom immediately
      requestAnimationFrame(() => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollIntoView({ behavior: "smooth" });
        }
      });

      // Show typing indicator after a small delay to ensure the UI updates first
      setTimeout(() => {
        setShowTypingIndicator(true);
        setIsDisabled(true);
      }, 0);

      try {
        // Send message in the background
        const res = await submitChatMessage({
          message: messageContent,
          userId,
          counselorId: user.id,
          studentId,
          lastMsgId,
          messageType: "human",
        });

        // Update with the server response
        setChatMessages((prev) => {
          // const witoutTemp = prev.filter((msg) => msg.id !== humanMessage.id);
          return [...prev, ...res.messages];
        });

        // router.refresh();
      } catch (error) {
        console.error("Error sending message:", error);
        // Show error to user
        toast.error("Failed to send message. Please try again.");
      } finally {
        setShowTypingIndicator(false);
        setIsDisabled(false);
      }
    },
    [input, userId, user.id, studentId, lastMsgId],
  );

  // Local message submission function for handling conversation context
  const localSubmitMessage = useCallback(
    async (
      messageContent: string,
      messageType = "human",
      hiddenContext?: string,
    ) => {
      // Add message to UI
      setChatMessages((prev) => [
        ...prev,
        {
          type: messageType,
          role: messageType,
          content: messageContent,
        },
      ]);

      setShowTypingIndicator(true);
      setIsDisabled(true);

      try {
        if (hiddenContext) {
          // For conversation context, we combine the visible message and hidden context
          // Format: visible message + special separator + hidden context
          const combinedMessage = `${messageContent}\n\n---HIDDEN_CONTEXT_BELOW---\n\n${hiddenContext}`;

          // Send as a single message with the combined content
          const res = await submitChatMessage({
            message: combinedMessage,
            userId,
            counselorId: user.id,
            studentId,
            lastMsgId,
            messageType: "human", // Send as human so it persists in history
          });
          setChatMessages((prev) => [...prev, ...res.messages]);
        } else {
          // Regular message handling
          const res = await submitChatMessage({
            message: messageContent,
            userId,
            counselorId: user.id,
            studentId,
            lastMsgId,
            messageType,
          });
          setChatMessages((prev) => [...prev, ...res.messages]);
          // router.refresh();
        }
      } catch (error) {
        console.error("Error sending message:", error);
      } finally {
        setShowTypingIndicator(false);
        setIsDisabled(false);
      }
    },
    [userId, user.id, studentId, lastMsgId, setChatMessages],
  );

  // handle to copy response texts
  const handleCopyMessage = async (messageId: string, content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopiedMessageId(messageId);
      setTimeout(() => setCopiedMessageId(null), 5000); // Reset button text after 5 seconds
    } catch (error) {
      console.error("Failed to copy text: ", error);
    }
  };

  const getPromptSuggestions = useCallback(async () => {
    setPromptRequestInProgress(true);

    const prompts = await getStudentChatPromptSuggestions();

    setPrompts(prompts);

    setPromptRequestInProgress(false);
  }, []);

  // Handle chat conversation from ConversationContext
  useEffect(() => {
    if (!chatConversation || !Object.keys(chatConversation).length) {
      return;
    }

    // Load answers first, then create context
    const loadAnswersAndCreateContext = async () => {
      try {
        // First, load the answers
        let answers = {};
        if (chatConversation.workflow_id && studentId) {
          const result = await getStudentAnswers(
            studentId,
            chatConversation.workflow_id,
          );
          answers = result.answerMap || {};
          // console.log("Loaded answers for conversation:", answers);
        }

        // Get the original conversation title (not cleaned)
        const conversationTitle =
          chatConversation.workflow?.name ||
          chatConversation.title ||
          "Conversation";

        // Create a visible message for the UI
        const visibleMessage = `I want to talk about ${conversationTitle}`;

        // Create a more detailed hidden context message for the AI
        let fullConversationContent = "# Conversation Context\n\n";
        fullConversationContent += `Title: ${conversationTitle}\n`;
        fullConversationContent += `Type: ${chatConversation.conversationType || chatConversation.workflow?.workflow_type || "Unknown"}\n`;
        fullConversationContent += `Status: ${chatConversation.status || "Unknown"}\n\n`;

        // Add structured conversation questions and answers
        if (
          chatConversation.workflow?.workflow_type === "STRUCTURED" ||
          chatConversation.conversationType === "Structured"
        ) {
          fullConversationContent += "## Questions and Answers\n\n";
          if (chatConversation.steps && chatConversation.steps.length > 0) {
            chatConversation.steps.forEach((step, index) => {
              // Extract the question details
              const questionObj = step.data;
              if (!questionObj) return;

              const questionId = questionObj.questionId || "";
              const question = questionObj.question || "Unknown question";
              const questionType =
                questionObj.table || questionObj.type || "Unknown type";

              // Get the answer from the loaded answers
              const answer = answers[questionId] || "Not answered";

              // Add to the context
              fullConversationContent += `Question ${index + 1}: ${question}\n`;
              fullConversationContent += `Type: ${questionType}\n`;
              fullConversationContent += `Answer: ${answer}\n\n`;
            });
          } else {
            fullConversationContent += "No questions available.\n\n";
          }
        }

        // Add unstructured conversation summary and insights
        if (
          chatConversation.workflow?.workflow_type === "UNSTRUCTURED" ||
          chatConversation.conversationType === "Unstructured" ||
          chatConversation.conversationType === "Open Ended"
        ) {
          if (chatConversation.summary) {
            fullConversationContent += `Summary: ${chatConversation.summary}\n\n`;
          }

          if (
            chatConversation.insights &&
            chatConversation.insights.length > 0
          ) {
            fullConversationContent += "## Insights\n\n";
            chatConversation.insights.forEach((insight, index) => {
              fullConversationContent += `Goal ${index + 1}: ${
                insight.goal || "Unknown goal"
              }\n`;
              fullConversationContent += `Insight: ${
                insight.insight || "No insight available"
              }\n\n`;
            });
          }

          if (chatConversation.steps && chatConversation.steps.length > 0) {
            fullConversationContent += "## Goals\n\n";
            chatConversation.steps.forEach((step, index) => {
              fullConversationContent += `Goal ${index + 1}: ${
                step.data?.goalText || "Unknown goal"
              }\n`;
            });
          }

          if (
            chatConversation.messages &&
            chatConversation.messages.length > 0
          ) {
            fullConversationContent += "\n## Conversation History\n\n";

            // Sort messages by timestamp if available
            const sortedMessages = [...chatConversation.messages].sort(
              (a, b) => {
                if (!a.timestamp && !b.timestamp) return 0;
                if (!a.timestamp) return -1;
                if (!b.timestamp) return 1;
                return (
                  new Date(a.timestamp).getTime() -
                  new Date(b.timestamp).getTime()
                );
              },
            );

            sortedMessages.forEach((message, index) => {
              // Use more descriptive speaker labels
              const speaker =
                message.type === "human" ? "Student" : "Addie (AI)";
              // Include the exact message content without any modifications
              fullConversationContent += `${speaker}: ${message.content}\n\n`;
            });
          }
        }

        // Add the prompt to the context
        fullConversationContent += DISCUSS_WITH_ADDIE_PROMPT;

        // Create the hidden context message
        const hiddenContextMessage = {
          id: `hidden-context-${Date.now()}`,
          content: fullConversationContent,
          type: "system",
          role: "system",
        };
        // console.log("hiddenContextMessage", hiddenContextMessage);
        // console.log("conversationAnswers at time of context creation:", answers);

        // Add the visible message to the UI and send the hidden context
        localSubmitMessage(
          visibleMessage,
          "human",
          hiddenContextMessage.content,
        );

        // Clear the chat conversation to prevent duplicate messages
        setChatConversation(null);
      } catch (error) {
        console.error("Error creating conversation context:", error);
      }
    };

    loadAnswersAndCreateContext();
  }, [chatConversation, setChatConversation, studentId, localSubmitMessage]);

  // Refresh page when the chat component is mounted
  useEffect(() => {
    if (isChatOpen) {
      router.refresh();
    }
  }, [isChatOpen, router]);

  // Scroll to bottom whenever component mounts or messages change
  useEffect(() => {
    // Use our robust utility to force scroll to bottom
    forceScrollToBottom(chatContainerRef);

    // Also schedule additional scrolls to handle dynamic content loading
    const timerId = setTimeout(
      () => forceScrollToBottom(chatContainerRef),
      100,
    );
    const timerIdLater = setTimeout(
      () => forceScrollToBottom(chatContainerRef),
      500,
    );

    return () => {
      clearTimeout(timerId);
      clearTimeout(timerIdLater);
    };
  }, [chatMessages]);

  // Explicit effect for component mount to ensure scroll works on initial page load
  useEffect(() => {
    // Apply multiple scroll attempts with increasing delays
    forceScrollToBottom(chatContainerRef);

    // Schedule multiple attempts to handle different loading scenarios
    const timers = [
      setTimeout(() => forceScrollToBottom(chatContainerRef), 50),
      setTimeout(() => forceScrollToBottom(chatContainerRef), 150),
      setTimeout(() => forceScrollToBottom(chatContainerRef), 300),
      setTimeout(() => forceScrollToBottom(chatContainerRef), 1000),
    ];

    return () => timers.forEach((timer) => clearTimeout(timer));
  }, []);

  // Also scroll when the chat is opened
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => forceScrollToBottom(chatContainerRef), 100);
    }
  }, [isOpen]);

  // Load prompt suggestions if needed
  useEffect(() => {
    if (prompts.length === 0 && chatMessages.length === 0) {
      getPromptSuggestions();
    }
  }, [prompts.length, chatMessages.length, getPromptSuggestions]);

  // Check saved notes when the component mounts or messages change
  useEffect(() => {
    if (notes.length > 0) {
      setSavedMessages(checkSavedNotes(chatMessages.map((msg) => msg.content)));
    }
  }, [notes, messages, chatMessages, checkSavedNotes]);

  // Filter messages for display to remove hidden context
  const displayMessages = chatMessages.map((msg) => {
    if (
      msg.content &&
      typeof msg.content === "string" &&
      msg.content.includes("---HIDDEN_CONTEXT_BELOW---")
    ) {
      // Only keep the part before the separator
      return {
        ...msg,
        content: msg.content.split("---HIDDEN_CONTEXT_BELOW---")[0].trim(),
      };
    }
    return msg;
  });

  const renderChatMessages = () => {
    return displayMessages.map((message, index) => {
      if (
        message.type === "system" ||
        message.role === "system" ||
        message.content.startsWith("[HIDDEN CONTEXT FOR AI ONLY")
      ) {
        return null;
      }

      return (
        <div key={index}>
          <div
            className={`mb-4 rounded-lg px-4 py-2 ${
              message.type === "Addie" || message.type === "ai"
                ? "bg-blue-50 text-blue-800"
                : "bg-gray-100 text-gray-800"
            }`}
          >
            <p className="mb-1 text-sm font-semibold">
              {message.type === "ai" ||
              message.type === "addie" ||
              message.type === "Addie"
                ? "Addie"
                : "Counselor"}
            </p>
            <Markdown content={message.content} />
          </div>
          {(message.type === "ai" ||
            message.type === "addie" ||
            message.type === "Addie") && (
            <div className="my-2 flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() =>
                  handleCopyMessage(index.toString(), message.content)
                }
              >
                {copiedMessageId === index.toString() ? (
                  <CopyCheck />
                ) : (
                  <Copy />
                )}
                {copiedMessageId === index.toString() ? "Copied" : "Copy"}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  handleAddNote(message.content);
                  setSavedMessages((prev) => [...prev, message.content]);
                }}
                disabled={savedMessages.includes(message.content)}
              >
                {savedMessages.includes(message.content) ? (
                  <NotebookPen />
                ) : (
                  <NotebookText />
                )}
                {savedMessages.includes(message.content)
                  ? "Note Saved"
                  : "Save to note"}
              </Button>
            </div>
          )}
        </div>
      );
    });
  };

  return (
    <Sidebar className="h-full w-1/2 border-l" side="right">
      {/* Header Section */}
      <SidebarHeader className="-h-10 -flex px-4">
        <div className="flex justify-end space-x-4">
          <Link
            href="https://getaddie.com/prompting-addie"
            target="_blank"
            className="flex w-24 items-center gap-2"
          >
            <Button variant="ghost">
              <BookOpen className="h-4 w-4" />
              Tutorial
            </Button>
          </Link>
          {chatMessages.length > 0 && (
            <DestructiveModal
              btnTitle="Clear History"
              className="w-32"
              handler={async () => {
                try {
                  const result = await clearChatHistory(messagesId);

                  if (result.success) {
                    // The welcome message should already be in the correct format
                    const welcomeMessage = result.welcomeMessage || {
                      content:
                        "I've reset our conversation. How can I help you today?",
                      type: "Addie",
                      id: Date.now().toString(),
                      created_at: new Date().toISOString(),
                    };

                    // Clear the current messages and set only the welcome message
                    setChatMessages([]);
                    setTimeout(() => {
                      setChatMessages([welcomeMessage]);
                    }, 100);
                    toast.success("Conversation has been reset");
                  } else {
                    toast.error(
                      `Failed to reset conversation: ${result.error}`,
                    );
                  }
                } catch (error) {
                  console.error("Error resetting conversation:", error);
                  toast.error(`An error occurred: ${error.message}`);
                }
              }}
              title="Clear History"
              description="Are you sure you want to reset the conversation? This will start a new conversation thread."
            />
          )}
        </div>
      </SidebarHeader>

      {/* Chat Content */}
      <SidebarContent className="">
        <ScrollArea className="m-2 h-[calc(85vh)] overflow-y-auto px-4">
          {renderChatMessages()}
          {showTypingIndicator && <TypingIndicator />}
          <div ref={chatContainerRef} className="h-10"></div>
        </ScrollArea>

        {/*Button to show prompts*/}
        <div className="flex justify-center p-2">
          <Button
            className={showPrompts ? "bg-red-50" : "bg-blue-50"}
            variant="outline"
            size="sm"
            onClick={() => {
              if (!prompts.length) {
                getPromptSuggestions();
              }
              setShowPrompts(!showPrompts);
            }}
          >
            {showPrompts
              ? "Hide Suggested Questions"
              : "Show Suggested Questions"}
          </Button>
        </div>

        {showPrompts && (
          <div className="mx-2 space-y-1">
            {promptRequestInProgress && (
              <div className="w-full p-4">
                <LoadingSpinner className="mx-auto" />
              </div>
            )}
            <div className="mx-auto flex w-3/4 flex-col justify-center gap-2">
              {prompts.slice(0, 5).map((prompt, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setInput(prompt.prompt);
                    localSubmitMessage(prompt.prompt);
                    setInput("");
                    setShowPrompts(false);
                  }}
                >
                  {prompt.key}
                </Button>
              ))}
            </div>
          </div>
        )}
        {/* Input Section - Optimized Component */}
        <ChatInput
          onSubmit={async (e: any) => {
            // Get the current value from the event
            const messageContent = e.currentValue;
            // Use messageContent directly instead of relying on input state
            if (messageContent && messageContent.trim()) {
              // Pass the message content directly to handleSendMessage
              await handleSendMessage(null, messageContent);
            }
          }}
          disabled={isDisabled}
          initialValue={input}
        />
      </SidebarContent>
    </Sidebar>
  );
}
