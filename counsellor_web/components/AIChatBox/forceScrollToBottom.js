// Script to force scroll to bottom of the chat container
// This will be injected into the component to ensure scrolling works on page load

export function forceScrollToBottom(containerRef) {
  if (!containerRef || !containerRef.current) return;

  // Only scroll the chat container, not affecting other parts of the page
  try {
    // Find the closest scroll area that contains our chat container
    const scrollArea = containerRef.current.closest(".ScrollArea, .overflow-y-auto, [data-radix-scroll-area-viewport]");
    if (scrollArea) {
      // Only scroll the found scroll area, not affecting other areas
      scrollArea.scrollTop = scrollArea.scrollHeight;
      return;
    }

    // If no suitable scroll area is found, only scroll the current element without propagation
    containerRef.current.scrollIntoView({
      block: "end",
      inline: "nearest",
      behavior: "auto",
    });
  } catch (e) {
    console.error("Error in chat scroll:", e);
  }
}
