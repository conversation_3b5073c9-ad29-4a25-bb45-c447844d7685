// ChatInput.tsx - Optimized chat input component to improve typing performance
"use client";

import React, { memo, useCallback, useState } from "react";
import { Send } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

interface ChatInputProps {
  onSubmit: (e: React.FormEvent | null) => Promise<void>;
  disabled: boolean;
  initialValue?: string;
}

// Use memo to prevent unnecessary re-renders
const ChatInput = memo(function ChatInput({
  onSubmit,
  disabled,
  initialValue = "",
}: ChatInputProps) {
  // Local state for input to avoid parent component re-renders on every keystroke
  const [inputValue, setInputValue] = useState(initialValue);

  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();
      if (!inputValue.trim()) return;

      // Pass the current value to the onSubmit handler
      const currentValue = inputValue;
      // Clear input immediately for better UX
      setInputValue("");

      // Call the parent's submit handler with the value
      onSubmit({
        ...e,
        preventDefault: () => e.preventDefault(),
        currentTarget: {
          ...e.currentTarget,
          reset: () => {},
        },
        // Add the current value as a property that can be accessed
        currentValue,
      } as any);
    },
    [inputValue, onSubmit],
  );

  return (
    <form
      onSubmit={handleSubmit}
      className="flex items-center gap-2 border-t p-2"
    >
      <Textarea
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        placeholder="Type your message..."
        // disabled={disabled}
        className="grow resize-none"
        onKeyDownCapture={(e) => {
          if (e.key === "Enter" && !e.shiftKey && !disabled) {
            e.preventDefault();
            handleSubmit(e);
          }
        }}
        rows={1}
      />

      <Button type="submit" disabled={disabled} className="h-10">
        <Send className="h-4 w-4" />
      </Button>
    </form>
  );
});

export default ChatInput;
