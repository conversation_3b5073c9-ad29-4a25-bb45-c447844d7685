import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  GraduationCap,
  Trash2,
  TrendingUp,
  Users,
} from "lucide-react";

import { cn } from "@/common/utils";
import { Badge } from "@/components/ui/badge";
import DestructiveModal from "@/components/destructive-modal";

export interface MajorData {
  id: string;
  name: string;
  matchScore: number;
  category: string;
  description: string;
  rationale: string;
  keyTraits: string[];
  careerOutlook: string;
  isStudentAdded?: boolean;
  isRecommended?: boolean;
  isCounselorAdded?: boolean;
}

interface MajorItemProps {
  major: MajorData;
  onRemove?: (majorId: string) => void;
  showRemoveButton?: boolean;
}

export const MajorItem: React.FC<MajorItemProps> = ({
  major,
  onRemove,
  showRemoveButton = false,
}) => {
  const getMatchScoreColor = (score: number) => {
    if (score >= 85) return "bg-green-100 text-green-800 border-green-200";
    if (score >= 70) return "bg-blue-100 text-blue-800 border-blue-200";
    if (score >= 60) return "bg-yellow-100 text-yellow-800 border-yellow-200";
    return "bg-gray-100 text-gray-800 border-gray-200";
  };

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case "stem":
        return <GraduationCap className="h-5 w-5" />;
      case "business":
        return <TrendingUp className="h-5 w-5" />;
      case "social sciences":
        return <Users className="h-5 w-5" />;
      default:
        return <BookOpen className="h-5 w-5" />;
    }
  };

  const getStatusBadge = () => {
    if (major.isStudentAdded) {
      return (
        <Badge
          variant="secondary"
          className="hover:none border-green-200 bg-green-50 text-center text-black"
        >
          Student Added
        </Badge>
      );
    }
    if (major.isCounselorAdded) {
      return (
        <Badge
          variant="secondary"
          className="hover:none border-green-200 bg-yellow-100 text-center text-black"
        >
          Your Addition
        </Badge>
      );
    }
    // if (major.isRecommended) {
    //   return (
    //     <Badge
    //       variant="outline"
    //       className="border-green-200 bg-green-50 text-center text-black"
    //     >
    //       Highly Recommended
    //     </Badge>
    //   );
    // }
    return (
      <Badge
        variant="outline"
        className={cn(
          "text-center text-xs",
          getMatchScoreColor(major.matchScore),
        )}
      >
        {major.matchScore}% Match
      </Badge>
    );
  };

  return (
    <div className="w-full rounded-lg border bg-card p-6 text-card-foreground shadow-sm transition-shadow hover:shadow-md">
      <div className="flex gap-4">
        {/* Left side - Icon */}
        <div className="flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-muted">
          {getCategoryIcon(major.category)}
        </div>

        {/* Right side - Content */}
        <div className="flex-1">
          {/* Header with title, badges and remove button */}
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-lg font-semibold">{major.name}</h3>
            </div>

            <div className="flex items-center gap-2">
              {getStatusBadge()}

              {showRemoveButton && onRemove && (
                <DestructiveModal
                  title="Remove Major"
                  description={`Are you sure you want to remove ${major.name} from the recommendations? This action cannot be undone.`}
                  handler={() => onRemove(major.id)}
                  variant="ghost"
                  btnTitle=" "
                  icon={<Trash2 className="h-4 w-4" />}
                  className="h-8 w-8 p-0 text-destructive"
                />
              )}
            </div>
          </div>

          {/* Description text */}
          <p className="mt-3 text-sm leading-relaxed text-muted-foreground">
            {major.rationale}
          </p>
        </div>
      </div>
    </div>
  );
};
