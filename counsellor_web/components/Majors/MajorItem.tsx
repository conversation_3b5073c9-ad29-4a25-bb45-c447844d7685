import React from "react";
import {
  <PERSON><PERSON>,
  BookOpen,
  Brain,
  Briefcase,
  Building,
  Calculator,
  Camera,
  Computer,
  Gavel,
  Globe,
  GraduationCap,
  Heart,
  Languages,
  Microscope,
  Music,
  Palette,
  PenTool,
  Stethoscope,
  ThumbsDown,
  ThumbsUp,
  Trash2,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>dingUp,
  <PERSON>,
  Wren<PERSON>,
} from "lucide-react";

import { cn } from "@/common/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import DestructiveModal from "@/components/destructive-modal";
import { TooltipWrapper } from "@/components/shared/TooltipWrapper";

export interface MajorData {
  id: string;
  student_major_id: string;
  major_name: string;
  match_percentage: number;
  summary: string | null;
  source: "ADDIE" | "STUDENT_ADDED" | "COUNSELOR_ADDED";
  liked: boolean | null;
  disliked: boolean | null;
  deleted: boolean;
  deleted_by: string | null;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

interface MajorItemProps {
  major: MajorData;
  onRemove?: (majorId: string) => void;
  showRemoveButton?: boolean;
  onLike?: (majorId: string) => void;
  onDislike?: (majorId: string) => void;
}

export const MajorItem: React.FC<MajorItemProps> = ({
  major,
  onRemove,
  showRemoveButton = false,
  onLike,
  onDislike,
}) => {
  const getMatchScoreColor = (score: number) => {
    if (score >= 85) return "bg-green-100 text-green-800 border-green-200";
    if (score >= 70) return "bg-blue-100 text-blue-800 border-blue-200";
    if (score >= 60) return "bg-yellow-100 text-yellow-800 border-yellow-200";
    return "bg-gray-100 text-gray-800 border-gray-200";
  };

  const getMajorIcon = (majorName: string) => {
    const name = majorName.toLowerCase();

    // Computer Science & Technology
    if (
      name.includes("computer") ||
      name.includes("software") ||
      name.includes("programming") ||
      name.includes("information technology") ||
      name.includes("data science") ||
      name.includes("cybersecurity")
    ) {
      return <Computer className="h-5 w-5" />;
    }

    // Psychology & Mental Health
    if (
      name.includes("psychology") ||
      name.includes("counseling") ||
      name.includes("therapy") ||
      name.includes("mental health") ||
      name.includes("behavioral")
    ) {
      return <Brain className="h-5 w-5" />;
    }

    // Business & Economics
    if (
      name.includes("business") ||
      name.includes("management") ||
      name.includes("marketing") ||
      name.includes("finance") ||
      name.includes("economics") ||
      name.includes("administration") ||
      name.includes("entrepreneurship") ||
      name.includes("accounting")
    ) {
      return <Briefcase className="h-5 w-5" />;
    }

    // Arts & Design
    if (
      name.includes("art") ||
      name.includes("design") ||
      name.includes("graphic") ||
      name.includes("visual") ||
      name.includes("creative") ||
      name.includes("fine arts") ||
      name.includes("illustration") ||
      name.includes("photography")
    ) {
      return <Palette className="h-5 w-5" />;
    }

    // Healthcare & Medicine
    if (
      name.includes("medicine") ||
      name.includes("nursing") ||
      name.includes("health") ||
      name.includes("medical") ||
      name.includes("pharmacy") ||
      name.includes("dental") ||
      name.includes("therapy") ||
      name.includes("healthcare")
    ) {
      return <Stethoscope className="h-5 w-5" />;
    }

    // Mathematics & Statistics
    if (
      name.includes("mathematics") ||
      name.includes("statistics") ||
      name.includes("math") ||
      name.includes("actuarial") ||
      name.includes("quantitative")
    ) {
      return <Calculator className="h-5 w-5" />;
    }

    // International Studies & Languages
    if (
      name.includes("international") ||
      name.includes("global") ||
      name.includes("foreign") ||
      name.includes("language") ||
      name.includes("linguistics") ||
      name.includes("translation") ||
      name.includes("cultural studies") ||
      name.includes("anthropology")
    ) {
      return <Globe className="h-5 w-5" />;
    }

    // Music & Performing Arts
    if (
      name.includes("music") ||
      name.includes("performing") ||
      name.includes("theater") ||
      name.includes("dance") ||
      name.includes("audio") ||
      name.includes("sound")
    ) {
      return <Music className="h-5 w-5" />;
    }

    // Sciences (Biology, Chemistry, Physics)
    if (
      name.includes("biology") ||
      name.includes("chemistry") ||
      name.includes("physics") ||
      name.includes("science") ||
      name.includes("research") ||
      name.includes("laboratory") ||
      name.includes("biochemistry") ||
      name.includes("molecular")
    ) {
      return <Microscope className="h-5 w-5" />;
    }

    // Engineering & Technology
    if (
      name.includes("engineering") ||
      name.includes("mechanical") ||
      name.includes("electrical") ||
      name.includes("civil") ||
      name.includes("industrial") ||
      name.includes("technical") ||
      name.includes("robotics") ||
      name.includes("automation")
    ) {
      return <Wrench className="h-5 w-5" />;
    }

    // Architecture & Construction
    if (
      name.includes("architecture") ||
      name.includes("construction") ||
      name.includes("building") ||
      name.includes("urban planning") ||
      name.includes("real estate")
    ) {
      return <Building className="h-5 w-5" />;
    }

    // Law & Legal Studies
    if (
      name.includes("law") ||
      name.includes("legal") ||
      name.includes("justice") ||
      name.includes("criminal") ||
      name.includes("paralegal") ||
      name.includes("judicial")
    ) {
      return <Gavel className="h-5 w-5" />;
    }

    // Social Sciences & Humanities
    if (
      name.includes("social") ||
      name.includes("sociology") ||
      name.includes("political") ||
      name.includes("history") ||
      name.includes("philosophy") ||
      name.includes("humanities") ||
      name.includes("anthropology") ||
      name.includes("geography")
    ) {
      return <Users className="h-5 w-5" />;
    }

    // Environmental & Natural Sciences
    if (
      name.includes("environmental") ||
      name.includes("ecology") ||
      name.includes("forestry") ||
      name.includes("agriculture") ||
      name.includes("sustainability") ||
      name.includes("marine") ||
      name.includes("wildlife") ||
      name.includes("conservation")
    ) {
      return <TreePine className="h-5 w-5" />;
    }

    // Media & Communications
    if (
      name.includes("media") ||
      name.includes("communication") ||
      name.includes("journalism") ||
      name.includes("broadcasting") ||
      name.includes("film") ||
      name.includes("television") ||
      name.includes("digital media") ||
      name.includes("public relations")
    ) {
      return <Camera className="h-5 w-5" />;
    }

    // Writing & Literature
    if (
      name.includes("english") ||
      name.includes("literature") ||
      name.includes("writing") ||
      name.includes("creative writing") ||
      name.includes("journalism") ||
      name.includes("publishing")
    ) {
      return <PenTool className="h-5 w-5" />;
    }

    // Education & Teaching
    if (
      name.includes("education") ||
      name.includes("teaching") ||
      name.includes("pedagogy") ||
      name.includes("curriculum") ||
      name.includes("early childhood")
    ) {
      return <GraduationCap className="h-5 w-5" />;
    }

    // Default fallback
    return <BookOpen className="h-5 w-5" />;
  };

  const getStatusBadge = () => {
    if (major.source === "STUDENT_ADDED") {
      if (major.match_percentage !== 0) {
        return (
          <div className="flex gap-2">
            <Badge
              variant="secondary"
              className="hover:none border-gray-100 bg-lime-100 text-center text-black"
            >
              Student Added
            </Badge>
            <Badge
              variant="outline"
              className={cn(
                "text-center text-xs",
                getMatchScoreColor(major.match_percentage),
              )}
            >
              {major.match_percentage}% Match
            </Badge>
          </div>
        );
      }

      return (
        <Badge
          variant="secondary"
          className="hover:none border-gray-100 bg-lime-100 text-center text-black"
        >
          Student Added
        </Badge>
      );
    }
    if (major.source === "COUNSELOR_ADDED") {
      if (major.match_percentage !== 0) {
        return (
          <div className="flex gap-2">
            <Badge
              variant="secondary"
              className="border-gray-100 bg-yellow-100 text-center text-black hover:bg-none"
            >
              Your Addition
            </Badge>
            <Badge
              variant="outline"
              className={cn(
                "text-center text-xs",
                getMatchScoreColor(major.match_percentage),
              )}
            >
              {major.match_percentage}% Match
            </Badge>
          </div>
        );
      }
      return (
        <Badge
          variant="secondary"
          className="bg-yellow-100 text-center text-black hover:bg-none"
        >
          Your Addition
        </Badge>
      );
    }
    return (
      <Badge
        variant="outline"
        className={cn(
          "text-center text-xs",
          getMatchScoreColor(major.match_percentage),
        )}
      >
        {major.match_percentage}% Match
      </Badge>
    );
  };

  return (
    <div className="w-full rounded-lg border bg-card p-4 text-card-foreground shadow-sm transition-shadow hover:shadow-md sm:p-6">
      <div className="flex flex-col gap-4 sm:flex-row">
        {/* Top/Left side - Icon and Title (mobile: stacked, desktop: side by side) */}
        <div className="flex items-center gap-3 sm:gap-4">
          <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-muted sm:h-12 sm:w-12">
            {getMajorIcon(major.major_name)}
          </div>

          {/* Title - visible on mobile, hidden on desktop (will be shown in content area) */}
          <div className="flex-1 sm:hidden">
            <h3 className="text-lg font-semibold">{major.major_name}</h3>
          </div>
        </div>

        {/* Right side - Content */}
        <div className="flex-1">
          {/* Header with title, badges and buttons */}
          <div className="flex items-start justify-between">
            <div className="flex-1">
              {/* Title - hidden on mobile, visible on desktop */}
              <h3 className="hidden text-lg font-semibold sm:block">
                {major.major_name}
              </h3>
            </div>

            <div className="ml-2 flex items-center gap-2">
              {getStatusBadge()}

              {/* Like/Dislike buttons */}
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "h-8 w-8 p-0",
                    major.liked
                      ? "text-green-600 hover:text-green-700"
                      : "text-gray-400 hover:text-green-600",
                  )}
                  onClick={() => onLike?.(major.id)}
                >
                  <ThumbsUp className="h-4 w-4" />
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "h-8 w-8 p-0",
                    major.disliked
                      ? "text-red-600 hover:text-red-700"
                      : "text-gray-400 hover:text-red-600",
                  )}
                  onClick={() => onDislike?.(major.id)}
                >
                  <ThumbsDown className="h-4 w-4" />
                </Button>
              </div>

              {showRemoveButton && onRemove && (
                <>
                  <DestructiveModal
                    title="Remove Major"
                    description={`Are you sure you want to remove ${major.major_name} from the recommendations? This action cannot be undone.`}
                    handler={() => onRemove(major.id)}
                    variant="ghost"
                    btnTitle=" "
                    icon={<Trash2 className="h-4 w-4" />}
                    className="h-8 w-8 p-0 text-destructive"
                  />
                </>
              )}
            </div>
          </div>

          {/* Description text */}
          <p className="mt-3 text-sm leading-relaxed text-muted-foreground">
            {major.summary}
          </p>
        </div>
      </div>
    </div>
  );
};
