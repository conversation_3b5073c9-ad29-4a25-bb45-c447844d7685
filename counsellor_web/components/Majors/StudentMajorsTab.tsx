import React, { useState } from "react";
import { toast } from "sonner";

import { AnalysisDisclaimerCard } from "@/components/Majors/AnalysisDisclaimerCard";

import AddMajorDialog from "./AddMajorDialog";
import AddMajorPopover from "./AddMajorPopover";
import EmptyMajorsState from "./EmptyMajorsState";
import { MajorData, MajorItem } from "./MajorItem";
import PersonalityProfile from "./PersonalityProfile";

interface StudentMajorsTabProps {
  majors?: MajorData[];
  student: any;
}

//mock  Predefined majors with match scores
const predefinedMajors = [
  { name: "English Literature", matchScore: 80 },
  { name: "Biology", matchScore: 75 },
  { name: "Mathematics", matchScore: 70 },
  { name: "History", matchScore: 68 },
  { name: "Chemistry", matchScore: 65 },
  { name: "Philosophy", matchScore: 62 },
  { name: "Economics", matchScore: 60 },
  { name: "Political Science", matchScore: 58 },
];

// Mock major Data
const mockMajors: MajorData[] = [
  {
    id: "1",
    name: "Computer Science",
    matchScore: 92,
    category: "STEM",
    description:
      "Study algorithms, programming, software engineering, and computational theory to solve complex problems and build innovative technology solutions.",
    rationale:
      "Your high openness to experience (85%) and conscientiousness (78%) align perfectly with the innovative and detail-oriented nature of computer science. Your moderate extraversion (62%) suggests you'd thrive in collaborative coding environments while still enjoying independent problem-solving.",
    keyTraits: [
      "High Openness",
      "Strong Conscientiousness",
      "Analytical Thinking",
      "Problem Solving",
    ],
    careerOutlook:
      "Excellent job growth projected with median salaries ranging from $85,000-$150,000+. High demand in tech, finance, healthcare, and emerging fields like AI.",
    isRecommended: true,
  },
  {
    id: "2",
    name: "Psychology",
    matchScore: 72,
    category: "Social Sciences",
    description:
      "Explore human behavior, mental processes, and emotional patterns to understand and help individuals and communities thrive.",
    rationale:
      "Your exceptional agreeableness (91%) and high openness (85%) make you naturally suited for understanding human behavior. Your emotional stability (low neuroticism at 23%) would help you maintain objectivity when working with diverse populations.",
    keyTraits: [
      "High Agreeableness",
      "Emotional Stability",
      "Empathy",
      "Communication",
    ],
    careerOutlook:
      "Growing field with opportunities in clinical, research, organizational, and educational settings. Median salary $80,000-$120,000 depending on specialization.",
    isRecommended: false,
  },
  {
    id: "3",
    name: "Business Administration",
    matchScore: 66,
    category: "Business",
    description:
      "Learn management principles, strategic thinking, and leadership skills to drive organizational success and innovation.",
    rationale:
      "Your strong conscientiousness (78%) and moderate-to-high extraversion (62%) indicate excellent leadership and organizational potential. Your balanced agreeableness (91%) suggests you'd excel in team management and client relations.",
    keyTraits: [
      "Leadership Potential",
      "Organizational Skills",
      "Strategic Thinking",
      "Team Management",
    ],
    careerOutlook:
      "Versatile degree with strong earning potential. Management roles typically range from $70,000-$140,000+ with significant growth opportunities.",
    isStudentAdded: true,
  },
  {
    id: "4",
    name: "Data Science",
    matchScore: 83,
    category: "STEM",
    description:
      "Combine statistics, programming, and domain expertise to extract insights from complex datasets and drive data-informed decisions.",
    rationale:
      "Your analytical mindset and high conscientiousness (78%) are perfect for the methodical nature of data analysis. Your openness to experience (85%) aligns with the rapidly evolving field of data science and machine learning.",
    keyTraits: [
      "Analytical Skills",
      "Detail-Oriented",
      "Statistical Thinking",
      "Innovation",
    ],
    careerOutlook:
      "One of the fastest-growing fields with excellent compensation ($95,000-$165,000+). High demand across all industries for data-driven insights.",
    isStudentAdded: true,
  },
];

export const StudentMajorsTab = ({
  majors = [],
  student,
}: StudentMajorsTabProps) => {
  // Mock data for development - will be replaced with real data from props
  const [displayMajors, setDisplayMajors] = useState(mockMajors);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedMajor, setSelectedMajor] = useState<{
    name: string;
    matchScore: number;
    isCustom?: boolean;
  } | null>(null);
  const [userRationale, setUserRationale] = useState("");
  const [customMajorName, setCustomMajorName] = useState("");

  // const displayMajors = majors.length > 0 ? majors : mockMajors;

  // Testing empty state
  // const displayMajors: MajorData[] = [];

  // For production, use this:
  // const displayMajors = majors.length > 0 ? majors : mockMajors;

  const handleAddMajor = () => {
    // TODO: add real logic here
    console.log("Add major clicked");
    // Implement add major functionality here
  };

  const handleDeleteMajor = async (majorId: string) => {
    // TODO: add real logic here - instead of using useState we would want to use server actions
    console.log("Delete major clicked:", majorId);
    setDisplayMajors((prev) => prev.filter((major) => major.id !== majorId));

    const majorName = displayMajors.find((major) => major.id === majorId)?.name;
    toast.success(`${majorName} deleted!`);
    // Implement delete major functionality here
  };

  const handleSelectMajor = (majorName: string) => {
    // Find the selected major from predefined list
    const selectedMajor = predefinedMajors.find((m) => m.name === majorName);

    if (selectedMajor) {
      // Store the selected major
      setSelectedMajor(selectedMajor);

      // Close the popover
      setIsPopoverOpen(false);

      // Open the dialog for user input
      setIsDialogOpen(true);
    }
  };

  const handleOtherSelect = () => {
    // Set up for custom major
    setSelectedMajor({
      name: "Custom Major",
      matchScore: 50,
      isCustom: true,
    });

    // Reset custom major name
    setCustomMajorName("");

    // Close the popover
    setIsPopoverOpen(false);

    // Open the dialog for user input
    setIsDialogOpen(true);
  };

  const handleSaveRationale = () => {
    // if selected major is already in the list, don't add it again - show toast error
    if (displayMajors.some((major) => major.name === selectedMajor?.name)) {
      toast.error("Major already exists!");
      setIsDialogOpen(false);
      setUserRationale("");
      return;
    }

    if (userRationale.trim() === "") {
      toast.error("Please enter a rationale!");
      return;
    }

    if (selectedMajor) {
      // Create a new major object
      const newMajor: MajorData = {
        id: `new-${Date.now()}`, // Generate a temporary ID
        name: selectedMajor.isCustom
          ? customMajorName || "Custom Major"
          : selectedMajor.name,
        matchScore: selectedMajor.matchScore,
        category: selectedMajor.isCustom ? "Custom" : "Other",
        description: selectedMajor.isCustom
          ? "Custom major added by the student."
          : `Study of ${selectedMajor.name}`,
        rationale:
          userRationale ||
          `This major has a ${selectedMajor.matchScore}% match with your personality profile.`,
        keyTraits: ["Match Score: " + selectedMajor.matchScore + "%"],
        careerOutlook: "Career opportunities available in various sectors.",
        isCounselorAdded: true, // Mark as counselor added
      };

      // Add the new major to the list
      setDisplayMajors((prev) => [...prev, newMajor]);

      toast.success(`${newMajor.name} added!`);

      // Reset state
      setIsDialogOpen(false);
      setSelectedMajor(null);
      setUserRationale("");
      setCustomMajorName("");
    }
  };

  // If there are no majors, show only the empty state component
  if (displayMajors.length === 0) {
    return (
      <div className="flex w-full items-center justify-center px-4 py-10">
        <EmptyMajorsState firstName={student.first_name} />
      </div>
    );
  }

  // If there are majors, show the full layout with sidebar
  return (
    <div className="flex justify-evenly gap-6">
      {/* Main Content - Majors List */}
      <div className="w-2/3 space-y-6">
        {/* Header with AI indicator */}
        <div className="flex items-center justify-between py-4">
          <div>
            <h2 className="text-xl font-semibold text-foreground">Majors</h2>
          </div>

          {/* Popover for adding majors */}
          <AddMajorPopover
            isOpen={isPopoverOpen}
            onOpenChange={setIsPopoverOpen}
            predefinedMajors={predefinedMajors}
            onSelectMajor={handleSelectMajor}
            onSelectOther={handleOtherSelect}
          />
        </div>

        {/* All Majors Combined */}
        <div className="space-y-4">
          {displayMajors.map((major, index) => (
            <MajorItem
              key={major.id ?? index}
              major={major}
              showRemoveButton={true}
              onRemove={handleDeleteMajor}
            />
          ))}
        </div>

        {/* Analysis Explanation */}
        <AnalysisDisclaimerCard />
      </div>

      {/* Sidebar - Personality Profile */}
      <div className="w-1/4">
        <PersonalityProfile />
      </div>

      {/* Dialog modal for counselor input */}
      <AddMajorDialog
        isOpen={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        selectedMajor={selectedMajor}
        studentName={student.first_name || ""}
        customMajorName={customMajorName}
        onCustomMajorNameChange={setCustomMajorName}
        userRationale={userRationale}
        onUserRationaleChange={setUserRationale}
        onSave={handleSaveRationale}
      />
    </div>
  );
};
