import React, { useState } from "react";
import {
  createCustomMajor,
  deleteMajor,
  updateMajorLikeStatus,
  updateMajorSource,
} from "@/actions/majors";
import { ChevronDown, ChevronUp } from "lucide-react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AnalysisDisclaimerCard } from "@/components/Majors/AnalysisDisclaimerCard";
import { useUI } from "@/app/context/UIContext";

import AddMajorDialog from "./AddMajorDialog";
import AddMajorPopover from "./AddMajorPopover";
import EmptyMajorsState from "./EmptyMajorsState";
import { MajorData, MajorItem } from "./MajorItem";
import PersonalityProfile from "./PersonalityProfile";

// Define the student interface for this component
export interface StudentForMajorsTab {
  id: string;
  first_name: string;
  ocean_scores: {
    id: string;
    student_id: string;
    extroversion: number;
    agreeableness: number;
    conscientiousness: number;
    neuroticism: number;
    openness_to_experience: number;
    created_at: Date;
    updated_at: Date;
  };
  student_majors: Array<{
    id: string;
    matches: MajorData[];
  }>;
}

interface StudentMajorsTabProps {
  student: StudentForMajorsTab;
}

enum MajorSource {
  ADDIE = "ADDIE",
  STUDENT_ADDED = "STUDENT_ADDED",
  COUNSELOR_ADDED = "COUNSELOR_ADDED",
}

export const StudentMajorsTab = ({ student }: StudentMajorsTabProps) => {
  const { isChatOpen } = useUI();
  const [isPersonalityExpanded, setIsPersonalityExpanded] = useState(false);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedMajor, setSelectedMajor] = useState<{
    name: string;
    matchScore: number;
    isCustom?: boolean;
  } | null>(null);
  const [summary, setSummary] = useState("");
  const [customMajorName, setCustomMajorName] = useState("");

  // Process majors data
  const allMatchesRaw = student?.student_majors?.[0]?.matches || [];
  const allMatches = allMatchesRaw.filter((m) => !m.deleted);

  // Separate ADDIE majors and counselor/student added majors
  const addieMajors = allMatches.filter((m) => m.source === MajorSource.ADDIE);
  const topAddieMajors = addieMajors
    .sort((a, b) => b.match_percentage - a.match_percentage)
    .slice(0, 3);

  const counselorAddedMajors = allMatches.filter(
    (m) =>
      m.source === MajorSource.COUNSELOR_ADDED ||
      m.source === MajorSource.STUDENT_ADDED,
  );

  // Display majors: top 3 ADDIE + all counselor/student added
  const displayMajors = [...topAddieMajors, ...counselorAddedMajors];

  // Remaining ADDIE majors for dropdown (including deleted ones)
  const allAddieMajors = allMatchesRaw.filter(
    (m) => m.source === MajorSource.ADDIE,
  );
  const remainingAddieMajors = allAddieMajors.filter(
    (m) => !topAddieMajors.some((top) => top.id === m.id),
  );
  const remainingMajors = remainingAddieMajors;

  const handleAddMajor = async () => {
    if (summary.trim() === "") {
      toast.error("Please enter a rationale!");
      return;
    }

    if (selectedMajor) {
      try {
        if (selectedMajor.isCustom) {
          // Create new custom major
          const studentMajorId = student?.student_majors?.[0]?.id || "";
          const result = await createCustomMajor(
            studentMajorId,
            customMajorName || selectedMajor.name,
            summary,
          );

          if (result.success) {
            toast.success(`${customMajorName || selectedMajor.name} added!`);
          } else {
            toast.error(result.error || "Failed to add major");
            return;
          }
        } else {
          // Update existing major's source to COUNSELOR_ADDED
          const existingMajor = remainingMajors.find(
            (m) => m.major_name === selectedMajor.name,
          );

          if (existingMajor) {
            const result = await updateMajorSource(existingMajor.id);

            if (result.success) {
              toast.success(`${selectedMajor.name} added!`);
            } else {
              toast.error(result.error || "Failed to add major");
              return;
            }
          }
        }

        // Reset state
        setIsDialogOpen(false);
        setSelectedMajor(null);
        setSummary("");
        setCustomMajorName("");

        // Page will automatically refresh due to revalidatePath in server action
      } catch (error) {
        console.error("Error adding major:", error);
        toast.error("Failed to add major");
      }
    }
  };

  const handleDeleteMajor = async (majorId: string) => {
    try {
      const result = await deleteMajor(majorId);

      if (result.success) {
        const majorName = displayMajors.find(
          (m) => m.id === majorId,
        )?.major_name;
        toast.success(`${majorName} deleted!`);
      } else {
        toast.error(result.error || "Failed to delete major");
      }
    } catch (error) {
      console.error("Error deleting major:", error);
      toast.error("Failed to delete major");
    }
  };

  const handleLikeMajor = async (majorId: string) => {
    try {
      const result = await updateMajorLikeStatus(majorId, "like");

      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.error || "Failed to update major");
      }
    } catch (error) {
      console.error("Error liking major:", error);
      toast.error("Failed to like major");
    }
  };

  const handleDislikeMajor = async (majorId: string) => {
    try {
      const result = await updateMajorLikeStatus(majorId, "dislike");

      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.error || "Failed to update major");
      }
    } catch (error) {
      console.error("Error disliking major:", error);
      toast.error("Failed to dislike major");
    }
  };

  const handleSelectMajor = (majorName: string) => {
    // Find the selected major from remaining list
    const selectedMajor = remainingMajors.find(
      (m) => m.major_name === majorName,
    );

    if (selectedMajor) {
      // Store the selected major with existing summary prefilled
      setSelectedMajor({
        name: selectedMajor.major_name,
        matchScore: selectedMajor.match_percentage,
      });

      // Prefill summary from existing major
      setSummary(selectedMajor.summary || "");

      // Close the popover
      setIsPopoverOpen(false);

      // Open the dialog for counselor input
      setIsDialogOpen(true);
    }
  };

  const handleOtherSelect = () => {
    // Set up for custom major
    setSelectedMajor({
      name: "Custom Major",
      matchScore: 50,
      isCustom: true,
    });

    // Reset custom major name and summary
    setCustomMajorName("");
    setSummary("");

    // Close the popover
    setIsPopoverOpen(false);

    // Open the dialog for counselor input
    setIsDialogOpen(true);
  };

  // If there are no majors, show only the empty state component
  if (displayMajors.length === 0) {
    return (
      <div className="flex w-full items-center justify-center px-4 py-10">
        <EmptyMajorsState firstName={student.first_name} />
      </div>
    );
  }

  // If there are majors, show the full layout with responsive design
  return (
    <div className="w-full">
      {/* Mobile/Chat Open Personality Profile - Collapsible when chat is open or on mobile */}
      {isChatOpen && (
        <div className="mb-6">
          <Card>
            <CardHeader
              className="cursor-pointer"
              onClick={() => setIsPersonalityExpanded(!isPersonalityExpanded)}
            >
              <CardTitle className="flex items-center justify-between text-base">
                <span>Personality Profile</span>
                {isPersonalityExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </CardTitle>
            </CardHeader>
            {isPersonalityExpanded && (
              <CardContent className="pt-0">
                <PersonalityProfile oceanScores={student.ocean_scores} />
              </CardContent>
            )}
          </Card>
        </div>
      )}

      {/* Main Layout Container - Responsive based on chat state */}
      <div
        className={`flex ${isChatOpen ? "flex-col" : "flex-col lg:flex-row lg:justify-evenly lg:gap-6"}`}
      >
        {/* Main Content - Majors List */}
        <div className={`w-full space-y-6 ${isChatOpen ? "" : "lg:w-2/3"}`}>
          {/* Header with AI indicator */}
          <div className="flex items-center justify-between py-4">
            <div>
              <h2 className="text-xl font-semibold text-foreground">Majors</h2>
            </div>

            {/* Popover for adding majors */}
            <AddMajorPopover
              isOpen={isPopoverOpen}
              onOpenChange={setIsPopoverOpen}
              predefinedMajors={remainingMajors.map((m) => ({
                name: m.major_name,
                matchScore: m.match_percentage,
              }))}
              onSelectMajor={handleSelectMajor}
              onSelectOther={handleOtherSelect}
            />
          </div>

          {/* All Majors Combined */}
          <div className="space-y-4">
            {displayMajors.map((major, index) => (
              <MajorItem
                key={major.id ?? index}
                major={major}
                showRemoveButton={true}
                onRemove={handleDeleteMajor}
                onLike={handleLikeMajor}
                onDislike={handleDislikeMajor}
              />
            ))}
          </div>

          {/* Analysis Explanation */}
          <AnalysisDisclaimerCard />
        </div>

        {/* Desktop Sidebar - Personality Profile - Only visible when chat is closed */}
        {!isChatOpen && (
          <div className="hidden lg:block lg:w-1/4">
            <PersonalityProfile oceanScores={student.ocean_scores} />
          </div>
        )}
      </div>

      {/* Dialog modal for counselor input */}
      <AddMajorDialog
        isOpen={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        selectedMajor={selectedMajor}
        studentName={student.first_name || ""}
        customMajorName={customMajorName}
        onCustomMajorNameChange={setCustomMajorName}
        summary={summary}
        onSummaryChange={setSummary}
        onSave={handleAddMajor}
      />
    </div>
  );
};
