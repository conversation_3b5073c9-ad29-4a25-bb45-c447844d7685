import React from "react";
import { Plus } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";

interface MajorOption {
  name: string;
  matchScore: number;
}

interface AddMajorPopoverProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  predefinedMajors: MajorOption[];
  onSelectMajor: (majorName: string) => void;
  onSelectOther: () => void;
}

export const AddMajorPopover: React.FC<AddMajorPopoverProps> = ({
  isOpen,
  onOpenChange,
  predefinedMajors,
  onSelectMajor,
  onSelectOther,
}) => {
  return (
    <Popover open={isOpen} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm">
          <Plus className="mr-2 h-4 w-4" />
          Add Major
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <Command>
          <CommandInput placeholder="Search major..." />
          <CommandList className="max-h-[300px]">
            <CommandEmpty>No major found.</CommandEmpty>
            <CommandGroup>
              {predefinedMajors.map((major) => (
                <CommandItem
                  key={major.name}
                  value={major.name}
                  onSelect={() => onSelectMajor(major.name)}
                  className="flex cursor-pointer items-center justify-between"
                >
                  <span>{major.name}</span>
                  <span className="text-sm text-muted-foreground">
                    {major.matchScore}% match
                  </span>
                </CommandItem>
              ))}
              <CommandItem
                value="other"
                onSelect={onSelectOther}
                className="mt-2 cursor-pointer border-t pt-2"
              >
                <span>Other</span>
              </CommandItem>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default AddMajorPopover;
