import React from "react";
import Image from "next/image";
import { GraduationCap, NotepadText, Plus } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

interface EmptyMajorsStateProps {
  firstName?: string;
}

export const EmptyMajorsState: React.FC<EmptyMajorsStateProps> = ({
  firstName,
}) => {
  return (
    <div className="flex w-full flex-col items-center justify-center gap-4 rounded-lg border border-gray-300 bg-white p-12 shadow-sm">
      <Image
        className="mb-4"
        alt="no-image"
        src="/images/little-woman-with-long-list-2.png"
        width={147}
        height={158}
      />
      <h4 className="text-lg font-semibold text-gray-900">
        No majors to display yet
      </h4>
      <p className="max-w-md px-4 text-center text-sm text-gray-500 sm:px-0">
        <PERSON><PERSON> will analyze {firstName ? `${firstName}'s` : "the student's"} <strong>Big Five Personality Test (BFI)</strong> results and 
        conversations to recommend major programs that best match their personality and interests.
      </p>
    </div>
  );
};

export default EmptyMajorsState;
