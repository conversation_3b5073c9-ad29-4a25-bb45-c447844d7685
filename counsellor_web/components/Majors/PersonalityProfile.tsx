import React from "react";
import { Spark<PERSON> } from "lucide-react";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { TooltipWrapper } from "@/components/shared/TooltipWrapper";

interface OceanScores {
  id: string;
  student_id: string;
  extroversion: number;
  agreeableness: number;
  conscientiousness: number;
  neuroticism: number;
  openness_to_experience: number;
  created_at: Date;
  updated_at: Date;
}

interface PersonalityTrait {
  name: string;
  value: number | null | string;
}

interface PersonalityProfileProps {
  oceanScores?: OceanScores | null;
  traits?: PersonalityTrait[];
}

export const PersonalityProfile: React.FC<PersonalityProfileProps> = ({
  oceanScores,
  traits,
}) => {
  // Convert OCEAN scores to display format (0-40 range to 0-100 percentage)
  const getTraitsFromOceanScores = (
    scores: OceanScores,
  ): PersonalityTrait[] => {
    return [
      {
        name: "Openness",
        value: Math.round((scores.openness_to_experience / 40) * 100) ?? "N/A",
      },
      {
        name: "Conscientiousness",
        value: Math.round((scores.conscientiousness / 40) * 100) ?? "N/A",
      },
      {
        name: "Extraversion",
        value: Math.round((scores.extroversion / 40) * 100) ?? "N/A",
      },
      {
        name: "Agreeableness",
        value: Math.round((scores.agreeableness / 40) * 100) ?? "N/A",
      },
      {
        name: "Neuroticism",
        value: Math.round((scores.neuroticism / 40) * 100) ?? "N/A",
      },
    ];
  };

  // Use real OCEAN scores if available, otherwise fall back to mock data or provided traits
  const displayTraits = oceanScores
    ? getTraitsFromOceanScores(oceanScores)
    : traits || [
        { name: "Openness", value: "N/A" },
        { name: "Conscientiousness", value: "N/A" },
        { name: "Extraversion", value: "N/A" },
        { name: "Agreeableness", value: "N/A" },
        { name: "Neuroticism", value: "N/A" },
      ];

  // Function to determine rating based on value
  const getRating = (value: number) => {
    if (value >= 80) return "High";
    if (value >= 60) return "Moderate";
    return "Low";
  };

  // Function to get tooltip content for each specific trait
  const getTraitTooltipContent = (traitName: string) => {
    const tooltips = {
      Openness: `                    OPENNESS

• High: You're curious, imaginative, and eager to explore new ideas, cultures, and experiences. You enjoy novelty and creative thinking.

• Moderate: You balance trying new things with sticking to what's familiar. You can adapt without feeling like you need constant change.

• Low: You prefer the familiar and value stability. You focus on practical approaches and tried-and-true methods.

Note: There's no right level of openness—each style brings unique strengths. For example, someone high in openness might thrive in brainstorming new club ideas, while someone lower in openness might excel at improving and perfecting existing traditions.`,

      Conscientiousness: `               CONSCIENTIOUSNESS

• High: You're organized, dependable, and self-disciplined. You like setting goals, planning ahead, and following through.

• Moderate: You can be structured when it matters but also flexible when plans change.

• Low: You're more spontaneous and may prefer going with the flow over strict schedules or detailed plans.

Note: There's no right level of conscientiousness. A highly conscientious person might excel at managing a long-term project, while someone lower in conscientiousness might bring fresh, adaptable energy to situations where plans shift quickly.`,

      Extraversion: `                  EXTRAVERSION

• High: You gain energy from being around people, enjoy group activities, and like to express yourself socially.

• Moderate: You enjoy socializing but also value your alone time to recharge.

• Low: You recharge best on your own and prefer smaller, more meaningful interactions over large group settings.

Note: There's no right level of extraversion. A highly extraverted person might shine leading a team event, while someone lower in extraversion might excel in one-on-one mentoring or deep individual work.`,

      Agreeableness: `                 AGREEABLENESS

• High: You're compassionate, cooperative, and place a strong value on getting along with others.

• Moderate: You balance kindness and empathy with standing your ground when needed.

• Low: You're more direct, value honesty over harmony, and are comfortable with healthy conflict.

Note: There's no right level of agreeableness. A highly agreeable person might help bring a group together during a tough decision, while someone lower in agreeableness might spark needed change by challenging the status quo.`,

      Neuroticism: `                   NEUROTICISM

• High: You're sensitive to stress and emotions, which can also make you empathetic and aware of others' feelings.

• Moderate: You experience emotions in a balanced way, handling ups and downs without being overwhelmed.

• Low: You're generally calm, steady, and resilient, even under pressure.

Note: There's no right level of neuroticism. Someone higher in neuroticism might pick up on tensions in a group early, while someone lower might help keep everyone grounded in a stressful moment.`,
    };

    return tooltips[traitName] || "";
  };

  return (
    <Card className="sticky top-6">
      <CardHeader className="border-b">
        <CardTitle className="text-md">Personality Profile</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        {/* Personality traits display */}
        <div>
          {displayTraits.map((trait, index) => (
            <div
              key={trait.name}
              className={`px-4 py-2 ${index !== displayTraits.length - 1 ? "border-b" : ""}`}
            >
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {trait.name}
                </span>
                <div className="flex flex-col items-end">
                  <span className="text-sm font-semibold">{trait.value}%</span>
                  <TooltipWrapper
                    tooltipContent={getTraitTooltipContent(trait.name)}
                  >
                    <span className="cursor-help text-sm text-muted-foreground">
                      {getRating(trait.value as number)}
                    </span>
                  </TooltipWrapper>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="border-t p-6 text-center">
          <p className="text-center text-sm text-muted-foreground">
            <Sparkles className="mr-1 inline h-4 w-4 text-blue-600" />
            Powered by <span className="font-medium text-blue-600">Addie</span>
          </p>
          <p className="mt-1 text-center text-xs text-muted-foreground">
            This analysis based on Big 5 OCEAN Framework, Thrive Index, your
            conversations with Addie, and a sprinkle of Addie AI magic.{" "}
            {/*<button className="font-bold hover:underline">Learn more</button>*/}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default PersonalityProfile;
