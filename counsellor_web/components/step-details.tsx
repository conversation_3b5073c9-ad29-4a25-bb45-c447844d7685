import Link from "next/link";
import { Edit } from "lucide-react";

import { WorkflowStep } from "@/common/model";
import { formatDate } from "@/common/utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface StepDetailsProps {
  step: WorkflowStep;
  workflow_id: string;
  step_id: string;
}

export function StepDetails({ step, workflow_id, step_id }: StepDetailsProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Step Information</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableBody>
              <TableRow>
                <TableHead className="font-medium">ID</TableHead>
                <TableCell>{step.id}</TableCell>
              </TableRow>
              <TableRow>
                <TableHead className="font-medium">Name</TableHead>
                <TableCell>{step.name}</TableCell>
              </TableRow>
              <TableRow>
                <TableHead className="font-medium">Setter</TableHead>
                <TableCell>{step.setter}</TableCell>
              </TableRow>
              <TableRow>
                <TableHead className="font-medium">
                  Parent Workflow ID
                </TableHead>
                <TableCell>
                  <Button
                    variant="link"
                    className="h-auto p-0 font-normal"
                    asChild
                  >
                    <Link href={`/workflow/${step.parent_workflow_id}`}>
                      {step.parent_workflow_id}
                    </Link>
                  </Button>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableHead className="font-medium">Created At</TableHead>
                <TableCell>
                  {formatDate(step.created_at.toLocaleString())}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableHead className="font-medium">Updated At</TableHead>
                <TableCell>
                  {formatDate(step.updated_at.toLocaleString())}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Goal</CardTitle>
        </CardHeader>
        <CardContent className="">
          <p className="whitespace-pre-wrap pb-4">{step.goal}</p>
          <Link href={`/workflow/${workflow_id}/${step_id}/edit_goal`}>
            <Button variant="default" size="default" aria-label="Edit goal">
              <Edit className="h-4 w-4" /> Edit Goal
            </Button>
          </Link>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Data (JSON)</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="overflow-x-auto rounded-md bg-gray-100 p-4">
            <code>{JSON.stringify(step.data, null, 2)}</code>
          </pre>
        </CardContent>
      </Card>
    </div>
  );
}
