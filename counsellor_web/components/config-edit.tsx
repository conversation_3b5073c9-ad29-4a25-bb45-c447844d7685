"use client";

import React, { useState } from "react";
import { redirect } from "next/navigation";
import { delay } from "@/actions/queue";
import { saveAddieConfig, saveConfig } from "@/actions/student-agent";

import { Config } from "@/common/model";
import { ToastQueue } from "@/common/toastQueue";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    timeZoneName: "short",
  });
};

interface ConfigEditProps {
  data: Config;
}

const toastQueue = new ToastQueue();

export default function ConfigEdit({ data }: ConfigEditProps) {
  const [config, setConfig] = useState<Config>(data);

  const handlePromptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setConfig((prev) => ({
      ...prev,
      prompt: { ...prev.prompt, content: e.target.value },
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const res = await saveAddieConfig(config);
    console.log("Submitting updated config:", res);

    toastQueue.addToast({
      title: "Configuration Updated",
      description: "Your student agent configuration has been updated.",
    });

    toastQueue.addToast({
      title: "Experiment Queued",
      description: "Your student agent configuration has been updated.",
    });

    toastQueue.renderToasts();

    redirect("/config");
  };

  return (
    <div className="w-full p-4">
      <Card className="w-full max-w-4xl border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">
            Addie Configuration
          </CardTitle>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <Label className="text-sm font-medium">ID</Label>
                <p className="mt-1 text-sm">{config.id}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Prompt ID</Label>
                <p className="mt-1 text-sm">{config.prompt_id}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Created At</Label>
                <p className="mt-1 text-sm">
                  {formatDate(config.created_at.toString())}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">Updated At</Label>
                <p className="mt-1 text-sm">
                  {formatDate(config.updated_at.toString())}
                </p>
              </div>
            </div>
            <div>
              <Label htmlFor="promptContent" className="text-sm font-medium">
                Prompt Content
              </Label>
              <Textarea
                id="promptContent"
                value={config.prompt.content}
                onChange={handlePromptChange}
                className="mt-1 h-[200px]"
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" className="w-full">
              Update Configuration
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
