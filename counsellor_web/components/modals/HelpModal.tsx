"use client";

import { useState } from "react";
import { sendSupportEmail } from "@/actions/support";
// import { sendSupportEmail } from "@/actions/support"; // Server Action
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Textarea } from "@/components/ui/textarea";

interface HelpModalProps {
  onToggle: () => void;
  userEmail: string;
  userName: string;
  userId: string;
}

export default function HelpModal({
  onToggle,
  userEmail,
  userId,
  userName,
}: HelpModalProps) {
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSendEmail = async () => {
    if (!message) {
      return toast.error("Please provide a message.");
    }
    setIsSubmitting(true);
    try {
      const toastId = toast.loading("Sending email...");
      const response = await sendSupportEmail({
        message,
        userEmail,
        userName,
        userId,
      });
      if (response.success) {
        toast.success(
          "Your issue has been reported. We will contact you shortly.",
          { id: toastId },
        );
        onToggle();
      } else {
        toast.error("Failed to send message.");
      }
    } catch (error) {
      toast.error("An error occurred while sending the email.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
      onClick={onToggle}
    >
      <div
        className="w-full max-w-lg rounded-lg bg-white p-6 shadow-lg dark:bg-gray-800"
        onClick={(e) => e.stopPropagation()}
      >
        <h2 className="mb-4 text-center text-xl font-bold text-gray-900 dark:text-gray-100">
          Help and Support
        </h2>
        <Textarea
          className="w-full"
          placeholder="Describe your issue..."
          value={message}
          onChange={(e) => setMessage(e.target.value)}
        />
        <div className="mt-4 flex justify-end space-x-2">
          <Button variant="ghost" onClick={onToggle}>
            Cancel
          </Button>
          <Button
            onClick={handleSendEmail}
            className="bg-blue-500 text-white hover:bg-blue-600"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <LoadingSpinner />
                Sending...
              </div>
            ) : (
              "Send Email"
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
