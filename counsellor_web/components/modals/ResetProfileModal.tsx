"use client";

import { useState } from "react";
import { UserRole } from "@prisma/client";
import { AlertTriangle } from "lucide-react";
import { toast } from "sonner";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { resetUserProfile } from "@/actions/reset-profile";

interface ResetProfileModalProps {
  userId: string;
  userRole?: UserRole;
  onSuccess?: () => void;
  trigger: React.ReactNode;
}

export function ResetProfileModal({
  userId,
  userRole,
  onSuccess,
  trigger,
}: ResetProfileModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isResetting, setIsResetting] = useState(false);

  // Only admins should be able to use this
  const isAdmin = userRole === UserRole.ADMIN;

  if (!isAdmin) {
    return null;
  }

  const handleResetProfile = async () => {
    setIsResetting(true);
    try {
      const result = await resetUserProfile(userId);
      if (result.success) {
        toast.success("Profile data has been reset");
        setIsOpen(false);
        if (onSuccess) {
          onSuccess();
        }
      } else {
        toast.error(result.error || "Failed to reset profile");
      }
    } catch (error) {
      toast.error("An unexpected error occurred");
      console.error(error);
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Reset Profile Data
          </DialogTitle>
          <DialogDescription>
            This will permanently clear all personal information from this user
            profile and set it as incomplete, requiring them to go through
            onboarding again.
          </DialogDescription>
        </DialogHeader>

        <div className="rounded-md bg-red-50 p-4 text-sm text-red-500">
          <p className="font-medium">Warning: Admin-only feature</p>
          <p>
            This action is intended for testing purposes only. All profile data
            will be lost.
          </p>
        </div>

        <DialogFooter className="flex gap-2 sm:justify-end">
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isResetting}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleResetProfile}
            disabled={isResetting}
          >
            {isResetting ? "Resetting..." : "Reset Profile Data"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
