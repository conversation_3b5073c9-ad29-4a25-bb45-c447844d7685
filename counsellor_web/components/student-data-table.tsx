"use client";

import * as React from "react";
import { useEffect } from "react";
import Link from "next/link";
import {
  deleteStudentAgentConfig,
  getExperimentProgress,
} from "@/actions/queue";
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  Row,
  RowData,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import { ArrowUpDown, Edit, MoreHorizontal, Trash2, View } from "lucide-react";

import log from "@/common/logger";
import { StudentAgent } from "@/common/model";
import { StudentWithWorkflows } from "@/common/types";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Progress } from "@/components/ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

function openStudentAgentDetails(row: Row<RowData>) {
  const original = row.original as StudentWithWorkflows;
  const studentAgentDetailUrl = `/student_agent/${original.student_agent_id}`;

  window.open(studentAgentDetailUrl, "_blank", "noopener noreferrer");
}

const columns: ColumnDef<StudentWithWorkflows>[] = [
  // add created_at columndef
  {
    accessorKey: "created_at",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Created At
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => (
      <div className="capitalize">
        {row.getValue<Date>("created_at").toLocaleString()}
      </div>
    ),
  },
  // {
  //   accessorKey: "config_id",
  //   header: ({ column }) => (
  //     <Button
  //       variant="ghost"
  //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //     >
  //       Config ID
  //       <ArrowUpDown className="ml-2 h-4 w-4" />
  //     </Button>
  //   ),
  //   cell: ({ row }) => (
  //     <Link href={`/student_agent_config/${row.getValue("config_id")}`}>
  //       <div className="">{row.getValue("config_id")}</div>
  //     </Link>
  //   ),
  // },
  // {
  //   accessorKey: "prompt_id",
  //   header: ({ column }) => (
  //     <Button
  //       variant="ghost"
  //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //     >
  //       Prompt ID
  //       <ArrowUpDown className="ml-2 h-4 w-4" />
  //     </Button>
  //   ),
  //   cell: ({ row }) => <div className="">{row.getValue("prompt_id")}</div>,
  // },
  {
    accessorKey: "role",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Agent Role
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => <div className="capitalize">{row.getValue("role")}</div>,
  },
  // {
  //   accessorKey: "first_name",
  //   header: ({ column }) => (
  //     <Button
  //       variant="ghost"
  //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //     >
  //       First Name
  //       <ArrowUpDown className="ml-2 h-4 w-4" />
  //     </Button>
  //   ),
  //   cell: ({ row }) => (
  //     <div className="capitalize">{row.getValue("first_name")}</div>
  //   ),
  // },
  // {
  //   accessorKey: "last_name",
  //   header: ({ column }) => (
  //     <Button
  //       variant="ghost"
  //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //     >
  //       Last Name
  //       <ArrowUpDown className="ml-2 h-4 w-4" />
  //     </Button>
  //   ),
  //   cell: ({ row }) => (
  //     <div className="capitalize">{row.getValue("last_name")}</div>
  //   ),
  // },
  // {
  //   accessorKey: "email",
  //   header: ({ column }) => (
  //     <Button
  //       variant="ghost"
  //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //     >
  //       Email
  //       <ArrowUpDown className="ml-2 h-4 w-4" />
  //     </Button>
  //   ),
  //   cell: ({ row }) => <div>{row.getValue("email")}</div>,
  // },
  // {
  //   accessorKey: "grade",
  //   header: ({ column }) => (
  //     <Button
  //       variant="ghost"
  //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //     >
  //       Grade
  //       <ArrowUpDown className="ml-2 h-4 w-4" />
  //     </Button>
  //   ),
  //   cell: ({ row }) => <div>{row.getValue("grade")}</div>,
  // },
  // {
  //   accessorKey: "date_of_birth",
  //   header: ({ column }) => (
  //     <Button
  //       variant="ghost"
  //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //     >
  //       Date of Birth
  //       <ArrowUpDown className="ml-2 h-4 w-4" />
  //     </Button>
  //   ),
  //   cell: ({ row }) => (
  //     <div>{row.getValue<Date>("date_of_birth")?.toLocaleDateString()}</div>
  //   ),
  // },
  {
    accessorKey: "msgsCount",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        # Messages
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => (
      <div>{row.getValue<Number>("msgsCount")?.toString()}</div>
    ),
  },
  {
    accessorKey: "completedWFSteps",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        # Completed Steps
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => (
      <div>{row.getValue<Number>("completedWFSteps")?.toString()}</div>
    ),
  },
  // {
  //   id: "actions",
  //   enableHiding: false,
  //   cell: ({ row }) => {
  //     const student = row.original;
  //
  //     return (
  //       <DropdownMenu>
  //         <DropdownMenuTrigger asChild>
  //           <Button variant="ghost" className="h-8 w-8 p-0">
  //             <span className="sr-only">Open menu</span>
  //             <MoreHorizontal className="h-4 w-4" />
  //           </Button>
  //         </DropdownMenuTrigger>
  //         <DropdownMenuContent align="end">
  //           <DropdownMenuLabel>Actions</DropdownMenuLabel>
  //           {/*<DropdownMenuItem*/}
  //           {/*  onClick={() =>*/}
  //           {/*    navigator.clipboard.writeText(student.id.toString())*/}
  //           {/*  }*/}
  //           {/*>*/}
  //           {/*  Copy student ID*/}
  //           {/*</DropdownMenuItem>*/}
  //           <DropdownMenuSeparator />
  //           <Link href={`/experiment/${student.student_agent_id}`}>
  //             <DropdownMenuItem>View Experiment details</DropdownMenuItem>
  //           </Link>
  //           <Link href={`/student_agent_config/edit/${student.config.id}`}>
  //             <DropdownMenuItem>Edit student</DropdownMenuItem>
  //           </Link>
  //         </DropdownMenuContent>
  //       </DropdownMenu>
  //     );
  //   },
  // },
];

export default function StudentDataTable(params: {
  students: StudentWithWorkflows[];
  remaining: number;
  progress: number;
}) {
  const { students } = params;
  const [progress, setProgress] = React.useState(params.progress);
  const [lastProgress, setLastProgress] = React.useState(params.progress);
  const [remaining, setRemaining] = React.useState(params.remaining);
  const now = new Date();

  useEffect(() => {
    setInterval(async () => {
      const data = await getExperimentProgress();
      setRemaining(data.remaining);
      const progress = (1 / data.remaining) * 100;
      setProgress(progress);

      if (lastProgress > 0 && lastProgress != progress) {
        window.location.reload();
      }

      setLastProgress(progress);
    }, 5000);
  }, [lastProgress]);
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  const table = useReactTable({
    data: students,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="w-full">
      <div className="flex items-center py-4">
        {remaining > 0 && (
          <div className="w-full space-y-4 pb-4">
            <h1>Tasks remaining: {remaining}</h1>
            <Progress value={progress} className="w-full max-w-2xl" />
          </div>
        )}

        {/*<Input*/}
        {/*  placeholder="Filter emails..."*/}
        {/*  value={(table.getColumn("email")?.getFilterValue() as string) ?? ""}*/}
        {/*  onChange={(event) =>*/}
        {/*    table.getColumn("email")?.setFilterValue(event.target.value)*/}
        {/*  }*/}
        {/*  className="max-w-sm"*/}
        {/*/>*/}
        {/*<DropdownMenu>*/}
        {/*  <DropdownMenuTrigger asChild>*/}
        {/*    <Button variant="outline" className="ml-auto">*/}
        {/*      Columns <ChevronDown className="ml-2 h-4 w-4" />*/}
        {/*    </Button>*/}
        {/*  </DropdownMenuTrigger>*/}
        {/*  <DropdownMenuContent align="end">*/}
        {/*    {table*/}
        {/*      .getAllColumns()*/}
        {/*      .filter((column) => column.getCanHide())*/}
        {/*      .map((column) => {*/}
        {/*        return (*/}
        {/*          <DropdownMenuCheckboxItem*/}
        {/*            key={column.id}*/}
        {/*            className="capitalize"*/}
        {/*            checked={column.getIsVisible()}*/}
        {/*            onCheckedChange={(value) =>*/}
        {/*              column.toggleVisibility(!!value)*/}
        {/*            }*/}
        {/*          >*/}
        {/*            {column.id}*/}
        {/*          </DropdownMenuCheckboxItem>*/}
        {/*        );*/}
        {/*      })}*/}
        {/*  </DropdownMenuContent>*/}
        {/*</DropdownMenu>*/}
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  // onClick={() => openStudentAgentDetails(row)}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                  <TableCell className="text-right">
                    <Link
                      href={`/student_agent_config/edit/${row.original.config_id}`}
                    >
                      <Button
                        variant="ghost"
                        size="icon"
                        aria-label="Edit configuration"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Link
                      href={`/experiment/${row.original.student_agent_id}`}
                    >
                      <Button variant="ghost" size="icon" aria-label="Details">
                        <View className="h-4 w-4" />
                      </Button>
                    </Link>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
