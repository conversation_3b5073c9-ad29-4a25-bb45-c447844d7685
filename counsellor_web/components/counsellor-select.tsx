"use client";

import * as React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Counselor {
  id: string;
  first_name: string;
  last_name: string;
  Counselor: {
    id: string;
  } | null;
}

interface CounsellorSelectProps {
  value?: string;
  onChange: (value: string) => void;
}

export function CounsellorSelect({ value, onChange }: CounsellorSelectProps) {
  const [counselors, setCounselors] = React.useState<Counselor[]>([]);

  React.useEffect(() => {
    async function fetchCounselors() {
      const response = await fetch("/api/counselors");
      const data = await response.json();
      setCounselors(data);
    }
    fetchCounselors();
  }, []);

  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="Assigned to..." />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Counsellors</SelectItem>
        {counselors.map((counselor) => (
          <SelectItem 
            key={counselor.Counselor?.id} 
            value={counselor.Counselor?.id || ""}
          >
            {counselor.first_name} {counselor.last_name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
} 