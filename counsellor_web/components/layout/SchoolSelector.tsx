"use client";

import React from "react";
import { UserRole } from "@prisma/client";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/common/utils";
import { useSchool } from "@/app/context/SchoolContext";
import { useUser } from "@/app/context/UserContext";

interface SchoolSelectorProps {
  collapsed?: boolean;
}

export function SchoolSelector({ collapsed = false }: SchoolSelectorProps) {
  const { schools, activeSchool, setActiveSchool } = useSchool();
  const { user } = useUser();

  if (collapsed || !user || user.role !== UserRole.ADMIN || schools.length === 0) {
    return null;
  }

  return (
    <Select
      value={activeSchool || undefined}
      onValueChange={setActiveSchool}
    >
      <SelectTrigger className="h-7 w-full border-none px-0 py-1 text-xs text-muted-foreground">
        <SelectValue placeholder="Select School" />
      </SelectTrigger>
      <SelectContent>
        {schools.map((school) => (
          <SelectItem key={school.id} value={school.id}>
            {school.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
