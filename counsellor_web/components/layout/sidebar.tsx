"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { UserRole } from "@prisma/client";
import {
  MessageCircleHeart,
  PanelLeftClose,
  PanelLeftOpen,
} from "lucide-react";

import { env } from "@/env.mjs";
import urls from "@/common/config/urls.json";
import { cn } from "@/common/utils";
import { Button } from "@/components/ui/button";
import HelpModal from "@/components/modals/HelpModal";
import { Icons } from "@/components/shared/icons";
import { useSchool } from "@/app/context/SchoolContext";
import { useUI } from "@/app/context/UIContext";
import { useUser } from "@/app/context/UserContext";

import { SchoolSelector } from "./SchoolSelector";

// Moved to SchoolContext

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
  children: React.ReactNode;
}

const navigation = [
  { name: "Students", href: urls.routes.students, icon: Icons.userRound },
  {
    name: "Conversations",
    href: urls.routes.conversations,
    icon: Icons.messages,
  },
  // { name: "Settings", href: urls.routes.settings, icon: Icons.settings },
  { name: "Profile", href: urls.routes.profile, icon: Icons.profile },
];

export const isDevelopment =
  env.NEXT_PUBLIC_APP_URL.includes("localhost") ||
  window.location.hostname.includes("dev-");

// School interface moved to SchoolContext

export function Sidebar({ className, children }: SidebarProps) {
  const router = useRouter();
  const { isSidebarCollapsed, toggleSidebar } = useUI();
  const { user } = useUser();
  const { schoolName } = useSchool();

  const [isHelpOpen, setIsHelpOpen] = useState(false);

  return (
    <div className="flex h-screen w-full">
      {/* Sidebar Container */}
      <div
        className={cn(
          "relative flex h-full flex-col border-r bg-background p-4 transition-all duration-300",
          isSidebarCollapsed ? "w-[68px]" : "w-64",
          className,
        )}
      >
        {/* Toggle Button */}
        <Button
          variant="ghost"
          size="icon"
          className="absolute -right-4 top-6 z-10 h-8 w-8 rounded-full border bg-background"
          onClick={toggleSidebar}
        >
          {isSidebarCollapsed ? (
            <PanelLeftOpen className="h-4 w-4" />
          ) : (
            <PanelLeftClose className="h-4 w-4" />
          )}
        </Button>

        {/* Logo and School Name */}
        <div
          className={cn(
            "mb-4 flex cursor-pointer items-center transition-all duration-300",
            isSidebarCollapsed ? "justify-center" : "justify-start gap-3",
          )}
          onClick={() => router.push("/")}
        >
          <div
            className={cn(
              "flex items-center justify-center rounded-lg bg-black p-2 transition-all duration-300",
              isSidebarCollapsed ? "h-8 w-8" : "h-12 w-12",
            )}
          >
            <Icons.logo className="text-white" />
          </div>
          <div
            className={cn(
              "overflow-hidden transition-all",
              isSidebarCollapsed && "w-0 opacity-0",
            )}
          >
            <h1 className="text-xl font-semibold">Addie</h1>
            {user?.role === UserRole.ADMIN ? (
              <SchoolSelector collapsed={isSidebarCollapsed} />
            ) : (
              <p className="text-muted-foreground">{schoolName}</p>
            )}
          </div>
        </div>

        {/* User Profile Section */}
        {user && !isSidebarCollapsed && (
          <div className="mb-4 overflow-hidden rounded-lg border bg-background/50 p-3 shadow-sm">
            <div className="flex items-center space-x-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary">
                {user.first_name && user.last_name ? (
                  <span className="text-xs font-medium">
                    {user.first_name[0]}
                    {user.last_name[0]}
                  </span>
                ) : (
                  <Icons.user className="h-4 w-4" />
                )}
              </div>
              <div className="overflow-hidden">
                <p className="truncate text-sm font-medium">
                  {user.first_name} {user.last_name}
                </p>
                <p className="truncate text-xs text-muted-foreground">
                  {user.email}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Collapsed User Profile */}
        {user && isSidebarCollapsed && (
          <div className="mb-4 flex justify-center">
            <div
              className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary"
              title={`${user.first_name} ${user.last_name}`}
            >
              {user.first_name && user.last_name ? (
                <span className="text-xs font-medium">
                  {user.first_name[0]}
                  {user.last_name[0]}
                </span>
              ) : (
                <Icons.user className="h-4 w-4" />
              )}
            </div>
          </div>
        )}

        <span className="mb-4 text-sm font-medium">
          <span className="flex flex-col space-y-1">
            {user?.role === UserRole.ADMIN && (
              <>
                <span className="truncate rounded bg-red-500 px-2 py-1 text-xs font-bold text-white">
                  ADMIN
                </span>
              </>
            )}
            {isDevelopment && (
              <span className="truncate rounded bg-yellow-500 px-2 py-1 text-xs font-bold text-white">
                DEVELOPMENT
              </span>
            )}
          </span>
        </span>

        {/* Navigation Links */}
        <nav className="flex-1 space-y-2">
          {navigation.map((item, index) => (
            <Button
              key={index}
              variant="ghost"
              className={cn(
                "w-full justify-start",
                isSidebarCollapsed && "px-3",
              )}
              asChild
            >
              <Link href={item.href}>
                <item.icon className="size-4 shrink-0" />
                <span
                  className={cn(
                    "ml-2 transition-all",
                    isSidebarCollapsed && "w-0 opacity-0",
                  )}
                >
                  {item.name}
                </span>
              </Link>
            </Button>
          ))}
        </nav>

        {/* Bottom Links */}
        <div className="mt-auto space-y-2">
          <Button
            variant="ghost"
            className={cn("w-full justify-start", isSidebarCollapsed && "px-3")}
            asChild
          >
            <Link href="/feedback">
              <MessageCircleHeart className="size-4 shrink-0" />
              <span
                className={cn(
                  "ml-2 transition-all",
                  isSidebarCollapsed && "w-0 opacity-0",
                )}
              >
                Feedback
              </span>
            </Link>
          </Button>
          <Button
            variant="ghost"
            className="w-full justify-start"
            onClick={() => setIsHelpOpen(true)}
          >
            <Icons.help className="size-4 shrink-0" />
            <span
              className={cn(
                "ml-2 transition-all",
                isSidebarCollapsed && "w-0 opacity-0",
              )}
            >
              Help and Support
            </span>
          </Button>
          <Link href="/api/auth/signout">
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start",
                isSidebarCollapsed && "px-3",
              )}
            >
              <Icons.close className="size-4 shrink-0" />
              <span
                className={cn(
                  "ml-2 transition-all",
                  isSidebarCollapsed && "w-0 opacity-0",
                )}
              >
                Logout
              </span>
            </Button>
          </Link>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1">{children}</div>

      {/* Help Modal */}
      {isHelpOpen && (
        <HelpModal
          onToggle={() => setIsHelpOpen(false)}
          userEmail={user?.email || ""}
          userName={`${user?.first_name} ${user?.last_name}`}
          userId={user?.id || ""}
        />
      )}
    </div>
  );
}
