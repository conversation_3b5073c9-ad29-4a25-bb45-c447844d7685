import { Button } from "@/components/ui/button";
import {
  Too<PERSON>ip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Icons } from "@/components/shared/icons";

interface TooltipWrapperProps {
  children: React.ReactNode;
  tooltipContent: string;
}
export const TooltipWrapper = ({
  children,
  tooltipContent,
}: TooltipWrapperProps) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>{children}</TooltipTrigger>
        <TooltipContent>{tooltipContent}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
