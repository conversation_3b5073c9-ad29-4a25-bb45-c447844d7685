import {
  AlertTriangle,
  ArrowRight,
  ArrowUpRight,
  BookOpen,
  BotMessageSquare,
  Check,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  CircleUserRound,
  ClipboardList,
  Copy,
  CreditCard,
  File,
  FileText,
  FlaskConical,
  Goal,
  HelpCircle,
  Home,
  Image,
  Laptop,
  LayoutPanelLeft,
  LineChart,
  Loader2,
  LucideIcon,
  LucideProps,
  MessagesSquare,
  Moon,
  MoreVertical,
  Package,
  PanelLeft,
  PanelRight,
  Plus,
  Puzzle,
  Search,
  Settings,
  SquareLibrary,
  SunMedium,
  Trash,
  User,
  UserCog,
  UsersRound,
  X,
} from "lucide-react";

export type Icon = LucideIcon;

export const Icons = {
  add: Plus,
  arrowRight: ArrowRight,
  arrowUpRight: ArrowUpRight,
  billing: CreditCard,
  bookOpen: BookOpen,
  chevronLeft: ChevronLeft,
  chevronRight: ChevronRight,
  chevronDown: ChevronDown,
  chevronUp: ChevronUp,
  check: Check,
  close: X,
  copy: Copy,
  dashboard: LayoutPanelLeft,
  ellipsis: MoreVertical,
  panelLeft: PanelLeft,
  panelRight: PanelRight,
  botMessageSquare: BotMessageSquare,
  goal: Goal,
  clipboardList: ClipboardList,
  squareLibrary: SquareLibrary,
  flaskConical: FlaskConical,
  profile: CircleUserRound,
  profileConfig: UserCog,
  gitHub: ({ ...props }: LucideProps) => (
    <svg
      aria-hidden="true"
      focusable="false"
      data-prefix="fab"
      data-icon="github"
      role="img"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 496 512"
      {...props}
    >
      <path
        fill="currentColor"
        d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3 .3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5 .3-6.2 2.3zm44.2-1.7c-2.9 .7-4.9 2.6-4.6 4.9 .3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3 .7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3 .3 2.9 2.3 3.9 1.6 1 3.6 .7 4.3-.7 .7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3 .7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3 .7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z"
      ></path>
    </svg>
  ),
  google: ({ ...props }: LucideProps) => (
    <svg
      aria-hidden="true"
      focusable="false"
      data-prefix="fab"
      data-icon="google"
      role="img"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 488 512"
      {...props}
    >
      <path
        d="M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"
        fill="currentColor"
      />
    </svg>
  ),
  nextjs: ({ ...props }: LucideProps) => (
    <svg
      aria-hidden="true"
      focusable="false"
      data-prefix="fab"
      data-icon="nextjs"
      role="img"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 15 15"
      {...props}
    >
      <path
        fill="currentColor"
        d="m4.5 4.5l.405-.293A.5.5 0 0 0 4 4.5zm3 9.5A6.5 6.5 0 0 1 1 7.5H0A7.5 7.5 0 0 0 7.5 15zM14 7.5A6.5 6.5 0 0 1 7.5 14v1A7.5 7.5 0 0 0 15 7.5zM7.5 1A6.5 6.5 0 0 1 14 7.5h1A7.5 7.5 0 0 0 7.5 0zm0-1A7.5 7.5 0 0 0 0 7.5h1A6.5 6.5 0 0 1 7.5 1zM5 12V4.5H4V12zm-.905-7.207l6.5 9l.81-.586l-6.5-9zM10 4v6h1V4z"
      ></path>
    </svg>
  ),
  help: HelpCircle,
  home: Home,
  laptop: Laptop,
  lineChart: LineChart,
  logo: ({ className, ...props }: LucideProps) => (
    <svg
      className={`dark:invert-1 invert ${className}`}
      aria-hidden="true"
      focusable="false"
      data-prefix="addie"
      data-icon="nextjs"
      role="img"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 106.59 80.96"
      {...props}
    >
      <path d="M101.38,35.76c-.2-2.22-.59-4.44-1.3-6.57-.5-1.51-1.3-2.69-3.16-2.44-1.31.18-1.62-.61-1.82-1.68-1.18-6.4-5-10.63-10.95-13.03-.7-.28-1.2-.73-1.63-1.31-1.9-2.51-4.42-4.03-7.5-4.56-6.18-1.07-12.45-1.17-18.69-1.32-7.06-.16-14.12.28-21.16,1.01-3.86.4-7.03,1.91-9.46,4.91-.57.71-1.25,1.12-2.06,1.48-5.73,2.55-9.47,6.76-10.5,13.07-.18,1.09-.56,1.57-1.64,1.42-1.58-.21-2.45.62-3.04,1.97-.47,1.08-.79,2.21-1,3.36-1.23,6.9-1.37,13.84-.64,20.81.25,2.32.52,4.65,1.28,6.88.63,1.87,1.65,3.14,3.88,2.8.94-.14,1.42.3,1.44,1.29,0,.45.15.9.24,1.35,1.04,5.3,5.68,9.58,11.05,10.13,9.77.99,19.56,1.46,29.9,1.57,8.98-.15,18.47-.58,27.94-1.48,6.6-.63,11.22-4.69,12.19-11.17.19-1.24.67-1.75,1.96-1.72,2.06.05,2.66-.41,3.36-2.38.32-.91.56-1.85.73-2.8,1.28-7.16,1.24-14.37.58-21.58ZM89.08,53.8c-.49,3.46-1.27,6.9-2.3,10.24-.49,1.6-1.64,3.14-3.24,4.32-1.54,1.14-3.25,1.77-4.81,1.77H29.46c-1.56,0-3.26-.63-4.81-1.77-1.6-1.19-2.75-2.72-3.24-4.32-1.11-3.61-1.92-7.34-2.41-11.08-.48-3.72-.66-7.51-.51-11.27.11-2.88.42-5.78.9-8.62.49-2.85,1.17-5.7,2.02-8.48.49-1.6,1.65-3.14,3.24-4.32,1.54-1.14,3.25-1.77,4.81-1.77h49.27c1.56,0,3.26.63,4.81,1.77h0c1.6,1.19,2.75,2.72,3.24,4.32.94,3.04,1.66,6.17,2.16,9.31.49,3.13.76,6.31.81,9.47.05,3.47-.18,6.98-.67,10.42Z" />
      <g>
        <path d="M39.99,48.39c1.41-.07,2.77.05,4.02.34,1.17.26,2.39-.29,2.84-1.39,1.7-4.12.98-9.48-1.99-12.71-2.98-3.25-8.34-3.13-11.28.27-1.96,2.28-2.5,4.71-2.35,7.43.11,2.03.38,4.02,1.33,5.79.57,1.07,1.87,1.56,3.01,1.14,1.33-.49,2.82-.8,4.4-.88Z" />
        <path d="M68.32,48.4c1.35.12,2.63.4,3.8.82,1.27.46,2.7-.1,3.33-1.29,2.04-3.83,1.97-9.03-.46-12.52-2.52-3.62-7.85-4.21-11.21-1.23-2.25,2-3.11,4.33-3.32,7.05-.15,1.9-.16,3.8.43,5.58.44,1.32,1.8,2.1,3.16,1.82s2.77-.36,4.27-.24Z" />
      </g>
    </svg>
  ),
  media: Image,
  messages: MessagesSquare,
  moon: Moon,
  package: Package,
  page: File,
  post: FileText,
  search: Search,
  settings: Settings,
  spinner: Loader2,
  sun: SunMedium,
  trash: Trash,
  twitter: ({ ...props }: LucideProps) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      aria-hidden="true"
      focusable="false"
      data-prefix="fab"
      data-icon="twitter"
      role="img"
      {...props}
    >
      <path
        d="M14.258 10.152L23.176 0h-2.113l-7.747 8.813L7.133 0H0l9.352 13.328L0 23.973h2.113l8.176-9.309 6.531 9.309h7.133zm-2.895 3.293l-.949-1.328L2.875 1.56h3.246l6.086 8.523.945 1.328 7.91 11.078h-3.246zm0 0"
        fill="currentColor"
      />
    </svg>
  ),
  user: User,
  userRound: UsersRound,
  warning: AlertTriangle,
};
