import React from "react";
import Link from "next/link";
import { Edit } from "lucide-react";

import { Config } from "@/common/model";
import { formatDate } from "@/common/utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Markdown } from "@/components/ui/content/markdown";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";

interface ConfigDetailProps {
  config: Config;
}

export default function ConfigDetail({ config }: ConfigDetailProps) {
  return (
    <div className="w-full p-4">
      <Card className="w-full max-w-4xl border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">
            Addie Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <Label className="text-sm font-medium">ID</Label>
              <p className="mt-1 text-sm">{config.id}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Prompt ID</Label>
              <p className="mt-1 text-sm">{config.prompt_id}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Created At</Label>
              <p className="mt-1 text-sm">
                {formatDate(config.created_at.toString())}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">Updated At</Label>
              <p className="mt-1 text-sm">
                {formatDate(config.updated_at.toString())}
              </p>
            </div>
          </div>
          <div>
            {/*<Label className="text-sm font-medium">Prompt Content</Label>*/}
            {/*<p className="mt-1 w-full border-none p-4">*/}
            {/*  {config.prompt.content}*/}
            {/*</p>*/}
            <Label className="text-2xl font-bold">Prompt Content</Label>
            <div className="mt-1 w-full font-light text-gray-700">
              <Markdown content={config.prompt.content} />
            </div>
          </div>
          <div>
            <Link href={`/config/edit/${config.id}`}>
              <Button className="mt-4 w-fit">
                <Edit className="mr-2 h-4 w-4" /> Edit
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
