import { ChevronRight } from "lucide-react";

interface SettingsHeaderProps {
  currentPage: string;
}

export function SettingsHeader({ currentPage }: SettingsHeaderProps) {
  return (
    <div className="flex items-center justify-between border-b px-6 py-4">
      <div className="flex items-center gap-2 text-sm">
        <span className="text-muted-foreground">Settings</span>
        <ChevronRight className="h-4 w-4 text-muted-foreground" />
        <span>{currentPage}</span>
      </div>
    </div>
  );
}
