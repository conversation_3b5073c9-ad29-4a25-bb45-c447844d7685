import React from "react";

import { getTimezoneGroups, TIMEZONE_OPTIONS } from "@/common/timezone";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface TimezoneDropdownProps {
  onChange: (value: string) => void;
  timezone?: string;
  disabled?: boolean;
  error?: string | null;
}

const TimezoneDropdown: React.FC<TimezoneDropdownProps> = ({
  onChange,
  disabled,
  timezone,
  error,
}) => {
  const groups = getTimezoneGroups();
  return (
    <div className="my-6 w-full space-y-1 md:w-1/2">
      <h4 className="mb-2 text-sm font-semibold">Your Timezone</h4>
      <Select
        disabled={disabled}
        value={timezone || ""}
        onValueChange={(value) => onChange(value)}
      >
        <SelectTrigger>
          <SelectValue placeholder="Select your timezone" />
        </SelectTrigger>
        <SelectContent>
          {groups.map((group, groupIndex) => (
            <React.Fragment key={`group-${groupIndex}`}>
              {groupIndex > 0 && <SelectSeparator />}
              <SelectGroup>
                <SelectLabel className="pl-4 text-sm">{group}</SelectLabel>
                {TIMEZONE_OPTIONS.filter((tz) => tz.group === group).map(
                  (timezone, index) => (
                    <SelectItem
                      key={`${timezone.value}-${index}`}
                      value={timezone.value}
                      className="cursor-pointer"
                    >
                      {timezone.label}
                    </SelectItem>
                  ),
                )}
              </SelectGroup>
            </React.Fragment>
          ))}
        </SelectContent>
      </Select>
      {error && <p className="text-sm text-red-500">{error}</p>}
    </div>
  );
};

export default TimezoneDropdown;
