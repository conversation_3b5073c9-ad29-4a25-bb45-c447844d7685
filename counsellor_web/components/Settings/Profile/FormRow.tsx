import { cn } from "@/common/utils";

export function FormRow({
  label,
  description,
  children,
  className,
}: {
  label: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={cn("flex flex-col gap-6 md:flex-row", className)}>
      <div className="w-1/3">
        <h3 className="text-md mb-4 font-semibold">{label}</h3>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>
      <div className="flex-1 space-y-2">{children}</div>
    </div>
  );
}
