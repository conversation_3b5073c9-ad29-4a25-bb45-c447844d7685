"use client";

import { AvailabilityBlock, AvailabilityDay } from "@prisma/client";

import { cn } from "@/common/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

export type PreferredAvailability = {
  day: AvailabilityDay;
  block: AvailabilityBlock;
};

interface PreferredAvailabilityFormSectionProps {
  disabled?: boolean;
  availability: PreferredAvailability[];
  onChange: (updated: PreferredAvailability[]) => void;
  error?: string | null;
}

const TIME_BLOCKS = [
  { label: "Mornings (8am–12pm)", value: AvailabilityBlock.MORNING },
  { label: "Afternoons (12pm–5pm)", value: AvailabilityBlock.AFTERNOON },
  { label: "Evenings (5pm–9pm)", value: AvailabilityBlock.EVENING },
];

export function PreferredAvailabilityFormSection({
  disabled = false,
  availability,
  onChange,
  error,
}: PreferredAvailabilityFormSectionProps) {
  const handleToggle = (
    day: AvailabilityDay,
    block: AvailabilityBlock,
    checked: boolean,
  ) => {
    const exists = availability.some(
      (slot) => slot.day === day && slot.block === block,
    );

    if (checked && !exists) {
      onChange([
        ...availability,
        { day, block: block as PreferredAvailability["block"] },
      ]);
    }

    if (!checked && exists) {
      onChange(
        availability.filter(
          (slot) => !(slot.day === day && slot.block === block),
        ),
      );
    }
  };

  const renderGroup = (day: AvailabilityDay, label: string) => (
    <div className="space-y-2">
      <h4 className="text-sm font-semibold">{label}</h4>
      <div className="flex flex-wrap gap-4">
        {TIME_BLOCKS.map((block) => {
          const checked = availability.some(
            (slot) => slot.day === day && slot.block === block.value,
          );

          return (
            <Label
              key={`${day}-${block.value}`}
              className="cursor-pointer"
              htmlFor={`${day}-${block.value}`}
            >
              <div
                key={`${day}-${block.value}`}
                className={cn(
                  "flex cursor-pointer items-center space-x-2 rounded-md border p-4 px-6",
                  disabled && "cursor-not-allowed",
                )}
              >
                <Checkbox
                  id={`${day}-${block.value}`}
                  checked={checked}
                  onCheckedChange={(val) =>
                    handleToggle(day, block.value, Boolean(val))
                  }
                  disabled={disabled}
                />
                <span className={cn(disabled && "text-muted-foreground")}>
                  {block.label}
                </span>
              </div>
            </Label>
          );
        })}
      </div>
    </div>
  );

  return (
    <div className="flex flex-col justify-center gap-4 space-y-4">
      {renderGroup(AvailabilityDay.WEEKDAY, "Weekdays")}
      {renderGroup(AvailabilityDay.WEEKEND, "Weekends")}
      {error && <p className="text-sm text-red-500">{error}</p>}
    </div>
  );
}
