"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { updateProfile } from "@/actions/profile";
import { UserRole } from "@prisma/client";
// Custom deep equality function instead of using lodash
import { Pen, RefreshCw } from "lucide-react";
import * as RPNInput from "react-phone-number-input";
import { isValidPhoneNumber } from "react-phone-number-input";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { PhoneInput } from "@/components/PhoneInput/PhoneInput";
import { FormRow } from "@/components/Settings/Profile/FormRow";
import { PreferredAvailabilityFormSection } from "@/components/Settings/Profile/PreferredContactTimeFormSection";
import TimezoneDropdown from "@/components/Settings/Profile/TimezoneDropdown";
import { ResetProfileModal } from "@/components/modals/ResetProfileModal";

export function ProfileForm({ initialData }) {
  const [profile, setProfile] = useState(initialData);
  const [isEditing, setIsEditing] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string | null }>({});
  const disabledEditing = !isEditing;
  const names = [initialData.first_name, initialData.last_name];

  const router = useRouter();
  // console.log("profile ==>", profile);
  // console.log("initialData ==>", initialData);
  // console.log("Phone number ==>", profile.phone_number);

  const initials = names
    .filter((name) => name !== undefined)
    .map((name) => name.slice(0, 1)[0].toUpperCase())
    .reverse()
    .join("");

  const validateProfile = (data: typeof profile) => {
    const newErrors: { [key: string]: string | null } = {};

    if (!data.phone_number) {
      newErrors.phone_number = "Phone number is required";
    } else if (!isValidPhoneNumber(data.phone_number)) {
      newErrors.phone_number = "Invalid phone number";
    }

    if (
      !data.preferred_availability ||
      data.preferred_availability.length === 0
    ) {
      newErrors.preferred_availability = "Preferred contact time is required";
    }

    if (!data.timezone || data.timezone === "") {
      newErrors.timezone = "Timezone is required";
    }

    return newErrors;
  };

  const handleChange = <T extends keyof typeof profile>(
    field: T,
    value: (typeof profile)[T],
  ) => {
    setProfile((prev) => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    const newErrors = validateProfile(profile);

    if (Object.values(newErrors).some((val) => val !== null)) {
      setErrors(newErrors);
      toast.error("Please fill in all required fields.");
      return;
    }

    // Custom deep comparison function
  function isDeepEqual(obj1: any, obj2: any): boolean {
    // Check if primitives or null/undefined
    if (obj1 === obj2) return true;
    if (obj1 == null || obj2 == null) return false;
    if (typeof obj1 !== "object" || typeof obj2 !== "object") return false;
    
    // Handle arrays
    if (Array.isArray(obj1) && Array.isArray(obj2)) {
      if (obj1.length !== obj2.length) return false;
      return obj1.every((item, index) => isDeepEqual(item, obj2[index]));
    }
    
    // Handle Date objects
    if (obj1 instanceof Date && obj2 instanceof Date) {
      return obj1.getTime() === obj2.getTime();
    }
    
    // Compare object keys
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    
    if (keys1.length !== keys2.length) return false;
    
    return keys1.every(key => 
      Object.prototype.hasOwnProperty.call(obj2, key) && 
      isDeepEqual(obj1[key], obj2[key])
    );
  }

  if (isDeepEqual(profile, initialData)) {
    toast.info("No changes made.");
    setIsEditing(false);
    return;
  }

    try {
      await updateProfile(profile.id, profile);
      setErrors({});
      toast.success("Profile updated successfully!");
      setIsEditing(false);
      router.refresh();
    } catch (e) {
      toast.error("Failed to update profile.");
    }
  };

  const handleCancel = () => {
    setProfile(initialData);
    setIsEditing(false);
    router.refresh();
  };

  useEffect(() => {
    setProfile(initialData);
  }, [initialData]);

  return (
    <div className="w-full space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-xl font-semibold">Account Profile</h1>
          <p className="text-sm text-muted-foreground">
            Your personal information and account security settings
          </p>
        </div>
        {isEditing ? (
          <div className="flex gap-4">
            <Button variant="outline" className="gap-2" onClick={handleCancel}>
              Cancel
            </Button>
            <Button className="gap-2" onClick={handleSave}>
              Save Changes
            </Button>
          </div>
        ) : (
          <div className="flex gap-2">
            {/* Admin-only Reset Profile button */}
            {profile.role === UserRole.ADMIN && (
              <ResetProfileModal
                userId={profile.id}
                userRole={profile.role}
                onSuccess={() => router.refresh()}
                trigger={
                  <Button
                    variant="outline"
                    className="gap-2 border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Reset Profile
                  </Button>
                }
              />
            )}
            <Button
              variant="outline"
              className="gap-2 text-black"
              onClick={() => setIsEditing(true)}
            >
              <Pen className="h-4 w-4" />
              Edit
            </Button>
          </div>
        )}
      </div>
      <Separator />

      <div className="space-y-6">
        {/* Profile Section*/}
        <FormRow label="Profile" description="Your personal information">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            {/* First + Last Name */}
            <div className="space-y-1">
              <h4 className="text-sm font-medium">First Name</h4>
              <Input
                disabled
                value={profile.first_name}
                onChange={(e) => handleChange("first_name", e.target.value)}
                placeholder="First name"
              />
            </div>
            <div className="space-y-1">
              <h4 className="text-sm font-medium">Last Name</h4>
              <Input
                disabled
                value={profile.last_name}
                onChange={(e) => handleChange("last_name", e.target.value)}
                placeholder="Last name"
              />
            </div>

            {/* Grid layout placeholder */}
            <div className="space-y-1">
              {/* Empty div to maintain grid layout */}
            </div>
            <div className="space-y-1">
              {/* Empty div to maintain grid layout */}
            </div>

            {/* Gender + Pronouns */}
            <div className="space-y-1">
              <h4 className="text-sm font-medium">Gender</h4>
              <Select
                disabled
                value={profile.gender ?? ""}
                onValueChange={(value) => handleChange("gender", value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                  <SelectItem value="non-binary">Non-binary</SelectItem>
                  <SelectItem value="prefer-not-to-say">
                    Prefer not to say
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-1">
              <h4 className="text-sm font-medium">Pronouns</h4>
              <Input disabled value={profile.pronouns ?? ""} />
            </div>
          </div>
        </FormRow>
        <Separator />
        {/* Contact section */}
        <FormRow label="Contact" description="Your contact information">
          {/* Phone + Email */}
          <div className="flex gap-6">
            <div className="w-full space-y-2">
              <h3 className="text-sm font-medium">Phone</h3>

              <PhoneInput
                // disabled={disabledEditing}
                disabled={true}
                defaultCountry="US"
                placeholder="Phone number"
                value={profile.phone_number ?? ""}
                international
                countries={["US", "CA"]}
                onChange={(value: RPNInput.Value) =>
                  handleChange("phone_number", value)
                }
              />
              {errors.phone_number && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.phone_number}
                </p>
              )}
            </div>

            <div className="w-full space-y-2">
              <h3 className="text-sm font-medium">Email</h3>
              <Input
                disabled
                value={profile.email}
                onChange={(e) => handleChange("email", e.target.value)}
                placeholder="Email address"
                type="email"
              />
            </div>
          </div>
        </FormRow>

        <Separator />

        {/* Preferred contact time */}
        <FormRow label="Preferences" description="Your contact preferences">
          <div className="grow">
            <h3 className="mb-4 text-sm font-semibold">
              Preferred Contact Time
            </h3>

            {/*Timezone*/}
            <TimezoneDropdown
              timezone={profile.timezone ?? null}
              onChange={(value) => handleChange("timezone", value)}
              disabled={disabledEditing}
              error={errors.timezone}
            />

            <h4 className="my-4 text-sm text-muted-foreground">
              All times below are in your timezone
            </h4>
            {/* Preferred Availability */}
            <PreferredAvailabilityFormSection
              availability={profile.preferred_availability ?? []}
              onChange={(value) =>
                handleChange("preferred_availability", value)
              }
              disabled={disabledEditing}
              error={errors.preferred_availability}
            />
          </div>
        </FormRow>
      </div>
    </div>
  );
}
