import React from "react";

import log from "@/common/logger";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";

type MessageType = "tool" | "Addie" | "Student Agent";

interface Message {
  type: MessageType;
  content: string;
  created_at: string | undefined;
}

const ChatMessage: React.FC<Message> = ({ type, content, created_at }) => {
  const getMessageStyle = (type: MessageType) => {
    switch (type) {
      case "tool":
        return "bg-gray-100 text-gray-800";
      case "Addie":
        return "bg-blue-100 text-blue-800";
      case "Student Agent":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getAvatar = (type: MessageType) => {
    switch (type) {
      case "tool":
        return { src: "/tool-avatar.png", fallback: "TL" };
      case "Addie":
        return { src: "/ai-avatar.png", fallback: "A" };
      case "Student Agent":
        return { src: "/human-avatar.png", fallback: "SA" };
      default:
        return { src: "/default-avatar.png", fallback: "DE" };
    }
  };

  const formatToolContent = (content: string) => {
    try {
      const parsedContent = JSON.parse(content);
      return (
        <div>
          {/*<p>*/}
          {/*  <strong>ID:</strong> {parsedContent.id}*/}
          {/*</p>*/}
          {/*<p>*/}
          {/*  <strong>Student ID:</strong> {parsedContent.student_id}*/}
          {/*</p>*/}
          <p>
            <strong>Question ID:</strong> {parsedContent.question_id}
          </p>
          <p>
            <strong>Response:</strong> {parsedContent.response}
          </p>
          {/*<p>*/}
          {/*  <strong>Questionnaire ID:</strong> {parsedContent.questionnaire_id}*/}
          {/*</p>*/}
        </div>
      );
    } catch (error) {
      return <p>{content}</p>;
    }
  };

  const avatar = getAvatar(type);

  return (
    <div
      className={`mb-4 flex items-start space-x-4 rounded-lg p-4 ${getMessageStyle(type)}`}
    >
      <Avatar>
        <AvatarImage src={avatar.src} alt={type} />
        <AvatarFallback>{avatar.fallback}</AvatarFallback>
      </Avatar>
      <div className="flex-1">
        <p className="mb-1 font-semibold capitalize">{type}</p>
        <div className="text-sm">
          {type === "tool" ? formatToolContent(content) : <p>{content}</p>}
        </div>
        {created_at && (
          <p className="mt-1 text-xs text-gray-500">
            {new Date(created_at).toLocaleString()}
          </p>
        )}
      </div>
    </div>
  );
};

interface ChatUIProps {
  messages?: any;
}

export default function ChatUI({ messages }: ChatUIProps) {
  return (
    <div className="mt-4">
      {messages.map((message, index) => (
        <ChatMessage key={index} {...message} />
      ))}
    </div>
  );
}
