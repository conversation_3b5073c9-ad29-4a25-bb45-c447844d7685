"use client";

import React, { useEffect } from "react";

import { SidebarProvider } from "@/components/ui/sidebar";
import { AIChatBox } from "@/components/AIChatBox/AIChatBox";
// import { AIChatAssistant } from "@/components/AIChatAssistant/AIChatAssistant";
import { StudentHeader } from "@/components/student/student-header";
import { StudentTabs } from "@/components/student/student-tabs";
import { NotesProvider } from "@/app/context/NotesContext";
import { useUI } from "@/app/context/UIContext";

interface StudentContentProps {
  student: any;
  metrics: any;
  messages: any[];
  onboarding: any[];
  teacherComments: any[];
  qnaireResponses: any[];
  user;
  teacherCommentsSummary: any[];
  studentChatSummary: any[];
  counselor;
  lastMsgId: number;
}

export function StudentContent({
  student,
  metrics,
  messages,
  onboarding,
  teacherComments,
  qnaireResponses,
  user,
  teacherCommentsSummary,
  studentChatSummary,
  counselor,
  lastMsgId,
}: StudentContentProps) {
  const {
    isChatOpen,
    toggleChat,
    isSidebarCollapsed,
    setIsChatOpen,
    setIsSidebarCollapsed,
  } = useUI();

  useEffect(() => {
    setIsChatOpen(true);
    setIsSidebarCollapsed(true);
    // Reset scroll position when student changes
    window.scrollTo(0, 0);
  }, [setIsChatOpen, setIsSidebarCollapsed, student.id]);

  return (
    <SidebarProvider>
      <NotesProvider studentId={student.id} counselorId={counselor.id}>
        <div className="relative flex h-screen w-full">
          <div
            className={`overflow-y-auto transition-all duration-300 ${
              isChatOpen && !isSidebarCollapsed
                ? "w-[calc(50%-8rem)] pr-6"
                : isChatOpen
                  ? "w-1/2 pr-6"
                  : "w-full"
            }`}
          >
            <StudentHeader
              student={{
                first_name: student.first_name || "N/A",
                last_name: student.last_name || "N/A",
                email: student.email || "N/A",
                student_id: student.id || "N/A",
              }}
              className={isChatOpen ? "mr-3" : ""}
              onChatClick={toggleChat}
            />
            {/*<StudentMetrics metrics={metrics} />*/}
            <StudentTabs
              student={student}
              messages={messages}
              onboarding={onboarding}
              teacherComments={teacherComments}
              qnaireResponses={qnaireResponses}
              metrics={metrics}
              teacherCommentsSummary={teacherCommentsSummary}
              studentChatSummary={studentChatSummary}
              counselor={counselor}
            />
            {isChatOpen && (
              <div className="fixed right-0 top-0 z-40 h-full w-1/2 border-l bg-white">
                <AIChatBox
                  lastMsgId={lastMsgId}
                  isOpen={isChatOpen}
                  onClose={toggleChat}
                  studentId={student.id}
                  userId={student.users[0]?.id!}
                  messages={messages}
                  user={user}
                  counselor={counselor}
                />
              </div>
            )}
          </div>
        </div>
      </NotesProvider>
    </SidebarProvider>
  );
}
