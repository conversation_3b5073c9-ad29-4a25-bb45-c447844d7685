"use client";

import { useEffect } from "react";
import matter from "gray-matter";
import { useTheme } from "next-themes";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

import log from "@/common/logger";
import { cn } from "@/common/utils";
import { components } from "@/components/ui/content/mdx-components";

export function Markdown({
  content,
  className,
}: {
  content: string;
  className?: string;
}) {
  let markdown = content;
  let parsingError = false;

  try {
    const result = matter(content);
    markdown = result.content;
  } catch (error) {
    log.error(error);
    parsingError = true;
  }

  const { setTheme } = useTheme();

  useEffect(() => {
    setTheme("light");
  }, [setTheme]);

  // Create a modified version of components where p tags are replaced with div
  const markdownComponents = {
    ...components,
    // Override the paragraph component to use div instead
    p: ({ className: compClassName, ...props }: any) => (
      <div
        className={cn(
          "markdown-paragraph leading-7 [&:not(:first-child)]:mt-6",
          compClassName,
        )}
        {...props}
      />
    ),
  };

  return (
    <div className={className}>
      {!parsingError && (
        <ReactMarkdown
          components={markdownComponents as any}
          remarkPlugins={[remarkGfm]}
        >
          {markdown}
        </ReactMarkdown>
      )}
      {parsingError && <div className="markdown-error">{markdown}</div>}
    </div>
  );
}
