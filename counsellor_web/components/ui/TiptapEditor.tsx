"use client";

import type React from "react";
import { useEffect, useMemo } from "react";
import BulletList from "@tiptap/extension-bullet-list";
import ListItem from "@tiptap/extension-list-item";
import TextStyle from "@tiptap/extension-text-style";
import Underline from "@tiptap/extension-underline";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { Bold, Italic, List, UnderlineIcon } from "lucide-react";
import markdownIt from "markdown-it";
import TurndownService from "turndown";

import { Button } from "@/components/ui/button";

interface TiptapEditorProps {
  initialContent?: string;
  onChange: (content: string) => void;
}

const TiptapEditor: React.FC<TiptapEditorProps> = ({
  initialContent = "",
  onChange,
}) => {
  // Use useMemo to prevent recreating these on every render
  const md = useMemo(() => markdownIt(), []);
  const turndownService = useMemo(() => new TurndownService(), []);

  const editor = useEditor({
    extensions: [
      StarterKit,
      TextStyle,
      Underline,
      BulletList.configure({ HTMLAttributes: { class: "list-disc ml-5" } }),
      ListItem,
    ],
    content: md.render(initialContent),
    onUpdate: ({ editor }) => {
      const updatedContent = turndownService.turndown(editor.getHTML());
      if (updatedContent !== initialContent) {
        onChange(updatedContent);
      }
    },
    autofocus: true,
  });

  useEffect(() => {
    if (
      editor &&
      initialContent !== turndownService.turndown(editor.getHTML())
    ) {
      editor.commands.setContent(md.render(initialContent), false);
    }
  }, [initialContent, editor, md, turndownService]); // Added all required dependencies

  if (!editor) return null;

  return (
    <div className="rounded-md border border-gray-300 p-4">
      {/* 🛠️ Tools */}
      <div className="mb-2 flex gap-2">
        <Button
          variant={editor.isActive("bold") ? "default" : "outline"}
          size="sm"
          onClick={() => editor.chain().focus().toggleBold().run()}
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button
          variant={editor.isActive("italic") ? "default" : "outline"}
          size="sm"
          onClick={() => editor.chain().focus().toggleItalic().run()}
        >
          <Italic className="h-4 w-4" />
        </Button>
        <Button
          variant={editor.isActive("underline") ? "default" : "outline"}
          size="sm"
          onClick={() => editor.chain().focus().toggleUnderline().run()}
        >
          <UnderlineIcon className="h-4 w-4" />
        </Button>
        <Button
          variant={editor.isActive("bulletList") ? "default" : "outline"}
          size="sm"
          onClick={() => editor.chain().focus().toggleBulletList().run()}
        >
          <List className="h-4 w-4" />
        </Button>
      </div>

      {/* 📝 Editor */}
      <EditorContent
        editor={editor}
        className="h-64 max-h-[500px] min-h-[300px] cursor-text overflow-auto rounded-md border p-3 focus:outline-none"
      />
      {/* Apply editor styles directly without jsx style tag */}
      <style>{`
        .ProseMirror {
          min-height: 100%;
          outline: none;
        }

        .ProseMirror p {
          margin: 0;
        }

        .ProseMirror p.is-editor-empty:first-child::before {
          color: #adb5bd;
          content: attr(data-placeholder);
          float: left;
          height: 0;
          pointer-events: none;
        }
      `}</style>
    </div>
  );
};

export default TiptapEditor;
