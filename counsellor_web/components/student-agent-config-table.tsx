"use client";

import Link from "next/link";
import { redirect } from "next/navigation";
import { deleteStudentAgentConfig } from "@/actions/queue";
import { Edit, Plus, Trash2 } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface StudentAgentConfigTableProps {
  configs?: any;
}

export function StudentAgentConfigTable({
  configs,
}: StudentAgentConfigTableProps) {
  function redirectToCreatePage() {
    redirect("/student_agent_config/create");
  }

  return (
    <div className="p-4">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold">Student Agent Configurations</h1>
        <Button onClick={redirectToCreatePage}>
          <Plus className="mr-2 h-4 w-4" /> Create
        </Button>
      </div>
      <div className="overflow-hidden rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Prompt ID</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead>Updated At</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {configs.map((config) => (
              <TableRow key={config.id}>
                <TableCell className="font-medium">{config.id}</TableCell>
                <TableCell>{config.role}</TableCell>
                <TableCell>{config.prompt_id}</TableCell>
                <TableCell>
                  {new Date(config.created_at).toLocaleString()}
                </TableCell>
                <TableCell>
                  {new Date(config.updated_at).toLocaleString()}
                </TableCell>
                <TableCell className="text-right">
                  <Link href={`/student_agent_config/${config.id}`}>
                    <Button
                      variant="ghost"
                      size="icon"
                      aria-label="Edit configuration"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </Link>

                  <Button
                    variant="ghost"
                    size="icon"
                    aria-label="Delete configuration"
                    onClick={async () => {
                      await deleteStudentAgentConfig(config.id);
                      // reload the page
                      window.location.reload();
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
