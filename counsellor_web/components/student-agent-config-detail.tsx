import React from "react";
import Link from "next/link";
import { Edit } from "lucide-react";

import { StudentAgentConfig } from "@/common/model";
import { formatDate } from "@/common/utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Markdown } from "@/components/ui/content/markdown";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";

interface StudentAgentConfigDetailProps {
  config: StudentAgentConfig;
}

export default function StudentAgentConfigDetail({
  config,
}: StudentAgentConfigDetailProps) {
  return (
    <div className="p-4">
      <Card className="mx-auto w-full max-w-4xl border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">
            Student Agent Configuration Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <Label className="text-sm font-medium">ID</Label>
              <p className="mt-1 text-sm">{config.id}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Role</Label>
              <p className="mt-1 text-sm">{config.role}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Prompt ID</Label>
              <p className="mt-1 text-sm">{config.prompt_id}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Created At</Label>
              <p className="mt-1 text-sm">
                {formatDate(config.created_at.toString())}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">Updated At</Label>
              <p className="mt-1 text-sm">
                {formatDate(config.updated_at.toString())}
              </p>
            </div>
          </div>
          <div>
            <Label className="text-2xl font-bold">Prompt Content</Label>
            <div className="mt-1 w-full font-light text-gray-700">
              <Markdown content={config.prompt.content} />
            </div>
          </div>
          <div>
            <Link href={`/student_agent_config/edit/${config.id}`}>
              <Button className="mt-4 w-fit">
                <Edit className="mr-2 h-4 w-4" /> Edit
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
