import React from "react";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import { Icons } from "@/components/shared/icons";

const ComingSoonOverlay = ({ ht = true }) => {
  return (
    <div className="flex w-full flex-col">
      <Link href="/" className="text-lg">
        <Button variant="ghost" className="m-4 mb-2 flex items-center">
          <Icons.chevronLeft style={{ width: "30px", height: "30px" }} />
          Back to students
        </Button>
      </Link>
      <div
        className={`w-full ${ht ? "h-full" : "h-[83.5%]"} font-oswald pointer-event-none z-0 flex items-center justify-center overflow-hidden bg-[radial-gradient(circle,_rgba(37,35,49,1)_0%,_rgba(37,35,49,0.7)_50%,_rgba(37,35,49,0.2)_100%)]`}
      >
        <div className="relative grid text-center text-[100px] font-bold uppercase leading-none">
          {/* Top Half */}
          <p className="clip-path-[polygon(0%_0%,_100%_0%,_100%_48%,_0%_48%)] text-white shadow-[0px_2px_4px_rgba(0,0,0,0.5)]">
            Coming Soon
          </p>

          {/* Bottom Half */}
          <p className="clip-path-[polygon(0%_48%,_100%_48%,_100%_100%,_0%_100%)] m-0 translate-x-[-0.022em] translate-y-[0.018em] bg-gradient-to-b from-black via-black to-white bg-clip-text text-transparent">
            Coming Soon
          </p>
        </div>
      </div>
    </div>
  );
};

export default ComingSoonOverlay;
