"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { router } from "next/client";
import { useRouter } from "next/navigation";
import {
  deleteWorkflowStep,
  QuestionItem,
} from "@/actions/conversations/conversation-workflow";
import { WorkflowType } from "@prisma/client";

import log from "@/common/logger";
import { hash, slugify } from "@/common/utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { MultipleChoiceQuestion } from "@/components/Conversations/QuestionBuilder/MultipleChoiceQuestion";
import { MultiSelectTags } from "@/components/Conversations/QuestionBuilder/MultiSelectTags";
import { QuestionTypeSelector } from "@/components/Conversations/QuestionBuilder/QuestionTypeSelector";
import { TextQuestion } from "@/components/Conversations/QuestionBuilder/TextQuestion";
import DestructiveModal from "@/components/destructive-modal";

interface MultiQuestionBuilderProps {
  initialQuestions?: QuestionItem[];
  onChange: (
    forms: QuestionItem[],
    allValid: boolean,
    description?: string,
    structuredType?: string,
  ) => void;
  saveButtonComponent?: React.ReactNode;
  initialDescription?: string;
  conversation?: {
    workflow_type?: string;
    tags?: string[];
  };
}

// Labels for the Big Five personality traits OCEAN
export const ALL_LABELS = [
  "openness",
  "conscientiousness",
  "extroversion",
  "agreeableness",
  "neuroticism",
];

export default function MultiQuestionBuilder({
  initialQuestions = [],
  onChange,
  saveButtonComponent,
  initialDescription = "",
  conversation,
}: MultiQuestionBuilderProps) {
  const [builderForms, setBuilderForms] = useState<QuestionItem[]>(
    () => initialQuestions || [],
  );
  const [description, setDescription] = useState(initialDescription);
  const [descriptionError, setDescriptionError] = useState<string | null>(null);

  // Extract current type from tags
  const getCurrentType = () => {
    if (!conversation?.tags) return "General";
    const typeTag = conversation.tags.find(tag => tag.startsWith("type:"));
    return typeTag ? typeTag.replace("type:", "") : "General";
  };

  const [structuredType, setStructuredType] = useState<string>(getCurrentType());
  const router = useRouter();
  // formErrors: key = question.id, value = array of error messages
  const [formErrors, setFormErrors] = useState<Record<string, string[]>>({});

  // Define validateForm first since it's used by validateAllForms
  const validateForm = useCallback((form: QuestionItem): string[] => {
    const errors: string[] = [];

    // Require question text
    if (!form.text.trim()) {
      errors.push("Question text is required");
    }

    // Require question type
    if (!form.type) {
      errors.push("Question type is required");
    }

    // For multiple-choice, ensure at least one non-empty option
    if (form.type === "multiple-choice") {
      const nonEmptyOptions =
        form.options?.filter((opt) => opt.trim().length > 0) || [];
      if (nonEmptyOptions.length === 0) {
        errors.push(
          "At least one non-empty option is required for multiple-choice questions",
        );
      }

      // check empty options
      if (
        form.options?.some(
          (option) => option.trim() === "" || option.length === 0,
        )
      ) {
        errors.push(
          "Some options are empty. Please remove or fill all options.",
        );
      }

      // check duplicated options
      const duplicateOptions = nonEmptyOptions.filter(
        (option, index) => nonEmptyOptions.indexOf(option) !== index,
      );

      if (duplicateOptions.length > 0) {
        errors.push("Multiple-choice options must be unique");
      }
    }

    // For short-answer and long-answer, validate minimum character requirements
    if (form.type === "short-answer" || form.type === "long-answer") {
      const minChars = form.characterLimit?.min;
      if (!minChars || minChars < 1) {
        errors.push(
          `Please specify a minimum character count for this ${form.type} question`,
        );
      }
    }

    // For likert type questions
    if (form.type === "likert") {
      const options = form.options || [];
      const nonEmptyOptions = options.filter((opt) => opt.trim().length > 0);

      if (nonEmptyOptions.length < 3) {
        errors.push("Likert scale must have at least 3 non-empty options.");
      }

      if (options.some((opt) => opt.trim() === "")) {
        errors.push(
          "Some Likert options are empty. Please fill or remove them.",
        );
      }

      const duplicateOptions = nonEmptyOptions.filter(
        (opt, idx) => nonEmptyOptions.indexOf(opt) !== idx,
      );

      if (duplicateOptions.length > 0) {
        errors.push("Likert options must be unique.");
      }
    }

    return errors;
  }, []);

  /**
   * Validate all forms and store errors in state
   * Using useCallback to prevent recreation on each render
   */
  const validateAllForms = useCallback(
    (forms: QuestionItem[]) => {
      const newErrors: Record<string, string[]> = {};

      // First pass: Validate individual questions
      forms.forEach((f) => {
        const errs = validateForm(f);
        if (errs.length > 0) {
          newErrors[f.id] = errs;
        }
      });

      // Second pass: Detect duplicate texts
      const textFrequency: Record<string, number> = {};

      // Count occurrences of each normalized text
      forms.forEach((f) => {
        const normalized = f.text.trim().toLowerCase();
        if (normalized) {
          // Skip empty texts
          textFrequency[normalized] = (textFrequency[normalized] || 0) + 1;
        }
      });

      // Add errors for duplicates
      forms.forEach((f) => {
        const normalized = f.text.trim().toLowerCase();
        if (normalized && textFrequency[normalized] > 1) {
          if (!newErrors[f.id]) newErrors[f.id] = [];
          newErrors[f.id].push(
            "Duplicate question text detected, please rename it.",
          );
        }
      });

      setFormErrors(newErrors);
    },
    [validateForm],
  );

  // Re-validate whenever the builder forms change
  useEffect(() => {
    validateAllForms(builderForms);
  }, [builderForms, validateAllForms]);

  // Validate description whenever it changes
  useEffect(() => {
    if (description.trim().length === 0) {
      setDescriptionError("Please provide a conversation introduction.");
    } else {
      setDescriptionError(null);
    }
  }, [description]);

  // allValid = "we have at least one question OR we have a description" AND "no form has errors" AND "all fields are filled"
  const allValid =
    // Must have at least one question OR
    (builderForms.length > 0 ||
      // Description must not be empty
      description.trim().length > 0) &&
    // No description error
    descriptionError === null &&
    // No validation errors
    Object.values(formErrors).every((errs) => errs.length === 0) &&
    // All questions must have their required fields filled
    builderForms.every((form) => {
      const hasText = form.text.trim().length > 0;
      const hasType = !!form.type;
      // const hasValidOptions =
      //   form.type !== "multiple-choice" ||
      //   form.type !== "likert" ||
      //   (form.options && form.options.some((opt) => opt.trim().length > 0));
      const hasValidOptions =
        form.type === "multiple-choice" || form.type === "likert"
          ? form.options && form.options.some((opt) => opt.trim().length > 0)
          : true;
      return hasText && hasType && hasValidOptions;
    });

  // Inform the parent whenever questions or validity changes
  useEffect(() => {
    // Pass both the questions and validation state to parent
    onChange(builderForms, allValid, description, structuredType);

    // Highlight any error fields for user attention
    // This adds visual feedback when there are validation problems
    validateAllForms(builderForms);
  }, [builderForms, allValid, onChange, validateAllForms, description, structuredType]);

  const addNewBuilder = () => {
    const newBuilder: QuestionItem = {
      id: Math.random().toString(36).substr(2, 9),
      text: "",
      type: "multiple-choice",
      options: [""],
      canSkip: false,
      workflowStepId: "",
      tags: [],
    };
    setBuilderForms((prev) => [...prev, newBuilder]);
  };

  const updateBuilderForm = (id: string, updatedForm: QuestionItem) => {
    setBuilderForms((prev) =>
      prev.map((form) => (form.id === id ? updatedForm : form)),
    );
  };

  // Remove a builder form if the user wants to delete it
  const removeBuilderForm = async (id: string) => {
    // Find the form to be removed
    const formToRemove = builderForms.find((form) => form.id === id);

    if (!formToRemove) return;

    // If the form has a workflowStepId, it means it's already saved in the database
    if (formToRemove.workflowStepId) {
      // Call the server action to delete the workflow step and related question
      const updatedQuestions = await deleteWorkflowStep(
        formToRemove.workflowStepId,
      );
      // console.log("updatedQuest ==>", updatedQuestions);

      log.debug(`# number of questions remaining ${updatedQuestions.length}`);

      // Update the builder forms with the returned questions
      setBuilderForms(updatedQuestions);
    } else {
      // If the form doesn't have a workflowStepId, it's not saved yet, so just remove it from state
      setBuilderForms((prev) => prev.filter((form) => form.id !== id));
    }

    router.refresh();
  };

  return (
    <div className="mx-auto w-3/4 space-y-6">
      <div className="space-y-2">
        <Label
          htmlFor="conversation-description"
          className="flex items-baseline gap-1"
        >
          <span>About This Conversation</span>
          <span className="mr-1 text-red-500">*</span>
        </Label>
        <Textarea
          id="conversation-description"
          placeholder="Introduce this conversation to students. Explain its purpose, what they'll learn, and how it will help them. This text will be visible at the start of the conversation."
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          className={`min-h-[200px] ${descriptionError ? "border-red-500" : ""}`}
        />
        {descriptionError && (
          <p className="mt-1 text-sm text-red-500">{descriptionError}</p>
        )}
      </div>

      {/* Structured Type Dropdown - Only show for STRUCTURED workflows */}
      {conversation?.workflow_type === WorkflowType.STRUCTURED && (
        <div className="space-y-2">
          <Label htmlFor="structured-type">Structured Type</Label>
          <Select
            value={structuredType}
            onValueChange={setStructuredType}
          >
            <SelectTrigger className="w-full">
              {structuredType}
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="General">
                <div className="flex flex-col gap-1">
                  <span className="font-medium">General</span>
                  <span className="text-sm text-muted-foreground">
                    Standard structured conversation
                  </span>
                </div>
              </SelectItem>
              <SelectItem value="The Big Five Personality Test (BFPT)">
                <div className="flex flex-col gap-1">
                  <span className="font-medium">The Big Five Personality Test (BFPT)</span>
                  <span className="text-sm text-muted-foreground">
                    Short survey for student&apos;s big five personality traits
                  </span>
                </div>
              </SelectItem>
              <SelectItem value="Comprehensive Inventory of Thriving (CIT)">
                <div className="flex flex-col gap-1">
                  <span className="font-medium">Comprehensive Inventory of Thriving (CIT)</span>
                  <span className="text-sm text-muted-foreground">
                    Detailed assessment for comprehensive student evaluation
                  </span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}

      {builderForms.map((form, index) => {
        const errorsForThisForm = formErrors[form.id] || [];
        return (
          <Card key={form.id}>
            <CardContent className="p-6">
              <div className="space-y-6">
                <div>
                  <Label htmlFor={`question-${form.id}`}>
                    Question {index + 1}
                    <span className="ml-1 text-red-500">*</span>
                  </Label>
                  <Input
                    id={`question-${form.id}`}
                    value={form.text}
                    onChange={(e) =>
                      updateBuilderForm(form.id, {
                        ...form,
                        text: e.target.value,
                      })
                    }
                    placeholder="Enter your question"
                    className="mt-2"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Question Type</Label>
                  <QuestionTypeSelector
                    value={form.type}
                    onChange={(type) =>
                      updateBuilderForm(form.id, {
                        ...form,
                        type,
                        options:
                          type === "multiple-choice"
                            ? [""]
                            : type === "likert"
                              ? [
                                  "Strongly agreed",
                                  "Agreed",
                                  "Neutral",
                                  "Disagreed",
                                  "Strongly disagreed",
                                ]
                              : undefined,
                        characterLimit:
                          type === "short-answer"
                            ? { min: 50, max: undefined }
                            : type === "long-answer"
                              ? { min: 200, max: undefined }
                              : undefined,
                      })
                    }
                  />
                </div>
                <div className="space-y-4">
                  {form.type === "multiple-choice" && (
                    <MultipleChoiceQuestion
                      options={form.options || []}
                      onChange={(options) =>
                        updateBuilderForm(form.id, { ...form, options })
                      }
                    />
                  )}
                  {(form.type === "short-answer" ||
                    form.type === "long-answer") && (
                    <TextQuestion
                      type={form.type}
                      characterLimit={
                        form.characterLimit || {
                          min: undefined,
                          max: undefined,
                        }
                      }
                      onChange={(characterLimit) =>
                        updateBuilderForm(form.id, { ...form, characterLimit })
                      }
                    />
                  )}
                  {form.type === "binary" && (
                    <div className="text-sm text-muted-foreground">
                      This is a binary (yes/no) question.
                    </div>
                  )}

                  {form.type === "likert" && (
                    <MultipleChoiceQuestion
                      options={form.options || []}
                      onChange={(options) =>
                        updateBuilderForm(form.id, { ...form, options })
                      }
                    />
                  )}
                </div>
                {/* Skippable Toggle */}
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id={`can-skip-${form.id}`}
                      checked={form.canSkip}
                      onCheckedChange={(checked) =>
                        updateBuilderForm(form.id, {
                          ...form,
                          canSkip: checked,
                        })
                      }
                    />
                    {/*Required / Optional toggle*/}
                    <Label htmlFor={`can-skip-${form.id}`}>
                      {
                        <>
                          <span
                            className={
                              form.canSkip
                                ? "text-muted-foreground"
                                : "font-bold"
                            }
                          >
                            Required
                          </span>
                          /
                          <span
                            className={
                              form.canSkip
                                ? "font-bold"
                                : "text-muted-foreground"
                            }
                          >
                            Optional
                          </span>
                        </>
                      }
                    </Label>
                  </div>
                  {/* A short line explaining the toggle's purpose */}
                  <p className="text-xs text-muted-foreground">
                    Toggle this to let students skip the question (optional)
                  </p>
                </div>

                {/* Assign tags to the question*/}

                <div className="space-y-2">
                  <Label>
                    Assign Label{" "}
                    <span className="text-muted-foreground">(Optional)</span>{" "}
                  </Label>
                  <MultiSelectTags
                    availableTags={ALL_LABELS}
                    selectedTags={form.tags ?? []}
                    onChange={(tags) =>
                      updateBuilderForm(form.id, { ...form, tags })
                    }
                  />
                </div>

                {/* Show errors if any */}
                {errorsForThisForm.length > 0 && (
                  <div className="mt-4 rounded-md bg-red-50 p-3">
                    <p className="font-medium text-red-600">
                      Please fix the following issues to enable autosave:
                    </p>
                    <ul className="list-disc pl-5 text-red-600">
                      {errorsForThisForm.map((err, i) => (
                        <li key={i}>{err}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Optionally, add a remove button */}
                <div className="flex justify-end">
                  {/*<Button*/}
                  {/*  variant="outline"*/}
                  {/*  onClick={() => removeBuilderForm(form.id)}*/}
                  {/*>*/}
                  {/*  Remove*/}
                  {/*</Button>*/}
                  <DestructiveModal
                    title="Remove this question"
                    description="Are you sure you would want to remove this question?"
                    btnTitle="Remove Question"
                    className="border"
                    variant="ghost"
                    action="Removing..."
                    handler={() => removeBuilderForm(form.id)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
      <div
        className={`flex justify-center ${builderForms?.length > 0 ? "border-t-2" : ""} border-t-gray-400 pt-4`}
      >
        <Button variant="outline" onClick={addNewBuilder}>
          + Add Question
        </Button>
      </div>
      {builderForms.length >= 3 && saveButtonComponent}
    </div>
  );
}
