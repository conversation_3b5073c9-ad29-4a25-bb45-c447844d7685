import { QuestionType } from "@/actions/conversations/conversation-workflow";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface QuestionTypeSelectorProps {
  value: QuestionType;
  onChange: (value: QuestionType) => void;
}

export function QuestionTypeSelector({
  value,
  onChange,
}: QuestionTypeSelectorProps) {
  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger className="w-[200px]">
        <SelectValue placeholder="Select question type" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="multiple-choice">Multiple choice</SelectItem>
        <SelectItem value="binary">Binary</SelectItem>
        <SelectItem value="short-answer">Short answer</SelectItem>
        <SelectItem value="long-answer">Long answer</SelectItem>
        <SelectItem value="likert">Likert</SelectItem>
      </SelectContent>
    </Select>
  );
}
