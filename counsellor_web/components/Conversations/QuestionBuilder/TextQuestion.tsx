import { useState } from "react";
import { toast } from "sonner";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface TextQuestionProps {
  type: "short-answer" | "long-answer";
  characterLimit: {
    min?: number;
    max?: number;
  };
  onChange: (characterLimit: { min?: number; max?: number }) => void;
}

export function TextQuestion({
  type,
  characterLimit,
  onChange,
}: TextQuestionProps) {
  const defaultMin = type === "short-answer" ? 100 : 400;
  const [error, setError] = useState<string | null>(null);

  const handleMinChange = (value: string) => {
    const newMin = value ? Number.parseInt(value) : undefined;
    const newMax = characterLimit.max;

    if (newMin !== undefined && newMax !== undefined && newMin > newMax) {
      setError("Minimum limit cannot be greater than maximum limit.");
      toast.error("Minimum limit cannot be greater than maximum limit.");
    } else {
      setError(null);
    }

    onChange({
      ...characterLimit,
      min: newMin,
    });
  };

  const handleMaxChange = (value: string) => {
    const newMax = value === "0" ? undefined : Number.parseInt(value);
    const newMin = characterLimit.min;

    if (newMin !== undefined && newMax !== undefined && newMin > newMax) {
      setError("Maximum limit cannot be smaller than minimum limit.");
      toast.error("Minimum limit cannot be greater than maximum limit.");
    } else {
      setError(null);
    }

    onChange({
      ...characterLimit,
      max: newMax,
    });
  };

  return (
    <div className="space-y-4">
      <div className="grid gap-4">
        <div>
          <label className="text-sm font-medium">Character Limit</label>
          <div className="mt-2 grid grid-cols-2 gap-4">
            {/* Minimum Limit Dropdown */}
            <div>
              <label className="text-sm text-muted-foreground">
                Minimum Limit
              </label>
              <Select
                value={characterLimit.min?.toString() || defaultMin.toString()}
                onValueChange={handleMinChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder={defaultMin.toString()} />
                </SelectTrigger>
                <SelectContent>
                  {/*<SelectItem value="0">No limit</SelectItem>*/}
                  {[1, 50, 100, 200, 400, 800].map((value) => (
                    <SelectItem key={value} value={value.toString()}>
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Maximum Limit Dropdown */}
            <div>
              <label className="text-sm text-muted-foreground">
                Maximum Limit
              </label>
              <Select
                value={characterLimit.max?.toString() || "0"}
                onValueChange={handleMaxChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="No limit" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">No limit</SelectItem>
                  {[100, 200, 400, 800, 1600].map((value) => (
                    <SelectItem key={value} value={value.toString()}>
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Error Message */}
          {error && <p className="mt-2 text-sm text-red-500">{error}</p>}
        </div>
      </div>
    </div>
  );
}
