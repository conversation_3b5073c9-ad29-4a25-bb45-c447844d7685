// components/ui/MultiSelectTags.tsx
"use client";

import * as React from "react";
import { ChevronDown } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface MultiSelectTagsProps {
  availableTags: string[];
  selectedTags: string[];
  onChange: (tags: string[]) => void;
  placeholder?: string;
}

export function MultiSelectTags({
  availableTags,
  selectedTags,
  onChange,
  placeholder = "Select labels",
}: MultiSelectTagsProps) {
  const [open, setOpen] = React.useState(false);
  const triggerRef = React.useRef<HTMLButtonElement>(null);

  const toggle = (tag: string) => {
    if (selectedTags.includes(tag)) {
      onChange(selectedTags.filter((t) => t !== tag));
    } else {
      onChange([...selectedTags, tag]);
    }
  };

  const displayValue = React.useMemo(() => {
    const n = selectedTags.length;
    if (n === 0) return placeholder;
    if (n <= 2) return selectedTags.join(", ");
    return `${selectedTags.slice(0, 2).join(", ")} + ${n - 2}`;
  }, [selectedTags, placeholder]);

  const triggerWidth = triggerRef.current?.offsetWidth;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          ref={triggerRef}
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          <span className="truncate font-normal">{displayValue}</span>
          {open ? (
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 rotate-180 transition-all" />
          ) : (
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 transition-all" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        side="bottom"
        align="start"
        style={{ minWidth: triggerWidth }}
        className="rounded-md border border-input bg-popover p-1 shadow-sm"
      >
        <div className="flex flex-col">
          {availableTags.map((tag) => {
            const id = `multiselect-${tag}`;
            const checked = selectedTags.includes(tag);

            return (
              <label
                key={tag}
                htmlFor={id}
                className="flex cursor-pointer items-center justify-between rounded px-2 py-1 hover:bg-accent"
              >
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id={id}
                    checked={checked}
                    onCheckedChange={() => toggle(tag)}
                  />
                  <span>{tag}</span>
                </div>
              </label>
            );
          })}
        </div>
      </PopoverContent>
    </Popover>
  );
}
