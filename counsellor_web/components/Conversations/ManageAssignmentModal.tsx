"use client";

import {
  use<PERSON><PERSON>back,
  useEffect,
  useMemo,
  useState,
  useTransition,
} from "react";
import {
  getAssignedStudents,
  updateStudentAssignments,
} from "@/actions/conversations/manage-assignment";
import { toast } from "sonner";
import { useSchool } from "@/app/context/SchoolContext";
import { useUser } from "@/app/context/UserContext";
import { UserRole } from "@prisma/client";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import DestructiveModal from "@/components/destructive-modal";

// Simplified interface for student assignment management
interface StudentForAssignment {
  id: string;
  grade: number;
  first_name: string;
  last_name: string;
  email: string | null;
  student_workflow: Array<{
    id: string;
    status: any; // StudentWorkflowStatus
  }>;
}

// Interface for assignment summary
interface AssignmentSummary {
  totalStudents: number;
  assignedStudents: number;
  otherSchoolAssigned: number;
}

// Interface for workflow creator information
interface WorkflowCreatorInfo {
  createdBy: string;
  creatorRole: string;
  creatorSchool?: string;
  isSchoolSpecific: boolean;
}

interface ManageAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  conversationId: string;
  conversationName: string;
}

export function ManageAssignmentModal({
  isOpen,
  onClose,
  conversationId,
  conversationName,
}: ManageAssignmentModalProps) {
  const { activeSchool, schools } = useSchool();
  const { user } = useUser();

  // Selected students persist even when filtering changes
  const [selectedStudents, setSelectedStudents] = useState<
    StudentForAssignment[]
  >([]);
  // State for search and grade filters
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedGrade, setSelectedGrade] = useState<string>("all");
  // State for school filter (admin only)
  const [selectedSchoolFilter, setSelectedSchoolFilter] = useState<string>("current");
  // Real student data and loading state
  const [allStudents, setAllStudents] = useState<StudentForAssignment[]>([]);
  const [assignedStudents, setAssignedStudents] = useState<
    StudentForAssignment[]
  >([]);
  const [assignmentSummary, setAssignmentSummary] =
    useState<AssignmentSummary | null>(null);
  const [workflowCreatorInfo, setWorkflowCreatorInfo] =
    useState<WorkflowCreatorInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isPending, startTransition] = useTransition();

  // Determine which school to use for filtering
  const getSchoolForFiltering = useCallback(() => {
    if (user?.role !== UserRole.ADMIN) {
      // For counselors, always use their own school (via activeSchool)
      return activeSchool;
    }

    // For admins, use the selected filter
    if (selectedSchoolFilter === "all") {
      return null; // null means all schools
    } else if (selectedSchoolFilter === "current") {
      return activeSchool;
    } else {
      return selectedSchoolFilter; // specific school ID
    }
  }, [user?.role, selectedSchoolFilter, activeSchool]);

  // Fetch assigned students when modal opens or filter changes
  const fetchAssignedStudents = useCallback(async () => {
    if (!isOpen) return;

    try {
      setLoading(true);
      const schoolFilter = getSchoolForFiltering();
      const result = await getAssignedStudents(conversationId, schoolFilter);

      if (result.success && result.assignedStudents && result.allStudents) {
        setAssignedStudents(result.assignedStudents);
        setAllStudents(result.allStudents);
        setSelectedStudents(result.assignedStudents);
        setAssignmentSummary(result.assignmentSummary || null);
        setWorkflowCreatorInfo(result.workflowCreatorInfo || null);
      } else {
        toast.error(result.error || "Failed to load assigned students");
      }
    } catch (error) {
      console.error("Failed to fetch assigned students:", error);
      toast.error("Failed to load assigned students");
    } finally {
      setLoading(false);
    }
  }, [conversationId, isOpen, getSchoolForFiltering]);

  useEffect(() => {
    if (isOpen) {
      fetchAssignedStudents();
    } else {
      // Reset state when modal closes
      setSelectedStudents([]);
      setAllStudents([]);
      setAssignedStudents([]);
      setAssignmentSummary(null);
      setWorkflowCreatorInfo(null);
      setSearchQuery("");
      setSelectedGrade("all");
      setSelectedSchoolFilter("current");
    }
  }, [isOpen, fetchAssignedStudents]);

  // Refetch students when school filter changes (for admin only)
  useEffect(() => {
    if (isOpen && user?.role === UserRole.ADMIN) {
      fetchAssignedStudents();
    }
  }, [selectedSchoolFilter, isOpen, user?.role, fetchAssignedStudents]);

  // Filter students based on search and grade
  const filteredStudents = useMemo(() => {
    return allStudents.filter((student) => {
      const matchesSearch =
        !searchQuery ||
        `${student.first_name} ${student.last_name}`
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        student.email?.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesGrade =
        selectedGrade === "all" || student.grade.toString() === selectedGrade;

      return matchesSearch && matchesGrade;
    });
  }, [allStudents, searchQuery, selectedGrade]);

  // Sort students: assigned students first, then unassigned
  const sortedStudents = useMemo(() => {
    const assignedIds = new Set(assignedStudents.map((s) => s.id));

    return filteredStudents.sort((a, b) => {
      const aIsAssigned = assignedIds.has(a.id);
      const bIsAssigned = assignedIds.has(b.id);

      if (aIsAssigned && !bIsAssigned) return -1;
      if (!aIsAssigned && bIsAssigned) return 1;

      // If both have same assignment status, sort by name
      return `${a.first_name} ${a.last_name}`.localeCompare(
        `${b.first_name} ${b.last_name}`,
      );
    });
  }, [filteredStudents, assignedStudents]);

  // Check if a student is selected
  const isStudentSelected = useCallback(
    (studentId: string) => {
      return selectedStudents.some((s) => s.id === studentId);
    },
    [selectedStudents],
  );

  // Toggle student selection
  const toggleStudentSelection = useCallback(
    (student: StudentForAssignment) => {
      setSelectedStudents((prev) => {
        const isSelected = prev.some((s) => s.id === student.id);
        if (isSelected) {
          return prev.filter((s) => s.id !== student.id);
        } else {
          return [...prev, student];
        }
      });
    },
    [],
  );

  // Select/deselect all filtered students
  const handleSelectAll = useCallback(
    (checked: boolean) => {
      if (checked) {
        // Add all filtered students to selection (avoiding duplicates)
        setSelectedStudents((prev) => {
          const existingIds = new Set(prev.map((s) => s.id));
          const newStudents = sortedStudents.filter(
            (s) => !existingIds.has(s.id),
          );
          return [...prev, ...newStudents];
        });
      } else {
        // Remove all filtered students from selection
        const filteredIds = new Set(sortedStudents.map((s) => s.id));
        setSelectedStudents((prev) =>
          prev.filter((s) => !filteredIds.has(s.id)),
        );
      }
    },
    [sortedStudents],
  );

  // Determine if all filtered students are selected
  const allFilteredStudentsSelected =
    sortedStudents.length > 0 &&
    sortedStudents.every((student) => isStudentSelected(student.id));

  // Handle save changes
  const handleSaveChanges = async () => {
    try {
      setIsSubmitting(true);
      const selectedStudentIds = selectedStudents.map((s) => s.id);

      const schoolFilter = getSchoolForFiltering();
      const result = await updateStudentAssignments(
        conversationId,
        selectedStudentIds,
        schoolFilter,
      );

      if (result.success) {
        toast.success(result.message || "Assignment updated successfully");
        onClose();
      } else {
        toast.error(result.error || "Failed to update assignment");
      }
    } catch (error) {
      console.error("Error updating assignment:", error);
      toast.error("Failed to update assignment");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get unique grades for filter
  const availableGrades = useMemo(() => {
    const grades = Array.from(new Set(allStudents.map((s) => s.grade))).sort(
      (a, b) => a - b,
    );
    return grades;
  }, [allStudents]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="flex max-h-[85vh] w-[90vw] max-w-4xl flex-col">
        <DialogHeader className="shrink-0">
          <DialogTitle>
            Manage Assigned Students - {conversationName}
          </DialogTitle>
        </DialogHeader>

        {/* Workflow Creator Information - Only for Admin */}
        {user?.role === UserRole.ADMIN && workflowCreatorInfo && (
          <div className="mb-4 shrink-0 rounded-lg border border-blue-200 bg-blue-50 p-3">
            <div className="text-sm text-blue-800">
              <div className="font-medium">
                Created by: {workflowCreatorInfo.createdBy}
                {workflowCreatorInfo.creatorRole === "ADMIN" ? " (Admin)" : " (Counselor)"}
                {workflowCreatorInfo.creatorSchool && (
                  <span> from {workflowCreatorInfo.creatorSchool}</span>
                )}
              </div>
              {workflowCreatorInfo.isSchoolSpecific && (
                <div className="mt-1 text-blue-700">
                  ⚠️ Please ensure you are assigning students from {workflowCreatorInfo.creatorSchool} only.
                </div>
              )}
            </div>
          </div>
        )}

        <div className="mb-4 shrink-0 text-sm text-gray-500">
          Select students who should have access to this conversation.
          <span className="font-bold">
            {" "}
            Currently assigned students are shown first and pre-selected.
          </span>
        </div>

        {loading ? (
          <div className="flex flex-1 flex-col items-center justify-center py-8">
            <LoadingSpinner />
            <div className="mt-2 text-center">Loading students...</div>
          </div>
        ) : (
          <div className="flex min-h-0 flex-1 flex-col space-y-4">
            {/* Search and Filter Controls */}
            <div className="flex shrink-0 gap-3">
              <Input
                placeholder="Search by name or email..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1"
              />

              {/* School Filter - Only for Admin */}
              {user?.role === UserRole.ADMIN && (
                <Select value={selectedSchoolFilter} onValueChange={setSelectedSchoolFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="School" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Students</SelectItem>
                    <SelectItem value="current">Current School</SelectItem>
                    {schools.map((school) => (
                      <SelectItem key={school.id} value={school.id}>
                        {school.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}

              <Select value={selectedGrade} onValueChange={setSelectedGrade}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Grade" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Grades</SelectItem>
                  {availableGrades.map((grade) => (
                    <SelectItem key={grade} value={grade.toString()}>
                      Grade {grade}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Student Selection Summary */}
            <div className="flex shrink-0 flex-col space-y-2 text-sm text-gray-600">
              <div className="flex items-center justify-between">
                <span>
                  {selectedStudents.length} of {allStudents.length} students
                  selected
                </span>
                <span className="text-xs text-gray-500">
                  {sortedStudents.length} shown (filtered)
                </span>
              </div>
              {assignmentSummary &&
                assignmentSummary.otherSchoolAssigned > 0 && (
                  <div className="rounded bg-blue-50 px-2 py-1 text-xs text-blue-600">
                    + {assignmentSummary.otherSchoolAssigned} students from
                    other schools are also assigned
                  </div>
                )}
            </div>

            {/* Student table with scroll - this takes remaining space */}
            <div className="min-h-0 flex-1 overflow-y-auto rounded-lg border">
              <div className="h-full">
                <table className="w-full table-fixed">
                  <thead className="sticky top-0 z-10 border-b bg-gray-50">
                    <tr>
                      <th className="w-12 px-3 py-3 text-left">
                        <Checkbox
                          checked={allFilteredStudentsSelected}
                          onCheckedChange={handleSelectAll}
                        />
                      </th>
                      <th className="w-48 px-3 py-3 text-left text-sm font-medium text-gray-900">
                        Student Name
                      </th>
                      <th className="w-64 px-3 py-3 text-left text-sm font-medium text-gray-900">
                        Email
                      </th>
                      <th className="w-20 px-3 py-3 text-center text-sm font-medium text-gray-900">
                        Grade
                      </th>
                      <th className="w-28 px-3 py-3 text-center text-sm font-medium text-gray-900">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {sortedStudents.map((student) => {
                      const isAssigned = assignedStudents.some(
                        (s) => s.id === student.id,
                      );
                      return (
                        <tr
                          key={student.id}
                          className={`border-b hover:bg-blue-100 ${
                            isAssigned ? "bg-blue-50" : ""
                          }`}
                        >
                          <td className="w-12 px-3 py-3">
                            <Checkbox
                              checked={isStudentSelected(student.id)}
                              onCheckedChange={() =>
                                toggleStudentSelection(student)
                              }
                            />
                          </td>
                          <td className="w-48 px-3 py-3">
                            <div
                              className="truncate font-medium text-gray-900"
                              title={`${student.first_name} ${student.last_name}`}
                            >
                              {student.first_name} {student.last_name}
                            </div>
                          </td>
                          <td className="w-64 px-3 py-3">
                            <div
                              className="truncate text-sm text-gray-600"
                              title={student.email || "No email"}
                            >
                              {student.email || "No email"}
                            </div>
                          </td>
                          <td className="w-20 px-3 py-3 text-center">
                            <span className="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800">
                              {student.grade}
                            </span>
                          </td>
                          <td className="w-28 px-3 py-3 text-center">
                            {isAssigned ? (
                              <span className="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
                                Assigned
                              </span>
                            ) : (
                              <span className="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600">
                                Not Assigned
                              </span>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex shrink-0 justify-end gap-2 pt-4">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <DestructiveModal
                handler={handleSaveChanges}
                title="Confirm Assignment Changes"
                description={`Are you sure you want to assign this conversation to ${selectedStudents.length} selected students? This will update their assignment status.`}
                btnTitle="Save Changes"
                variant="default"
                action="Assigning..."
                showIcon={false}
              />
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
