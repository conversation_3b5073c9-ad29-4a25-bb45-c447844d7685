"use client";

import { useState } from "react";
import { CustomConversation } from "@/actions/conversations/conversation-workflow";
import { ChevronLeft, ChevronRight } from "lucide-react";

import { Button } from "@/components/ui/button";
import { ConversationHeader } from "@/components/Conversations/ConversationHeader";
import SaveAndPublishButtons from "@/components/Conversations/SaveAndPublishButtons";

interface ConversationDetailProps {
  conversation: CustomConversation;
}

export function ConversationDetail({ conversation }: ConversationDetailProps) {
  const [currentPage, setCurrentPage] = useState(0);
  const questionsPerPage = 5;
  // console.log("log convo ==>", conversation);
  // Calculate the total number of pages
  const totalPages = Math.ceil(
    (conversation.questions?.length ?? 0) / questionsPerPage,
  );

  // Generate the questions for the current page
  const paginatedQuestions = conversation.questions
    ? conversation.questions.slice(
        currentPage * questionsPerPage,
        (currentPage + 1) * questionsPerPage,
      )
    : [];

  // Generate an array of page numbers for rendering
  const pageNumbers = Array.from({ length: totalPages }, (_, index) => index);

  return (
    <div className="flex h-full flex-col p-6">
      {/* Header */}
      <div className="border-b p-6">
        <h2 className="flex justify-center text-center text-2xl">
          {conversation.name}
        </h2>
      </div>
      <SaveAndPublishButtons
        type={conversation.type}
        conversationId={conversation.id}
        conversationName={conversation.name}
        status={conversation.status}
        workflowOwnerId={conversation.owner_id}
      />

      {/* Scrollable Questions Container */}
      <div className="grow overflow-auto p-6">
        <div className="space-y-4">
          {paginatedQuestions.map((question, index) => (
            <div
              key={index}
              className="flex items-start rounded-lg border p-4 text-muted-foreground"
            >
              <span className="mr-2 shrink-0 font-semibold">
                Q{currentPage * questionsPerPage + index + 1}:
              </span>
              <p className="break-words">{question.text}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Fixed Pagination Controls */}
      {(conversation.questions?.length ?? 0) > 0 && (
        <div className="border-t bg-background p-4">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === 0}
              onClick={() => setCurrentPage((prev) => prev - 1)}
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>

            <div className="hidden space-x-2 sm:flex">
              {pageNumbers.map((page) => (
                <Button
                  key={page}
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentPage(page)}
                >
                  {page + 1}
                </Button>
              ))}
            </div>

            <div className="sm:hidden">
              <span className="text-sm text-muted-foreground">
                Page {currentPage + 1} of {totalPages}
              </span>
            </div>

            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === totalPages - 1}
              onClick={() => setCurrentPage((prev) => prev + 1)}
            >
              Next
              <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
