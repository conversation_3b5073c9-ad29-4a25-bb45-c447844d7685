"use client";

import {
  useC<PERSON>back,
  useEffect,
  useMemo,
  useState,
  useTransition,
} from "react";
import { getAllSchools } from "@/actions/schools";
import { fetchStudents, fetchStudentsBasicInfo } from "@/actions/students";
import { UserRole } from "@prisma/client";

import log from "@/common/logger";
import { getCurrentUser } from "@/common/session";
import { StudentWithWorkflows, User } from "@/common/types";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useSchool } from "@/app/context/SchoolContext";
import { useUser } from "@/app/context/UserContext";

interface AssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (
    assignToAll: boolean,
    selectedStudents: StudentWithWorkflows[],
    selectedSchoolId?: string,
  ) => Promise<void>;
  conversationId: string;
  isSubmitting?: boolean;
}

export function AssignmentModal({
  isOpen,
  onClose,
  onConfirm,
  conversationId,
  isSubmitting = false,
}: AssignmentModalProps) {
  const { user } = useUser();
  const { activeSchool } = useSchool();

  // State for assignment type ("all" vs "specific")
  const [assignmentType, setAssignmentType] = useState<"all" | "specific">(
    "all",
  );
  // Selected students persist even when filtering changes
  const [selectedStudents, setSelectedStudents] = useState<
    StudentWithWorkflows[]
  >([]);
  // State for search and grade filters
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedGrade, setSelectedGrade] = useState<string>("all");
  // Real student data and loading state
  const [students, setStudents] = useState<StudentWithWorkflows[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [isPending, startTransition] = useTransition();

  // School selection state (independent from global activeSchool)
  const [availableSchools, setAvailableSchools] = useState<
    Array<{ id: string; name: string }>
  >([]);
  const [selectedSchoolForModal, setSelectedSchoolForModal] =
    useState<string>("all");
  const [schoolsLoading, setSchoolsLoading] = useState<boolean>(false);
  const [totalStudentsInSelectedSchool, setTotalStudentsInSelectedSchool] =
    useState<number>(0);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      // Reset to default state when modal opens
      if (user?.role === UserRole.ADMIN) {
        // For admin users, default to active school if available, otherwise "all"
        setSelectedSchoolForModal(activeSchool || "all");
      }
      setSelectedStudents([]);
      setTotalStudentsInSelectedSchool(0);
    }
  }, [isOpen, activeSchool, user]);

  // Load schools for admin users
  useEffect(() => {
    async function loadSchools() {
      if (user?.role === UserRole.ADMIN) {
        try {
          setSchoolsLoading(true);
          const result = await getAllSchools();
          if (!result.error && result.schools) {
            setAvailableSchools(result.schools);
          }
        } catch (error) {
          console.error("Failed to load schools:", error);
        } finally {
          setSchoolsLoading(false);
        }
      }
    }

    if (isOpen) {
      loadSchools();
    }
  }, [isOpen, user]);

  const _fetchStudents = useCallback(async () => {
    try {
      setLoading(true);
      const gradeParam = selectedGrade === "all" ? null : selectedGrade;
      if (user) {
        // Determine which school to filter by
        let schoolIdToUse: string | undefined;
        if (user.role === UserRole.ADMIN) {
          // For admin users, use the selected school from modal (unless "all" is selected)
          schoolIdToUse =
            selectedSchoolForModal === "all"
              ? undefined
              : selectedSchoolForModal;
        } else {
          // For counselors, use their assigned school (handled in fetchStudentsBasicInfo)
          schoolIdToUse = undefined;
        }

        const data = await fetchStudentsBasicInfo({
          grade: gradeParam,
          search: searchQuery || null,
          user,
          schoolId: schoolIdToUse,
          //   TODO: add counselorId and user in the future
        });

        const students = Array.isArray(data) ? data : [];
        setStudents(students);
        // Update total students count for "assign all" display
        setTotalStudentsInSelectedSchool(students.length);
      }
    } catch (error) {
      console.error("Failed to fetch students:", error);
      setStudents([]);
      setTotalStudentsInSelectedSchool(0);
    } finally {
      setLoading(false);
    }
  }, [searchQuery, selectedGrade, user, selectedSchoolForModal]);

  // Load students when modal opens or when filters change
  useEffect(() => {
    if (isOpen) {
      const timeoutId = setTimeout(() => {
        startTransition(() => {
          _fetchStudents();
        });
      }, 300);
      return () => clearTimeout(timeoutId);
    }
  }, [isOpen, selectedGrade, searchQuery, _fetchStudents]);

  // useEffect(() => {
  //   console.log("selected students===>", selectedStudents);
  // }, [selectedStudents]);

  // Clear selected students when school changes (for admin users in specific mode)
  useEffect(() => {
    if (user?.role === UserRole.ADMIN && assignmentType === "specific") {
      setSelectedStudents([]);
    }
  }, [selectedSchoolForModal, user, assignmentType]);

  // Filter students based on grade only (search is handled by backend)
  const filteredStudents: StudentWithWorkflows[] = useMemo(() => {
    let filtered = [...students];

    // Only filter by grade on frontend since search is handled by backend
    if (selectedGrade !== "all") {
      filtered = filtered.filter(
        (student: any) => student.grade.toString() === selectedGrade,
      );
    }
    return filtered;
  }, [selectedGrade, students]);

  // Update selection for an individual student
  const handleSelectStudent = (
    student: StudentWithWorkflows,
    isChecked: boolean,
  ) => {
    setSelectedStudents((prevSelected: StudentWithWorkflows[]) => {
      if (isChecked) {
        return [...prevSelected.filter((s) => s.id !== student.id), student];
      } else {
        return prevSelected.filter((s) => s.id !== student.id);
      }
    });
  };

  // Handle "Select All" for currently filtered students
  const handleSelectAll = (isChecked: boolean) => {
    setSelectedStudents((prevSelected: StudentWithWorkflows[]) => {
      if (isChecked) {
        const newSelections = filteredStudents.filter(
          (student) => !prevSelected.some((s) => s.id === student.id),
        );
        return [...prevSelected, ...newSelections];
      } else {
        return prevSelected.filter(
          (student) => !filteredStudents.some((s) => s.id === student.id),
        );
      }
    });
  };

  // Check if a student is selected
  const isStudentSelected = (studentId: string) => {
    return selectedStudents.some((s) => s.id === studentId);
  };

  // Determine if all filtered students are selected
  const allFilteredStudentsSelected =
    filteredStudents.length > 0 &&
    filteredStudents.every((student) => isStudentSelected(student.id));

  // Confirm the assignment
  const handleConfirm = async () => {
    if (isSubmitting) return; // Prevent multiple submissions
    // For counselors, don't pass school ID (they're automatically scoped to their school)
    // For admins, pass the selected school ID if not "all"
    const schoolIdToPass = user?.role === UserRole.ADMIN && selectedSchoolForModal !== "all"
      ? selectedSchoolForModal
      : undefined;
    await onConfirm(assignmentType === "all", selectedStudents, schoolIdToPass);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md md:max-w-lg">
        <DialogHeader>
          <DialogTitle>Assign Conversation</DialogTitle>
        </DialogHeader>

        <div className="mb-4 text-sm text-gray-500">
          Select who you&apos;d like to assign this conversation to.
        </div>

        {/* School Selector - Only visible for Admin users */}
        {user?.role === UserRole.ADMIN && (
          <div className="mb-4 space-y-2">
            <h3 className="text-sm font-medium">School (Only for Admins)</h3>
            <Select
              value={selectedSchoolForModal}
              onValueChange={setSelectedSchoolForModal}
              disabled={schoolsLoading}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={schoolsLoading ? "Loading school list..." : "Select School"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Schools</SelectItem>
                {availableSchools.map((school) => (
                  <SelectItem key={school.id} value={school.id}>
                    {school.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Assignment Type: All or Specific Students */}
        <div className="space-y-4">
          <h3 className="mb-2 text-sm font-medium">Assignees</h3>
          <RadioGroup
            value={assignmentType}
            onValueChange={(value) => {
              setAssignmentType(value as "all" | "specific");
              if (value === "all") {
                setSelectedStudents([]); // Clear selected students when switching to "All"
              }
            }}
            className="space-y-2"
            disabled={schoolsLoading || loading || isPending}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="all" id="all-students" />
              <Label htmlFor="all-students" className="flex items-center gap-2">
                Assign to All Students
                <span className="text-xs text-gray-500">
                  (
                  {loading
                    ? "..."
                    : `${totalStudentsInSelectedSchool} students`}
                  )
                </span>
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="specific" id="specific-students" />
              <Label htmlFor="specific-students">
                Assign to Specific Students
              </Label>
            </div>
          </RadioGroup>
        </div>

        {/* Show student table only if "Specific" is selected */}
        {assignmentType === "specific" && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Select Recipients</h3>

            <div className="flex gap-2">
              <div className="flex-1">
                <Input
                  placeholder="Search by name or ID"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <Select value={selectedGrade} onValueChange={setSelectedGrade}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select Grade" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Grades</SelectItem>
                  <SelectItem value="9">Grade 9</SelectItem>
                  <SelectItem value="10">Grade 10</SelectItem>
                  <SelectItem value="11">Grade 11</SelectItem>
                  <SelectItem value="12">Grade 12</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Student table with scroll; selected state persists regardless of filtering */}
            <div className="max-h-60 overflow-y-auto overflow-x-hidden rounded-md border">
              {loading ? (
                <div className="flex flex-col items-center justify-center">
                  <LoadingSpinner />
                  <div className="p-4 text-center">Loading students...</div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full table-fixed">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="w-[10%] px-4 py-2">
                          <Checkbox
                            checked={allFilteredStudentsSelected}
                            onCheckedChange={handleSelectAll}
                          />
                        </th>
                        <th className="w-[30%] px-4 py-2">Student</th>
                        <th className="w-2/5 px-4 py-2">Student ID</th>
                        <th className="w-1/5 px-4 py-2">Grade</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredStudents.map((student) => (
                        <tr key={student.id} className="border-t text-sm">
                          <td className="px-4 py-2">
                            <Checkbox
                              checked={isStudentSelected(student.id)}
                              onCheckedChange={(checked) =>
                                handleSelectStudent(student, !!checked)
                              }
                            />
                          </td>
                          <td
                            className="truncate px-4 py-2"
                            title={`${student.first_name} ${student.last_name}`}
                          >
                            {student.first_name} {student.last_name}
                          </td>
                          <td className="truncate px-4 py-2" title={student.id}>
                            {student.id}
                          </td>
                          <td className="px-4 py-2">{student.grade}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}

        {selectedStudents.length > 0 && (
          <div className="mt-2 flex items-center justify-between rounded-md bg-gray-100 p-2 text-sm text-gray-700">
            <span>
              Selected{" "}
              <span className="font-semibold">{selectedStudents.length}</span>{" "}
              {selectedStudents.length === 1 ? "student" : "students"}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSelectedStudents([])}
              className="text-red-500 hover:bg-red-100"
            >
              Clear Selection
            </Button>
          </div>
        )}

        {/* Cancel and Confirm buttons */}
        <div className="mt-4 flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            className="bg-black text-white hover:bg-gray-800"
            disabled={isSubmitting}
            onClick={handleConfirm}
          >
            {isSubmitting ? <LoadingSpinner /> : "Confirm & Publish"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
