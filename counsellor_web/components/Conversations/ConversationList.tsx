"use client";

import { usePathname, useRouter } from "next/navigation";
import { CustomConversation } from "@/actions/conversations/conversation-workflow";
import {
  BotMessageSquare,
  ClipboardList,
  FlaskConical,
  NotebookPen,
  SquareLibrary,
} from "lucide-react";
import { useEffect, useState } from "react";

import urls from "@/common/config/urls.json";
import { formatTimeAgo, workflowTypeHelper } from "@/common/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { TooltipWrapper } from "@/components/shared/TooltipWrapper";

interface ConversationListProps {
  conversations: CustomConversation[];
}

export function ConversationList({ conversations }: ConversationListProps) {
  const router = useRouter();
  const pathName = usePathname();
  const currentConversationId = pathName.split("/")[2];

  // State to force re-render every minute
  const [_, setMinuteTick] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setMinuteTick((prev) => prev + 1);
    }, 60 * 1000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  return (
    // <div className="h-full -space-y-1 overflow-y-auto py-2">
    <ScrollArea className="h-full -space-y-1 overflow-y-auto px-1 py-2">
      {conversations.map((conversation, index) => (
        <button
          key={conversation.id}
          onClick={() => {
            // onSelectConversation(conversation);
            router.push(`${urls.routes.conversations}/${conversation.id}`);
          }}
          className={`flex w-full flex-col gap-2 ${index === 0 ? "border-t" : ""} border-b border-gray-300 px-4 py-4 text-left hover:bg-sky-50 ${conversation.id === currentConversationId ? "bg-sky-50" : ""} `}
        >
          {/* Header Row */}
          <div className="mb-2 flex w-full items-center justify-between">
            {/*status*/}
            <div
              className={`rounded-md px-2 py-1 text-xs font-medium outline outline-1 outline-gray-300 ${
                conversation.status === "PUBLISHED"
                  ? "bg-green-100"
                  : conversation.status === "INACTIVE"
                    ? "bg-gray-100"
                    : "bg-yellow-100"
              }`}
            >
              <span className="px-0">{conversation.status}</span>
            </div>
            {/*updated time*/}
            <div className="text-xs font-light">
              {formatTimeAgo(new Date(conversation.updatedAt))}
            </div>
          </div>
          {/*Middle row*/}
          <div className="mb-2 text-sm font-medium">{conversation.name}</div>
          {/* Footer Row */}
          <div className="flex items-center justify-between text-xs">
            {/*<div>{`${conversation.status || "Unpublished"} `}</div>*/}
            {conversation.type === "custom" ? (
              <TooltipWrapper tooltipContent="Conversation built manually by a counselor">
                <div className="flex items-center gap-1">
                  <FlaskConical className="h-5 w-5" />
                  <span>Custom</span>
                </div>
              </TooltipWrapper>
            ) : (
              <TooltipWrapper tooltipContent="Conversation built using an Addie system template">
                <div className="flex items-center gap-1">
                  <SquareLibrary className="h-5 w-5" />
                  <span>Addie Template</span>
                </div>
              </TooltipWrapper>
            )}
            <div className="flex items-center justify-center gap-1">
              {workflowTypeHelper(conversation.workflow_type) ===
                "Open Ended" && (
                <TooltipWrapper tooltipContent="Conversation uses Addie’s natural conversation abilities to achieve the desired goal and uncover insights">
                  <div className="flex items-center gap-1">
                    <BotMessageSquare className="h-5 w-5" />
                    <span>
                      {workflowTypeHelper(conversation.workflow_type)}
                    </span>
                  </div>
                </TooltipWrapper>
              )}
              {workflowTypeHelper(conversation.workflow_type) ===
                "Structured" && (
                <TooltipWrapper tooltipContent="Conversation uses a structured form approach to record student responses">
                  <div className="flex items-center gap-1">
                    <ClipboardList className="h-5 w-5" />
                    <span>
                      {workflowTypeHelper(conversation.workflow_type)}
                    </span>
                  </div>
                </TooltipWrapper>
              )}

              {workflowTypeHelper(conversation.workflow_type) ===
                "Assignment" && (
                <TooltipWrapper tooltipContent="Assignment uses Addie’s natural conversation abilities to achieve the desired goal and uncover insights">
                  <div className="flex items-center gap-1">
                    <NotebookPen className="h-5 w-5" />
                    <span>
                      {workflowTypeHelper(conversation.workflow_type)}
                    </span>
                  </div>
                </TooltipWrapper>
              )}
            </div>
          </div>
        </button>
      ))}
    </ScrollArea>
    // </div>
  );
}
