"use client";

import React, { useCallback, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  CustomConversation,
  deleteConversation,
  publishConversation,
  QuestionItem,
  saveQuestionsToWorkflow,
  updateQuestionsInWorkflow,
} from "@/actions/conversations/conversation-workflow";
import { WorkflowType } from "@prisma/client";
import { toast } from "sonner";

import { useAutoSave } from "@/hooks/useAutoSave";
import { Button } from "@/components/ui/button";
import { ConversationHeader } from "@/components/Conversations/ConversationHeader";
import { DisplayQuestions } from "@/components/Conversations/DisplayQuestions";
import BuildQuestion from "@/components/Conversations/QuestionBuilder/BuildQuestion";
import SaveAndPublishButtons from "@/components/Conversations/SaveAndPublishButtons";
import DestructiveModal from "@/components/destructive-modal";
import { useUser } from "@/app/context/UserContext";

// Helper: format a "time ago" message.
function formatTimeAgo(date: Date): string {
  const diffMinutes = Math.round((Date.now() - date.getTime()) / 60000);
  return diffMinutes <= 0
    ? "just now"
    : diffMinutes === 1
      ? "1 min ago"
      : `${diffMinutes} min ago`;
}

interface CustomConversationDetailProps {
  conversation: CustomConversation;
  onEditToggle?: (isEditing: boolean) => void;
  onSaveStatusChange?: (isSaving: boolean, message: string) => void;
  onPublishDisableChange?: (disabled: boolean) => void;
  isEditingExternal?: boolean;
}

export function CustomConversationDetail({
  conversation,
  onEditToggle,
  onSaveStatusChange,
  onPublishDisableChange,
  isEditingExternal,
}: CustomConversationDetailProps) {
  const { user } = useUser();
  if (!user?.id) {
    throw new Error("User ID is required for conversation operations");
  }
  const userId = user.id;

  // Unified state for all questions (array of QuestionItem)
  const [questions, setQuestions] = useState<QuestionItem[]>(
    conversation.questions || [],
  );
  // For now, if no questions exist and it's not a custom conversation, start in editing mode
  // Use external editing state if provided, otherwise manage internally
  const [isEditingInternal, setIsEditingInternal] = useState<boolean>(
    questions.length === 0 && conversation.type !== "custom",
  );
  const isEditing =
    isEditingExternal !== undefined ? isEditingExternal : isEditingInternal;

  const [isAllValid, setIsAllValid] = useState<boolean>(questions.length > 0);
  const [disablePublish, setDisablePublish] = useState(false);
  // Add description state
  const [description, setDescription] = useState<string>(
    conversation.description || ""
  );

  // Extract current type from tags
  const getCurrentType = () => {
    if (!conversation.tags) return "General";
    const typeTag = conversation.tags.find(tag => tag.startsWith("type:"));
    return typeTag ? typeTag.replace("type:", "") : "General";
  };

  const [structuredType, setStructuredType] = useState<string>(getCurrentType());

  const router = useRouter();

  // Use the autoSave hook instead of manual implementation
  const { isSaving, lastSavedTime, saveError, saveStatus } = useAutoSave(
    { questions, description, structuredType },
    isAllValid && conversation.status === "DRAFT",
    async (updatedData) => {
      // This is the callback that actually saves to DB
      if (conversation.questions?.length === 0) {
        // New conversation: save new questions.
        await saveQuestionsToWorkflow(
          conversation.id,
          updatedData.questions,
          userId,
          updatedData.description,
        );
      } else {
        // Existing conversation: update questions.
        const originalWorkflowStepIds = conversation.questions?.map(
          (q) => q.workflowStepId,
        );
        await updateQuestionsInWorkflow(
          conversation.id,
          updatedData.questions,
          originalWorkflowStepIds ?? [],
          userId,
          updatedData.description,
          updatedData.structuredType,
        );
      }
      router.refresh();
    },
    1500, // 1.5 seconds debounce for autosave
  );

  // If auto-save error occurs, show a toast
  useEffect(() => {
    if (saveError) {
      toast.error(saveError);
    }
  }, [saveError]);

  // Update parent component with save status if needed
  useEffect(() => {
    if (onSaveStatusChange) {
      onSaveStatusChange(
        isSaving,
        getSaveStatusMessage(saveStatus, lastSavedTime),
      );
    }
  }, [isSaving, saveStatus, lastSavedTime, onSaveStatusChange]);

  // ensure that always rendered the new questions
  useEffect(() => {
    setQuestions(conversation.questions || []);
  }, [conversation]);

  useEffect(() => {
    // Logic to determine if publish should be disabled
    const shouldDisable = questions.length === 0;
    setDisablePublish(shouldDisable);
    if (onPublishDisableChange) {
      onPublishDisableChange(shouldDisable);
    }
  }, [questions, onPublishDisableChange]);

  // Generate appropriate message based on save status
  function getSaveStatusMessage(
    status: "idle" | "saving" | "saved" | "error",
    lastSavedTime: Date | null,
  ): string {
    switch (status) {
      case "saving":
        return "Saving...";
      case "saved":
        return lastSavedTime
          ? `Saved ${formatTimeAgo(lastSavedTime)}`
          : "Saved";
      case "error":
        return "Failed to save changes";
      default:
        return lastSavedTime
          ? `Auto-saved ${formatTimeAgo(lastSavedTime)}`
          : "";
    }
  }

  // Manual save function for explicit save button clicks
  const handleManualSave = async () => {
    // Only allow saving when in draft mode
    if (conversation.status !== "DRAFT") {
      toast.error("Only draft conversations can be updated.");
      return false;
    }

    if (!isAllValid) {
      toast.error("Please fill out all questions completely before saving.");
      return false;
    }

    try {
      if (conversation.questions?.length === 0) {
        // New conversation: save new questions.
        await saveQuestionsToWorkflow(conversation.id, questions, userId, description);
      } else {
        // Existing conversation: update questions.
        const originalWorkflowStepIds = conversation.questions?.map(
          (q) => q.workflowStepId,
        );
        await updateQuestionsInWorkflow(
          conversation.id,
          questions,
          originalWorkflowStepIds ?? [],
          userId,
          description,
        );
      }

      toast.success("Questions saved successfully!");
      router.refresh();
      return true;
    } catch (error) {
      console.error(error);
      toast.error("Failed to save questions.");
      return false;
    }
  };

  const handlePublish = async () => {
    try {
      // If in edit mode or there are unsaved changes, save first
      if (isEditing) {
        console.log("Saving questions before publishing...");
        const saveSuccess = await handleManualSave();
        if (!saveSuccess) return;

        // Exit edit mode after saving
        setIsEditingInternal(false);
      }

      // Now proceed with publishing
      await publishConversation(conversation.id);
      toast.success("Conversation published successfully!");
      router.refresh();
    } catch (error) {
      console.error(error);
      toast.error("Failed to publish conversation.");
      throw error; // Re-throw to prevent modal from closing
    }
  };

  // Callback to update questions state from BuildQuestion (which gets data from MultiQuestionBuilder)
  const handleQuestionsChange = (
    newQuestions: QuestionItem[],
    allValid: boolean,
    newDescription?: string,
    newStructuredType?: string,
  ) => {
    setQuestions(newQuestions);
    setIsAllValid(allValid);
    if (newDescription !== undefined) {
      setDescription(newDescription);
    }
    if (newStructuredType !== undefined) {
      setStructuredType(newStructuredType);
    }
  };

  const handleCancelEdit = () => {
    if (isEditingExternal === undefined) {
      setIsEditingInternal(false);
    }
    if (onEditToggle) {
      onEditToggle(false);
    }
    // Reset description to original value from conversation
    setDescription(conversation.description || "");
    // Reset questions to original values
    setQuestions(conversation.questions || []);
    router.refresh();
  };

  const handleStartEditing = () => {
    // Don't allow editing for custom conversation types
    if (conversation.type === "custom") {
      toast.error("Custom conversations cannot be edited.");
      return;
    }

    if (isEditingExternal === undefined) {
      setIsEditingInternal(true);
    }
    if (onEditToggle) {
      onEditToggle(true);
    }
  };

  const handleDeleteConversation = async (workflowId) => {
    try {
      await deleteConversation(workflowId);
      router.push("/conversations");
      toast.success("Conversation is deleted");
    } catch (e) {
      toast.error("Failed to delete this conversation...");
    }
  };

  return (
    <div className="flex h-full flex-col">
      {/* Header */}
      <ConversationHeader
        isEditing={isEditing}
        conversation={conversation}
        editButtonText={
          conversation.workflow_type === WorkflowType.UNSTRUCTURED
            ? "Edit Goals"
            : "Edit Questions"
        }
        onEditToggle={setIsEditingInternal}
        isSaving={isSaving}
        autoSaveMessage={getSaveStatusMessage(saveStatus, lastSavedTime)}
      />
      {/* Main Content Area */}
      <div className="flex-1 overflow-auto p-6">
        {/* BuildQuestion receives onQuestionsChange callback to update the*/}
        {/*unified questions state*/}
        {isEditing ? (
          <BuildQuestion
            conversation={{...conversation, description}}
            onQuestionsChange={handleQuestionsChange}
            saveButtonComponent={
              <SaveAndPublishButtons
                type={conversation.type}
                conversationId={conversation.id}
                onPublish={handlePublish}
                isSaving={isSaving}
                status={conversation.status}
                autoSaveMessage={getSaveStatusMessage(
                  saveStatus,
                  lastSavedTime,
                )}
                disablePublish={disablePublish}
                hideSaveButton={true}
                workflowOwnerId={conversation.owner_id}
              />
            }
          />
        ) : (
          // Display mode
          <DisplayQuestions questions={questions} description={description} />
        )}
      </div>
    </div>
  );
}
