"use client";

import { useC<PERSON>back, useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { upsertAssignmentGoals } from "@/actions/conversations/assignment-conversation";
import {
  CustomConversation,
  deleteConversation,
  Goal,
} from "@/actions/conversations/conversation-workflow";
import { upsertUnstructuredGoals } from "@/actions/conversations/unstructured-conversations";
import { WorkflowType, UserRole } from "@prisma/client";
import { toast } from "sonner";

import { useAutoSave } from "@/hooks/useAutoSave";
import { Button } from "@/components/ui/button";
import { ConversationHeader } from "@/components/Conversations/ConversationHeader";
import SaveAndPublishButtons from "@/components/Conversations/SaveAndPublishButtons";
import { DisplayGoals } from "@/components/Conversations/Unstructured-Conversation/DisplayGoals";
import GoalBuilder from "@/components/Conversations/Unstructured-Conversation/GoalBuilder/GoalBuilder";
import DestructiveModal from "@/components/destructive-modal";

// Helper: format a "time ago" message.
function formatTimeAgo(date: Date): string {
  const diffMinutes = Math.round((Date.now() - date.getTime()) / 60000);
  return diffMinutes <= 0
    ? "just now"
    : diffMinutes === 1
      ? "1 min ago"
      : `${diffMinutes} min ago`;
}

// Define the Goal interface

interface UnstructuredConversationDetailProps {
  conversation: CustomConversation;
  onEditToggle?: (isEditing: boolean) => void;
  onSaveStatusChange?: (isSaving: boolean, message: string) => void;
  onPublishDisableChange?: (disabled: boolean) => void;
  isEditingExternal?: boolean;
  user?: { role: UserRole } | null;
}

export function UnstructuredConversationDetail({
  conversation,
  onEditToggle,
  onSaveStatusChange,
  onPublishDisableChange,
  isEditingExternal,
  user,
}: UnstructuredConversationDetailProps) {
  // State for goals
  const [goals, setGoals] = useState<Goal[]>(conversation.goals || []);
  // State for early end message - pass into child components to consume
  const [earlyEndMessage, setEarlyEndMessage] = useState<string>(
    conversation.earlyEndMessage || "",
  );
  // Use external editing state if provided, otherwise manage internally
  // Start in edit mode if either: there are no goals, or if there's exactly one goal with empty text
  const [isEditingInternal, setIsEditingInternal] = useState<boolean>(
    goals.length === 0 || (goals.length === 1 && (!goals[0].goalText || goals[0].goalText.trim() === "")),
  );
  const isEditing =
    isEditingExternal !== undefined ? isEditingExternal : isEditingInternal;
  const [isAllValid, setIsAllValid] = useState<boolean>(goals.length > 0);
  const [disableSystemPrompt, setDisableSystemPrompt] = useState<boolean>(
    conversation.disable_system_prompt || false
  );
  const router = useRouter();

  const isUnstructured =
    conversation.workflow_type === WorkflowType.UNSTRUCTURED;
  const isAssignment = conversation.workflow_type === WorkflowType.ASSIGNMENT;

  // Ensure that we always render the latest goals
  useEffect(() => {
    const shouldBeValid = goals.length > 0;
    setIsAllValid(shouldBeValid);

    if (onPublishDisableChange) {
      onPublishDisableChange(!shouldBeValid);
    }
  }, [goals, onPublishDisableChange]);

  // Handle disable system prompt change
  const handleDisableSystemPromptChange = useCallback((disabled: boolean) => {
    setDisableSystemPrompt(disabled);
  }, []);

  const saveGoals = useCallback(
    async (payload: { goals: Goal[]; earlyEndMessage: string; disableSystemPrompt: boolean }) => {
      if (isUnstructured) {
        await upsertUnstructuredGoals(
          conversation.id,
          payload.goals,
          payload.earlyEndMessage,
          payload.disableSystemPrompt,
        );
      }

      if (isAssignment) {
        await upsertAssignmentGoals(
          conversation.id,
          payload.goals,
          payload.earlyEndMessage,
          payload.disableSystemPrompt,
        );
      }
    },
    [conversation.id, isAssignment, isUnstructured],
  );

  const autoSaveData = { goals, earlyEndMessage, disableSystemPrompt };
  const { isSaving, lastSavedTime, saveError, saveStatus } = useAutoSave(
    autoSaveData,
    isAllValid,
    saveGoals,
    1500, // optional custom delay
  );

  // If auto‐save error occurs, you can show a toast or message
  useEffect(() => {
    if (saveError) {
      toast.error(saveError);
    }
  }, [saveError]);

  // Auto-save effect using our custom hook
  const handleManualSave = async () => {
    if (!isAllValid) {
      toast.error("Goals are incomplete!");
      return;
    }
    try {
      await upsertUnstructuredGoals(conversation.id, goals, earlyEndMessage);
      return true;
    } catch (e) {
      toast.error("Failed to save goals");
      return false;
    }
  };

  // Callback to update goals state from GoalBuilder
  const handleGoalsChange = (newGoals: Goal[], allValid: boolean) => {
    setGoals(newGoals);
    setIsAllValid(allValid);
  };

  const handleCancelEdit = () => {
    if (isEditingExternal === undefined) {
      setIsEditingInternal(false);
    }
    if (onEditToggle) {
      onEditToggle(false);
    }
    // Reset to original goals from the conversation
    setGoals(conversation.goals || []);
    setEarlyEndMessage(conversation.earlyEndMessage || "");
  };

  const handleDeleteConversation = async (workflowId) => {
    try {
      // Replace with your actual delete function
      await deleteConversation(workflowId);
      router.push("/conversations");
      toast.success("Conversation is deleted");
    } catch (e) {
      toast.error("Failed to delete this conversation...");
    }
  };

  function getSaveStatusMessage(
    saveStatus: "idle" | "saving" | "saved" | "error",
    lastSavedTime: Date | null,
  ): string {
    switch (saveStatus) {
      case "saving":
        return "Saving...";
      case "saved":
        // If we have a timestamp, say “Saved 2 min ago”, else just “Saved”
        return lastSavedTime
          ? `Saved ${formatTimeAgo(lastSavedTime)}`
          : "Saved";
      case "error":
        return "Failed to save changes";
      default:
        // “idle” or fallback
        return lastSavedTime
          ? `Auto-saved ${formatTimeAgo(lastSavedTime)}`
          : "";
    }
  }

  return (
    <div className="flex h-full flex-col">
      {/* Header */}
      <ConversationHeader
        conversation={conversation}
        isEditing={isEditing}
        editButtonText={
          conversation.workflow_type === WorkflowType.UNSTRUCTURED
            ? "Edit Goals"
            : "Edit Questions"
        }
        onEditToggle={setIsEditingInternal}
        isSaving={isSaving}
        autoSaveMessage={getSaveStatusMessage(saveStatus, lastSavedTime)}
      />

      <div className="flex-1 overflow-auto p-6">
        {isEditing ? (
          <GoalBuilder
            conversation={conversation}
            onGoalsChange={handleGoalsChange}
            initialGoals={goals}
            earlyEndMessage={earlyEndMessage}
            onEarlyEndMessageChange={setEarlyEndMessage}
            onDisableSystemPromptChange={handleDisableSystemPromptChange}
            user={user}
          />
        ) : (
          <DisplayGoals
            goals={goals}
            earlyEndMessage={earlyEndMessage}
            conversationType={conversation.workflow_type as WorkflowType}
          />
        )}
      </div>
    </div>
  );
}
