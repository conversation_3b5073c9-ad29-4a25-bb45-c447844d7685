"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  AssignmentGoal,
  Goal,
  UnstructuredGoal,
} from "@/actions/conversations/conversation-workflow";
import { WorkflowType } from "@prisma/client";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { UserRole } from "@prisma/client";
import DestructiveModal from "@/components/destructive-modal";

interface MultiGoalBuilderProps {
  initialGoals?: Goal[];
  // Now we pass back both the list of goals and a boolean indicating if they’re all valid
  onChange: (forms: Goal[], allValid: boolean) => void;
  saveButtonComponent?: React.ReactNode;
  earlyEndMessage?: string;
  onEarlyEndMessageChange: (msg: string) => void;
  conversationType: WorkflowType;
  onDisableSystemPromptChange?: (disabled: boolean) => void;
  user?: { role: UserRole } | null;
  initialDisableSystemPrompt?: boolean;
}

export default function MultiGoalBuilder({
  initialGoals = [],
  onChange,
  saveButtonComponent,
  earlyEndMessage,
  onEarlyEndMessageChange,
  conversationType,
  onDisableSystemPromptChange,
  user,
  initialDisableSystemPrompt = false,
}: MultiGoalBuilderProps) {
  // console.log("conversation type", conversationType);
  const [builderForms, setBuilderForms] = useState<Goal[]>(initialGoals ?? []);
  // formErrors: key = goal.id, value = array of error strings
  const [formErrors, setFormErrors] = useState<Record<string, string[]>>({});
  const [disableSystemPrompt, setDisableSystemPrompt] = useState(initialDisableSystemPrompt);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  console.log("builderForms", builderForms);

  // Handle disable system prompt checkbox change
  const handleDisableSystemPromptChange = (checked: boolean) => {
    if (checked) {
      setShowConfirmDialog(true);
    } else {
      setDisableSystemPrompt(false);
      onDisableSystemPromptChange?.(false);
    }
  };

  // Handle confirmation dialog
  const handleConfirmDisable = () => {
    setDisableSystemPrompt(true);
    setShowConfirmDialog(false);
    onDisableSystemPromptChange?.(true);
  };

  const handleCancelDisable = () => {
    setShowConfirmDialog(false);
  };

  // Validate a single goal
  const validateForm = useCallback(
    (form: Goal): string[] => {
      const errors: string[] = [];
      if (!form.goalText.trim()) {
        errors.push("Goal of the conversation is required");
      }
      if (conversationType === WorkflowType.ASSIGNMENT) {
        const g = form as AssignmentGoal;
        if (!g.followUpNumber || isNaN(g.followUpNumber)) {
          errors.push("Follow-up time is required");
        }
        if (
          g.followUpNumber <= 0 ||
          isNaN(g.followUpNumber) ||
          g.followUpNumber > 50
        ) {
          errors.push("Follow-up time should be between 1 and 50");
        }

        if (!g.followUpUnit) {
          errors.push("Follow-up unit is required");
        }
      }
      return errors;
    },
    [conversationType],
  );

  // Validate all forms and store errors
  const validateAllForms = useCallback(
    (forms: Goal[]) => {
      const newErrors: Record<string, string[]> = {};
      forms.forEach((f) => {
        const errs = validateForm(f);
        if (errs.length > 0) {
          newErrors[f.id] = errs;
        }
      });
      setFormErrors(newErrors);
    },
    [validateForm],
  );

  // Whenever builderForms changes, re-validate and tell the parent
  useEffect(() => {
    validateAllForms(builderForms);
  }, [builderForms, validateAllForms]);

  // Clear Early End message when there is no Goals
  useEffect(() => {
    if (builderForms.length === 0) {
      // console.log("early message deleteing");
      onEarlyEndMessageChange("");
    }
  }, [builderForms, onEarlyEndMessageChange]);

  // Compute allValid whenever builderForms or formErrors changes
  // allValid = "no form is missing fields" OR "we have at least 1 goal"
  const allValid =
    // EITHER zero goals is okay
    builderForms.length === 0 ||
    // OR (builderForms.length > 0 and no missing fields)
    (builderForms.length > 0 &&
      Object.values(formErrors).every((errs) => errs.length === 0));

  // Inform the parent of the current forms and whether they’re all valid
  useEffect(() => {
    onChange(builderForms, allValid);
  }, [builderForms, allValid, onChange]);

  // Add a new empty goal
  const addNewBuilder = () => {
    if (builderForms.length >= 3) return; // limit to 3 goals

    if (conversationType === WorkflowType.UNSTRUCTURED) {
      const newGoal: Goal = {
        id: Math.random().toString(36).substr(2, 9),
        goalText: "",
        mode: "unstructured",
      };
      setBuilderForms((prev) => [...prev, newGoal]);
    }

    if (conversationType === WorkflowType.ASSIGNMENT) {
      const assignmentGoal: Goal = {
        id: Math.random().toString(36).substr(2, 9),
        goalText: "",
        followUpNumber: 3,
        followUpUnit: "days",
        mode: "assignment",
      };
      setBuilderForms((prev) => [...prev, assignmentGoal]);
    }
  };

  // Update a single goal’s fields and re-validate it
  const updateBuilderForm = (id: string, updatedForm: Partial<Goal>) => {
    setBuilderForms((prev: Goal[]): Goal[] =>
      prev.map(
        (form: Goal): Goal =>
          form.id === id ? ({ ...form, ...updatedForm } as Goal) : form,
      ),
    );
  };

  // Remove a goal
  const removeBuilderForm = (id: string) => {
    setBuilderForms((prev) => prev.filter((form) => form.id !== id));
    setFormErrors((prev) => {
      const copy = { ...prev };
      delete copy[id];
      return copy;
    });
  };

  return (
    <div className="flex h-full w-full flex-col">
      {builderForms.map((form, index) => {
        const errorsForThisForm = formErrors[form.id] || [];
        return (
          <div
            key={form.id || `builder-form-${index}`}
            className="flex h-full w-full flex-col"
          >
            {/* Show disable system prompt checkbox only for first goal and only for ADMIN users */}
            {index === 0 && user?.role === UserRole.ADMIN && (
              <div className="mb-4 flex items-center space-x-2">
                <Checkbox
                  id="disable-system-prompt"
                  checked={disableSystemPrompt}
                  onCheckedChange={handleDisableSystemPromptChange}
                />
                <Label
                  htmlFor="disable-system-prompt"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Disable System Prompt
                </Label>
              </div>
            )}

            <div className="mb-2 flex-none">
              <h2 className="text-xl font-semibold">Goal</h2>
            </div>
            <div className="relative grow">
              <Textarea
                id={`goal-${form.id}`}
                value={form.goalText}
                onChange={(e) =>
                  updateBuilderForm(form.id, { goalText: e.target.value })
                }
                placeholder="Enter the specific goal you want to achieve with this conversation. E.g., Understand the student's main concerns about college applications."
                className="h-full w-full resize-none"
              />
            </div>

            {/*Follow up time for assignment conversation*/}
            {conversationType === WorkflowType.ASSIGNMENT && (
              <div className="mt-4">
                <Label htmlFor={`time-${form.id}`}>Follow-up time</Label>
                <div className="flex gap-2">
                  <Input
                    id={`time-${form.id}`}
                    type="number"
                    min={0}
                    max={50}
                    value={String((form as AssignmentGoal).followUpNumber ?? 1)}
                    onChange={(e) =>
                      updateBuilderForm(form.id, {
                        followUpNumber: parseInt(e.target.value, 10) || 0,
                      })
                    }
                    placeholder="Enter follow-up time in days or hours"
                    className="mt-2"
                  />
                  <Select
                    value={(form as AssignmentGoal).followUpUnit ?? "hours"}
                    onValueChange={(value) =>
                      updateBuilderForm(form.id, {
                        followUpUnit: value as "hours" | "days",
                      })
                    }
                  >
                    <SelectTrigger className="mt-2">
                      <SelectValue placeholder="Select unit" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hours">Hours</SelectItem>
                      <SelectItem value="days">Days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}

            {errorsForThisForm.length > 0 && (
              <div className="mt-4 rounded-md bg-red-50 p-3">
                <p className="font-medium text-red-600">
                  Please fill out the following fields:
                </p>
                <ul className="list-disc pl-5 text-red-600">
                  {errorsForThisForm.map((err, i) => (
                    <li key={i}>{err}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        );
      })}

      {/*Hide the add goal button for now*/}

      {/*<div*/}
      {/*  className={`flex justify-center ${*/}
      {/*    builderForms?.length > 0 ? "border-t-2" : ""*/}
      {/*  } border-t-gray-400 pt-4`}*/}
      {/*>*/}
      {/*  <Button*/}
      {/*    variant="outline"*/}
      {/*    onClick={addNewBuilder}*/}
      {/*    disabled={builderForms.length >= 3}*/}
      {/*  >*/}
      {/*    + Add Goal{" "}*/}
      {/*    {builderForms.length > 0 ? `(${builderForms.length}/3)` : ""}*/}
      {/*  </Button>*/}
      {/*</div>*/}

      {/*Hiding the early end message for now*/}
      {/*{builderForms.length >= 1 && (*/}
      {/*  <Card className="mt-8">*/}
      {/*    <CardContent className="p-6">*/}
      {/*      <h2 className="mb-4 text-sm font-semibold">Early End Message</h2>*/}
      {/*      <textarea*/}
      {/*        value={earlyEndMessage}*/}
      {/*        onChange={(e) => onEarlyEndMessageChange(e.target.value)}*/}
      {/*        placeholder="Thanks for chatting! We are going to move on to other conversation. Stay tuned until I reach back out!"*/}
      {/*        className="w-full rounded-md border p-2 text-sm"*/}
      {/*      />*/}
      {/*    </CardContent>*/}
      {/*  </Card>*/}
      {/*)}*/}

      {/* Show the Save button (etc.) if you want */}
      {builderForms.length >= 1 && saveButtonComponent}

      {/* Confirmation dialog for disabling system prompt */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Disable System Prompt</DialogTitle>
            <DialogDescription>
              Are you sure you want to disable the system prompt? This will remove the base AI instructions and only use the conversation-specific prompts.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelDisable}>
              Cancel
            </Button>
            <Button onClick={handleConfirmDisable}>
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
