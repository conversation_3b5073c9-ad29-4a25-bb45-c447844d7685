import {
  AssignmentGoal,
  Goal,
  UnstructuredGoal,
} from "@/actions/conversations/conversation-workflow";
import { WorkflowType } from "@prisma/client";

import { Card, CardContent } from "@/components/ui/card";
import { Markdown } from "@/components/ui/content/markdown";

interface DisplayGoalsProps {
  goals: Goal[];
  earlyEndMessage: string;
  conversationType: WorkflowType;
}

const followUpUnitConverter = (
  followUpNumber: number,
  followUpUnit: "hours" | "days",
) => {
  if (followUpNumber === 1 && followUpUnit === "hours") {
    return "hour";
  }

  if (followUpNumber === 1 && followUpUnit === "days") {
    return "day";
  }

  return followUpUnit;
};

export function DisplayGoals({
  goals,
  earlyEndMessage,
  conversationType,
}: DisplayGoalsProps) {
  const isAssignment = conversationType === WorkflowType.ASSIGNMENT;
  const isUnstructured = conversationType === WorkflowType.UNSTRUCTURED;

  return (
    <div className="flex h-full w-full flex-col overflow-y-auto">
      {goals.length === 0 ? (
        <div className="flex h-full items-center justify-center">
          <p className="text-muted-foreground">No goals have been added yet.</p>
        </div>
      ) : (
        goals.map((goal, index) => (
          <div key={goal.id || `goal-${index}`} className="flex h-full w-full flex-col">
            <div className="mb-2 flex-none">
              <h2 className="text-xl font-semibold">Goal</h2>
            </div>
            <div className="grow">
              <div className="h-full w-full overflow-hidden whitespace-normal break-all rounded-md bg-muted p-3">
                <Markdown content={goal.goalText} />
              </div>
            </div>

            {/* Only show follow-up time for assignment conversations */}
            {isAssignment && (
              <div className="mt-4 flex-none">
                <h3 className="font-medium">Follow-up time</h3>
                <p className="mt-2 overflow-hidden whitespace-normal break-all rounded-md bg-muted p-3">
                  {(goal as AssignmentGoal).followUpNumber}{" "}
                  {followUpUnitConverter(
                    (goal as AssignmentGoal).followUpNumber,
                    (goal as AssignmentGoal).followUpUnit,
                  )}
                </p>
              </div>
            )}
          </div>
        ))
      )}
      {/*We hide the early end message for now*/}
      {/*{goals.length > 0 && (*/}
      {/*  <Card className="mt-8">*/}
      {/*    <CardContent className="p-6">*/}
      {/*      <h2 className="mb-4 text-xl font-semibold">Early End Message</h2>*/}
      {/*      <div className="overflow-hidden whitespace-normal break-all rounded-md bg-muted p-3">*/}
      {/*        {earlyEndMessage*/}
      {/*          ? earlyEndMessage*/}
      {/*          : "No early end message saved yet."}*/}
      {/*      </div>*/}
      {/*    </CardContent>*/}
      {/*  </Card>*/}
      {/*)}*/}
    </div>
  );
}
