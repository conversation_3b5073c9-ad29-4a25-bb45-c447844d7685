"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  CustomConversation,
  deleteConversation,
  publishConversation,
} from "@/actions/conversations/conversation-workflow";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import SaveAndPublishButtons from "@/components/Conversations/SaveAndPublishButtons";
import DestructiveModal from "@/components/destructive-modal";
import { TooltipWrapper } from "@/components/shared/TooltipWrapper";
import { checkWorkflowPermissions } from "@/lib/workflow-permissions";
import { useUser } from "@/app/context/UserContext";

interface ConversationHeaderProps {
  conversation: CustomConversation;
  onEditToggle?: (isEditing: boolean) => void;
  isEditing?: boolean;
  isSaving?: boolean;
  autoSaveMessage?: string;
  disablePublish?: boolean;
  onPublish?: () => Promise<void>;
  onSave?: () => Promise<void>;
  editButtonText?: string;
}

export function ConversationHeader({
  conversation,
  onEditToggle,
  isEditing = false,
  isSaving = false,
  autoSaveMessage = "",
  disablePublish = false,
  onPublish,
  onSave,
  editButtonText = "Edit Questions",
}: ConversationHeaderProps) {
  const router = useRouter();
  const { user } = useUser();

  // Check permissions for this workflow
  const permissions = user ? checkWorkflowPermissions(
    user.role, 
    user.id, 
    conversation.owner_id
  ) : { canEdit: false, canDelete: false, restrictionReason: "User not logged in" };

  const handleCancelEdit = () => {
    if (onEditToggle) onEditToggle(false);
    router.refresh();
  };

  const handleEdit = () => {
    if (onEditToggle) onEditToggle(true);
    router.refresh();
  };

  const handleDeleteConversation = async (workflowId: string) => {
    try {
      await deleteConversation(workflowId);
      router.push("/conversations");
      toast.success("Conversation is deleted");
    } catch (e) {
      toast.error("Failed to delete this conversation...");
    }
  };

  // Default publish handler if none provided
  const handleDefaultPublish = async () => {
    try {
      if (onPublish) {
        await onPublish();
        return;
      }

      const result = await publishConversation(conversation.id);
      if (result.success) {
        toast.success("Conversation published successfully!");
        router.refresh();
      } else {
        toast.error(result.error || "Failed to publish conversation");
      }
    } catch (error) {
      toast.error("An error occurred while publishing the conversation");
    }
  };

  return (
    <>
      {/* Header: conversation title and (optionally) an Edit button */}
      <div className="flex items-center justify-between border-b p-4">
        <h1 className="text-center text-lg font-semibold">
          {conversation.name}
        </h1>

        <div className="flex items-center gap-4">
          {conversation.status === "DRAFT" ? (
            conversation.type === "custom" ? (
              onEditToggle ? (
                isEditing ? (
                  <Button onClick={handleCancelEdit}>Cancel Edit</Button>
                ) : (
                  <>
                    {!permissions.canEdit && permissions.restrictionReason ? (
                      <TooltipWrapper tooltipContent={permissions.restrictionReason}>
                        <div>
                          <Button disabled={true}>{editButtonText}</Button>
                        </div>
                      </TooltipWrapper>
                    ) : (
                      <Button onClick={handleEdit}>{editButtonText}</Button>
                    )}
                  </>
                )
              ) : null
            ) : (
              <div className="flex items-center gap-1">
                <Button className="cursor-not-allowed" disabled={true}>
                  Can&#39;t edit Addie Templates
                </Button>
              </div>
            )
          ) : (
            <div className="flex items-center gap-1">
              <Button className="cursor-not-allowed" disabled={true}>
                Can&#39;t edit published{" "}
                {conversation.workflow_type === "UNSTRUCTURED"
                  ? "goals"
                  : "questions"}
              </Button>
            </div>
          )}

          {!conversation.isSystemConversation && (
            <>
              {!permissions.canDelete && permissions.restrictionReason ? (
                <TooltipWrapper tooltipContent={permissions.restrictionReason}>
                  <div>
                    <DestructiveModal
                      handler={() => handleDeleteConversation(conversation.id)}
                      title="Delete Conversation"
                      description="Are you sure you want to delete this conversation? This action cannot be undone."
                      btnTitle="Delete"
                      variant="destructive"
                      className="ml-2"
                      disabled={true}
                    />
                  </div>
                </TooltipWrapper>
              ) : (
                <DestructiveModal
                  handler={() => handleDeleteConversation(conversation.id)}
                  title="Delete Conversation"
                  description="Are you sure you want to delete this conversation? This action cannot be undone."
                  btnTitle="Delete"
                  variant="destructive"
                  className="ml-2"
                  disabled={false}
                />
              )}
            </>
          )}
        </div>
      </div>

      {/* Save and Publish Buttons */}
      <SaveAndPublishButtons
        type={conversation.type}
        conversationId={conversation.id}
        conversationName={conversation.name}
        onPublish={onPublish || handleDefaultPublish}
        isSaving={isSaving}
        status={conversation.status}
        autoSaveMessage={autoSaveMessage}
        disablePublish={disablePublish}
        hideSaveButton={!onSave}
        onSave={onSave}
        workflowOwnerId={conversation.owner_id}
      />
    </>
  );
}
