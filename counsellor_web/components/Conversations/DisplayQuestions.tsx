"use client";

import React from "react";
import { QuestionItem } from "@/actions/conversations/conversation-workflow";

import { Markdown } from "@/components/ui/content/markdown";
import { ShowMoreLess } from "@/components/shared/ShowMoreLess";

const markdownStyles = `
  .markdown-list-fix ol {
    padding-left: 2.5rem !important;
  }
  .markdown-list-fix ol li {
    margin-left: 0.5rem;
  }
  .markdown-list-fix ul {
    padding-left: 2rem !important;
  }
`;

interface DisplayQuestionsProps {
  questions: QuestionItem[];
  description?: string;
}

export function DisplayQuestions({
  questions,
  description,
}: DisplayQuestionsProps) {
  return (
    <div className="space-y-4">
      <style jsx global>
        {markdownStyles}
      </style>

      {description && (
        <div className="mb-6 rounded-lg border p-4">
          <h3 className="mb-4 font-bold">About This Conversation</h3>
          <ShowMoreLess
            content={
              <Markdown
                content={description}
                className="hyphens-auto break-all text-muted-foreground"
              />
            }
            truncatedContent={
              <Markdown
                content={`${description.slice(0, 300)}...`}
                className="hyphens-auto break-all text-muted-foreground"
              />
            }
            threshold={300}
            className="markdown-list-fix break-words"
            isLong={description.length > 300}
          />
        </div>
      )}

      {questions && questions.length > 0 ? (
        questions.map((question, index) => (
          <div
            key={index}
            className="flex flex-col justify-start rounded-lg border p-4"
          >
            {/*First line*/}
            <div key={index} className="flex justify-between">
              <div className="flex items-center justify-center">
                <strong className="mr-1">Question {index + 1}:</strong>{" "}
                {question.text}
                {question.canSkip || question.canSkip === undefined ? (
                  <span className="ml-1 text-xs text-muted-foreground">
                    (Optional)
                  </span>
                ) : (
                  <span className="ml-1 text-xs text-red-500">(Required)</span>
                )}
              </div>
              <div className="italic text-muted-foreground">
                Type: {question.type}
              </div>
            </div>

            {/*  Second line - question tags*/}
            <div>
              {question.tags && question.tags.length > 0 && (
                <div className="mt-3 flex flex-wrap gap-2">
                  {question.tags.map((tag, index) => (
                    <div
                      key={index}
                      className="rounded-lg border bg-muted p-1 px-2 text-xs font-semibold"
                    >
                      {tag}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        ))
      ) : (
        <div className="text-center">No questions saved.</div>
      )}
    </div>
  );
}
