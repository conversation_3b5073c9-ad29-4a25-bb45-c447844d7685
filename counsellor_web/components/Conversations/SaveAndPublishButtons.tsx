"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  publishConversation,
  unpublishConversation,
  type ConversationStatus,
} from "@/actions/conversations/conversation-workflow";
import { Users } from "lucide-react";
import { toast } from "sonner";

import { checkWorkflowPermissions } from "@/lib/workflow-permissions";
import { Button } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { ManageAssignmentModal } from "@/components/Conversations/ManageAssignmentModal";
import { EnhancedPublishButton } from "@/components/Conversations/Student-ConversationTab/EnhancedPublishButton";
import DestructiveModal from "@/components/destructive-modal";
import { TooltipWrapper } from "@/components/shared/TooltipWrapper";
import { useUser } from "@/app/context/UserContext";

interface SaveAndPublishButtonsProps {
  type?: string;
  conversationId: string;
  conversationName?: string; // Add conversation name for the modal
  onSave?: () => Promise<void>;
  onPublish?: () => Promise<void>;
  isSaving?: boolean;
  status?: ConversationStatus;
  autoSaveMessage?: string;
  disablePublish?: boolean;
  hideSaveButton?: boolean; // New prop to support autosave-only mode
  workflowOwnerId?: string | null;
}

const SaveAndPublishButtons = ({
  type,
  conversationId,
  conversationName = "Conversation", // Default name
  onSave,
  onPublish,
  isSaving,
  status,
  autoSaveMessage,
  disablePublish,
  hideSaveButton = false, // Default to showing save button for backward compatibility
  workflowOwnerId,
}: SaveAndPublishButtonsProps) => {
  const [isPublishing, setIsPublishing] = useState(false);
  const [localStatus, setLocalStatus] = useState<ConversationStatus>(
    status || "DRAFT",
  );
  const [showManageModal, setShowManageModal] = useState(false);
  const { user } = useUser();

  const router = useRouter();

  // Check workflow permissions
  const permissions = user
    ? checkWorkflowPermissions(user.role, user.id, workflowOwnerId)
    : {
        canEdit: false,
        canDelete: false,
        canPublish: false,
        canUnpublish: false,
        restrictionReason: "Authentication required",
      };

  // Sync localStatus with prop changes.
  useEffect(() => {
    setLocalStatus(status || "DRAFT");
  }, [status]);

  const handlePublish = async () => {
    if (disablePublish) {
      toast.error(
        "Make sure you have goals or questions added before publishing",
      );
      return;
    }

    setIsPublishing(true);
    try {
      // Use the onPublish prop which now saves questions before publishing
      if (onPublish) {
        await onPublish();
        setLocalStatus("PUBLISHED");
      } else {
        // Fallback to direct API call if onPublish not provided
        const result = await publishConversation(conversationId);
        if (result?.error) {
          toast.error("This conversation is already published");
        }
        if (result?.success) {
          setLocalStatus("PUBLISHED");
        }
      }
    } catch (error) {
      console.error("Publishing error:", error);
      toast.error("Failed to publish conversation");
    } finally {
      setIsPublishing(false);
      router.refresh();
    }
  };

  const handleUnpublish = async () => {
    setIsPublishing(true);
    const result = await unpublishConversation(conversationId);
    if (result?.error) {
      toast.error("Failed to take down this conversation");
    }
    if (result?.success) {
      toast.success("Conversation is taken down successfully!");
      setLocalStatus("DRAFT");
    }
    setIsPublishing(false);
    router.refresh();
  };

  return (
    <div className="flex items-center justify-between p-4">
      {/*need to replace with real updatedAt*/}
      {type !== "onboarding" ? (
        <span className="text-sm text-gray-400">{autoSaveMessage}</span>
      ) : (
        <span> </span>
      )}

      <div className="flex gap-2">
        {type !== "onboarding" && !hideSaveButton && (
          <>
            {!permissions.canEdit && permissions.restrictionReason ? (
              <TooltipWrapper tooltipContent={permissions.restrictionReason}>
                <div>
                  <Button variant="outline" onClick={onSave} disabled={true}>
                    {isSaving ? <LoadingSpinner /> : "Save"}
                  </Button>
                </div>
              </TooltipWrapper>
            ) : (
              <Button
                variant="outline"
                onClick={onSave}
                disabled={isSaving || isPublishing}
              >
                {isSaving ? <LoadingSpinner /> : "Save"}
              </Button>
            )}
          </>
        )}
        {/* Show autosave indicator if in autosave mode */}
        {hideSaveButton && isSaving && (
          <div className="flex items-center text-sm text-gray-500">
            <LoadingSpinner className="mr-2 h-4 w-4" />
            <span>Saving changes...</span>
          </div>
        )}
        {localStatus === "DRAFT" ? (
          <EnhancedPublishButton
            conversationId={conversationId}
            onPublish={handlePublish}
            isPublishing={isPublishing}
            setLocalStatus={setLocalStatus}
            disablePublish={disablePublish || !permissions.canPublish}
            workflowOwnerId={workflowOwnerId}
            restrictionReason={permissions.restrictionReason}
          />
        ) : (
          <>
            <Button
              variant="outline"
              onClick={() => setShowManageModal(true)}
              className="cursor-pointer bg-white text-black hover:bg-gray-100"
            >
              <Users className="h-4 w-4" />
              Manage Assigned Students
            </Button>
            {!permissions.canUnpublish && permissions.restrictionReason ? (
              <TooltipWrapper tooltipContent={permissions.restrictionReason}>
                <div>
                  <DestructiveModal
                    handler={handleUnpublish}
                    title="Unpublish Conversation"
                    description="Are you sure you want to unpublish this conversation? This will remove it from all students. Use Manage Assigned Students to modify student assignments instead."
                    btnTitle="Unpublish"
                    className="bg-black px-4 hover:bg-gray-800"
                    action="Unpublishing..."
                    disabled={true}
                  />
                </div>
              </TooltipWrapper>
            ) : (
              <DestructiveModal
                handler={handleUnpublish}
                title="Unpublish Conversation"
                description="Are you sure you want to unpublish this conversation? This will remove it from all students. Use Manage Assigned Students to modify student assignments instead."
                btnTitle="Unpublish"
                className="bg-black px-4 hover:bg-gray-800"
                action="Unpublishing..."
                disabled={false}
              />
            )}
          </>
        )}
      </div>

      {/* Manage Assignment Modal */}
      <ManageAssignmentModal
        isOpen={showManageModal}
        onClose={() => setShowManageModal(false)}
        conversationId={conversationId}
        conversationName={conversationName}
      />
    </div>
  );
};

export default SaveAndPublishButtons;
