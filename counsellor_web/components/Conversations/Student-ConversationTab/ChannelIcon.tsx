// components/Conversations/Student-ConversationTab/ChannelIcon.tsx
"use client";

import React from "react";
import { Globe, MessageSquare, Phone } from "lucide-react";

interface ChannelIconProps {
  source: string;
  className?: string;
}

export function ChannelIcon({ source, className = "" }: ChannelIconProps) {
  const getChannelIcon = (source: string) => {
    switch (source?.toLowerCase()) {
      case "voice":
        return <Phone className="h-3 w-3" />;
      case "sms":
        return <MessageSquare className="h-3 w-3" />;
      case "web":
      default:
        return <Globe className="h-3 w-3" />;
    }
  };

  const getChannelLabel = (source: string) => {
    switch (source?.toLowerCase()) {
      case "voice":
        return "Voice Call";
      case "sms":
        return "SMS";
      case "web":
      default:
        return "Web";
    }
  };

  return (
    <span
      className={`inline-flex items-center justify-center ${className}`}
      title={getChannelLabel(source)}
      aria-label={`Message sent via ${getChannelLabel(source)}`}
    >
      {getChannelIcon(source)}
    </span>
  );
}
