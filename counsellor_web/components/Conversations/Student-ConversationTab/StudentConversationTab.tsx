import { useEffect, useState } from "react";
import {
  getPublishedStudentConversations,
  getStudentAnswers,
} from "@/actions/conversations/student-conversation";

import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { useConversation } from "@/app/context/ConversationContext";

import { StudentConvoDetails } from "./StudentConvoDetails";
import { StudentConvoList } from "./StudentConvoList";

interface StudentConversationTabProps {
  studentId: string;
}

export function StudentConversationTab({
  studentId,
}: StudentConversationTabProps) {
  const { activeConversation, setActiveConversation } = useConversation();
  const [conversations, setConversations] = useState<any>([]);
  const [isLoadingConvoList, setIsLoadingConvoList] = useState(true);
  const [convoStats, setConvoStats] = useState<
    Record<
      string,
      {
        answeredCount: number;
        totalQuestions: number;
      }
    >
  >({});

  // Reset state when studentId changes
  useEffect(() => {
    setConversations([]);
    setActiveConversation(null);
    setIsLoadingConvoList(true);
  }, [studentId, setActiveConversation]);

  // Fetch conversations
  useEffect(() => {
    let isMounted = true;

    const fetchConversations = async () => {
      try {
        setIsLoadingConvoList(true);
        const data = await getPublishedStudentConversations(studentId);
        if (!isMounted) return;

        setConversations(data);

        // Don't auto-select any conversation by default
        // This prevents details from showing before the list is ready

        const stats: Record<
          string,
          { answeredCount: number; totalQuestions: number }
        > = {};

        for (const convo of data) {
          const workflowId = convo.workflow.id;
          const { answerMap } = await getStudentAnswers(studentId, workflowId);

          const answeredCount = Object.values(answerMap).filter(
            (ans) => ans && ans.trim() !== "" && ans.trim() !== "No answer",
          ).length;

          // totalQuestions = convo.steps.length
          const totalQuestions = convo.steps.length;

          stats[convo.id] = { answeredCount, totalQuestions };
        }
        setConvoStats(stats);
      } catch (e) {
        console.error("Fetching conversations failed:", e);
      } finally {
        if (isMounted) {
          setIsLoadingConvoList(false);
        }
      }
    };

    fetchConversations();

    return () => {
      isMounted = false;
    };
  }, [studentId]);

  return (
    <div className="mt-6 flex h-[calc(100vh-200px)] min-h-[600px] pr-8">
      <div className="min-w-[250px] max-w-[40%] flex-[0_0_23%]">
        {isLoadingConvoList ? (
          <div className="flex h-full flex-col items-center justify-center gap-2">
            <LoadingSpinner />
            <span className="animate-in">Loading conversations...</span>
          </div>
        ) : (
          <StudentConvoList
            conversations={conversations}
            selectedId={activeConversation?.id}
            onSelect={setActiveConversation}
            convoStats={convoStats}
          />
        )}
      </div>

      <div className="ml-6 flex flex-1 flex-col">
        {isLoadingConvoList ? (
          <div className="flex h-full items-center justify-center">
            <LoadingSpinner />
          </div>
        ) : activeConversation ? (
          <StudentConvoDetails
            conversation={activeConversation}
            studentId={studentId}
            convoStats={convoStats}
          />
        ) : (
          <div className="flex h-full flex-col items-center justify-center text-muted-foreground">
            <p>Select a conversation to view details</p>
          </div>
        )}
      </div>
    </div>
  );
}
