import { StudentWorkflowStatus, WorkflowType } from "@prisma/client";
import {
  <PERSON>t<PERSON><PERSON>ageS<PERSON><PERSON>,
  ClipboardList,
  FlaskConical,
  NotebookPen,
  SquareLibrary,
} from "lucide-react";

import { formatTimeAgo, workflowTypeHelper } from "@/common/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { TooltipWrapper } from "@/components/shared/TooltipWrapper";

export interface ConvoStats {
  [workflowId: string]: {
    answeredCount: number;
    totalQuestions: number;
  };
}
interface StudentConvoListProps {
  conversations: any;
  selectedId?: string;
  onSelect: (conversation: any) => void;
  convoStats: ConvoStats;
}

export function getCompletionPercent(
  workflowType: WorkflowType,
  answeredCount: number,
  totalQuestions: number,
  completedGoals: number,
  totalGoals: number,
): number {
  if (workflowType === WorkflowType.STRUCTURED) {
    return totalQuestions > 0
      ? Math.round((answeredCount / totalQuestions) * 100)
      : 0;
  }
  if (
    workflowType === WorkflowType.UNSTRUCTURED ||
    workflowType === WorkflowType.ASSIGNMENT
  ) {
    return totalGoals > 0 ? Math.round((completedGoals / totalGoals) * 100) : 0;
  }
  return 0;
}

export const statusConverter = (status: string) => {
  if (status === StudentWorkflowStatus.IN_PROGRESS) return "In Progress";
  if (status === StudentWorkflowStatus.COMPLETED) return "Completed";
  if (status === StudentWorkflowStatus.NOT_STARTED) return "Not Started";
  if (status === StudentWorkflowStatus.COUNSELOR_UNPUBLISHED)
    return "Unpublished";
  return "Unknown";
};
export function StudentConvoList({
  conversations,
  selectedId,
  onSelect,
  convoStats,
}: StudentConvoListProps) {
  const statusConverter = (status: string) => {
    if (status === StudentWorkflowStatus.IN_PROGRESS) return "In Progress";
    if (status === StudentWorkflowStatus.COMPLETED) return "Completed";
    if (status === StudentWorkflowStatus.NOT_STARTED) return "Not Started";
    if (status === StudentWorkflowStatus.COUNSELOR_UNPUBLISHED)
      return "Unpublished";
    return "Unknown";
  };

  const conversationGeneratedTypeTooltip = (conversationTag: string[]) => {
    if (conversationTag.includes("custom")) {
      return (
        <TooltipWrapper tooltipContent="Conversation built manually by a counselor">
          <div className="flex items-center gap-1 text-sm">
            <FlaskConical className="h-4 w-4" />
            <span className="text-xs">Custom</span>
          </div>
        </TooltipWrapper>
      );
    } else {
      return (
        <TooltipWrapper tooltipContent="Conversation built using an Addie system template">
          <div className="flex items-center gap-1 text-sm">
            <SquareLibrary className="h-4 w-4" />
            <span className="text-xs">Addie Template</span>
          </div>
        </TooltipWrapper>
      );
    }
  };

  const workflowTypeTooltip = (workflowType?: WorkflowType) => {
    if (!workflowType) return "Unknown";

    if (workflowTypeHelper(workflowType) === "Structured") {
      return (
        <TooltipWrapper tooltipContent="Conversation uses a structured form approach to record student responses">
          <div className="flex items-center gap-1">
            <ClipboardList className="h-4 w-4" />
            <span className="text-sm font-medium">
              {workflowTypeHelper(workflowType)}
            </span>
          </div>
        </TooltipWrapper>
      );
    }

    if (workflowTypeHelper(workflowType) === "Open Ended") {
      return (
        <TooltipWrapper tooltipContent="Conversation uses Addie’s natural conversation abilities to achieve the desired goal and uncover insights">
          <div className="flex items-center gap-1">
            <BotMessageSquare className="h-4 w-4" />
            <span className="text-sm font-medium">
              {workflowTypeHelper(workflowType)}
            </span>
          </div>
        </TooltipWrapper>
      );
    }

    if (workflowTypeHelper(workflowType) === "Assignment") {
      return (
        <TooltipWrapper tooltipContent="Assignment uses Addie’s natural conversation abilities to achieve the desired goal and uncover insights">
          <div className="flex items-center gap-1">
            <NotebookPen className="h-4 w-4" />
            <span className="text-sm font-medium">
              {workflowTypeHelper(workflowType)}
            </span>
          </div>
        </TooltipWrapper>
      );
    }
  };

  return (
    <ScrollArea className="h-full w-full rounded-lg border">
      <div className="space-y-1">
        {conversations.map((conversation) => {
          const workflowType = conversation.workflow.workflow_type;
          const stats = convoStats[conversation.id] || {
            answeredCount: 0,
            totalQuestions: conversation.steps.length,
          };
          const answeredCount = stats.answeredCount;
          const totalQuestions = stats.totalQuestions;
          const percent =
            totalQuestions > 0
              ? Math.round((answeredCount / totalQuestions) * 100)
              : 0;

          // stats for unstructured convos
          const totalGoals = conversation.steps.length;
          const completedGoals = conversation.steps.filter(
            (goal) => goal.completed,
          ).length;

          return (
            <button
              key={conversation.id}
              onClick={() => onSelect(conversation)}
              className={`flex w-full flex-col gap-3 border-b border-t border-gray-300 px-4 py-3 text-left hover:bg-sky-50 ${
                selectedId === conversation.id ? "bg-sky-50" : ""
              }`}
            >
              <div className="flex justify-between">
                {conversationGeneratedTypeTooltip(
                  conversation.workflow.tags || [],
                )}
                <span className="border-spacing-1 rounded-md border border-gray-300 px-1 text-sm font-medium">
                  {getCompletionPercent(
                    workflowType,
                    answeredCount,
                    totalQuestions,
                    completedGoals,
                    totalGoals,
                  )}
                  %
                </span>
              </div>
              <div className="text-sm font-medium">
                {/*Conversation name*/}
                {conversation.workflow.name}
              </div>
              {/*Header row*/}
              <div className="mb-2 flex w-full items-center justify-between gap-4 text-xs">
                <div className="text-xs font-light text-muted-foreground">
                  {formatTimeAgo(new Date(conversation.updated_at))}
                </div>
              </div>

              {/*Bottom row include number of questions and complete status*/}

              <div className="flex justify-between">
                <div
                  className={`rounded-md px-2 py-1 text-xs font-medium outline outline-1 outline-gray-300 ${
                    conversation.status === StudentWorkflowStatus.COMPLETED
                      ? "bg-green-100"
                      : conversation.status ===
                            StudentWorkflowStatus.NOT_STARTED ||
                          conversation.status ===
                            StudentWorkflowStatus.COUNSELOR_UNPUBLISHED
                        ? "bg-gray-100"
                        : "bg-yellow-100"
                  }`}
                >
                  <span className="truncate whitespace-nowrap px-0 text-right text-xs">
                    {statusConverter(conversation.status)}
                  </span>
                </div>

                {workflowTypeTooltip(workflowType)}
              </div>
            </button>
          );
        })}
      </div>
    </ScrollArea>
  );
}
