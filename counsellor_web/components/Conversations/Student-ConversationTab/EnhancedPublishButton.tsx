"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { publishConversation } from "@/actions/conversations/conversation-workflow";
import { toast } from "sonner";

import log from "@/common/logger";
import { StudentWithWorkflows } from "@/common/types";
import { Button } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { AssignmentModal } from "@/components/Conversations/AssignmentModal";
import { useSchool } from "@/app/context/SchoolContext";
import { TooltipWrapper } from "@/components/shared/TooltipWrapper";

interface EnhancedPublishButtonProps {
  conversationId: string;
  onPublish: () => Promise<void>;
  isPublishing?: boolean;
  setLocalStatus: (status: "DRAFT" | "PUBLISHED" | "INACTIVE") => void;
  disablePublish?: boolean;
  workflowOwnerId?: string | null;
  restrictionReason?: string;
}

export function EnhancedPublishButton({
  conversationId,
  onPublish,
  isPublishing = false,
  setLocalStatus,
  disablePublish,
  workflowOwnerId,
  restrictionReason,
}: EnhancedPublishButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const router = useRouter();
  const { activeSchool } = useSchool();

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleConfirmAssignment = async (
    assignToAll: boolean,
    selectedStudents: StudentWithWorkflows[],
    selectedSchoolId?: string,
  ) => {
    try {
      // return if counselors didn't select anyone
      if (!assignToAll && selectedStudents.length === 0) {
        toast.error("You haven't selected any students");
        return;
      }
      const loadingToastId = toast.loading(
        "Assigning to students... Please wait.",
      );

      if (assignToAll) {
        // Publish to ALL (with school filtering)
        // For admin users, use selectedSchoolId; for counselors, use activeSchool
        const schoolToUse = selectedSchoolId || activeSchool || undefined;
        const result = await publishConversation(conversationId, undefined, schoolToUse);

        if (result.error) {
          throw new Error(result.error);
        }
      } else {
        // Publish to SPECIFIC students
        const studentIds = selectedStudents.map((s) => s.id);
        const schoolToUse = selectedSchoolId || activeSchool || undefined;

        const result = await publishConversation(conversationId, studentIds, schoolToUse);
        if (result.error) {
          throw new Error(result.error);
        }
      }

      toast.dismiss(loadingToastId);
      toast.success("Conversation assigned and published successfully!");
      setLocalStatus("PUBLISHED");
      handleCloseModal();
      log.debug("## getting ready to refresh ##");
      window.location.reload();
    } catch (error) {
      console.error("Error assigning conversation:", error);
      toast.dismiss();
      toast.error(`Failed to assign and publish conversation: ${error}`);
    }
  };

  return (
    <>
      {disablePublish && restrictionReason ? (
        <TooltipWrapper tooltipContent={restrictionReason}>
          <div>
            <Button
              onClick={handleOpenModal}
              disabled={true}
            >
              {isPublishing ? <LoadingSpinner /> : "Publish"}
            </Button>
          </div>
        </TooltipWrapper>
      ) : (
        <Button
          onClick={handleOpenModal}
          disabled={isPublishing}
        >
          {isPublishing ? <LoadingSpinner /> : "Publish"}
        </Button>
      )}

      <AssignmentModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onConfirm={handleConfirmAssignment}
        conversationId={conversationId}
      />
    </>
  );
}
