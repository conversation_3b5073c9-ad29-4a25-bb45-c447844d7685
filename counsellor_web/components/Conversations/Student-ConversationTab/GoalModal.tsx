"use client";

import type React from "react";
import { useState } from "react";
import { ChevronDown } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Markdown } from "@/components/ui/content/markdown";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import type { Steps } from "@/components/Conversations/Student-ConversationTab/StudentUnstructuredConvo";
import { TooltipWrapper } from "@/components/shared/TooltipWrapper";

interface GoalModalProps {
  steps: Steps[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const GoalModal: React.FC<GoalModalProps> = ({ steps, open, onOpenChange }) => {
  const [showMore, setShowMore] = useState<Record<number, boolean>>({});
  const isLongText = (text: string) => text.length > 300;
  const toggleShowMore = (index: number) => {
    setShowMore((prev) => ({ ...prev, [index]: !prev[index] }));
  };
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md md:max-w-[75vw]">
        <DialogHeader>
          <DialogTitle className="text-center text-xl font-semibold">
            {steps.length > 1 ? "Goals" : "Goal"}{" "}
            <span className="text-sm text-muted-foreground">
              {steps.length > 1 ? `(${steps.length} goals)` : "(1 goal)"}
            </span>
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="max-h-[70vh] w-full px-1 py-4">
          <div className="flex w-full flex-col justify-center gap-4">
            {steps.map((step, index) => (
              <Card
                key={step.id}
                className="flex h-full w-full flex-col justify-between p-6"
              >
                <div className="flex w-full flex-col items-center justify-center">
                  <h1 className="mb-4 text-center text-xl font-bold">
                    Goal {steps.length > 1 ? `${index + 1}` : ""}
                  </h1>

                  <div className="w-full overflow-hidden">
                    <div className="text-start text-gray-700 transition-all">
                      {isLongText(step.data?.goalText || "") &&
                      !showMore[index] ? (
                        <Markdown
                          content={`${step.data?.goalText?.slice(0, 300)}...`}
                        />
                      ) : (
                        <Markdown
                          content={
                            step.data?.goalText || "No goal text provided"
                          }
                        />
                      )}
                    </div>
                  </div>
                  {/**/}
                  {isLongText(step.data?.goalText || "") && (
                    <TooltipWrapper
                      tooltipContent={
                        showMore[index] ? "Show less" : "Show more"
                      }
                    >
                      <Button
                        variant="outline"
                        onClick={() => toggleShowMore(index)}
                        className="mt-2"
                      >
                        <ChevronDown
                          className={`mx-auto h-4 w-4 text-gray-700 transition-all ${
                            showMore[index] ? "rotate-180" : "animate-bounce"
                          }`}
                        />
                      </Button>
                    </TooltipWrapper>
                  )}
                </div>

                <div className="mt-4 flex justify-center">
                  <div
                    className={`inline-block rounded-md px-2 py-1 text-xs font-bold ${
                      step.completed
                        ? "bg-green-100 text-green-800"
                        : "bg-gray-100 text-gray-600"
                    }`}
                  >
                    {step.completed ? "Completed" : "Not Completed"}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </ScrollArea>

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default GoalModal;
