import { Card } from "@/components/ui/card";
import { Markdown } from "@/components/ui/content/markdown";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { ScrollArea } from "@/components/ui/scroll-area";
import { StudentConvoHeader } from "@/components/Conversations/Student-ConversationTab/StudentConvoHeader";

interface StudentStructuredConvoProps {
  loading: boolean;
  conversation: any;
  answerMap: Record<string, string>;
  completionPercent?: number;
}

export default function StudentStructuredConvo({
  conversation,
  loading,
  answerMap,
  completionPercent = 0,
}: StudentStructuredConvoProps) {
  return (
    <div className="flex h-full flex-col bg-muted p-4">
      <StudentConvoHeader
        conversation={conversation}
        completionPercent={completionPercent}
      />
      <div className="mb-4 space-y-1">
        <h2 className="text-xl font-semibold">Questions and Answers</h2>
        <p className="text-sm text-muted-foreground">
          {conversation.steps?.length || 0} questions
        </p>
      </div>

      {loading ? (
        <div className="flex h-full items-center justify-center p-8 text-muted-foreground">
          <LoadingSpinner />
          <span>Loading conversation...</span>
        </div>
      ) : (
        <ScrollArea className="flex-1 pr-4">
          <div className="space-y-4" key={conversation.id}>
            {conversation.steps
              ?.sort((a, b) => {
                const workflowStepA = conversation.workflow.steps.find(
                  (ws: any) => ws.id === a.step_id,
                );
                const workflowStepB = conversation.workflow.steps.find(
                  (ws: any) => ws.id === b.step_id,
                );
                return (
                  (workflowStepA?.index ?? 0) - (workflowStepB?.index ?? 0)
                );
              })
              .map((question: any, index: number) => {
                const qId = question.data.questionId;
                const qText = question.data.question;
                const studentAnswer = answerMap[qId] || "No answer";

                return (
                  <Card key={qId} className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <span className="flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-muted text-sm font-medium">
                          {index + 1}
                        </span>
                        <div className="space-y-1">
                          <h3 className="font-medium leading-tight">{qText}</h3>
                        </div>
                      </div>
                      <div className="ml-9">
                        {studentAnswer !== "No answer" ? (
                          <div className="rounded-md bg-muted px-3 py-2">
                            <Markdown content={studentAnswer} />
                          </div>
                        ) : (
                          <div className="h-auto rounded-md px-3 py-2"></div>
                        )}
                      </div>
                    </div>
                  </Card>
                );
              })}
          </div>
        </ScrollArea>
      )}
    </div>
  );
}
