// components/Conversations/UnstructuredConvoModal.tsx
"use client";

import React, { useState } from "react";
import {
  getSystemPromptByMessageId,
  SystemPromptData,
} from "@/actions/conversations/system-prompts";
import { Info } from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";

import { ChannelIcon } from "./ChannelIcon";
import { Message } from "./StudentUnstructuredConvo";
import { SystemPromptModal } from "./SystemPromptModal";

const getInitials = (name: string) => {
  const [firstName, lastName] = name?.split(" ");
  if (!firstName || !lastName) return "Student";
  return `${firstName.charAt(0)}${lastName.charAt(0)}`;
};

interface UnstructuredConvoModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  messages: Message[];
  title?: string;
  studentName?: string; // e.g. "{Last name}, {First name}"
}

export function UnstructuredConvoModal({
  open,
  onOpenChange,
  messages,
  title = "Full Conversation",
  studentName,
}: UnstructuredConvoModalProps) {
  // console.log("studentName ==>", studentName);
  const initials = getInitials(studentName ?? "");

  // State for system prompt modal
  const [systemPromptModal, setSystemPromptModal] = useState({
    open: false,
    data: null as SystemPromptData | null,
    loading: false,
  });

  // Handler for system prompt button
  const handleSystemPromptClick = async (messageId: number) => {
    if (!messageId) return;

    setSystemPromptModal({ open: true, data: null, loading: true });

    try {
      const data = await getSystemPromptByMessageId(messageId);
      setSystemPromptModal({ open: true, data, loading: false });
    } catch (error) {
      console.error("Error fetching system prompt:", error);
      setSystemPromptModal({ open: true, data: null, loading: false });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="h-[95vh] w-full max-w-[70vw]">
        {/* header */}
        <DialogHeader className="flex items-start justify-between">
          <div className="flex flex-col gap-2">
            <DialogTitle className="text-xl font-semibold">{title}</DialogTitle>
            <DialogDescription className="text-sm text-muted-foreground">
              {studentName || "the student"}
            </DialogDescription>
          </div>
        </DialogHeader>

        <ScrollArea className="h-[75vh] w-full border p-2">
          <div
            className={`flex flex-col space-y-4 px-4 py-2 ${messages.length <= 1 ? "h-full items-center justify-center" : "h-full"} `}
          >
            {messages.length === 0 && (
              <div className="flex h-48 items-center justify-center text-muted-foreground">
                No messages yet.
              </div>
            )}
            {messages.map((msg, idx) => (
              <div key={idx} className="flex w-full">
                {msg.type === "human" ? (
                  // Human message layout - right aligned
                  <>
                    <div className="flex-1" />{" "}
                    {/* Spacer to push content right */}
                    <div className="flex max-w-[75%] items-start gap-2">
                      <div className="inline-block rounded-lg bg-blue-500 text-white">
                        <div className="whitespace-pre-line p-3">
                          {msg.content}
                        </div>
                      </div>
                      <div className="flex shrink-0 flex-col items-center">
                        <Avatar className="h-10 w-10 shrink-0">
                          <AvatarFallback className="text-sm font-medium">
                            {initials}
                          </AvatarFallback>
                        </Avatar>
                        {/* Channel indicator below avatar */}
                        <div className="mt-1">
                          <ChannelIcon
                            source={msg.source || "web"}
                            className="opacity-70"
                          />
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  // AI message layout - left aligned
                  <div className="flex max-w-[75%] items-start gap-2">
                    <Avatar className="h-10 w-10 shrink-0">
                      <AvatarImage
                        src="/_static/addie_logo_symbolBLK.png"
                        alt="Addie Logo"
                      />
                      <AvatarFallback>A</AvatarFallback>
                    </Avatar>
                    <div className="inline-block rounded-lg bg-gray-100 text-gray-800">
                      {/* Message content */}
                      <div className="whitespace-pre-line p-3">
                        {msg.content}
                      </div>

                      {/* Actions bar - only show for AI messages with system prompt */}
                      {msg.id && (
                        <div className="flex items-center justify-end gap-2 border-t border-gray-200 px-3 py-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 gap-1 px-2 text-xs text-gray-600 hover:bg-gray-200 hover:text-gray-800"
                            onClick={() => handleSystemPromptClick(msg.id!)}
                            title="View system prompt"
                          >
                            <Info className="h-3 w-3" />
                            <span>System Prompt</span>
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </ScrollArea>
      </DialogContent>

      {/* System Prompt Modal */}
      <SystemPromptModal
        open={systemPromptModal.open}
        onOpenChange={(open) =>
          setSystemPromptModal((prev) => ({ ...prev, open }))
        }
        systemPromptData={systemPromptModal.data}
        loading={systemPromptModal.loading}
      />
    </Dialog>
  );
}
