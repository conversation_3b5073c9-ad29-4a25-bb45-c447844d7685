import React, { useState } from "react";
import {
  getUnstructuredConvoMessagesBySessionId,
  getUnstructuredConvoSummary,
} from "@/actions/conversations/unstructured-conversations";
import { getUserByStudentId } from "@/actions/students";
import { StudentWorkflowStatus } from "@prisma/client";
import { ExternalLink, NotebookPen } from "lucide-react";

import log from "@/common/logger";
import {
  conversationGeneretedByHelper,
  formatTimeAgo,
  workflowTypeHelper,
} from "@/common/utils";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { statusConverter } from "@/components/Conversations/Student-ConversationTab/StudentConvoList";
import { Icons } from "@/components/shared/icons";
import { TooltipWrapper } from "@/components/shared/TooltipWrapper";
import { useConversation } from "@/app/context/ConversationContext";
import { useUI } from "@/app/context/UIContext";

// Define proper types for the messages and summary
interface Message {
  content: string;
  type: string;
  timestamp?: string;
  [key: string]: any;
}

interface SummaryInsight {
  goal: string;
  insight: string;
  [key: string]: any;
}

interface UnstructuredConversationSummary {
  content: string;
  insights?: SummaryInsight[];
  [key: string]: any;
}

interface StudentConvoHeaderProps {
  conversation: any;
  completionPercent?: number;
  steps?: any[];
  setShowGoalModal?: (show: boolean) => void;
}

export const StudentConvoHeader = ({
  conversation,
  completionPercent = 0,
  steps,
  setShowGoalModal,
}: StudentConvoHeaderProps) => {
  const { setIsChatOpen } = useUI();
  const { conversationsData, setChatConversation } = useConversation();
  const [conversationMessages, setConversationMessages] = useState<Message[]>(
    [],
  );
  const [conversationSummary, setConversationSummary] =
    useState<UnstructuredConversationSummary | null>(null);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);

  // console.log("Conversation==>", conversation);
  const titlePrefixes = ["Conversation:", "Unstructured Conversation:"];
  const conversationType =
    workflowTypeHelper(conversation?.workflow?.workflow_type) || "Unknown";
  const isStructured = conversationType === "Structured";
  const isOpenEnded = conversationType === "Open Ended";
  const isAssignment = conversationType === "Assignment";

  // log.debug(`### workflow tags ${conversation?.workflow?.tags?.length} ###`);
  // log.debug(conversation?.workflow?.tags);

  const conversationGeneratedBy = conversationGeneretedByHelper(
    conversation?.workflow?.tags,
  );
  const isAddieTemplate = conversationGeneratedBy === "Addie Template";

  const cleanName = (name: string) => {
    let cleanedName = name;
    titlePrefixes.forEach((prefix) => {
      if (cleanedName.startsWith(prefix)) {
        cleanedName = cleanedName.slice(prefix.length); // Remove prefix if it exists
      }
    });
    return cleanedName.trim();
  };

  const questionCount = conversation?.steps?.length || 0;

  // Fetch conversation summary and insights
  const fetchConversationSummary = async () => {
    if (!conversation?.student_id || !conversation?.workflow_id) return null;

    try {
      const summary = await getUnstructuredConvoSummary(
        conversation.student_id,
        conversation.workflow_id,
      );
      setConversationSummary(summary);
      return summary;
    } catch (error) {
      console.error("Error fetching conversation summary:", error);
      return null;
    }
  };

  // Fetch conversation messages when needed
  const fetchConversationMessages = async () => {
    if (!conversation?.student_id || !conversation?.workflow_id) return [];

    setIsLoadingMessages(true);
    try {
      const user = await getUserByStudentId(conversation.student_id);
      if (!user) {
        console.error("User not found");
        return [];
      }

      const sessionId = `${conversation.workflow_id}-${user.id}`;
      const messages = await getUnstructuredConvoMessagesBySessionId(sessionId);
      setConversationMessages(messages);
      return messages;
    } catch (error) {
      console.error("Error fetching messages:", error);
      return [];
    } finally {
      setIsLoadingMessages(false);
    }
  };

  // Handle clicking the "Ask Addie" button
  const handleAskAddie = async () => {
    setIsLoadingMessages(true);
    try {
      // Get conversation data from context (already accessed at the top level)
      const storedConversationData =
        conversationsData[conversation.workflow_id];

      // Fetch conversation messages if they're not already loaded or available in context
      let messages: Message[] = conversationMessages;
      if (
        (conversationType === "Open Ended" ||
          conversation?.workflow?.workflow_type === "UNSTRUCTURED") &&
        messages.length === 0 &&
        !storedConversationData?.messages
      ) {
        messages = await fetchConversationMessages();
      } else if (storedConversationData?.messages) {
        // Use the messages from context if available
        messages = storedConversationData.messages;
      }

      // Fetch summary and insights if not already loaded or available in context
      let summary: UnstructuredConversationSummary | null = conversationSummary;
      let insights: SummaryInsight[] = [];
      if (
        conversationType === "Open Ended" ||
        conversation?.workflow?.workflow_type === "UNSTRUCTURED"
      ) {
        if (!summary && !storedConversationData?.summary) {
          const summaryData = await fetchConversationSummary();
          summary = summaryData;
          insights = summaryData?.insights || [];
        } else if (storedConversationData?.summary) {
          // Use the summary and insights from context if available
          summary = {
            content: storedConversationData.summary,
            insights: storedConversationData.insights || [],
          };
          insights = storedConversationData.insights || [];
        }
      }

      // Set the conversation for chat in the ConversationContext
      setChatConversation({
        ...conversation,
        messages: messages,
        conversationType: conversationType,
        summary: summary?.content || storedConversationData?.summary || "",
        insights:
          insights.length > 0
            ? insights
            : storedConversationData?.insights || [],
        // Include the original, unmodified conversation object
        originalConversation: conversation,
      });

      // Make sure the chat is open
      setIsChatOpen(true);
    } catch (error) {
      console.error("Error preparing conversation data:", error);
    } finally {
      setIsLoadingMessages(false);
    }
  };

  return (
    <div className="w-full max-w-full space-y-5">
      {/* First line - title, ask addie, updated time */}
      <div className="mb-2 flex flex-wrap items-center justify-between gap-2 sm:items-center">
        <div className="flex flex-wrap items-center gap-2">
          <h2 className="text-lg font-semibold leading-none sm:text-xl">
            {cleanName(conversation?.workflow?.name)}
          </h2>

          <div
            className={`flex items-center self-center rounded-md px-2 py-1 text-xs outline outline-1 outline-gray-300 ${
              conversation.status === StudentWorkflowStatus.COMPLETED
                ? "bg-green-100"
                : conversation.status === StudentWorkflowStatus.NOT_STARTED
                  ? "bg-gray-100"
                  : "bg-yellow-100"
            }`}
          >
            <span className="truncate whitespace-nowrap text-right text-xs font-semibold">
              {statusConverter(conversation.status)}
            </span>
          </div>
        </div>

        <TooltipWrapper tooltipContent="Ask Addie follow-up questions about this conversation">
          <Button
            variant="outline"
            size="sm"
            className="flex h-9 w-auto items-center justify-center bg-white px-4 py-2"
            onClick={handleAskAddie}
            disabled={isLoadingMessages}
          >
            <Icons.botMessageSquare className="h-5 w-5 text-blue-600" />
            <span className="text-blue-600">Discuss with Addie</span>
          </Button>
        </TooltipWrapper>
      </div>

      {/* Second line - details */}
      <div className="flex flex-wrap items-center justify-start gap-4 pb-4 md:gap-1">
        {/*<div className="flex items-center gap-2 text-muted-foreground">*/}
        {/*  <Icons.goal className="h-4 w-4" />*/}
        {/*  <span className="text-sm font-medium">*/}
        {/*    {completionPercent}% Complete*/}
        {/*  </span>*/}
        {/*</div>*/}
        <span className="text-sm text-muted-foreground">
          {questionCount}{" "}
          {isStructured
            ? questionCount > 1
              ? "questions"
              : "question"
            : questionCount > 1
              ? "goals"
              : "goal"}
        </span>
        <span className="text-sm text-muted-foreground">•</span>
        <div className="text-sm text-muted-foreground">
          {formatTimeAgo(new Date(conversation.updated_at))}
        </div>
        {(isOpenEnded || isAssignment) && (
          <Button
            variant="link"
            className="flex items-center justify-center pl-2"
            onClick={() => setShowGoalModal && setShowGoalModal(true)}
          >
            <span>Goal</span>
            <ExternalLink className="h-3 w-3" />
          </Button>
        )}
        {/*Hide the types for now, we might want to show it in the future*/}

        {/*<div className="flex flex-wrap items-center gap-2">*/}
        {/*  <span className="text-sm text-muted-foreground">Type</span>*/}
        {/*  <div className="flex flex-wrap gap-2">*/}
        {/*    <TooltipWrapper*/}
        {/*      tooltipContent={*/}
        {/*        isAddieTemplate*/}
        {/*          ? "Conversation built using an Addie system template"*/}
        {/*          : "Conversation built manually by a counselor"*/}
        {/*      }*/}
        {/*    >*/}
        {/*      <div className="flex items-center justify-center gap-1 text-xs font-semibold sm:text-sm">*/}
        {/*        {isAddieTemplate ? (*/}
        {/*          <Icons.squareLibrary className="h-3 w-3 sm:h-3.5 sm:w-3.5" />*/}
        {/*        ) : (*/}
        {/*          <Icons.flaskConical className="h-3 w-3 sm:h-3.5 sm:w-3.5" />*/}
        {/*        )}*/}
        {/*        <span>{conversationGeneratedBy}</span>*/}
        {/*      </div>*/}
        {/*    </TooltipWrapper>*/}

        {/*    /!* Tooltip for different conversation types *!/*/}
        {/*    {isStructured && (*/}
        {/*      <TooltipWrapper tooltipContent="Conversation uses a structured form approach to record student responses">*/}
        {/*        <div className="flex items-center justify-center gap-1 text-xs font-semibold sm:text-sm">*/}
        {/*          <Icons.clipboardList className="h-3 w-3 sm:h-3.5 sm:w-3.5" />*/}

        {/*          <span>{conversationType}</span>*/}
        {/*        </div>*/}
        {/*      </TooltipWrapper>*/}
        {/*    )}*/}
        {/*    {isOpenEnded && (*/}
        {/*      <TooltipWrapper tooltipContent="Conversation uses Addie’s natural conversation abilities to achieve the desired goal and uncover insights">*/}
        {/*        <div className="flex items-center justify-center gap-1 text-xs font-semibold sm:text-sm">*/}
        {/*          <Icons.botMessageSquare className="h-3 w-3 sm:h-3.5 sm:w-3.5" />*/}

        {/*          <span>{conversationType}</span>*/}
        {/*        </div>*/}
        {/*      </TooltipWrapper>*/}
        {/*    )}*/}
        {/*    {isAssignment && (*/}
        {/*      <TooltipWrapper tooltipContent="Assignment uses Addie’s natural conversation abilities to achieve the desired goal and uncover insights">*/}
        {/*        <div className="flex items-center justify-center gap-1 text-xs font-semibold sm:text-sm">*/}
        {/*          <NotebookPen className="h-3 w-3 sm:h-3.5 sm:w-3.5" />*/}
        {/*          <span>{conversationType}</span>*/}
        {/*        </div>*/}
        {/*      </TooltipWrapper>*/}
        {/*    )}*/}
        {/*  </div>*/}
        {/*</div>*/}
      </div>
      <Separator />
    </div>
  );
};
