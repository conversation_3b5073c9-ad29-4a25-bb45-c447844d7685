"use client";

import React from "react";
import { Di<PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { SystemPromptData } from "@/actions/conversations/system-prompts";

interface SystemPromptModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  systemPromptData: SystemPromptData | null;
  loading: boolean;
}

export function SystemPromptModal({
  open,
  onOpenChange,
  systemPromptData,
  loading
}: SystemPromptModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="h-[90vh] w-full max-w-[80vw]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            System Context & Prompt
            {systemPromptData && (
              <Badge variant="outline">{systemPromptData.agentType}</Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="h-[75vh] w-full">
          {loading ? (
            <div className="flex h-32 items-center justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
            </div>
          ) : systemPromptData ? (
            <div className="space-y-6 p-4">
              {/* System Prompt */}
              <div>
                <h3 className="mb-2 text-lg font-semibold">System Prompt</h3>
                <div className="rounded-lg bg-gray-50 p-4">
                  <pre className="whitespace-pre-wrap font-mono text-sm">
                    {systemPromptData.systemPrompt}
                  </pre>
                </div>
              </div>

              <Separator />

              {/* Student Context */}
              <div>
                <h3 className="mb-2 text-lg font-semibold">Student Context</h3>
                <div className="rounded-lg bg-blue-50 p-4">
                  <pre className="whitespace-pre-wrap font-mono text-sm">
                    {JSON.stringify(systemPromptData.studentContext, null, 2)}
                  </pre>
                </div>
              </div>

              {/* Metadata */}
              <div className="text-sm text-gray-600">
                <p><strong>Agent Type:</strong> {systemPromptData.agentType}</p>
                <p><strong>Message ID:</strong> {systemPromptData.messageId}</p>
                <p><strong>Created:</strong> {new Date(systemPromptData.createdAt).toLocaleString()}</p>
              </div>
            </div>
          ) : (
            <div className="flex h-32 items-center justify-center text-gray-500">
              No system prompt data available for this message
            </div>
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}