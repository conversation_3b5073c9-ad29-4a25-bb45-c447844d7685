import { useCallback, useEffect, useState } from "react";
import { getStudentAnswers } from "@/actions/conversations/student-conversation";
import { WorkflowType } from "@prisma/client";

import {
  ConvoStats,
  getCompletionPercent,
} from "@/components/Conversations/Student-ConversationTab/StudentConvoList";
import StudentStructuredConvo from "@/components/Conversations/Student-ConversationTab/StudentStructuredConvo";
import { StudentUnstructuredConvo } from "@/components/Conversations/Student-ConversationTab/StudentUnstructuredConvo";

interface StudentConvoDetailsProps {
  conversation: any;
  studentId: string;
  convoStats: ConvoStats;
}

export function StudentConvoDetails({
  conversation,
  studentId,
  convoStats,
}: StudentConvoDetailsProps) {
  // console.log("Conversation==>", conversation);
  const [answerMap, setAnswerMap] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const conversationType = conversation.workflow.workflow_type;

  // Calculate completion percentage
  const stats = convoStats[conversation.id] || {
    answeredCount: 0,
    totalQuestions: conversation.steps.length,
  };
  const answeredCount = stats.answeredCount;
  const totalQuestions = stats.totalQuestions;
  const totalGoals = conversation.steps.length;
  const completedGoals = conversation.steps.filter(
    (goal) => goal.completed,
  ).length;

  const completionPercent = getCompletionPercent(
    conversationType,
    answeredCount,
    totalQuestions,
    completedGoals,
    totalGoals,
  );

  const fetchAnswers = useCallback(async () => {
    try {
      setLoading(true);
      // getStudentAnswers( studentId, workflowId )

      const { answerMap: answers } = await getStudentAnswers(
        studentId,
        conversation.workflow_id,
      );
      setAnswerMap(answers || {});
    } catch (error) {
      console.error("Error fetching answers:", error);
    } finally {
      setLoading(false);
    }
  }, [studentId, conversation.workflow_id, setLoading, setAnswerMap]);

  useEffect(() => {
    fetchAnswers();
  }, [fetchAnswers]);

  if (conversationType === WorkflowType.STRUCTURED) {
    return (
      <StudentStructuredConvo
        conversation={conversation}
        answerMap={answerMap}
        loading={loading}
        completionPercent={completionPercent}
      />
    );
  }

  if (
    conversationType === WorkflowType.UNSTRUCTURED ||
    conversationType === WorkflowType.ASSIGNMENT
  ) {
    return (
      <StudentUnstructuredConvo
        conversation={conversation}
        loading={loading}
        completionPercent={completionPercent}
      />
    );
  }
}
