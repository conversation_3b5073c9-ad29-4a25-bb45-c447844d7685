"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";

export const CompleteScreen = () => {
  const router = useRouter();
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    if (countdown === 0) {
      router.push("/");
    }

    const timerId = setTimeout(() => {
      setCountdown((prev) => prev - 1);
    }, 1000);

    return () => clearTimeout(timerId);
  }, [countdown, router]);

  return (
    <div className="flex min-h-full w-full items-center justify-center">
      <div className="mx-auto flex max-w-sm flex-col items-center justify-center rounded-xl px-8 py-10 text-center">
        <Image
          className="mb-6"
          alt="completed"
          src="/images/complete-screen.png"
          width={132}
          height={156}
        />
        <h2 className="mb-4 text-lg font-semibold">Profile setup complete!</h2>
        <p className="mb-6 max-w-xs break-words text-center text-sm text-muted-foreground">
          Thank you for providing your information. We&apos;re excited to help
          you with your college application journey.
        </p>

        <Button variant="default" onClick={() => router.push("/")}>
          Finish
        </Button>

        {countdown > 0 && (
          <p className="mt-4 text-sm text-muted-foreground">
            You will be redirected to the home page in {countdown} seconds.
          </p>
        )}
      </div>
    </div>
  );
};
