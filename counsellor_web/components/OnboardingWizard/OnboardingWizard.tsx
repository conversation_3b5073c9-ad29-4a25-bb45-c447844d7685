"use client";

import React, { useState } from "react";
import { updateProfile } from "@/actions/profile";
import { toast } from "sonner";

import { Availability } from "@/components/OnboardingWizard/Availablity";
import { CompleteScreen } from "@/components/OnboardingWizard/CompleteScreen";
import { PersonalInfo } from "@/components/OnboardingWizard/PersonalInfo";
import { PhoneNumber } from "@/components/OnboardingWizard/PhoneNumber";
import { StepIndicator } from "@/components/OnboardingWizard/StepIndicator";

// const isDevelopment = process.env.NODE_ENV === "development";
const isDevelopment = false;

export type OnboardingStep = "phone-number" | "personal-info" | "availability";

const STEP_LABELS = ["Phone Number", "Personal Info", "Availability"];

export const OnboardingWizard = ({ initialData }) => {
  // Determine starting step based on available profile data
  const determineStartingStep = () => {
    // If no phone number, start at step 0
    if (!initialData?.phone_number) {
      return 0; // phone number step
    }
    // If phone number but missing personal info, start at step 1
    else if (!initialData?.gender || !initialData?.first_name || !initialData?.last_name) {
      return 1; // personal info step
    }
    // Otherwise start at step 2 (availability)
    else {
      return 2; // availability step
    }
  };

  const [currentStep, setCurrentStep] = useState(determineStartingStep());
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [formData, setFormData] = useState(initialData ?? {});

  // console.log("formData", formData);

  // Reset wizard (for edit phone)
  const resetWizard = () => {
    setCurrentStep(0);
    setCompletedSteps(new Set());
    setFormData(initialData ?? {});
  };

  // Step navigation
  const goToNextStep = () => {
    setCompletedSteps((prev) => new Set(prev).add(currentStep));
    setCurrentStep((prev) => prev + 1);
  };
  const goToPrevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  const isComplete = currentStep >= STEP_LABELS.length;

  const handleComplete = async () => {
    try {
      // Extract only the fields we want to update on the User model
      const {
        // Exclude any nested objects or relations from the update payload
        students,
        Counselor,
        ...userUpdateData
      } = formData;

      // Include isProfileComplete flag
      const dataToSubmit = {
        ...userUpdateData,
        isProfileComplete: true
      };

      console.log("Submitting profile data:", dataToSubmit);
      
      await updateProfile(initialData.id, dataToSubmit);
      goToNextStep();
      toast.success("Profile updated successfully!");
    } catch (e) {
      console.error("Error completing profile:", e);
      return toast.error("Failed to update profile.");
    }
  };

  // Step rendering
  let stepContent: React.ReactNode = null;
  if (currentStep === 0) {
    stepContent = (
      <PhoneNumber
        value={formData.phone_number ?? ""}
        onChange={(val: string) =>
          setFormData((f) => ({ ...f, phone_number: val }))
        }
        onNext={goToNextStep}
        onEditPhone={resetWizard}
        userId={initialData.id}
        isDevelopment={isDevelopment}
      />
    );
  } else if (currentStep === 1) {
    stepContent = (
      <PersonalInfo
        onNext={goToNextStep}
        onBack={resetWizard}
        formData={formData}
        setFormData={setFormData}
        // Pass formData, setFormData as needed
      />
    );
  } else if (currentStep === 2) {
    stepContent = (
      <Availability
        availability={formData.preferred_availability ?? []}
        timezone={formData.timezone ?? ""}
        onTimezoneChange={(val: string) =>
          setFormData((prev) => ({ ...prev, timezone: val }))
        }
        onNext={handleComplete}
        onBack={goToPrevStep}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, preferred_availability: value }))
        }
      />
    );
  }

  return (
    <div className="flex h-screen w-screen flex-col items-center justify-center bg-[#FAFAFA]">
      {!isComplete && (
        <StepIndicator
          labels={STEP_LABELS}
          currentStep={currentStep + 1}
          completedGroups={STEP_LABELS.map((_, i) => completedSteps.has(i))}
        />
      )}
      {isComplete ? <CompleteScreen /> : stepContent}
    </div>
  );
};
