import { OnboardingStepCard } from "@/components/OnboardingWizard/OnboardingStepCard";
import {
  PreferredAvailability,
  PreferredAvailabilityFormSection,
} from "@/components/Settings/Profile/PreferredContactTimeFormSection";
import TimezoneDropdown from "@/components/Settings/Profile/TimezoneDropdown";

interface AvailabilityProps {
  onNext: () => void;
  onBack: () => void;
  onChange: (value: any) => void;
  availability: PreferredAvailability[];
  timezone: string;
  onTimezoneChange: (value: string) => void;
}
export const Availability = ({
  onNext,
  onBack,
  onChange,
  availability,
  timezone,
  onTimezoneChange,
}: AvailabilityProps) => {
  return (
    <OnboardingStepCard
      title="When are you available?"
      description="Choose the times you're usually free to chat with <PERSON><PERSON> and work on your college applications together."
      disableNext={availability.length === 0 || timezone === "" || !timezone}
      nextText="Complete Setup"
      onNext={onNext}
      onBack={onBack}
    >
      <TimezoneDropdown
        onChange={onTimezoneChange}
        timezone={timezone}
        error={!timezone || timezone === "" ? "Timezone is required" : null}
      />
      <PreferredAvailabilityFormSection
        availability={availability ?? []}
        onChange={(value) => onChange(value)}
        error={
          availability.length === 0 ? "Availability is required" : undefined
        }
      />
    </OnboardingStepCard>
  );
};
