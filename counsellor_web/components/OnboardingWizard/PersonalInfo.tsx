"use client";

import { useState } from "react";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { OnboardingStepCard } from "@/components/OnboardingWizard/OnboardingStepCard";

interface PersonalInfoProps {
  onNext?: () => void;
  onBack?: () => void;
  formData?: any;
  setFormData: (data: any) => void;
}

export const PersonalInfo = ({
  onNext,
  onBack,
  formData = {},
  setFormData = () => {},
}: PersonalInfoProps) => {
  const [firstName, setFirstName] = useState(formData.first_name || "");
  const [lastName, setLastName] = useState(formData.last_name || "");
  const [gender, setGender] = useState(formData.gender || "");
  const [pronouns, setPronouns] = useState(formData.pronouns || "");

  const handleFirstNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFirstName(value);
    setFormData({ ...formData, first_name: value });
  };

  const handleLastNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLastName(value);
    setFormData({ ...formData, last_name: value });
  };

  const handleGenderChange = (value: string) => {
    setGender(value);
    setFormData({ ...formData, gender: value });
  };

  const handlePronounsChange = (value: string) => {
    setPronouns(value);
    setFormData({ ...formData, pronouns: value });
  };

  // Check if form is valid to enable the continue button
  const isFormValid = 
    gender !== "" && 
    firstName.trim() !== "" && 
    lastName.trim() !== "" &&
    pronouns !== "";

  return (
    <OnboardingStepCard
      title="Tell us about yourself"
      description="This helps us personalize your experience and address you correctly."
      disableNext={!isFormValid}
      nextText="Continue"
      onNext={onNext}
      onBack={onBack}
    >
      <div className="space-y-6">
        {/* Name Fields */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="first_name" className="font-medium">
              First Name <span className="text-red-500">*</span>
            </Label>
            <Input 
              id="first_name"
              value={firstName}
              onChange={handleFirstNameChange}
              placeholder="Enter your first name"
              className="w-full"
            />
            {firstName.trim() === "" && (
              <p className="text-xs text-red-500">First name is required</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="last_name" className="font-medium">
              Last Name <span className="text-red-500">*</span>
            </Label>
            <Input 
              id="last_name"
              value={lastName}
              onChange={handleLastNameChange}
              placeholder="Enter your last name"
              className="w-full"
            />
            {lastName.trim() === "" && (
              <p className="text-xs text-red-500">Last name is required</p>
            )}
          </div>
        </div>
        
        <div className="space-y-3">
          <h3 className="text-base font-medium">
            Gender <span className="text-red-500">*</span>
          </h3>
          <RadioGroup
            value={gender}
            onValueChange={handleGenderChange}
            className="grid grid-cols-2 gap-y-3"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="male" id="male" />
              <Label htmlFor="male" className="font-normal">
                Male
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="female" id="female" />
              <Label htmlFor="female" className="font-normal">
                Female
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="non-binary" id="non-binary" />
              <Label htmlFor="non-binary" className="font-normal">
                Non-binary
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="prefer-not-to-say"
                id="prefer-not-to-say"
              />
              <Label htmlFor="prefer-not-to-say" className="font-normal">
                Prefer not to say
              </Label>
            </div>
          </RadioGroup>
          {gender === "" && (
            <p className="text-xs text-red-500">Gender selection is required</p>
          )}
        </div>

        <div className="space-y-3">
          <h3 className="text-base font-medium">
            Pronouns <span className="text-red-500">*</span>
          </h3>
          <Select value={pronouns} onValueChange={handlePronounsChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select your pronouns" />
            </SelectTrigger>
            <SelectContent className="cursor-pointer">
              <SelectItem className="cursor-pointer" value="she/her">
                She/Her
              </SelectItem>
              <SelectItem className="cursor-pointer" value="he/him">
                He/Him
              </SelectItem>
              <SelectItem className="cursor-pointer" value="they/them">
                They/Them
              </SelectItem>
              <SelectItem className="cursor-pointer" value="other">
                Other
              </SelectItem>
              <SelectItem className="cursor-pointer" value="prefer-not-to-say">
                Prefer not to say
              </SelectItem>
            </SelectContent>
          </Select>
          {pronouns === "" && (
            <p className="text-xs text-red-500">Pronouns are required</p>
          )}
        </div>
      </div>
    </OnboardingStepCard>
  );
};
