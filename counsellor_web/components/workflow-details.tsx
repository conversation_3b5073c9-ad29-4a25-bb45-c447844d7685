import Link from "next/link";
import { Eye } from "lucide-react";

import { Workflow } from "@/common/model";
import { formatDate } from "@/common/utils";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface WorkflowDetailsProps {
  workflow: Workflow;
}

export function WorkflowDetails({ workflow }: WorkflowDetailsProps) {
  const steps = workflow.steps;
  steps?.sort((a, b) => a.index - b.index);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h2 className="mb-2 text-lg font-semibold">Workflow ID</h2>
          <Button variant="link" className="h-auto p-0 font-normal" asChild>
            <Link href={`/workflow/${workflow.id}`}>{workflow.id}</Link>
          </Button>
        </div>
        <div>
          <h2 className="mb-2 text-lg font-semibold">Parent Step ID</h2>
          <Button variant="link" className="h-auto p-0 font-normal" asChild>
            <Link href={`/workflow/${workflow.parent_step_id}`}>
              {workflow.parent_step_id}
            </Link>
          </Button>
        </div>
        <div>
          <h2 className="mb-2 text-lg font-semibold">Created At</h2>
          <p className="text-gray-600">
            {formatDate(workflow.created_at.toLocaleString())}
          </p>
        </div>
        <div>
          <h2 className="mb-2 text-lg font-semibold">Updated At</h2>
          <p className="text-gray-600">
            {formatDate(workflow.updated_at.toLocaleString())}
          </p>
        </div>
      </div>

      <div>
        <h2 className="mb-4 text-2xl font-semibold">Steps</h2>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Goal</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {steps?.map((step) => (
              <TableRow key={step.id}>
                <TableCell className="font-medium">
                  <Button
                    variant="link"
                    className="h-auto p-0 font-normal"
                    asChild
                  >
                    <Link href={`/workflow/${workflow.id}/${step.id}`}>
                      {step.id}
                    </Link>
                  </Button>
                </TableCell>
                <TableCell>{step.goal}</TableCell>
                <TableCell className="text-right">
                  <Link href={`/workflow/${workflow.id}/${step.id}`}>
                    <Button
                      variant="ghost"
                      size="icon"
                      aria-label="View Workflow Step"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </Link>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
