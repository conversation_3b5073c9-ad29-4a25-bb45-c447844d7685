"use client";

import { useState } from "react";
import { queueExperiments } from "@/actions/student-agent";
import { Play } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";

interface ConfigModalProps {
  configs?: any;
}

export default function ConfigModal({ configs }: ConfigModalProps) {
  const [open, setOpen] = useState(false);
  const [selectedConfigs, setSelectedConfigs] = useState<string[]>(
    configs.map((config) => config.id),
  );

  const handleToggle = (id: string) => {
    setSelectedConfigs((prev) =>
      prev.includes(id)
        ? prev.filter((configId) => configId !== id)
        : [...prev, id],
    );
  };

  const handleSelectAll = () => {
    setSelectedConfigs(
      selectedConfigs.length === configs.length
        ? []
        : configs.map((config) => config.id),
    );
  };

  const handleRun = async () => {
    await queueExperiments(selectedConfigs);
    console.log("Running configs:", selectedConfigs);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={() => setOpen(!open)}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <Play className="size-4" />
          Queue Experiments
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Student Agents</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col">
          <Button
            variant="outline"
            onClick={handleSelectAll}
            className="mb-5 self-start"
          >
            {selectedConfigs.length === configs.length
              ? "Deselect All"
              : "Select All"}
          </Button>
          <ScrollArea className="grow">
            {configs.map((config) => (
              <div key={config.id} className="mb-2 flex items-center space-x-2">
                <Checkbox
                  id={`config-${config.id}`}
                  checked={selectedConfigs.includes(config.id)}
                  onCheckedChange={() => handleToggle(config.id)}
                />
                <label
                  htmlFor={`config-${config.id}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {config.role}
                </label>
              </div>
            ))}
          </ScrollArea>
          <Button onClick={handleRun} className="mt-4 self-end">
            Run
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
