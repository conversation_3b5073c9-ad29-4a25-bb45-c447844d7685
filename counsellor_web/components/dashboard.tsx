"use client";

import { fetchStudents, fetchStudentsBasicInfo } from "@/actions/students";
import { fetchDashboardStudents } from "@/actions/dashboard_students";
import React, { useCallback, useEffect, useState } from "react";
import { usePathname } from "next/navigation";

import { DashboardStudent } from "@/common/types";
import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { StudentTableCounsellorDashboard } from "@/components/student-table-counsellor-dashboard";
import { StudentDetailPageSkeleton } from "@/components/StudentDetailPageSkeleton/StudentDetailPageSkeleton";
import { useSchool } from "@/app/context/SchoolContext";
import log from "@/common/logger";

interface DashboardProps {
  user: any;
}

export default function Dashboard({ user }: DashboardProps) {
  const { activeSchool } = useSchool();
  const pathname = usePathname();
  const [students, setStudents] = useState<DashboardStudent[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingOnboardingData, setLoadingOnboardingData] = useState(false);
  const [selectedGrade, setSelectedGrade] = useState(""); // Empty string is used in API calls to mean 'all grades'
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");
  const [hasInitiallyLoaded, setHasInitiallyLoaded] = useState(false);
  const grades = ["Grade 9", "Grade 10", "Grade 11", "Grade 12"];

  // No need to get the active school from localStorage
  // We're now using the SchoolContext to get this information

  // Debounce search query to prevent excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Create refs to track previous values to avoid unnecessary fetches
  const prevGradeRef = React.useRef(selectedGrade);
  const prevSearchRef = React.useRef(debouncedSearchQuery);
  const prevSchoolRef = React.useRef(activeSchool);

  // Fetch student data using the materialized view
  const fetchStudentData = useCallback(async () => {
    // Don't fetch data if activeSchool is null
    if (!activeSchool) {
      setStudents([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      
      // Use materialized view for all student data in a single query
      const data = await fetchDashboardStudents({
        grade: selectedGrade,
        search: debouncedSearchQuery,
        user: { role: user.role, id: user.id },
        schoolId: activeSchool // Use the active school from context
      });

      setStudents(data);
    } catch (error) {
      console.error("Failed to fetch student data:", error);
      setStudents([]);
    } finally {
      setLoading(false);
    }
  }, [selectedGrade, debouncedSearchQuery, user, activeSchool]);

  // Only fetch data when relevant dependencies actually change
  useEffect(() => {
    const gradeChanged = prevGradeRef.current !== selectedGrade;
    const searchChanged = prevSearchRef.current !== debouncedSearchQuery;
    const schoolChanged = prevSchoolRef.current !== activeSchool;
    
    // Update refs
    prevGradeRef.current = selectedGrade;
    prevSearchRef.current = debouncedSearchQuery;
    prevSchoolRef.current = activeSchool;
    
    // Only fetch if something changed or on initial load
    if (gradeChanged || searchChanged || schoolChanged || !hasInitiallyLoaded) {
      fetchStudentData();
      if (!hasInitiallyLoaded) {
        setHasInitiallyLoaded(true);
      }
    }
  }, [selectedGrade, debouncedSearchQuery, activeSchool, fetchStudentData, hasInitiallyLoaded]);
  
  // This effect will run when the component is navigated to
  useEffect(() => {
    // If pathname is the dashboard path and we've loaded before,
    // this means we're returning to this page - refetch the data
    if (pathname === "/" && hasInitiallyLoaded) {
      fetchStudentData();
    }
  }, [pathname, fetchStudentData, hasInitiallyLoaded]);

  return (
    <div className="flex h-screen w-full">
      <div className="flex flex-1 flex-col overflow-hidden p-6">
        {!activeSchool ? (
          <div className="flex h-full items-center justify-center">
            <div className="text-center">
              <h2 className="mb-4 text-xl font-medium text-gray-500">No school selected</h2>
              <p className="text-gray-400">Please select a school to view student data</p>
            </div>
          </div>
        ) : (
          <>
            <div className="mb-6">
              <h2 className="mb-4 text-2xl font-bold">Students</h2>
              <div className="flex items-center gap-4">
                <Select
                  value={selectedGrade || "all"}
                  onValueChange={(value) => setSelectedGrade(value === "all" ? "" : value)}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="All Grades" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Grades</SelectItem>
                    {grades.map((grade) => (
                      <SelectItem key={grade} value={grade}>
                        {grade}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/*<CounsellorSelect*/}
                {/*  value={selectedCounsellor}*/}
                {/*  onChange={setSelectedCounsellor}*/}
                {/*/>*/}

                <div className="flex-1">
                  <Input
                    type="search"
                    placeholder="Search student name or id"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
            </div>

            <div className="w-full flex-1 overflow-auto">
              {loading ? (
                <>
                  {/*// <div>Loading...</div>*/}
                  <div className="mx-auto flex w-full flex-col items-center justify-center">
                    <LoadingSpinner />
                  </div>
                  <StudentDetailPageSkeleton />
                </>
              ) : (
                <StudentTableCounsellorDashboard
                  students={students}
                  loadingOnboardingData={loadingOnboardingData}
                />
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
