import Link from "next/link";
import { Edit } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface QAListProps {
  data?: any;
}

export default function QAList({ data }: QAListProps) {
  return (
    <div className="py-10">
      <h1 className="text-2xl font-bold">Question and Answers</h1>
      <Table>
        <TableHeader>
          <TableRow>
            {/*<TableHead className="w-[100px]">ID</TableHead>*/}
            <TableHead className="w-[400px]">Question</TableHead>
            <TableHead>Response</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((item) => (
            <TableRow key={item.question.id}>
              {/*<TableCell className="font-mono">{item.question.id}</TableCell>*/}
              <TableCell>{item.question.question}</TableCell>
              <TableCell>{item.response}</TableCell>
              <TableCell>
                <Link
                  href={`/workflow/${item.workflow_id}/${item.workflow_step_id}/edit_goal`}
                >
                  <Button
                    variant="default"
                    size="default"
                    aria-label="Edit goal"
                  >
                    <Edit className="h-4 w-4" /> Edit Goal
                  </Button>
                </Link>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
