"use client";

import { useState } from "react";
import Link from "next/link";
import { Clipboard, MessageCircle, User } from "lucide-react";

import { StudentAgent } from "@/common/model";
import { formatDate } from "@/common/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import ChatUI from "@/components/chat-ui";
import QAList from "@/components/qa-list";

interface StudentDetailArgs {
  value: StudentAgent;
  messages?: any;
  qnaireResponses?: any;
}

export default function ExperimentDetail(params: StudentDetailArgs) {
  const { value, messages, qnaireResponses } = params;
  const [showFullPrompt, setShowFullPrompt] = useState(false);

  const formatDateOfBirth = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <Card className="w-full border-none p-4">
      <CardHeader className="flex flex-row items-center space-x-4 pb-2">
        <Avatar className="h-20 w-20">
          <AvatarImage
            src={value.image || ""}
            alt={`${value.first_name} ${value.last_name}`}
          />
          <AvatarFallback>
            <User className="h-10 w-10" />
          </AvatarFallback>
        </Avatar>
        <div>
          <CardTitle className="text-2xl font-bold">
            {value.first_name} {value.middle_name} {value.last_name}
          </CardTitle>
          <CardDescription>
            Student Agent ID: {value.student_agent_id}
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="font-semibold">Role:</span>
            <Badge variant="secondary" className="ml-2">
              {value.role}
            </Badge>
          </div>
          <div>
            <span className="font-semibold">Student ID:</span>
            <span className="ml-2">{value.student_id}</span>
          </div>
          <div>
            <span className="font-semibold">User ID:</span>
            <span className="ml-2 font-mono text-sm">{value.user_id}</span>
          </div>
          <div>
            <span className="font-semibold">Date of Birth:</span>
            <span className="ml-2">
              {formatDateOfBirth(value.date_of_birth)}
            </span>
          </div>
          <div>
            <span className="font-semibold">Email:</span>
            <span className="ml-2">{value.email}</span>
          </div>
          <div>
            <span className="font-semibold">Email Verified:</span>
            <span className="ml-2">{value.emailVerified ? "Yes" : "No"}</span>
          </div>
          <div>
            <span className="font-semibold">Messages Count:</span>
            <span className="ml-2">{value.msgsCount}</span>
          </div>
          <div>
            <span className="font-semibold">Completed Workflow Steps:</span>
            <span className="ml-2">{value.completedWFSteps}</span>
          </div>
        </div>
        <div className="space-y-2">
          <span className="font-semibold">Prompt:</span>
          <p className="text-lg text-muted-foreground">
            {showFullPrompt
              ? value.prompt.content
              : `${value.prompt.content.slice(0, 100)}...`}
          </p>
          <Link href={`/student_agent_config/edit/${value.config_id}`}>
            <Button variant="link">
              {showFullPrompt ? "Show Less" : "Show More"}
            </Button>
          </Link>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="font-semibold">Created At:</span>
            <span className="block text-sm">
              {formatDate(value.created_at.toLocaleString())}
            </span>
          </div>
          <div>
            <span className="font-semibold">Updated At:</span>
            <span className="block text-sm">
              {formatDate(value.updated_at.toLocaleString())}
            </span>
          </div>
        </div>
        <div>
          <span className="font-semibold">Messages ID:</span>
          <span className="ml-2 font-mono text-sm">{value.messages_id}</span>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end space-x-2">
        {/*<Button variant="outline">*/}
        {/*  <Clipboard className="mr-2 h-4 w-4" />*/}
        {/*  Copy Details*/}
        {/*</Button>*/}
        {/*<Button>*/}
        {/*  <MessageCircle className="mr-2 h-4 w-4" />*/}
        {/*  View Messages*/}
        {/*</Button>*/}
      </CardFooter>
      <QAList data={qnaireResponses} />
      <h1 className="text-2xl font-bold">Messages</h1>
      <ChatUI messages={messages} />
    </Card>
  );
}
