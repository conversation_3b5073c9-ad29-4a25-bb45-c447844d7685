"use client";

import { useEffect } from "react";
import { usePathname } from "next/navigation";
import { Userpilot } from "userpilot";
import { useUser } from "@/app/context/UserContext";
import { env } from "@/env.mjs";

export function UserpilotProvider() {
  const { user } = useUser();
  const pathname = usePathname();

  // Initialize Userpilot
  useEffect(() => {
    Userpilot.initialize(env.NEXT_PUBLIC_USERPILOT_TOKEN);
  }, []);

  // Identify user when available
  useEffect(() => {
    if (user?.id) {
      Userpilot.identify(user.id, {
        name: `${user.first_name} ${user.last_name}`,
        email: user.email,
        role: user.role,
        created_at: new Date().toISOString(), // You might want to add a createdAt field to your User type if available
      });
    }
  }, [user]);

  // Reload Userpilot on route changes
  useEffect(() => {
    Userpilot.reload();
  }, [pathname]);

  return null;
}
