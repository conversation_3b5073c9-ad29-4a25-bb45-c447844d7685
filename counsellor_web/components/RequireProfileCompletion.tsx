"use client";

import { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useSession } from "next-auth/react";
import { Loader2 } from "lucide-react";
import log from "@/common/logger";
import { getUserProfile } from "@/actions/user";

interface RequireProfileCompletionProps {
  children: React.ReactNode;
}

export default function RequireProfileCompletion({ children }: RequireProfileCompletionProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkProfileCompletion = async () => {
      try {
        // Always set a baseline state for consistency
        let shouldRedirect = false;
        let redirectTarget = "";
        let shouldContinueChecking = true;
        
        // Skip check for exempt paths but maintain hook consistency
        if (pathname === "/onboarding" || pathname === "/login" || pathname.startsWith("/login") || pathname.startsWith("/api/auth")) {
          shouldContinueChecking = false;
        }
        
        // Special handling for root path - use sessionStorage to prevent loops
        if (pathname === "/" && shouldContinueChecking) {
          const redirectCount = sessionStorage.getItem("redirectCount") || "0";
          const count = parseInt(redirectCount, 10);
          
          // If we've redirected too many times, just show the root page
          if (count > 2) {
            sessionStorage.setItem("redirectCount", "0");
            shouldContinueChecking = false;
          } else {
            // Increment redirect count
            sessionStorage.setItem("redirectCount", (count + 1).toString());
          }
        }

        // Only continue profile check when session is loaded and we should continue
        if (status === "loading" || !shouldContinueChecking) {
          setIsChecking(false);
          return;
        }
        
        // If we're on login page, don't do any redirects
        if (pathname.startsWith("/login")) {
          setIsChecking(false);
          return;
        }
        
        if (status === "authenticated" && session?.user && session.user.id) {
          const userId = session.user.id;
          
          const userProfile = await getUserProfile(userId);
          
          let isProfileComplete = false;
          
          if (userProfile) {
            if (typeof userProfile.isProfileComplete === "boolean") {
              isProfileComplete = userProfile.isProfileComplete;
            } else {
              // Fall back to checking individual fields
              isProfileComplete = Boolean(
                userProfile.phone_number && 
                userProfile.gender && 
                userProfile.first_name && 
                userProfile.last_name
              );
            }
          }
          
          if (!isProfileComplete && pathname !== "/onboarding") {
            shouldRedirect = true;
            redirectTarget = "/onboarding";
          }
        } else if (pathname !== "/login" && !pathname.startsWith("/login")) {
          shouldRedirect = true;
          redirectTarget = "/login";
        }
        
        if (shouldRedirect && redirectTarget) {
          router.push(redirectTarget);
        } else {
          setIsChecking(false);
        }
      } catch (error) {
        // Error handling - log and allow children to render
        log.error("Error in profile completion check:", error);
        setIsChecking(false);
      }
    };
    
    checkProfileCompletion();
  }, [session, status, router, pathname]);

  // Show loading indicator while checking profile status
  if (isChecking && pathname !== "/onboarding" && pathname !== "/login" && !pathname.startsWith("/login")) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return <>{children}</>;
}
