"use client";

import * as React from "react";
import { redirect, useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { userIsAuthorized } from "@/actions/chat";
import { checkUserEmail} from "@/actions/auth";
import { SubscriptionPlan } from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { UserRole } from "@prisma/client";
import { signIn } from "next-auth/react";
import qs from "qs";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import * as z from "zod";

import { ALLOWED_DOMAINS } from "@/common/constants";
import log from "@/common/logger";
import { cn } from "@/common/utils";
import { userAuthSchema } from "@/common/validations/auth";
import { buttonVariants } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Icons } from "@/components/shared/icons";

interface UserAuthFormProps extends React.HTMLAttributes<HTMLDivElement> {
  type?: string;
  priceId?: string;
  redirectToChat?: boolean;
  baseUrl: string;
  defaultEmail?: string;
}

type FormData = z.infer<typeof userAuthSchema>;

export function UserAuthForm({
  className,
  type,
  priceId,
  redirectToChat,
  baseUrl,
  defaultEmail,
  ...props
}: UserAuthFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<FormData>({
    resolver: zodResolver(userAuthSchema),
  });
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [isGoogleLoading, setIsGoogleLoading] = React.useState<boolean>(false);
  const searchParams = useSearchParams();
  
  // Set default email from prop if provided
  React.useEffect(() => {
    if (defaultEmail) {
      setValue("email", defaultEmail);
    }
  }, [defaultEmail, setValue]);

  const router = useRouter();

  // Log initial component state
  React.useEffect(() => {
    log.debug("==== AUTH FORM MOUNTED ====");
    log.debug("Default Email:", defaultEmail);
    log.debug("Base URL:", baseUrl);
    log.debug("Search params:", { 
      callbackUrl: searchParams?.get("callbackUrl"),
      from: searchParams?.get("from"),
      error: searchParams?.get("error"),
      paramCount: searchParams ? Array.from(searchParams.keys()).length : 0
    });
    log.debug("Environment variables:", {
      location_origin: typeof window !== "undefined" ? window.location.origin : "not-available",
      location_host: typeof window !== "undefined" ? window.location.host : "not-available"
    });
    
    // Check if we're in a potential redirect loop
    const referrer = document.referrer;
    if (referrer) {
      log.debug("Document referrer:", referrer);
      if (referrer.includes("/login") && window.location.pathname.includes("/login")) {
        log.warn("⚠️ POTENTIAL REDIRECT LOOP DETECTED: Login page was referred from another login page");
      }
    }
    
    // Return cleanup function
    return () => {
      log.debug("==== AUTH FORM UNMOUNTED ====");
    };
  }, [searchParams, baseUrl, defaultEmail]);

  async function onSubmit(data: FormData) {
    log.debug("==== AUTH FORM SUBMIT START ====");
    setIsLoading(true);
    // We no longer need to verify email domains - we accept all domains

    // Get callbackUrl from searchParams or default to the baseUrl or root
    const callbackUrl = searchParams?.get("callbackUrl") || baseUrl || "/";
    log.debug("🔑 Auth form submit with email:", data.email);
    log.debug("🔗 Using callbackUrl:", callbackUrl);

    let options = {
      email: data.email.toLowerCase(),
      redirect: false,
      callbackUrl
    };

    log.debug("Calling signIn with options:", options);
    const signInResult = await signIn("resend", options);

    log.debug("📩 Sign in result:", signInResult);

    setIsLoading(false);

    if (signInResult?.error) {
      log.error("❌ Auth error:", signInResult.error);
      // Handle different error types
      if (signInResult.error === "AccessDenied") {
        toast.error("Access Denied", {
          description: "Your account hasn't been created yet. Please contact an administrator to set up your account.",
        });
      } else if (signInResult.error.includes("account creation")) {
        toast.error("Account Not Found", {
          description: "Your account doesn't exist. Please contact an administrator to create your account.",
        });
      } else {
        toast.error("Sign In Error", {
          description: signInResult.error || "An unexpected error occurred.",
        });
      }
      log.debug("==== AUTH FORM SUBMIT END (ERROR) ====");
      return;
    }

    if (signInResult?.ok) {
      log.debug("✅ Sign in successful, email sent");
      toast.success("Please Check Your Email", {
        description: "You will receive an email with a link to sign in.",
      });
      // Stay on the login page after email is sent - don't redirect
    } else {
      log.error("❌ Sign in failed but no error provided");
      toast.error("Sign In Error", {
        description: "Unable to sign in. If you don't have an account yet, please contact an administrator.",
      });
    }
    log.debug("==== AUTH FORM SUBMIT END ====");
  }

  return (
    <div className={cn("grid gap-6", className)} {...props}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid gap-2">
          <div className="grid gap-1">
            <Label className="sr-only" htmlFor="email">
              Email
            </Label>
            <Input
              id="email"
              placeholder="Email"
              type="email"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              disabled={isLoading || isGoogleLoading}
              {...register("email")}
            />
            {errors?.email && (
              <p className="px-1 text-xs text-red-600">
                {errors.email.message}
              </p>
            )}
          </div>
          <button className={cn(buttonVariants())} disabled={isLoading}>
            {isLoading && (
              <Icons.spinner className="mr-2 size-4 animate-spin" />
            )}
            {type === "register" ? "Sign Up" : "Sign In"}
          </button>
        </div>
      </form>
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Sign in with Google
          </span>
        </div>
      </div>
      <button
        type="button"
        className={cn(buttonVariants({ variant: "outline" }))}
        onClick={async () => {
          setIsGoogleLoading(true);
          try {
            if (baseUrl.includes("getaddie.com") || baseUrl.includes("tunnelmole.net")) {
              // For production, attempt Google sign-in but ensure redirect happens
              await signIn("google", { redirect: false });
              // If signIn doesn't redirect automatically, we'll do it manually
              redirect("/");
            } else {
              const token = await signIn("credentials", { redirect: false });
              redirect("/");
            }
          } catch (error) {
            // Even if there's an error, redirect to home
            log.error("Google sign-in error:", error);
            redirect("/");
          }
        }}
        disabled={isLoading || isGoogleLoading}
      >
        {isGoogleLoading ? (
          <Icons.spinner className="mr-2 size-4 animate-spin" />
        ) : (
          <Icons.google className="mr-2 size-4" />
        )}{" "}
        Google
      </button>
    </div>
  );
}
