#!/usr/bin/env ts-node
/**
 * Migration script to convert all unstructured conversations from the old format
 * (single step with goals array) to the new format (one step per goal).
 *
 * Run with: npx ts-node scripts/migrate-unstructured-conversations.ts
 */
import { migrateAllUnstructuredConversations } from "../actions/conversations/unstructured-conversations";

async function main() {
  console.log("Starting migration of unstructured conversations...");

  try {
    const migratedCount = await migrateAllUnstructuredConversations();
    console.log(
      `Successfully migrated ${migratedCount} unstructured conversations.`,
    );
  } catch (error) {
    console.error("Error during migration:", error);
    process.exit(1);
  }

  console.log("Migration completed successfully.");
  process.exit(0);
}

main();
