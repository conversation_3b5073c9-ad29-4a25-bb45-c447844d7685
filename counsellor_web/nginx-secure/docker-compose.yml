version: '3.9'

services:
  proxy:
    image: nginxproxy/nginx-proxy
    labels:
      com.github.jrcs.letsencrypt_nginx_proxy_companion.nginx_proxy: "true"
    container_name: proxy
    restart: unless-stopped
    ports:
      - target: 80
        published: 80
        protocol: tcp
        mode: host
      - target: 443
        published: 443
        protocol: tcp
        mode: host
    volumes:
      - $APP_DIR/web/nginx-secure/nginx/conf.d:/etc/nginx/conf.d
      - $APP_DIR/web/nginx-secure/nginx/vhost.d:/etc/nginx/vhost.d
      - $APP_DIR/web/nginx-secure/nginx/html:/usr/share/nginx/html
      - $APP_DIR/web/nginx-secure/nginx/certs:/etc/nginx/certs:ro
      - /var/run/docker.sock:/tmp/docker.sock:ro
    environment:
      - LOG_FORMAT=$$host $$remote_addr - $$remote_user [$$time_local] "$$request" $$status $$body_bytes_sent "$$http_referer" "$$http_user_agent" "$$upstream_addr" "$$http_x_forwarded_host" "$$proxy_add_x_forwarded_for"
    networks:
      - qa-network

  letsencrypt:
    image: nginxproxy/acme-companion
    container_name: letsencrypt
    restart: unless-stopped
    volumes:
      - $APP_DIR/web/nginx-secure/nginx/conf.d:/etc/nginx/conf.d
      - $APP_DIR/web/nginx-secure/nginx/vhost.d:/etc/nginx/vhost.d
      - $APP_DIR/web/nginx-secure/nginx/html:/usr/share/nginx/html
      - $APP_DIR/web/nginx-secure/nginx/certs:/etc/nginx/certs:rw
      - $APP_DIR/web/nginx-secure/etc/acme.sh:/etc/acme.sh
      - /var/run/docker.sock:/var/run/docker.sock:ro

    environment:
      - NGINX_DOCKER_GEN_CONTAINER=proxy
      - NGINX_PROXY_CONTAINER=proxy
      - DEFAULT_EMAIL=<EMAIL>

  qa:
    image: us-docker.pkg.dev/addie-440119/addie/qa-web
    container_name: qa
    restart: unless-stopped
    ports:
      - "0.0.0.0:3000:3000"
    environment:
      - LETSENCRYPT_EMAIL=<EMAIL>
      - LETSENCRYPT_HOST=qa.getaddie.com
      - VIRTUAL_HOST=qa.getaddie.com
      - VIRTUAL_PORT=3000
    env_file:
      - $APP_DIR/web/.env.production
    networks:
      - qa-network
    volumes:
      - $APP_DIR/web/nginx-secure/nginx/html/.well-known:/opt/www/.well-known

networks:
  qa-network:
    external: true

