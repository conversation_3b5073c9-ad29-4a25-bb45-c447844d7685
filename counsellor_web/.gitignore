## See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

 dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# email
/.react-email/

.vscode
.contentlayer
.idea

nginx-secure/nginx/conf.d/
/web/nginx-secure/etc/acme.sh/

#/web/nginx-secure/etc/*
#/web/nginx-secure/etc/acme.sh/**/*
#/web/nginx-secure/nginx/conf.d/
#/web/nginx-secure/nginx/

!/web/lib/