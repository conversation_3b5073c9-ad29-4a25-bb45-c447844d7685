import { prisma } from "@/common/db";

interface CounselorNote {
  id: string;
  student_id: string;
  counselor_id: string;
  topic: string;
  content: string;
  created_at: string;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

export const getCounselorNotes = async (
  studentId: string,
): Promise<CounselorNote[]> => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/api/students/${studentId}/counselorNotes`,
    );
    if (!response.ok) throw new Error("Failed to fetch counselor notes");
    return response.json();
  } catch (e) {
    console.error("Fetching counselor notes failed", e);
    return [];
  }
};

export const addCounselorNote = async (
  studentId: string,
  counselorId: string,
  content: string,
  topic: string = "General",
): Promise<CounselorNote | null> => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/api/students/${studentId}/counselorNotes`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          counselor_id: counselorId,
          topic,
          content,
        }),
      },
    );

    if (!response.ok) throw new Error("Failed to add counselor note");

    return response.json();
  } catch (error) {
    console.error("Error adding a counselor note:", error);
    return null;
  }
};

export const deleteCounselorNote = async (
  noteId: string,
  studentId: string,
): Promise<CounselorNote | null> => {
  try {
    const res = await fetch(
      `${API_BASE_URL}/api/students/${studentId}/counselorNotes`,
      {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          noteId,
        }),
      },
    );
    if (!res.ok) throw new Error("Failed to delete counselor note");
    return res.json();
  } catch (e) {
    console.error("Error deleting a counselor note:", e);
    return null;
  }
};

export const editCounselorNote = async (
  noteId: string,
  content: string,
  studentId: string,
): Promise<CounselorNote | null> => {
  try {
    const res = await fetch(
      `${API_BASE_URL}/api/students/${studentId}/counselorNotes`,
      {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          noteId,
          content,
        }),
      },
    );
    if (!res.ok) throw new Error("Failed to edit counselor note");
    return res.json();
  } catch (e) {
    console.error("Failed to edit counselor note");
    return null;
  }
};
