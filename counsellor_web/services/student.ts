import { prisma } from "@/common/db";
import log from "@/common/logger";

const API_BASE_URL = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

export async function getStudentData(id: string): Promise<any | null> {
  const response = await fetch(`${API_BASE_URL}/api/students/${id}`);
  if (!response.ok) {
    throw new Error("Failed to fetch student data");
  }
  const data = await response.json();

  if (data.users && data.users.length > 0) {
    data.first_name = data.users[0].first_name;
    data.last_name = data.users[0].last_name;
    data.email = data.users[0].email;
  }
  return data;
}

export async function getStudentMetrics(id: string) {
  const student = await prisma.student.findUnique({
    where: { id },
    include: {
      academic_achievements: true,
      workflow_steps: true,
      student_workflow: {
        include: {
          workflow: {
            include: {
              steps: true,
            },
          },
        },
      },
      ocean_scores: true,
      student_majors: {
        include: {
          matches: true,
          ocean_score: true,
        },
      },
    },
  });

  if (!student)
    return {
      academicProgress: 0,
      onboardingProgress: 0,
      lastInteraction: null,
    };

  // Calculate academic progress
  const academicProgress = Math.min(
    (student.academic_achievements.length / 10) * 100,
    100,
  );

  // Get total steps from the workflow
  // const totalSteps = student.student_workflow[0]?.workflow.steps.length;
  const totalSteps =
    student.workflow_steps.length ||
    student.student_workflow.reduce(
      (total, workflow) => total + (workflow.workflow.steps?.length || 0),
      0,
    ) ||
    0;
  const completedSteps = student.workflow_steps.filter(
    (step) => step.completed,
  ).length;
  const onboardingProgress = Math.min((completedSteps / totalSteps) * 100, 100);

  // Check steps
  // console.log("student metrics ==>", student);
  // console.log(
  //   "student_worklow.workflow.steps===>",
  //   student.student_workflow[0].workflow.steps,
  //   "student wortflow==>",
  //   student.student_workflow[0].workflow,
  //   "student workflow parent ===>",
  //   student.student_workflow,
  //   "reduced workflow total ==>",
  //   // This should be the total steps
  //   student.student_workflow.reduce(
  //     (total, workflow) => total + workflow.workflow.steps.length,
  //     0,
  //   ),
  // );
  // console.log(
  //   "completed steps ==>",
  //   student.workflow_steps.filter((step) => step.completed).length,
  // );
  // console.log(
  //   "non completed steps==>",
  //   student.workflow_steps.filter((step) => !step.completed).length,
  // );
  // console.log("total steps===>", student.workflow_steps.length);
  return {
    academicProgress,
    onboardingProgress,
    lastInteraction: student.updated_at,
    completedWFSteps: completedSteps,
    totalSteps: totalSteps,
  };
}

export async function getStudentMessages(id: string) {
  const student = await prisma.student.findUnique({
    where: { id },
    include: {
      users: true,
      st_agents: true, // Include student agents
    },
  });

  if (!student?.users[0]?.id) {
    return [];
  }

  const messagesId = student.users[0].id;

  const messages = await prisma.messages.findMany({
    where: { session_id: messagesId },
    orderBy: { created_at: "asc" },
  });

  let minMessages = messages.map((message: any) => {
    const msgData = message.message.data;
    const data = {
      id: msgData.id,
      type: msgData.type,
      content: msgData.content,
      created_at: msgData.created_at || message.created_at,
    };

    return data;
  });

  // Transform message types
  for (const message of minMessages) {
    if (message.type === "human") {
      message.type = "Student";
    }
    if (message.type === "ai") {
      message.type = "Addie";
    }
  }

  // Filter out empty messages
  minMessages = minMessages.filter((message) => message.content !== "");

  return minMessages;
}

export async function getStudentOnboarding(id: string) {
  try {
    return await prisma.studentWorkFlowStep.findMany({
      where: {
        student_id: id,
      },
      orderBy: {
        created_at: "desc",
      },
    });
  } catch (error) {
    console.error("Error fetching onboarding data:", error);
    return [];
  }
}

export async function getTeacherComments(id: string) {
  const comments = await prisma.teacherComment.findMany({
    where: {
      student_id: id,
    },
    orderBy: {
      created_at: "desc",
    },
    select: {
      id: true,
      comment: true,
      teacher_name: true,
      created_at: true,
      context: true,
    },
  });

  return comments.map((comment) => ({
    id: comment.id,
    comment: comment.comment,
    teacher_name: comment.teacher_name || "Unknown Teacher",
    date: comment.created_at,
    context: comment.context,
  }));
}

export async function getQuestionResponses(id: string) {
  const responses = await prisma.questionResponse.findMany({
    where: {
      student_id: id,
    },
    include: {
      question: {
        select: {
          id: true,
          question: true,
          question_hash: true,
          questionnaire_id: true,
        },
      },
    },
  });

  return responses.map((response) => ({
    id: response.id,
    question: {
      id: response.question?.id,
      text: response.question?.question,
      type: "text", // Default type since it's not in the schema
    },
    response: response.response,
    workflow_step_id: response.step_id,
  }));
}

// API to get students generated teacher comment summary
export const getTeacherCommentsSummary = async (studentId: string) => {
  try {
    return await prisma.generatedTeacherCommentsSummary.findMany({
      where: { student_id: studentId },
      orderBy: { created_at: "desc" },
    });
  } catch (error) {
    console.error("Error fetching generated teacher comment summary", error);
    return [];
  }
};

export const getStudentChatSummary = async (studentId: string) => {
  try {
    return await prisma.generatedStudentConversationSummary.findMany({
      where: { student_id: studentId },
      orderBy: { created_at: "desc" },
    });
  } catch (error) {
    console.error("Error fetching student chat summary", error);
    return [];
  }
};

export async function getCounselorMessages(
  messagesId: string,
  lastMsgId: number,
) {
  log.debug(`### session-id ${messagesId} ###`);

  const messages = await prisma.messages.findMany({
    where: { session_id: messagesId, id: { gt: lastMsgId } },
    orderBy: { id: "asc" },
  });

  let minMessages = messages.map((message: any) => {
    const msgData = message.message.data;
    const data = {
      id: message.id,
      type: msgData.type,
      role: msgData.role || msgData.type, // Add role if it exists
      content: msgData.content,
      created_at: msgData.created_at || message.created_at,
    };

    return data;
  });

  // console.log("minMessages ===>", minMessages);

  // Filter out system messages - don't display them in the UI
  minMessages = minMessages.filter(
    (message) => message.type !== "system" && message.role !== "system",
  );

  // Transform message types
  for (const message of minMessages) {
    if (message.type === "human") {
      message.type = "Student";
    }
    if (message.type === "ai") {
      message.type = "Addie";
    }
  }

  // Filter out empty messages
  minMessages = minMessages.filter((message) => message.content !== "");

  log.debug(`## num-msgs: ${minMessages.length} ##`);

  return minMessages;
}
