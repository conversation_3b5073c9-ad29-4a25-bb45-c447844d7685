// "use server";
//
// import { cookies } from "next/headers";
//
// const allowedEmails = ["<EMAIL>", "<EMAIL>"];
//
// export async function createSession(email: string) {
//   if (!allowedEmails.includes(email)) {
//     throw new Error("Unauthorized email");
//   }
//
//   const sessionValue = btoa(email);
//   const myCookies = cookies();
//   await myCookies.set("counsellor-session", sessionValue, {
//     httpOnly: true,
//     secure: true,
//     maxAge: 7 * 24 * 60 * 60,
//   });
// }
//
// export async function deleteSession() {
//   const myCookies = cookies();
//   await myCookies.delete("counsellor-session");
// }
//
// export async function getSession() {
//   const myCookies = cookies();
//   const session = myCookies.get("counsellor-session")?.value;
//   if (!session) return null;
//
//   try {
//     const email = atob(session); // Decode session value
//     return email; // Return email for further validation
//   } catch (err) {
//     console.error("Invalid session encoding:", err);
//     return null; // Return null if session decoding fails
//   }
// }
