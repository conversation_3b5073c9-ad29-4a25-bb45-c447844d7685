// prisma/seed.ts
import { PrismaClient, UserRole } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  // Create counselor users first
  const counselor1 = await prisma.user.create({
    data: {
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      email: "<EMAIL>",
      role: UserRole.COUNSELOR,
      Counselor: {
        create: {
          id: "c1",
        },
      },
    },
    include: {
      Counselor: true,
    },
  });

  const counselor2 = await prisma.user.create({
    data: {
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      email: "<EMAIL>",
      role: UserRole.COUNSELOR,
      Counselor: {
        create: {
          id: "c2",
        },
      },
    },
    include: {
      Counselor: true,
    },
  });

  const counselor3 = await prisma.user.create({
    data: {
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      email: "<EMAIL>",
      role: User<PERSON><PERSON>.COUNSELOR,
      Counselor: {
        create: {
          id: "c3",
        },
      },
    },
    include: {
      Counselor: true,
    },
  });

  // First, create users that will be associated with students
  const user1 = await prisma.user.create({
    data: {
      first_name: "<PERSON>",
      last_name: "Bueller",
      email: "<EMAIL>",
      role: UserRole.STUDENT,
      date_of_birth: new Date("1967-09-29"),
    },
  });

  const user2 = await prisma.user.create({
    data: {
      first_name: "Cameron",
      last_name: "Frye",
      email: "<EMAIL>",
      role: UserRole.STUDENT,
      date_of_birth: new Date("1967-03-20"),
    },
  });

  const user3 = await prisma.user.create({
    data: {
      first_name: "Sloane",
      last_name: "Peterson",
      email: "<EMAIL>",
      role: UserRole.STUDENT,
      date_of_birth: new Date("1967-07-05"),
    },
  });

  // Create students with counselor assignments
  const student1 = await prisma.student.create({
    data: {
      student_id: "1234567890",
      grade: 12,
      users: {
        connect: { id: user1.id },
      },
      counselors: {
        connect: { id: counselor1.Counselor?.id },
      },
      academic_achievements: {
        create: {
          id: "ach1",
          title: "Honor Roll",
          description: "Achieved Honor Roll for Fall 1985",
        },
      },
      extra_curriculars: {
        create: {
          activity: "Computer Club",
          role: "Team Slacker",
          description: "Was never there but got the recognition anyway",
          start_date: new Date("1984-09-01"),
        },
      },
      skills: {
        create: {
          name: "Leadership",
          proficiency: "Advanced",
          category: "Soft Skills",
        },
      },
    },
  });

  const student2 = await prisma.student.create({
    data: {
      student_id: "0987654321",
      grade: 12,
      users: {
        connect: { id: user2.id },
      },
      counselors: {
        connect: { id: counselor2.Counselor?.id },
      },
      academic_achievements: {
        create: {
          id: "ach2",
          title: "Science Fair Winner",
          description: "First Place in Regional Science Fair",
        },
      },
      extra_curriculars: {},
      skills: {
        create: {
          name: "Critical Thinking",
          proficiency: "Advanced",
          category: "Soft Skills",
        },
      },
    },
  });

  const student3 = await prisma.student.create({
    data: {
      student_id: "1029384756",
      grade: 11,
      users: {
        connect: { id: user3.id },
      },
      counselors: {
        connect: { id: counselor3.Counselor?.id },
      },
      academic_achievements: {
        create: {
          id: "ach3",
          title: "Art Showcase Winner",
          description: "Won first prize at the 1985 Regional Art Showcase",
        },
      },
      extra_curriculars: {
        create: {
          activity: "Drama Club",
          role: "Lead Actress",
          description:
            "Starred in multiple school productions, known for her captivating performances",
          start_date: new Date("1984-09-01"),
        },
      },
      skills: {
        create: {
          name: "Creativity",
          proficiency: "Advanced",
          category: "Artistic Skills",
        },
      },
    },
  });

  // First create the prompts
  const initialPrompt1 = await prisma.prompt.create({
    data: {
      content: "Initial chat prompt",
    },
  });

  const configPrompt1 = await prisma.prompt.create({
    data: {
      content: "Configuration prompt",
    },
  });

  // Then create the config
  const agentConfig1 = await prisma.studentAgentConfig.create({
    data: {
      role: "student",
      prompt: {
        connect: {
          id: configPrompt1.id,
        },
      },
    },
  });

  // After creating students, create their chat agents and messages
  const studentAgent1 = await prisma.studentAgent.create({
    data: {
      student_id: student1.id,
      messages_id: "123e4567-e89b-12d3-a456-426614174000",
      prompt_id: initialPrompt1.id,
      config_id: agentConfig1.id,
    },
  });

  // Create messages for the chat
  await prisma.messages.createMany({
    data: [
      {
        session_id: "123e4567-e89b-12d3-a456-426614174000",
        message: {
          role: "assistant",
          content: "Hi Ferris! How can I help you today?",
          timestamp: new Date("2024-03-23T13:00:00"),
        },
      },
      {
        session_id: "123e4567-e89b-12d3-a456-426614174000",
        message: {
          role: "user",
          content: "I need help with my college applications",
          timestamp: new Date("2024-03-23T13:01:00"),
        },
      },
      {
        session_id: "123e4567-e89b-12d3-a456-426614174000",
        message: {
          role: "assistant",
          content:
            "I'd be happy to help with your college applications. What specific aspect would you like to discuss?",
          timestamp: new Date("2024-03-23T13:02:00"),
        },
      },
    ],
  });

  // Create a workflow first
  const onboardingWorkflow = await prisma.workflow.create({
    data: {
      name: "Student Onboarding",
      steps: {
        create: [
          {
            name: "Personal Info",
            goal: "Complete personal information",
            data: { type: "form", required: true },
          },
          {
            name: "Academic Goals",
            goal: "Set academic goals",
            data: { type: "form", required: true },
          },
          {
            name: "Post-Secondary Preferences",
            goal: "Define study preferences",
            data: { type: "form", required: true },
          },
          {
            name: "Career Interests",
            goal: "Explore career interests",
            data: { type: "form", required: true },
          },
          {
            name: "Schedule Setup",
            goal: "Set up study schedule",
            data: { type: "form", required: true },
          },
        ],
      },
    },
    include: {
      steps: true,
    },
  });

  // Create student workflow and steps for each student
  const studentWorkflow1 = await prisma.studentWorkflow.create({
    data: {
      student_id: student1.id,
      workflow_id: onboardingWorkflow.id,
      steps: {
        create: [
          {
            step_id: onboardingWorkflow.steps[0].id,
            student_id: student1.id,
            completed: true,
            data: { completed_at: new Date() },
          },
          {
            step_id: onboardingWorkflow.steps[1].id,
            student_id: student1.id,
            completed: true,
            data: { completed_at: new Date() },
          },
          {
            step_id: onboardingWorkflow.steps[2].id,
            student_id: student1.id,
            completed: false,
            data: {},
          },
        ],
      },
    },
  });

  const studentWorkflow2 = await prisma.studentWorkflow.create({
    data: {
      student_id: student2.id,
      workflow_id: onboardingWorkflow.id,
      steps: {
        create: [
          {
            step_id: onboardingWorkflow.steps[0].id,
            student_id: student2.id,
            completed: true,
            data: { completed_at: new Date() },
          },
        ],
      },
    },
  });

  const studentWorkflow3 = await prisma.studentWorkflow.create({
    data: {
      student_id: student3.id,
      workflow_id: onboardingWorkflow.id,
      steps: {
        create: [
          {
            step_id: onboardingWorkflow.steps[0].id,
            student_id: student3.id,
            completed: true,
            data: { completed_at: new Date() },
          },
          {
            step_id: onboardingWorkflow.steps[1].id,
            student_id: student3.id,
            completed: true,
            data: { completed_at: new Date() },
          },
          {
            step_id: onboardingWorkflow.steps[2].id,
            student_id: student3.id,
            completed: true,
            data: { completed_at: new Date() },
          },
          {
            step_id: onboardingWorkflow.steps[3].id,
            student_id: student3.id,
            completed: true,
            data: { completed_at: new Date() },
          },
        ],
      },
    },
  });

  console.log({
    counselors: [counselor1, counselor2, counselor3],
    students: [student1, student2, student3],
  });
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
