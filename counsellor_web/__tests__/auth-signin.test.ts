import { describe, it, expect, beforeEach, jest } from "@jest/globals";

// Mock Prisma client without importing actual db
const mockPrismaFindUnique = jest.fn();
const mockPrisma = {
  user: {
    findUnique: mockPrismaFindUnique,
  },
};

// Mock console for logging
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

beforeAll(() => {
  console.log = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
});

// Mock auth callback function to test
const mockSignInCallback = async ({ user, account, profile }: any) => {
  // First check if we have an email
  if (!user?.email) return false;
  
  try {
    // Check if this user already exists in our database and get their role
    const existingUser = await mockPrisma.user.findUnique({ 
      where: { email: user.email },
      select: { 
        id: true, 
        role: true, 
        enabled: true,
        email: true 
      }
    });
    
    // Only allow sign-in if the user already exists
    if (!existingUser) {
      console.log(`Sign-in denied: User not found for email ${user.email}`);
      return false;
    }

    // Only allow COUNSELOR and ADMIN roles for the counselor site
    if (existingUser.role !== "COUNSELOR" && existingUser.role !== "ADMIN") {
      console.log(`Sign-in denied: User ${user.email} has role ${existingUser.role}, expected COUNSELOR or ADMIN`);
      return false;
    }

    // Check if user account is enabled
    if (existingUser.enabled === false) {
      console.log(`Sign-in denied: User ${user.email} account is disabled`);
      return false;
    }

    console.log(`Sign-in allowed: ${existingUser.role} user ${user.email}`);
    return true;
  } catch (error) {
    // If there's an error checking the user, deny sign-in
    console.error("Error verifying existing user:", error);
    return false;
  }
};

describe("Counselor Site Auth SignIn Callback", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should deny sign-in when no email is provided", async () => {
    const result = await mockSignInCallback({
      user: {},
      account: {},
      profile: {},
    });

    expect(result).toBe(false);
  });

  it("should deny sign-in when user is not found", async () => {
    mockPrismaFindUnique.mockResolvedValue(null);

    const result = await mockSignInCallback({
      user: { email: "<EMAIL>" },
      account: {},
      profile: {},
    });

    expect(result).toBe(false);
    expect(console.log).toHaveBeenCalledWith("Sign-in denied: User not found <NAME_EMAIL>");
  });

  it("should deny sign-in when user has STUDENT role", async () => {
    mockPrismaFindUnique.mockResolvedValue({
      id: "123",
      email: "<EMAIL>",
      role: "STUDENT",
      enabled: true,
    });

    const result = await mockSignInCallback({
      user: { email: "<EMAIL>" },
      account: {},
      profile: {},
    });

    expect(result).toBe(false);
    expect(console.log).toHaveBeenCalledWith("Sign-in denied: User <EMAIL> has role STUDENT, expected COUNSELOR or ADMIN");
  });

  it("should allow sign-in when user has COUNSELOR role", async () => {
    mockPrismaFindUnique.mockResolvedValue({
      id: "123",
      email: "<EMAIL>",
      role: "COUNSELOR",
      enabled: true,
    });

    const result = await mockSignInCallback({
      user: { email: "<EMAIL>" },
      account: {},
      profile: {},
    });

    expect(result).toBe(true);
    expect(console.log).toHaveBeenCalledWith("Sign-in allowed: <NAME_EMAIL>");
  });

  it("should allow sign-in when user has ADMIN role", async () => {
    mockPrismaFindUnique.mockResolvedValue({
      id: "123",
      email: "<EMAIL>",
      role: "ADMIN",
      enabled: true,
    });

    const result = await mockSignInCallback({
      user: { email: "<EMAIL>" },
      account: {},
      profile: {},
    });

    expect(result).toBe(true);
    expect(console.log).toHaveBeenCalledWith("Sign-in allowed: <NAME_EMAIL>");
  });

  it("should deny sign-in when counselor user is disabled", async () => {
    mockPrismaFindUnique.mockResolvedValue({
      id: "123",
      email: "<EMAIL>",
      role: "COUNSELOR",
      enabled: false,
    });

    const result = await mockSignInCallback({
      user: { email: "<EMAIL>" },
      account: {},
      profile: {},
    });

    expect(result).toBe(false);
    expect(console.log).toHaveBeenCalledWith("Sign-in denied: User <EMAIL> account is disabled");
  });

  it("should allow sign-in for counselor user with enabled=undefined (default true)", async () => {
    mockPrismaFindUnique.mockResolvedValue({
      id: "123",
      email: "<EMAIL>",
      role: "COUNSELOR",
      enabled: undefined,
    });

    const result = await mockSignInCallback({
      user: { email: "<EMAIL>" },
      account: {},
      profile: {},
    });

    expect(result).toBe(true);
    expect(console.log).toHaveBeenCalledWith("Sign-in allowed: <NAME_EMAIL>");
  });

  it("should deny sign-in when database error occurs", async () => {
    const dbError = new Error("Database connection failed");
    mockPrismaFindUnique.mockRejectedValue(dbError);

    const result = await mockSignInCallback({
      user: { email: "<EMAIL>" },
      account: {},
      profile: {},
    });

    expect(result).toBe(false);
    expect(console.error).toHaveBeenCalledWith("Error verifying existing user:", dbError);
  });
});