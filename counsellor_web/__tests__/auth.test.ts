import { describe, it } from "@jest/globals";
import { v4 as uuid } from "uuid";

import { createUser } from "@/common/prisma-auth-adapter";

describe("createUser", () => {
  it("create user without middle name", async () => {
    const id: string = uuid();

    const data = {
      id,
      name: "<PERSON>",
      email: "<EMAIL>",
      emailVerified: null,
    };

    await createUser(data);
  });
});
