import { describe, it, expect, beforeEach, jest } from "@jest/globals";

// Mock dependencies
const mockPrismaFindUnique = jest.fn();
const mockConsole = {
  log: jest.fn(),
  error: jest.fn(),
};

// Replace console methods for testing
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

beforeAll(() => {
  console.log = mockConsole.log;
  console.error = mockConsole.error;
});

afterAll(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
});

// Mock prisma
jest.mock("@/common/db", () => ({
  prisma: {
    user: {
      findUnique: () => mockPrismaFindUnique(),
    },
  },
}));

// Simulated counselor site auth callback
const validateCounselorOAuthSignIn = async ({ user, account, profile }: any) => {
  // CRITICAL: For OAuth providers, validate that the profile email matches the user email
  if (account?.provider === "google" && profile?.email) {
    if (user.email !== profile.email) {
      console.error(`SECURITY: Email mismatch detected! User email: ${user.email}, Profile email: ${profile.email}`);
      console.error(`This indicates a potential account linking attack or misconfiguration`);
      return false;
    }
    
    const actualEmail = profile.email;
    console.log(`OAuth login attempt with email from profile: ${actualEmail}`);
    
    const actualUser = await mockPrismaFindUnique();
    
    if (!actualUser) {
      console.log(`Sign-in denied: User not found for OAuth email ${actualEmail}`);
      return false;
    }
    
    // Only allow COUNSELOR and ADMIN roles for the counselor site
    if (actualUser.role !== "COUNSELOR" && actualUser.role !== "ADMIN") {
      console.log(`Sign-in denied: OAuth user ${actualEmail} has role ${actualUser.role}, expected COUNSELOR or ADMIN`);
      return false;
    }
    
    if (actualUser.enabled === false) {
      console.log(`Sign-in denied: OAuth user ${actualEmail} account is disabled`);
      return false;
    }
    
    console.log(`Sign-in allowed: ${actualUser.role} user ${actualEmail} via OAuth`);
    return true;
  }

  // For non-OAuth providers
  if (!user?.email) return false;
  
  const existingUser = await mockPrismaFindUnique();
  
  if (!existingUser) {
    console.log(`Sign-in denied: User not found for email ${user.email}`);
    return false;
  }

  if (existingUser.role !== "COUNSELOR" && existingUser.role !== "ADMIN") {
    console.log(`Sign-in denied: User ${user.email} has role ${existingUser.role}, expected COUNSELOR or ADMIN`);
    return false;
  }

  if (existingUser.enabled === false) {
    console.log(`Sign-in denied: User ${user.email} account is disabled`);
    return false;
  }

  console.log(`Sign-in allowed: ${existingUser.role} user ${user.email}`);
  return true;
};

describe("Counselor Site OAuth Security - Account Linking Attack Prevention", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Critical Security: Email Mismatch Detection", () => {
    it("should BLOCK login when OAuth profile email differs from linked user email", async () => {
      // Scenario: Student tries to use a Google account linked to counselor
      const result = await validateCounselorOAuthSignIn({
        user: {
          id: "counselor-id",
          email: "<EMAIL>",
          role: "COUNSELOR",
        },
        account: {
          provider: "google",
          providerAccountId: "shared-google-id",
        },
        profile: {
          email: "<EMAIL>", // Different email!
          sub: "shared-google-id",
        },
      });

      expect(result).toBe(false);
      expect(mockConsole.error).toHaveBeenCalledWith(
        "SECURITY: Email mismatch detected! User email: <EMAIL>, Profile email: <EMAIL>"
      );
      expect(mockConsole.error).toHaveBeenCalledWith(
        "This indicates a potential account linking attack or misconfiguration"
      );
    });

    it("should BLOCK student using counselor's linked Google account", async () => {
      mockPrismaFindUnique.mockResolvedValue({
        id: "student-id",
        email: "<EMAIL>",
        role: "STUDENT",
        enabled: true,
      });

      const result = await validateCounselorOAuthSignIn({
        user: {
          id: "counselor-id",
          email: "<EMAIL>",
          role: "COUNSELOR",
        },
        account: {
          provider: "google",
          providerAccountId: "shared-google-id",
        },
        profile: {
          email: "<EMAIL>",
          sub: "shared-google-id",
        },
      });

      expect(result).toBe(false);
      expect(mockConsole.error).toHaveBeenCalledWith(
        "SECURITY: Email mismatch detected! User email: <EMAIL>, Profile email: <EMAIL>"
      );
    });

    it("should allow OAuth login when emails match and user is counselor", async () => {
      mockPrismaFindUnique.mockResolvedValue({
        id: "counselor-id",
        email: "<EMAIL>",
        role: "COUNSELOR",
        enabled: true,
      });

      const result = await validateCounselorOAuthSignIn({
        user: {
          id: "counselor-id",
          email: "<EMAIL>",
          role: "COUNSELOR",
        },
        account: {
          provider: "google",
          providerAccountId: "counselor-google-id",
        },
        profile: {
          email: "<EMAIL>", // Emails match
          sub: "counselor-google-id",
        },
      });

      expect(result).toBe(true);
      expect(mockConsole.error).not.toHaveBeenCalled();
      expect(mockConsole.log).toHaveBeenCalledWith(
        "Sign-in allowed: <NAME_EMAIL> via OAuth"
      );
    });

    it("should allow OAuth login when emails match and user is admin", async () => {
      mockPrismaFindUnique.mockResolvedValue({
        id: "admin-id",
        email: "<EMAIL>",
        role: "ADMIN",
        enabled: true,
      });

      const result = await validateCounselorOAuthSignIn({
        user: {
          id: "admin-id",
          email: "<EMAIL>",
          role: "ADMIN",
        },
        account: {
          provider: "google",
          providerAccountId: "admin-google-id",
        },
        profile: {
          email: "<EMAIL>",
          sub: "admin-google-id",
        },
      });

      expect(result).toBe(true);
      expect(mockConsole.log).toHaveBeenCalledWith(
        "Sign-in allowed: <NAME_EMAIL> via OAuth"
      );
    });
  });

  describe("OAuth Role Validation for Counselor Site", () => {
    it("should block OAuth login for STUDENT role even if emails match", async () => {
      mockPrismaFindUnique.mockResolvedValue({
        id: "student-id",
        email: "<EMAIL>",
        role: "STUDENT",
        enabled: true,
      });

      const result = await validateCounselorOAuthSignIn({
        user: {
          id: "student-id",
          email: "<EMAIL>",
          role: "STUDENT",
        },
        account: {
          provider: "google",
          providerAccountId: "student-google-id",
        },
        profile: {
          email: "<EMAIL>",
          sub: "student-google-id",
        },
      });

      expect(result).toBe(false);
      expect(mockConsole.log).toHaveBeenCalledWith(
        "Sign-in denied: <NAME_EMAIL> has role STUDENT, expected COUNSELOR or ADMIN"
      );
    });

    it("should allow COUNSELOR role on counselor site", async () => {
      mockPrismaFindUnique.mockResolvedValue({
        id: "counselor-id",
        email: "<EMAIL>",
        role: "COUNSELOR",
        enabled: true,
      });

      const result = await validateCounselorOAuthSignIn({
        user: {
          id: "counselor-id",
          email: "<EMAIL>",
          role: "COUNSELOR",
        },
        account: {
          provider: "google",
          providerAccountId: "counselor-google-id",
        },
        profile: {
          email: "<EMAIL>",
          sub: "counselor-google-id",
        },
      });

      expect(result).toBe(true);
      expect(mockConsole.log).toHaveBeenCalledWith(
        "Sign-in allowed: <NAME_EMAIL> via OAuth"
      );
    });

    it("should allow ADMIN role on counselor site", async () => {
      mockPrismaFindUnique.mockResolvedValue({
        id: "admin-id",
        email: "<EMAIL>",
        role: "ADMIN",
        enabled: true,
      });

      const result = await validateCounselorOAuthSignIn({
        user: {
          id: "admin-id",
          email: "<EMAIL>",
          role: "ADMIN",
        },
        account: {
          provider: "google",
          providerAccountId: "admin-google-id",
        },
        profile: {
          email: "<EMAIL>",
          sub: "admin-google-id",
        },
      });

      expect(result).toBe(true);
      expect(mockConsole.log).toHaveBeenCalledWith(
        "Sign-in allowed: <NAME_EMAIL> via OAuth"
      );
    });

    it("should block OAuth login for disabled counselor", async () => {
      mockPrismaFindUnique.mockResolvedValue({
        id: "counselor-id",
        email: "<EMAIL>",
        role: "COUNSELOR",
        enabled: false,
      });

      const result = await validateCounselorOAuthSignIn({
        user: {
          id: "counselor-id",
          email: "<EMAIL>",
          role: "COUNSELOR",
        },
        account: {
          provider: "google",
          providerAccountId: "counselor-google-id",
        },
        profile: {
          email: "<EMAIL>",
          sub: "counselor-google-id",
        },
      });

      expect(result).toBe(false);
      expect(mockConsole.log).toHaveBeenCalledWith(
        "Sign-in denied: <NAME_EMAIL> account is disabled"
      );
    });
  });

  describe("Non-OAuth Provider Validation", () => {
    it("should validate magic link login for counselor", async () => {
      mockPrismaFindUnique.mockResolvedValue({
        id: "counselor-id",
        email: "<EMAIL>",
        role: "COUNSELOR",
        enabled: true,
      });

      const result = await validateCounselorOAuthSignIn({
        user: {
          id: "counselor-id",
          email: "<EMAIL>",
        },
        account: {
          provider: "email",
        },
        profile: null,
      });

      expect(result).toBe(true);
      expect(mockConsole.log).toHaveBeenCalledWith(
        "Sign-in allowed: <NAME_EMAIL>"
      );
    });

    it("should block magic link for student users", async () => {
      mockPrismaFindUnique.mockResolvedValue({
        id: "student-id",
        email: "<EMAIL>",
        role: "STUDENT",
        enabled: true,
      });

      const result = await validateCounselorOAuthSignIn({
        user: {
          id: "student-id",
          email: "<EMAIL>",
        },
        account: {
          provider: "email",
        },
        profile: null,
      });

      expect(result).toBe(false);
      expect(mockConsole.log).toHaveBeenCalledWith(
        "Sign-in denied: User <EMAIL> has role STUDENT, expected COUNSELOR or ADMIN"
      );
    });
  });
});