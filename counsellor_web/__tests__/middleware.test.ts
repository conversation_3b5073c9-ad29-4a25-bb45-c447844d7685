/**
 * @jest-environment node
 */
import { describe, it, expect, beforeEach, jest } from "@jest/globals";

// Mock getToken before importing other modules
const mockGetToken = jest.fn();
jest.mock("next-auth/jwt", () => ({
  getToken: mockGetToken,
}));

// Mock environment variables
jest.mock("@/env.mjs", () => ({
  env: {
    AUTH_SECRET: "test-secret",
    AUTH_URL: "https://test-counselor.com",
  },
}));

// Simplified middleware logic test
const testCounselorMiddlewareLogic = async (pathname: string, token: any, headers: Record<string, string> = {}) => {
  const protectedRoutes = ["/", "/dashboard", "/students", "/counselors", "/settings", "/onboarding"];
  
  // Skip auth checks for login page, auth API routes, static assets and favicon
  if (
    pathname.startsWith("/login") ||
    pathname.startsWith("/register") ||
    pathname.startsWith("/api/auth") ||
    pathname.startsWith("/_next") ||
    pathname.includes("/favicon.ico") ||
    pathname.includes("/site.webmanifest")
  ) {
    return { status: 200, type: 'allowed' };
  }

  // Check if route requires authentication
  const isProtectedRoute = protectedRoutes.some(
    (route) => pathname === route || pathname.startsWith(`${route}/`),
  );

  // Handle authentication for protected routes
  if (isProtectedRoute && !token) {
    return { status: 307, type: 'redirect', reason: 'no_token' };
  }

  // Check if user has appropriate role for counselor site - only allow COUNSELOR and ADMIN
  if (isProtectedRoute && token && token.role !== "COUNSELOR" && token.role !== "ADMIN") {
    return { status: 307, type: 'redirect', reason: 'unauthorized' };
  }

  // Check if user is disabled for protected routes
  // Only block if explicitly disabled (enabled === false), allow if enabled is undefined/null
  if (isProtectedRoute && token && token.enabled === false) {
    return { status: 307, type: 'redirect', reason: 'account_disabled' };
  }

  return { status: 200, type: 'allowed' };
};

describe("Counselor Site Middleware Logic", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should allow access to login page", async () => {
    const result = await testCounselorMiddlewareLogic("/login", null);
    expect(result.status).toBe(200);
    expect(result.type).toBe('allowed');
  });

  it("should allow access to auth API routes", async () => {
    const result = await testCounselorMiddlewareLogic("/api/auth/signin", null);
    expect(result.status).toBe(200);
    expect(result.type).toBe('allowed');
  });

  it("should redirect to login when no token on protected route", async () => {
    const result = await testCounselorMiddlewareLogic("/dashboard", null);
    expect(result.status).toBe(307);
    expect(result.reason).toBe('no_token');
  });

  it("should deny access for STUDENT role user", async () => {
    const token = {
      sub: "123",
      email: "<EMAIL>",
      role: "STUDENT",
      enabled: true,
    };
    
    const result = await testCounselorMiddlewareLogic("/dashboard", token);
    expect(result.status).toBe(307);
    expect(result.reason).toBe('unauthorized');
  });

  it("should allow access for COUNSELOR role user", async () => {
    const token = {
      sub: "123",
      email: "<EMAIL>",
      role: "COUNSELOR",
      enabled: true,
    };
    
    const result = await testCounselorMiddlewareLogic("/dashboard", token);
    expect(result.status).toBe(200);
    expect(result.type).toBe('allowed');
  });

  it("should allow access for ADMIN role user", async () => {
    const token = {
      sub: "123",
      email: "<EMAIL>",
      role: "ADMIN",
      enabled: true,
    };
    
    const result = await testCounselorMiddlewareLogic("/dashboard", token);
    expect(result.status).toBe(200);
    expect(result.type).toBe('allowed');
  });

  it("should deny access for disabled counselor user", async () => {
    const token = {
      sub: "123",
      email: "<EMAIL>",
      role: "COUNSELOR",
      enabled: false,
    };
    
    const result = await testCounselorMiddlewareLogic("/dashboard", token);
    expect(result.status).toBe(307);
    expect(result.reason).toBe('account_disabled');
  });

  it("should deny access for disabled admin user", async () => {
    const token = {
      sub: "123",
      email: "<EMAIL>",
      role: "ADMIN",
      enabled: false,
    };
    
    const result = await testCounselorMiddlewareLogic("/dashboard", token);
    expect(result.status).toBe(307);
    expect(result.reason).toBe('account_disabled');
  });

  it("should allow access for counselor with enabled=undefined (default true)", async () => {
    const token = {
      sub: "123",
      email: "<EMAIL>",
      role: "COUNSELOR",
      enabled: undefined,
    };
    
    const result = await testCounselorMiddlewareLogic("/dashboard", token);
    expect(result.status).toBe(200);
    expect(result.type).toBe('allowed');
  });

  it("should allow access to non-protected routes without token", async () => {
    const result = await testCounselorMiddlewareLogic("/some-public-page", null);
    expect(result.status).toBe(200);
    expect(result.type).toBe('allowed');
  });

  it("should handle multiple unauthorized roles correctly", async () => {
    const roles = ["STUDENT", "TEACHER", "PARENT"];
    
    for (const role of roles) {
      const token = {
        sub: "123",
        email: `${role.toLowerCase()}@example.com`,
        role: role,
        enabled: true,
      };
      
      const result = await testCounselorMiddlewareLogic("/dashboard", token);
      expect(result.status).toBe(307);
      expect(result.reason).toBe('unauthorized');
    }
  });
});