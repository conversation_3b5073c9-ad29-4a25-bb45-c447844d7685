// Use NextAuth's built-in middleware (no Prisma dependency)
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";
import { env } from "./env.mjs";

// Routes that require authentication
const protectedRoutes = ["/", "/dashboard", "/students", "/counselors", "/settings", "/onboarding"];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Skip auth checks for login page, auth API routes, static assets and favicon
  if (
    pathname.startsWith("/login") ||
    pathname.startsWith("/register") ||
    pathname.startsWith("/api/auth") ||
    pathname.startsWith("/_next") ||
    pathname.includes("/favicon.ico") ||
    pathname.includes("/site.webmanifest")
  ) {
    return NextResponse.next();
  }

  // Create a modified request to ensure cookies are processed correctly with tunnelmole
  const isTunnelmole = request.headers.get("host")?.includes("tunnelmole");
  
  // Get the session token from the request (no database access)
  const token = await getToken({ 
    req: request, 
    secret: env.AUTH_SECRET,
    secureCookie: process.env.NODE_ENV === "production" || isTunnelmole
  });
  
  // Check if route requires authentication
  const isProtectedRoute = protectedRoutes.some(route => pathname === route || pathname.startsWith(`${route}/`));
  
  // Handle authentication for protected routes
  if (isProtectedRoute && !token) {
    // Redirect to login if no auth token
    const url = new URL("/login", request.url);
    
    // Use AUTH_URL as the base for the callback URL, preserving the path
    const authUrl = env.AUTH_URL;
    if (authUrl) {
      const callbackUrl = new URL(request.nextUrl.pathname, authUrl);
      url.searchParams.set("callbackUrl", encodeURI(callbackUrl.toString()));
    } else {
      // Fallback to the original behavior if AUTH_URL is not set
      url.searchParams.set("callbackUrl", encodeURI(request.url));
    }
    
    return NextResponse.redirect(url);
  }
  
  // Check if user is disabled for protected routes
  // Only block if explicitly disabled (enabled === false), allow if enabled is undefined/null
  if (isProtectedRoute && token && token.enabled === false) {
    // Redirect to login with disabled message
    const url = new URL("/login", request.url);
    url.searchParams.set("error", "account_disabled");
    return NextResponse.redirect(url);
  }
  
  // We have token OR non-protected route - let the request through
  return NextResponse.next();
}

export const config = {
  matcher: [
    "/((?!_next/static|_next/image|favicon.ico).*)",
  ],
};
