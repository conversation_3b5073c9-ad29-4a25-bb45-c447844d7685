"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { handlePostSignIn } from "@/actions/auth-actions";

// This hook handles post-authentication operations by calling our server action
export const useAuthCallback = () => {
  const { data: session, status } = useSession();

  useEffect(() => {
    // Only run when authentication is completed successfully
    if (status === "authenticated" && session?.user?.id) {
      // Call our server action to handle post-authentication operations
      // like creating a counselor record
      handlePostSignIn()
        .then(result => {
          if (!result.success) {
            console.error("Failed to process post-authentication operations:", result.error);
          }
        })
        .catch(error => {
          console.error("Error during post-authentication process:", error);
        });
    }
  }, [session, status]);

  return null;
};
