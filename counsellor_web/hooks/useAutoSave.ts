import { useCallback, useEffect, useRef, useState } from "react";

type SaveStatus = "idle" | "saving" | "saved" | "error";

/**
 * A generic auto‐save hook that debounces changes to `data` and calls `saveFn`.
 *
 * @param data      The data to watch for changes (could be goals, questions, etc.).
 * @param isValid   If `false`, auto‐save is skipped entirely.
 * @param saveFn    An async function that saves the data to the server.
 * @param delay     Debounce delay in ms (default 1500 = 1.5s).
 */
export function useAutoSave<T>(
  data: T,
  isValid: boolean,
  saveFn: (updatedData: T) => Promise<void>,
  delay: number = 1500,
) {
  const [saveStatus, setSaveStatus] = useState<SaveStatus>("idle");
  const [lastSavedTime, setLastSavedTime] = useState<Date | null>(null);
  const [saveError, setSaveError] = useState<string | null>(null);

  // lastSavedData stored in a ref (no re‐renders on assignment)
  const lastSavedRef = useRef<T>(data);

  const timerRef = useRef<number | null>(null);

  const isDataChanged = useCallback((a: T, b: T) => {
    return JSON.stringify(a) !== JSON.stringify(b);
  }, []);

  useEffect(() => {
    // If invalid, skip auto‐save
    if (!isValid) {
      setSaveStatus("idle");
      return;
    }

    // Compare current data with the ref’s data
    if (!isDataChanged(data, lastSavedRef.current)) {
      // Nothing has changed—no need to schedule a save
      return;
    }

    // Debounce any existing timer
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    timerRef.current = window.setTimeout(async () => {
      try {
        setSaveStatus("saving");
        setSaveError(null);
        // console.log("Saving..");
        await saveFn(data);

        // After successful save, update the ref
        lastSavedRef.current = data;
        setLastSavedTime(new Date());
        setSaveStatus("saved");
      } catch (err) {
        console.error("Auto‐save error:", err);
        setSaveStatus("error");
        setSaveError("Failed to auto‐save. Please try again.");
      }
    }, delay);

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [data, isValid, saveFn, delay, isDataChanged]);

  const isSaving = saveStatus === "saving";

  return {
    isSaving,
    lastSavedTime,
    saveError,
    saveStatus,
  };
}
