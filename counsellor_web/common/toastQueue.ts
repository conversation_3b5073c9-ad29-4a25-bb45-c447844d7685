import { delay } from "@/actions/queue";
import { toast } from "@/components/ui/use-toast";

export class ToastQueue {
  toasts: any[] = [];
  delay: number = 3000;

  addToast(toast: any) {
    this.toasts.push(toast);
  }

  async renderToasts() {
    for (const data of this.toasts) {
      const t = toast(data);
      await delay(this.delay);
      t.dismiss();
    }

    this.clearToasts();
  }

  clearToasts() {
    this.toasts = [];
  }
}
