// Helper: Map front-end question type to corresponding DB table name.
import { QuestionItem } from "@/actions/conversations/conversation-workflow";
import { MultipleChoiceType } from "@prisma/client";

import { prisma } from "@/common/db";
import { hash, slugify } from "@/common/utils";

export function mapQuestionTypeToTable(qType: string): string {
  switch (qType) {
    case "binary":
      return "BinaryQuestion";
    case "multiple-choice":
      return "MultipleChoiceQuestion";
    case "short-answer":
    case "long-answer":
      return "Question";
    case "likert":
      return "LikertQuestion";
    default:
      return "Question";
  }
}

/**
 * Helper: Update an existing WorkflowStep and its underlying question record.
 *
 * If the type (i.e. the target table) remains the same, we simply update the
 * existing records. If the type has changed, we need to create a new underlying
 * question record and update the WorkflowStep accordingly.
 *
 * @param workflowStepId - The ID of the WorkflowStep to update.
 * @param indexOffset - The new index (for ordering).
 * @param question - The updated question from the front end.
 * @param newTable - The table name corresponding to the current question type.
 * @param questionnaireId - The questionnaire id used when creating new underlying records.
 * @returns The updated underlying record info { table, questionId }.
 */
export async function updateExistingWorkflowStep(
  workflowStepId: string,
  indexOffset: number,
  question: QuestionItem,
  newTable: string,
  questionnaireId: string,
  workflowId?: string,
): Promise<{ table: string; questionId: string }> {
  // Fetch current WorkflowStep to preserve its stored data.
  const currentStep = await prisma.workflowStep.findUnique({
    where: { id: workflowStepId },
  });
  if (!currentStep) {
    throw new Error("WorkflowStep not found");
  }
  const currentData = currentStep.data as {
    table: string;
    questionId: string;
    options?: string[];
  };

  // If the underlying table remains the same, update directly.
  if (currentData.table === newTable) {
    await prisma.workflowStep.update({
      where: { id: workflowStepId },
      data: {
        name: `Question-${currentData.table}-${currentData.questionId}`,
        index: indexOffset,
        data: {
          table: currentData.table, // preserve
          questionId: currentData.questionId, // preserve
          question: question.text,
          ...((question.type === "multiple-choice" ||
            question.type === "likert") && {
            options: question.options,
          }),
          ...(question.type === "short-answer" ||
          question.type === "long-answer"
            ? { characterLimit: question.characterLimit }
            : {}),
          canSkip: question.canSkip,
        },
        tags: question.tags,
      },
    });
    // Update the underlying question record.
    await updateUnderlyingQuestionRecord(
      currentData.table,
      currentData.questionId,
      question,
      workflowId,
    );
    return { table: currentData.table, questionId: currentData.questionId };
  } else {
    // Type has changed: First delete the old question record
    await deleteOldQuestionRecord(currentData.table, currentData.questionId);

    // Then create a new underlying question record for the new type
    let newQuestionRef: { table: string; questionId: string };
    if (newTable === "MultipleChoiceQuestion") {
      const mcQuestion = await prisma.multipleChoiceQuestion.create({
        data: {
          question: question.text,
          slug: slugify(question.text),
          question_hash: hash(
            `${question.text}-${workflowId || questionnaireId}`,
          ),
          questionnaire: { connect: { id: questionnaireId } },
          index: indexOffset,
          choices: {
            create: question.options?.map((option) => ({ option })) || [],
          },
        },
      });
      newQuestionRef = { table: newTable, questionId: mcQuestion.id };
    } else if (newTable === "LikertQuestion") {
      const mcQuestion = await prisma.multipleChoiceQuestion.create({
        data: {
          question: question.text,
          slug: slugify(question.text),
          question_hash: hash(
            `${question.text}-${workflowId || questionnaireId}`,
          ),
          questionnaire: { connect: { id: questionnaireId } },
          index: indexOffset,
          choices: {
            create: question.options?.map((option) => ({ option })) || [],
          },
          type: MultipleChoiceType.LIKERT,
        },
      });
      newQuestionRef = { table: newTable, questionId: mcQuestion.id };
    } else if (newTable === "Question") {
      const textQuestion = await prisma.question.create({
        data: {
          question: question.text,
          slug: slugify(question.text),
          question_hash: hash(
            `${question.text}-${workflowId || questionnaireId}`,
          ),
          questionnaire: { connect: { id: questionnaireId } },
          index: indexOffset,
        },
      });
      newQuestionRef = { table: newTable, questionId: textQuestion.id };
    } else if (newTable === "BinaryQuestion") {
      const binaryQuestion = await prisma.binaryQuestion.create({
        data: {
          question: question.text,
          slug: slugify(question.text),
          question_hash: hash(
            `${question.text}-${workflowId || questionnaireId}`,
          ),
          questionnaire: { connect: { id: questionnaireId } },
          index: indexOffset,
        },
      });
      newQuestionRef = { table: newTable, questionId: binaryQuestion.id };
    } else {
      throw new Error("Unsupported question type");
    }
    // Update the existing WorkflowStep to reference the new underlying record.
    await prisma.workflowStep.update({
      where: { id: workflowStepId },
      data: {
        name: `Question ${indexOffset + 1} - ${newQuestionRef.questionId}`,
        index: indexOffset,
        data: {
          table: newTable,
          questionId: newQuestionRef.questionId,
          question: question.text,
          ...(question.type === "multiple-choice" && {
            options: question.options,
          }),
        },
        tags: question.tags,
      },
    });
    return newQuestionRef;
  }
}

/**
 * Helper: Update the underlying question record in the appropriate table.
 *
 * @param table - The table name (e.g. "MultipleChoiceQuestion").
 * @param questionId - The id of the underlying question record.
 * @param question - The updated question from the front end.
 */
async function updateUnderlyingQuestionRecord(
  table: string,
  questionId: string,
  question: QuestionItem,
  workflowId?: string,
): Promise<void> {
  // Generate a unique hash by combining the question text with the workflowId
  // This ensures uniqueness even if the question text is the same across different workflows
  const uniqueHash = hash(`${question.text}-${workflowId || questionId}`);

  if (table === "MultipleChoiceQuestion") {
    await prisma.multipleChoiceQuestion.update({
      where: { id: questionId },
      data: {
        question: question.text,
        question_hash: uniqueHash,
        choices: {
          deleteMany: {},
          create: question.options?.map((option) => ({ option })) || [],
        },
        can_skip: question.canSkip,
      },
    });
  } else if (table === "Question") {
    await prisma.question.update({
      where: { id: questionId },
      data: {
        question: question.text,
        question_hash: uniqueHash,
        can_skip: question.canSkip,
      },
    });
  } else if (table === "BinaryQuestion") {
    await prisma.binaryQuestion.update({
      where: { id: questionId },
      data: {
        question: question.text,
        question_hash: uniqueHash,
        can_skip: question.canSkip,
      },
    });
  } else if (table === "LikertQuestion") {
    await prisma.multipleChoiceQuestion.update({
      where: { id: questionId },
      data: {
        question: question.text,
        question_hash: uniqueHash,
        choices: {
          deleteMany: {},
          create: question.options?.map((option) => ({ option })) || [],
        },
        type: MultipleChoiceType.LIKERT,
        can_skip: question.canSkip,
      },
    });
  }
}
/**
 * Helper: Delete an old question record when the question type changes.
 *
 * @param table - The table name of the old question (e.g. "MultipleChoiceQuestion").
 * @param questionId - The id of the old question record.
 */
async function deleteOldQuestionRecord(
  table: string,
  questionId: string,
): Promise<void> {
  if (table === "MultipleChoiceQuestion") {
    // For MultipleChoice, find and delete via the parent MultipleChoiceQuestion
    // This will cascade delete the associated choices due to the relation
    await prisma.multipleChoiceQuestion.delete({
      where: { id: questionId },
    });
  } else if (table === "Question") {
    await prisma.question.delete({
      where: { id: questionId },
    });
  } else if (table === "BinaryQuestion") {
    await prisma.binaryQuestion.delete({
      where: { id: questionId },
    });
  } else if (table === "LikertQuestion") {
    await prisma.multipleChoiceQuestion.delete({
      where: { id: questionId },
    });
  }
  console.log(
    `Successfully deleted old question record: ${table} - ${questionId}`,
  );
}
