import { MagicLinkEmail } from "@/emails/magic-link-email";
import { UserRole } from "@prisma/client";
import { Resend } from "resend";

import { env } from "@/env.mjs";
import { siteConfig } from "@/config/site";
import { ALLOWED_DOMAINS } from "@/common/constants";

import log from "./logger";
import { getUserByEmail } from "./user";

export const resend = new Resend(env.RESEND_API_KEY);

export async function sendVerificationRequest(args) {
  const { identifier, url, provider, ...rest } = args as any;
  const user = await getUserByEmail(identifier);

  const allowedRoles: UserRole[] = [UserRole.ADMIN, UserRole.COUNSELOR];

  if (!allowedRoles.includes(user?.role as UserRole)) {
    throw new Error("Not Authorized");
  }

  if (!user || !user.first_name) return;

  const userVerified = user?.emailVerified ? true : false;
  const authSubject = userVerified
    ? `Sign-in link for ${siteConfig.name}`
    : "Activate your account";

  try {
    const to = identifier;
    const params = {
      from: provider.from!,
      to,
      subject: authSubject,
      react: MagicLinkEmail({
        firstName: user?.first_name as string,
        actionUrl: url,
        mailType: userVerified ? "login" : "register",
        siteName: siteConfig.name,
      }),
      // Set this to prevent Gmail from threading emails.
      // More info: https://resend.com/changelog/custom-email-headers
      headers: {
        "X-Entity-Ref-ID": new Date().getTime() + "",
      },
    };
    const result = await resend.emails.send(params);
    const { error, data } = result;

    if (error || !data) {
      throw new Error(error?.message);
    }
  } catch (error) {
    throw new Error("Failed to send verification email.");
  }
}
