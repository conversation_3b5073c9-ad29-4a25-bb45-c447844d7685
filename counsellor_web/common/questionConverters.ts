import {
  QuestionItem,
  QuestionType,
} from "@/actions/conversations/conversation-workflow";

export interface RawQuestion {
  question: string;
  table: string;
  questionId: string;
  options?: string[];
  workflowStepId: string;
  questionnaire_id?: string;
  canSkip?: boolean;
  characterLimit?: { min?: number; max?: number };
  tags?: string[];
}

/**
 * Convert a RawQuestion into a front-end QuestionItem.
 */
export function convertQuestion(raw: RawQuestion): QuestionItem {
  // Determine type based on the table value:
  let type: QuestionType; //"binary" | "multiple-choice" | "short-answer" | "long-answer" | "likert";
  switch (raw.table) {
    case "BinaryQuestion":
      type = "binary";
      break;
    case "MultipleChoiceQuestion":
      type = "multiple-choice";
      break;
    case "Question":
      // Here, you might decide between short and long answer.
      type = raw.question.length > 200 ? "long-answer" : "short-answer";
      break;
    case "LikertQuestion":
      type = "likert";
      break;
    default:
      type = "short-answer";
  }

  const canSkip = raw.canSkip ?? false;

  let characterLimit;
  if (raw.characterLimit) {
    // If DB has it, use it
    characterLimit = raw.characterLimit;
  } else if (type === "short-answer") {
    // Default min=50 if not specified
    characterLimit = { min: 50, max: undefined };
  } else if (type === "long-answer") {
    // Default min=200 if not specified
    characterLimit = { min: 200, max: undefined };
  } else {
    characterLimit = undefined;
  }
  return {
    id: raw.questionId,
    workflowStepId: raw.workflowStepId,
    text: raw.question,
    type,
    options:
      type === "multiple-choice" || type === "likert"
        ? raw.options || []
        : undefined,
    canSkip,
    questionnaire_id: raw.questionnaire_id,
    characterLimit,
    tags: raw.tags,
  };
}
