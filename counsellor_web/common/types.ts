import { Goal } from "@/actions/conversations/conversation-workflow";
import { UserRole } from "@prisma/client";

interface WorkflowStep {
  id: string;
  name: string;
  status: "completed" | "in-progress" | "not-started";
  due_date?: Date;
  metadata?: Record<string, unknown>;
}

export function isValidWorkflowStep(step: any): step is WorkflowStep {
  return (
    typeof step?.id === "string" &&
    typeof step?.name === "string" &&
    ["completed", "in-progress", "not-started"].includes(step?.status)
  );
}

export function calculateProgress(steps?: WorkflowStep[]) {
  if (!steps) return 0;
  const validSteps = steps.filter(isValidWorkflowStep);
  const completed = validSteps.filter((s) => s.status === "completed").length;
  return validSteps.length > 0 ? completed / validSteps.length : 0;
}

export interface StudentWithWorkflows {
  id: string;
  user_id: string;
  grade: number;
  first_name: string;
  last_name: string;
  email: string | null;
  config_id: string | null;
  updated_at: Date;
  student_agent_id: string | null;
  workflow_steps: any[];
  student_workflow: any[];
  onboardingAnsweredCount: number;
  onboardingTotalQuestions: number;
}
export interface User {
  id: string;
  role: UserRole;
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string | null;
  gender: string | null;
  last_active: Date;
  isProfileComplete: boolean;
  enabled: boolean;
}

export interface PublishedWorkflow {
  id: string;
  name: string;
  owner_id: string | null;
  status: string;
  parent_step_id: string | null;
  workflow_type: string;
  created_at: Date;
  updated_at: Date;
  steps: {
    id: string;
    goal: string;
    index: number;
    name: string;
    setter: string | null;
    data: {
      table: string;
      question: string;
      questionId: string;
      options?: string[]; // Optional for multiple choice questions
    };
    parent_workflow_id: string;
    created_at: Date;
    updated_at: Date;
  }[];
}

export interface UnstructuredStepData {
  goals?: Goal[];
  earlyEndMessage?: string;
}

export interface DashboardStudent {
  id: string;
  student_id: string;
  grade: number;
  created_at: Date;
  updated_at: Date;
  school_id: string | null;
  first_name: string;
  last_name: string;
  email: string | null;
  phone_number: string | null;
  isProfileComplete: boolean;
  onboardingAnsweredCount: number;
  onboardingTotalQuestions: number;
}

