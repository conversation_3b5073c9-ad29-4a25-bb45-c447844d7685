import { Timezone } from "@prisma/client";

export interface TimezoneOption {
  label: string;
  value: Timezone;
  group?: string;
}

// Organized by region/country
export const TIMEZONE_OPTIONS: TimezoneOption[] = [
  // United States
  {
    label: "Eastern Time (UTC-05:00) – New York, Miami",
    value: Timezone.UTC_MINUS_05_00,
    group: "United States",
  },
  {
    label: "Central Time (UTC-06:00) – Chicago, Dallas",
    value: Timezone.UTC_MINUS_06_00,
    group: "United States",
  },
  {
    label: "Mountain Time (UTC-07:00) – Denver, Phoenix",
    value: Timezone.UTC_MINUS_07_00,
    group: "United States",
  },
  {
    label: "Pacific Time (UTC-08:00) – Los Angeles, Seattle",
    value: Timezone.UTC_MINUS_08_00,
    group: "United States",
  },
  {
    label: "Alaska Time (UTC-09:00) – Anchorage",
    value: Timezone.UTC_MINUS_09_00,
    group: "United States",
  },
  {
    label: "Hawaii Time (UTC-10:00) – Honolulu",
    value: Timezone.UTC_MINUS_10_00,
    group: "United States",
  },

  // Europe - we could add more countries later
  {
    label: "Central European Time (UTC+01:00) – Rome, Milan",
    value: Timezone.UTC_PLUS_01_00,
    group: "Europe",
  },

  // Brazil - Main timezone
  {
    label: "Brasília Time (UTC-03:00) – São Paulo, Rio de Janeiro, Brasília",
    value: Timezone.UTC_MINUS_03_00,
    group: "Brazil",
  },
];

// Get unique groups from timezone options
export const getTimezoneGroups = (): string[] => {
  // Fix: Use Array.from instead of spread operator to avoid TS2802 error
  return Array.from(
    new Set(TIMEZONE_OPTIONS.map((option) => option.group)),
  ).filter(Boolean) as string[];
};
