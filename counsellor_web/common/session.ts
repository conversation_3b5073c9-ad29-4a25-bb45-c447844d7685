import "server-only";

import { cache } from "react";
import { auth } from "@/auth";

import { prisma } from "@/common/db";
import { getUserById } from "@/common/user";
import { User } from "./types";

export const getCurrentUser = cache(async (): Promise<User | undefined> => {
  const session = await auth();
  if (!session?.user) {
    return;
  }
  const dbUser = await getUserById(session.user?.id!);

  // Update lastActive timestamp
  if (dbUser) {
    await prisma.user.update({
      where: { id: dbUser.id },
      data: { last_active: new Date() }
    });
    return dbUser;
  }
});
