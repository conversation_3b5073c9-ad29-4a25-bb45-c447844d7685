import { env } from "@/env.mjs";

/**
 * Determines the appropriate API URL based on the host
 * Uses HTTP for localhost/0.0.0.0 and HTTPS for all other hosts
 */
export function getBaseApiUrl(): string {
  const host = env.ADDIE_API_HOST;
  const protocol = host.includes("localhost") || host.includes("0.0.0.0") ? "http" : "https";
  return `${protocol}://${host}`;
}

// Pre-compute the base URL to avoid recalculating it on every request
export const baseApiUrl = getBaseApiUrl();
