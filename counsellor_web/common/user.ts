import { UserRole } from "@prisma/client";

import { prisma } from "@/common/db";
import log from "@/common/logger";
import { User } from "./types";

export const ensureCounselorRecord = async (userId: string) => {
  const counselor = await prisma.counselor.findUnique({
    where: {
      user_id: userId,
    },
  });

  if (!counselor) {
    await prisma.counselor.create({
      data: {
        user_id: userId,
      },
    });
  }
};

export const getUserByEmail = async (email: string) => {
  try {
    const user = await prisma.user.findUnique({
      where: {
        email: email,
      },
      include: {
        Counselor: true,
      },
    });

    return user;
  } catch {
    return null;
  }
};

export const getUserById = async (id: string) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        first_name: true,
        last_name: true,
        middle_name: true,
        phone_number: true,
        gender: true,
        pronouns: true,
        isProfileComplete: true,
        enabled: true,
        role: true,
        updated_at: true,
        last_active: true,
        timezone: true,  // Include timezone
        // Also include relations
        students: true,
        Counselor: true,
        // Include the preferred_availability relation
        preferred_availability: {
          select: {
            day: true,
            block: true
          }
        }
      }
    });

    return user as User;
  } catch (error) {
    log.error('Error getting user by ID', error);
    return null;
  }
};

export const getCounselorById = async (id: string) => {
  let counselor = await prisma.counselor.findUnique({
    where: { user_id: id },
    include: {
      user: true,
    },
  });

  if (!counselor) {
    counselor = await prisma.counselor.create({
      data: {
        user_id: id,
      },
      include: {
        user: true,
      },
    });
  }

  log.debug(counselor)
  const allowedRoles: UserRole[] = [UserRole.COUNSELOR, UserRole.ADMIN];

  if (!allowedRoles.includes(counselor.user?.role)) {
    throw new Error("User does not have counselor or admin role");
  }
  return counselor;
};
