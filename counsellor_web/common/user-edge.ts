// Edge-compatible user functions (no Prisma dependency)
// This file provides JWT-only versions of user-related functions for use in Edge Runtime

import { jwtDecode } from 'jwt-decode';

// Simple JWT token decoding to get user info without database access
// This is safe to use in middleware or edge functions
export const getUserFromToken = (token: string) => {
  try {
    const decoded = jwtDecode(token);
    return decoded;
  } catch (error) {
    console.error('Error decoding JWT token:', error);
    return null;
  }
};

// Basic role checking without database access
export const hasRole = (token: any, allowedRoles: string[]) => {
  if (!token || !token.role) return false;
  return allowedRoles.includes(token.role);
};

// Validation helpers that don't require database access
export const isEmailAllowed = (email: string, allowedDomains: string[]) => {
  if (!email) return false;
  const domain = email.split('@')[1];
  return allowedDomains.includes(domain);
};
