import type { AdapterUser } from "@auth/core/adapters";
import type { Adapter } from "@auth/core/adapters";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { UserRole } from "@prisma/client";
import { PrismaClientKnownRequestError } from "@prisma/client/runtime/library";

import { env } from "@/env.mjs";
import { prisma } from "@/common/db";
import log from "@/common/logger";
import { stripUndefined } from "@/common/utils";

const prismaAuthAdapter = PrismaAdapter(prisma);

// No longer restricting domains - we accept any email

export function splitName(name: string, data: Record<string, any>) {
  const names = name.split(" ");
  // last index
  data.first_name = names[0];
  data.last_name = names[names.length - 1];
  delete data["name"];

  if (names.length > 2) {
    data.middle_name = names[1];
  }
}

// Since the signIn callback in auth.ts already verifies that users exist before reaching here,
// this function should only be called for existing users. However, we'll keep a backup check.
export async function createUser({ id, ...data }: any) {
  const email = data.email;
  
  // Get the existing user - this should always succeed because of the signIn callback check
  const existingUser = await prisma.user.findUnique({ where: { email } });
  
  if (existingUser) {
    // If the user exists, return it (normal sign-in flow)
    return {
      ...existingUser,
      // Ensure role is included to satisfy the type requirement
      // role: existingUser.role as UserRole
    } as any;
  }
  
  // This should theoretically never be reached because of the signIn callback
  // But keep it as a safety net
  log.warn("Attempted account creation prevented after signIn check - this should not happen", { email });
  throw new Error("Account creation during login is not allowed. Contact an administrator to create your account.");
}

const redirectUri = `${env.NEXTAUTH_URL}/api/auth/callback/google`;

async function useVerificationToken(identifier_token) {
  try {
    const verificationToken = await prisma.verificationToken.findFirst({
      where: { ...identifier_token },
    });

    // If verificationToken is null, just return it
    if (!verificationToken) return null;
    
    // @ts-expect-errors // MongoDB needs an ID, but we don't
    if (verificationToken.id) delete verificationToken.id;
    return verificationToken;
  } catch (error) {
    // If token already used/deleted, just return null
    // https://www.prisma.io/docs/reference/api-reference/error-reference#p2025
    if ((error as PrismaClientKnownRequestError).code === "P2025") return null;
    throw error;
  }
}

prismaAuthAdapter.getUser = async (id) => {
  const user = await prisma.user.findUnique({ where: { id } });

  return user as any;
};
prismaAuthAdapter.getUserByEmail = async (email) => {
  const user = await prisma.user.findUnique({ where: { email } });

  return user as any;
};

async function getUserByAccount(provider_providerAccountId) {
  const account = await prisma.account.findUnique({
    where: { provider_providerAccountId },
    select: { user: true },
  });
  return (account?.user as AdapterUser) ?? null;
}

// Type assertion to address the adapter version mismatch
const typedAdapter = prismaAuthAdapter as unknown as Adapter;
typedAdapter.createUser = createUser as any;
typedAdapter.getUserByAccount = getUserByAccount as any;
typedAdapter.useVerificationToken = useVerificationToken as any;
export default typedAdapter;
