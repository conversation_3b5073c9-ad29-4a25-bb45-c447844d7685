### Setup

1. Install dependencies using pnpm:

```sh
pnpm install
```

2. Copy `.env.example` to `.env.local` and update the variables.

```sh
cp .env.example .env.local
```

1. Set Environment variables

```shell
 set -a && source .env
```

1. Generate Prisma Client

```sh
pnpm prisma generate --generator client_js
```

3. Start the development server:

```sh
pnpm run dev
```

# Addie Data Setup Instructions
step-by-step instructions to set up the Addie Data project on your local machine.
## Prerequisites

1. Install Docker and Docker Compose.
2. For Windows users, download and install **PostgreSQL ONLY CLIENT**. During installation, select "Command Line Tools Only". Refer to this guide: [PostgreSQL Client Installation](https://stackoverflow.com/questions/33854798/how-do-i-install-just-the-client-tools-for-postgresql-on-windows).

## Setup Steps

#### 1. Deploy Docker Stack

If you are a Windows user, use the following command to deploy the stack:
```bash
docker stack deploy -c docker-compose-data-windows.yml addie_data
```
Otherwise, use this command
```bash
docker stack deploy -c docker-compose-data.yml addie_data
```
<br>

#### 2. Update .env Files
 Ensure the .env files are correctly configured:
###### Root .env file (located in the root folder):

- Add database credentials
- Replace all occurrences of HOST_IP with your system's IP address.
```bash
ipconfig
```

###### Web .env file (located in the web folder):
- Also, remember to change the host IP here for DBURL
This file is separate from the root .env file and is specific to the web service.

  <br>


### Drop database
- You need to drop the old database, and create a new database
````
DROP DATABASE qa;
````
#### 3. Set Up PostgreSQL Database
- Create the local database
```bash
  CREATE DATABASE qa_dev;
  ```
- Obtain the 'addie_qa.sql' file (contact the team to get this file).
- Set up the database
````bash
psql -h $HOST_IP -U $POSTGRES_USER -d $POSTGRES_DB -p $DATABASE_PORT < ~/addie_qa.sql
````
- Here, the addie_qa.sql path, you will need to find where your .sql is located
- For example:
````
psql -h $HOST_IP -U $POSTGRES_USER -d $POSTGRES_DB -p $DATABASE_PORT < /c/Users/<USER>/Downloads/addie_qa_2.sql
````
### Python downloading/installing packages
pip install -r requirements.txt

pip install -r requirements.txt
10:53
pip install -e .

<br>

- In your .bashrc file, put those lines
- export PATH=$PATH:/c/Users/<USER>/AppData/Local/Programs/Python/Python310
export PATH=$PATH:/c/Users/<USER>/.cargo/bin
export PATH=$PATH:/c/Users/<USER>/AppData/Local/Programs/Python/Python310/Scripts

##  Path
/c/Users/<USER>/Downloads/addie_qa_2.sql

#### 4. Generate the Prisma Client
````bash
pnpm prisma generate --generator client_js
````
<br>

#### 5. Reload Environment Variables
````bash
set -a && source .env
````
<br>

#### 6. Start the development server


```sh
pnpm run dev
```


### take down the stack
```bash
docker stack rm addie_data
```

### start the stack
```bash
docker stack deploy -c docker-compose-data.yml addie_data
```

### connect to the log
```bash
 docker service logs addie_data_postgres -f
```

### Run celery
```bash
celery -A addie.tasks.app worker --loglevel=error -c 8
```

### Run fastapi
```bash
fastapi run addie/api/app.py
```
### activate pyenv (run this before running fastapi and celery)
```bash
pyenv activate addie
```

> [!NOTE]
> I use [npm-check-updates](https://www.npmjs.com/package/npm-check-updates) package for update this project.
>
> Use this command for update your project: `ncu -i --format group`

