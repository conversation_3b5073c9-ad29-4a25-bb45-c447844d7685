export type QuestionType = "short" | "long" | "binary" | "multiple";
export type ConversationStatus = "active" | "inactive" | "draft";
export type WorkflowType = "onboarding" | "college" | "custom";

export interface WorkflowStepData {
  questionText: string;
  type: QuestionType;
  minLength?: number;
  maxLength?: number;
  options?: string[];
  required?: boolean;
}

export interface WorkflowStep {
  id: string;
  goal: string;
  index: number;
  name: string;
  data: WorkflowStepData;
  created_at: string;
  updated_at: string;
}

export interface Workflow {
  id: string;
  name: string;
  type: WorkflowType;
  status: "active" | "inactive" | "draft";
  steps: WorkflowStep[];
  created_at: string;
  updated_at: string;
}
