import { UserRole } from "@prisma/client";
import { User } from "next-auth";
import { JWT } from "next-auth/jwt";

export type ExtendedUser = User & {
  role: UserRole;
  enabled?: boolean;
  isProfileComplete?: boolean;
  first_name?: string;
  last_name?: string;
  phone_number?: string;
  gender?: string;
};

declare module "next-auth/jwt" {
  interface JWT {
    role: UserRole;
    enabled?: boolean;
    isProfileComplete?: boolean;
  }
}

declare module "next-auth" {
  interface Session {
    user: ExtendedUser;
  }
}
