import { UserRole } from "@prisma/client";

// Extend Next Auth types for our custom user properties
declare module "next-auth" {
  interface User {
    id: string;
    email: string;
    first_name?: string | null;
    last_name?: string | null;
    middle_name?: string | null;
    phone_number?: string | null;
    gender?: string | null;
    pronouns?: string | null;
    isProfileComplete?: boolean;
    role: UserRole;
    image?: string | null;
  }

  interface Session {
    user: {
      id: string;
      email: string;
      first_name?: string | null;
      last_name?: string | null;
      phone_number?: string | null;
      gender?: string | null;
      isProfileComplete?: boolean;
      role: UserRole;
      image?: string | null;
      name?: string | null;
    }
  }
}

// Also extend JWT payload type to include our custom fields
declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    email: string;
    first_name?: string | null;
    last_name?: string | null;
    phone_number?: string | null;
    gender?: string | null;
    isProfileComplete?: boolean;
    role: UserRole;
    picture?: string | null;
  }
}
