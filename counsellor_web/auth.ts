import authConfig from "@/auth.config";
import { UserRole } from "@prisma/client";
import NextAuth, { type DefaultSession } from "next-auth";
import { prisma } from "@/common/db";
import prismaAuthAdapter from "@/common/prisma-auth-adapter";
import { getUserById } from "@/common/user";

// More info: https://authjs.dev/getting-started/typescript#module-augmentation
declare module "next-auth" {
  interface Session {
    user: {
      role: UserRole;
      enabled?: boolean;
      isProfileComplete?: boolean;
      first_name?: string | null;
      last_name?: string | null;
      phone_number?: string | null;
      gender?: string | null;
    } & DefaultSession["user"];
  }

  interface User {
    role: UserRole;
    enabled?: boolean;
    isProfileComplete?: boolean;
    first_name?: string | null;
    last_name?: string | null;
    phone_number?: string | null;
    gender?: string | null;
  }
}

declare module "@auth/core/adapters" {
  interface AdapterUser {
    role: UserRole;
    enabled?: boolean;
    isProfileComplete?: boolean;
    first_name?: string | null;
    last_name?: string | null;
    phone_number?: string | null;
    gender?: string | null;
  }
}

// Create a separate auth configuration for middleware that doesn't depend on Prisma
export const {
  handlers: { GET, POST },
  auth,
} = NextAuth({
  adapter: prismaAuthAdapter as any,
  session: { 
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: "/login",
    signOut: "/logout",
  },
  // Trust the reverse proxy and use the forwarded headers
  trustHost: true,
  callbacks: {
    // This callback runs in the Edge Runtime (middleware)
    // We must avoid any Prisma calls here
    async session({ token, session }) {
      if (session.user) {
        if (token.sub) {
          session.user.id = token.sub;
        }

        if (token.email) {
          session.user.email = token.email;
        }

        if (token.role) {
          session.user.role = token.role as UserRole;
        }

        session.user.name = token.name ? (token.name as string) : "";
        session.user.image = token.picture as string;
      }

      return session;
    },

    // This runs in a regular server environment when establishing the JWT
    // We pre-load all needed user data into the token here 
    async jwt({ token, user, account, profile, trigger }) {
      if (!token.sub) return token;

      // Fetch user from database to get all current data
      const dbUser = await getUserById(token.sub);

      if (!dbUser) return token;

      // Update token with fresh data from database
      const name = `${dbUser.last_name}, ${dbUser.first_name}`;
      token.name = name;
      token.email = dbUser.email;
      token.picture = token.picture; // Keep existing picture from token
      token.role = dbUser.role;
      token.enabled = dbUser.enabled ?? true;
      
      // Store isProfileComplete in the token for middleware to use
      // Check multiple fields to determine if profile is complete
      const hasRequiredFields = !!(dbUser.first_name && dbUser.last_name && 
                                 dbUser.phone_number && dbUser.gender);
      token.isProfileComplete = dbUser.isProfileComplete === true || hasRequiredFields;
      
      // For sign-in processing
      if (account && profile) {
        // We don't need to validate domains anymore - we accept all domains
        // We'll handle counselor record creation in a separate API route
        // but we'll make sure the token is properly set up
      }
      
      return token;
    },
    
    // Only allow sign-in for existing users
    async signIn({ user, account, profile }) {
      // First check if we have an email
      if (!user?.email) return false;
      
      try {
        // Check if this user already exists in our database
        // This check is redundant with what we do in the adapter, but it provides an additional layer of security
        const existingUser = await prisma.user.findUnique({ 
          where: { email: user.email },
          select: { id: true }
        });
        
        // Only allow sign-in if the user already exists
        return !!existingUser;
      } catch (error) {
        // If there's an error checking the user, deny sign-in
        console.error("Error verifying existing user:", error);
        return false;
      }
    },
  },
  ...authConfig,
});
