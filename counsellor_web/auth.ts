import authConfig from "@/auth.config";
import { UserRole } from "@prisma/client";
import NextAuth, { type DefaultSession } from "next-auth";
import { prisma } from "@/common/db";
import prismaAuthAdapter from "@/common/prisma-auth-adapter";
import { getUserById } from "@/common/user";

// More info: https://authjs.dev/getting-started/typescript#module-augmentation
declare module "next-auth" {
  interface Session {
    user: {
      role: UserRole;
      enabled?: boolean;
      isProfileComplete?: boolean;
      first_name?: string | null;
      last_name?: string | null;
      phone_number?: string | null;
      gender?: string | null;
    } & DefaultSession["user"];
  }

  interface User {
    role: UserRole;
    enabled?: boolean;
    isProfileComplete?: boolean;
    first_name?: string | null;
    last_name?: string | null;
    phone_number?: string | null;
    gender?: string | null;
  }
}

declare module "@auth/core/adapters" {
  interface AdapterUser {
    role: UserRole;
    enabled?: boolean;
    isProfileComplete?: boolean;
    first_name?: string | null;
    last_name?: string | null;
    phone_number?: string | null;
    gender?: string | null;
  }
}

// Create a separate auth configuration for middleware that doesn't depend on Prisma
export const {
  handlers: { GET, POST },
  auth,
} = NextAuth({
  adapter: prismaAuthAdapter as any,
  session: { 
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: "/login",
    signOut: "/logout",
  },
  // Trust the reverse proxy and use the forwarded headers
  trustHost: true,
  callbacks: {
    // This callback runs in the Edge Runtime (middleware)
    // We must avoid any Prisma calls here
    async session({ token, session }) {
      if (session.user) {
        if (token.sub) {
          session.user.id = token.sub;
        }

        if (token.email) {
          session.user.email = token.email;
        }

        if (token.role) {
          session.user.role = token.role as UserRole;
        }

        session.user.name = token.name ? (token.name as string) : "";
        session.user.image = token.picture as string;
      }

      return session;
    },

    // This runs in a regular server environment when establishing the JWT
    // We pre-load all needed user data into the token here 
    async jwt({ token, user, account, profile, trigger }) {
      if (!token.sub) return token;

      // Fetch user from database to get all current data
      const dbUser = await getUserById(token.sub);

      if (!dbUser) return token;

      // Update token with fresh data from database
      const name = `${dbUser.last_name}, ${dbUser.first_name}`;
      token.name = name;
      token.email = dbUser.email;
      token.picture = token.picture; // Keep existing picture from token
      token.role = dbUser.role;
      token.enabled = dbUser.enabled ?? true;
      
      // Store isProfileComplete in the token for middleware to use
      // Check multiple fields to determine if profile is complete
      const hasRequiredFields = !!(dbUser.first_name && dbUser.last_name && 
                                 dbUser.phone_number && dbUser.gender);
      token.isProfileComplete = dbUser.isProfileComplete === true || hasRequiredFields;
      
      // For sign-in processing
      if (account && profile) {
        // We don't need to validate domains anymore - we accept all domains
        // We'll handle counselor record creation in a separate API route
        // but we'll make sure the token is properly set up
      }
      
      return token;
    },
    
    // Only allow COUNSELOR and ADMIN users to sign in to the counselor site
    async signIn({ user, account, profile }) {
      // CRITICAL: For OAuth providers, validate that the profile email matches the user email
      // This prevents account linking attacks where one Google account is used for multiple users
      if (account?.provider === "google" && profile?.email) {
        if (user.email !== profile.email) {
          console.error(`SECURITY: Email mismatch detected! User email: ${user.email}, Profile email: ${profile.email}`);
          console.error(`This indicates a potential account linking attack or misconfiguration`);
          return false;
        }
        
        // Use the profile email for validation, not the linked user email
        const actualEmail = profile.email;
        console.log(`OAuth login attempt with email from profile: ${actualEmail}`);
        
        // Look up the user by the actual email from the OAuth profile
        const actualUser = await prisma.user.findUnique({
          where: { email: actualEmail },
          select: { 
            id: true, 
            role: true, 
            enabled: true,
            email: true 
          }
        });
        
        if (!actualUser) {
          console.log(`Sign-in denied: User not found for OAuth email ${actualEmail}`);
          return false;
        }
        
        // Only allow COUNSELOR and ADMIN roles for the counselor site
        if (actualUser.role !== "COUNSELOR" && actualUser.role !== "ADMIN") {
          console.log(`Sign-in denied: OAuth user ${actualEmail} has role ${actualUser.role}, expected COUNSELOR or ADMIN`);
          return false;
        }
        
        // Check if user account is enabled
        if (actualUser.enabled === false) {
          console.log(`Sign-in denied: OAuth user ${actualEmail} account is disabled`);
          return false;
        }
        
        console.log(`Sign-in allowed: ${actualUser.role} user ${actualEmail} via OAuth`);
        return true;
      }
      
      // For non-OAuth providers (magic links, credentials)
      if (!user?.email) return false;
      
      try {
        // Check if this user already exists in our database and get their role
        const existingUser = await prisma.user.findUnique({ 
          where: { email: user.email },
          select: { 
            id: true, 
            role: true, 
            enabled: true,
            email: true 
          }
        });
        
        // Only allow sign-in if the user already exists
        if (!existingUser) {
          console.log(`Sign-in denied: User not found for email ${user.email}`);
          return false;
        }

        // Only allow COUNSELOR and ADMIN roles for the counselor site
        if (existingUser.role !== "COUNSELOR" && existingUser.role !== "ADMIN") {
          console.log(`Sign-in denied: User ${user.email} has role ${existingUser.role}, expected COUNSELOR or ADMIN`);
          return false;
        }

        // Check if user account is enabled
        if (existingUser.enabled === false) {
          console.log(`Sign-in denied: User ${user.email} account is disabled`);
          return false;
        }

        console.log(`Sign-in allowed: ${existingUser.role} user ${user.email}`);
        return true;
      } catch (error) {
        // If there's an error checking the user, deny sign-in
        console.error("Error verifying existing user:", error);
        return false;
      }
    },
  },
  ...authConfig,
});
