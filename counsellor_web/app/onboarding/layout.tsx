import Link from "next/link";
import { redirect } from "next/navigation";
import { LogOut } from "lucide-react";

import { getCurrentUser } from "@/common/session";
import { Button } from "@/components/ui/button";
import { Icons } from "@/components/shared/icons";

interface OnboardingLayoutProps {
  children: React.ReactNode;
}
export default async function OnboardingLayout({ children }) {
  const user = await getCurrentUser();
  // STRICTLY CHECK each required field for profile completion
  const hasPhone =
    typeof user?.phone_number === "string" && user?.phone_number.length > 0;
  const hasFirstName =
    typeof user?.first_name === "string" && user?.first_name.length > 0;
  const hasLastName =
    typeof user?.last_name === "string" && user?.last_name.length > 0;

  // Only consider profile complete if ALL required fields are present
  const isProfileComplete = hasPhone && hasFirstName && hasLastName;

  if (isProfileComplete) {
    return redirect("/");
  }

  return (
    <div className="flex h-screen flex-col">
      <header className="flex w-full items-center justify-between border-b bg-[#FAFAFA] p-4">
        <div className="flex h-8 w-8 items-center justify-center rounded-md bg-gray-100">
          <Icons.logo className="size-6 rounded-md bg-background fill-current p-0.5 text-black" />
        </div>
        <Link href="/api/auth/signout">
          <Button variant="outline" size="sm">
            <LogOut className="h-4 w-4" />
            <span>Logout</span>
          </Button>
        </Link>
      </header>
      {children}
    </div>
  );
}
