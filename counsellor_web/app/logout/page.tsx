"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { signOut } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { ACTIVE_SCHOOL_KEY } from "../context/SchoolContext";

export default function LogoutPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams?.get("callbackUrl") || "/login";
  const [confirmingLogout, setConfirmingLogout] = useState(true);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  async function handleLogout() {
    setIsLoggingOut(true);
    
    // Clear all localStorage items
    if (typeof window !== "undefined") {
      // Clear everything to ensure a complete logout
      localStorage.clear();
      console.log("Cleared localStorage during logout");
    }

    // Sign out using NextAuth
    await signOut({ 
      redirect: true,
      callbackUrl: callbackUrl 
    });
  }
  
  function handleCancel() {
    router.back(); // Go back to the previous page
  }

  return (
    <div className="flex h-screen items-center justify-center bg-gray-50">
      <Card className="w-[380px] shadow-lg">
        <CardHeader>
          <CardTitle>Sign Out</CardTitle>
          <CardDescription>
            Are you sure you want to sign out of your account?
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoggingOut ? (
            <div className="py-4 text-center">
              <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
              <p>Signing you out...</p>
              <p className="mt-2 text-sm text-muted-foreground">Please wait while we clear your session.</p>
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">
              This will clear all local data and end your current session. You&apos;ll need to sign in again to access your account.
            </p>
          )}
        </CardContent>
        {!isLoggingOut && (
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={handleCancel}>Cancel</Button>
            <Button onClick={handleLogout} variant="destructive">Sign Out</Button>
          </CardFooter>
        )}
      </Card>
    </div>
  );
}
