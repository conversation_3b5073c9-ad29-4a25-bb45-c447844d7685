import React from "react";
import { redirect } from "next/navigation";

import log from "@/common/logger";
import { getCurrentUser } from "@/common/session";
import { AuthCallbackWrapper } from "@/components/auth/auth-callback-wrapper";
import { Sidebar } from "@/components/layout/sidebar";
import { ConversationProvider } from "@/app/context/ConversationContext";
import { SchoolProvider } from "@/app/context/SchoolContext";
import { UIProvider } from "@/app/context/UIContext";
import { UserProvider } from "@/app/context/UserContext";

interface CounselorLayoutProps {
  children: React.ReactNode;
}

export default async function CounselorLayout({
  children,
}: CounselorLayoutProps) {
  const user = await getCurrentUser();

  // Redirect to login if no user is found
  if (!user) {
    log.debug("🚫 PROTECTED_LAYOUT: No user found, redirecting to login");
    return redirect("/login");
  }

  // STRICTLY CHECK each required field for profile completion
  const hasPhone =
    typeof user.phone_number === "string" && user.phone_number.length > 0;
  const hasGender = typeof user.gender === "string" && user.gender.length > 0;
  const hasFirstName =
    typeof user.first_name === "string" && user.first_name.length > 0;
  const hasLastName =
    typeof user.last_name === "string" && user.last_name.length > 0;

  // Only consider profile complete if ALL required fields are present
  const isProfileComplete =
    hasPhone && hasGender && hasFirstName && hasLastName;

  // IMPORTANT: Redirect to onboarding if profile is incomplete
  if (!isProfileComplete) {
    log.debug("⚠️ PROTECTED_LAYOUT: Redirecting to onboarding: Incomplete profile detected");
    return redirect("/onboarding");
  }

  return (
    <UIProvider>
      <UserProvider user={user}>
        <SchoolProvider>
          <ConversationProvider>
            <AuthCallbackWrapper>
              <div className="flex h-full w-full">
                <Sidebar>{children}</Sidebar>
              </div>
            </AuthCallbackWrapper>
          </ConversationProvider>
        </SchoolProvider>
      </UserProvider>
    </UIProvider>
  );
}
