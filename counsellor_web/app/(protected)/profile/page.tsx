import { getCurrentUser } from "@/common/session";
import { User } from "@/common/types";
import { ProfileForm } from "@/components/Settings/Profile/ProfileForm";
import { SettingsHeader } from "@/components/Settings/SettingsHeader";

export default async function ProfilePage() {
  const currentUser = await getCurrentUser();
  // console.log(currentUser);
  if (!currentUser) {
    return <div>User not found</div>;
  }

  return (
    <div className="min-h-screen w-full bg-background">
      <SettingsHeader currentPage="Profile" />
      <div className="container px-6 py-6">
        <ProfileForm initialData={currentUser} />
      </div>
    </div>
  );
}
