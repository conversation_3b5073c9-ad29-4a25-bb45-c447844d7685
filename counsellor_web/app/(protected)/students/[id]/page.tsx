import { cookies } from "next/headers";
import { notFound } from "next/navigation";
import { getCounselorNotes } from "@/services/counselorNotes";
import {
  getCounselorMessages,
  getQuestionResponses,
  getStudentChatSummary,
  getStudentData,
  getStudentMessages,
  getStudentMetrics,
  getStudentOnboarding,
  getTeacherComments,
  getTeacherCommentsSummary,
} from "@/services/student";

import { prisma } from "@/common/db";
import log from "@/common/logger";
import { getCurrentUser } from "@/common/session";
import { getCounselorById } from "@/common/user";
import { StudentContent } from "@/components/ui/student-content";

export default async function StudentDetailsPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const cookieStore = await cookies();
  const resolvedParams = await params;

  // Validate the id
  if (!resolvedParams?.id) {
    notFound();
  }

  const user = (await getCurrentUser())!;
  const counselor = await getCounselorById(user.id);
  const studentId = resolvedParams.id;
  const messagesId = `${user.id}-${studentId}`;
  const lastMsgId = parseInt(cookieStore.get(messagesId)?.value || "0");
  log.debug(`## student-page-last-msg-id: ${lastMsgId} ##`);

  const [
    student,
    metrics,
    messages,
    onboarding,
    teacherComments,
    qnaireResponses,
    teacherCommentsSummary,
    studentChatSummary,
  ] = await Promise.all([
    getStudentData(studentId),
    getStudentMetrics(studentId),
    getCounselorMessages(messagesId, lastMsgId),
    getStudentOnboarding(studentId),
    getTeacherComments(studentId),
    getQuestionResponses(studentId),
    getTeacherCommentsSummary(studentId) || [],
    getStudentChatSummary(studentId) || [],
  ]);

  if (!student) {
    notFound();
  }

  // Get the last interaction from the newest message
  const lastInteraction =
    messages.length > 0 ? messages[messages.length - 1].created_at : null;

  return (
    <div className="flex h-screen w-full">
      <div className="flex-1 overflow-auto">
        <StudentContent
          student={student}
          metrics={{ ...metrics, lastInteraction }}
          messages={messages}
          onboarding={onboarding}
          teacherComments={teacherComments}
          qnaireResponses={qnaireResponses}
          user={user}
          teacherCommentsSummary={teacherCommentsSummary}
          studentChatSummary={studentChatSummary}
          lastMsgId={lastMsgId}
          counselor={counselor}
        />
        {/*<StudentHeader*/}
        {/*  student={{*/}
        {/*    firstName: student.users[0]?.first_name || "N/A",*/}
        {/*    lastName: student.users[0]?.last_name || "N/A",*/}
        {/*    studentId: student.id,*/}
        {/*  }}*/}
        {/*/>*/}
        {/*<StudentMetrics metrics={{ ...metrics, lastInteraction }} />*/}
        {/*<StudentTabs*/}
        {/*  student={student}*/}
        {/*  messages={messages}*/}
        {/*  onboarding={onboarding}*/}
        {/*  teacherComments={teacherComments}*/}
        {/*  qnaireResponses={qnaireResponses}*/}
        {/*/>*/}
      </div>
    </div>
  );
}
