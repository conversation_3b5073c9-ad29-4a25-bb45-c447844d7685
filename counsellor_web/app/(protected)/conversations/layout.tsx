// app/conversations/layout.tsx
import React, { Suspense } from "react";
import {
  assignWorkflowToAllStudents,
  getAllConversations,
} from "@/actions/conversations/conversation-workflow";
import { auth } from "@/auth";

import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { ConversationList } from "@/components/Conversations/ConversationList";
import { CreateConversationButton } from "@/components/Conversations/CreateConversationButton";
import log from "@/common/logger";

export default async function ConversationsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();
  const userId = session?.user?.id || "";
  const conversations = await getAllConversations(userId);

  return (
    <div className="flex h-screen w-full">
      {/* Left Sidebar */}
      <div className="flex h-full w-80 flex-col gap-3 border-r">
        <CreateConversationButton />
        <Suspense
          fallback={
            <div className="flex flex-col items-center justify-center">
              <LoadingSpinner />
              <div>Loading conversations...</div>
            </div>
          }
        >
          <ConversationList conversations={conversations} />
        </Suspense>
      </div>
      {/* Right Side - dynamic content */}
      <div className="h-full flex-1">{children}</div>
    </div>
  );
}
