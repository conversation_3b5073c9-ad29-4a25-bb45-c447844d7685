import React from "react";
import { getConversationById } from "@/actions/conversations/conversation-workflow";
import { WorkflowType } from "@prisma/client";
import { auth } from "@/auth";

import log from "@/common/logger";
import { getCurrentUser } from "@/common/session";
import { ConversationDetail } from "@/components/Conversations/ConversationDetail";
import { ConversationHeader } from "@/components/Conversations/ConversationHeader";
import { CustomConversationDetail } from "@/components/Conversations/CustomConversationDetail";
import { UnstructuredConversationDetail } from "@/components/Conversations/Unstructured-Conversation/UnstructuredConversationDetail";

export default async function ConversationDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const resolvedParams = await params;
  const conversation = await getConversationById(resolvedParams.id);
  const session = await auth();

  // console.log("conversation ===>", conversation);

  if (!conversation) {
    return (
      <div className="mt-20 flex h-full items-center justify-center">
        <span className="my-auto">Conversation not found</span>
      </div>
    );
  }

  // Get user role for admin-only features
  const user = session?.user ? { role: session.user.role } : null;

  // Content wrapper with header
  return (
    <div className="flex h-screen flex-col overflow-auto">
      {/* Detail components */}
      {/*<div className="flex-1 overflow-auto">*/}
      {conversation.workflow_type === WorkflowType.STRUCTURED ? (
        conversation.type === "onboarding" ? (
          <ConversationDetail conversation={conversation} />
        ) : (
          <CustomConversationDetail conversation={conversation} />
        )
      ) : (
        <UnstructuredConversationDetail conversation={conversation} user={user} />
      )}
      {/*</div>*/}
    </div>
  );
}
