"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";

import { env } from "@/env.mjs";
import { cn } from "@/common/utils";
import { buttonVariants } from "@/components/ui/button";
import { UserAuthForm } from "@/components/forms/user-auth-form";
import { checkUserEmail, signOutUser } from "@/actions/auth";
import { Icons } from "@/components/shared/icons";

export default function LoginPage() {
  const baseUrl = env.NEXT_PUBLIC_APP_URL;
  const searchParams = useSearchParams();
  const router = useRouter();
  const [inviteEmail, setInviteEmail] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Get the callback URL or fallback to a safe default
  const callbackUrl = searchParams?.get("callbackUrl");
  const backUrl = callbackUrl ? decodeURIComponent(callbackUrl) : "/dashboard";
  
  // Handle invite parameters
  useEffect(() => {
    async function handleInviteParams() {
      // Extract parameters
      const inviteType = searchParams?.get("invite");
      const schoolId = searchParams?.get("school");
      const email = searchParams?.get("email");
      
      // Only process if this is a counselor invite with email
      if (inviteType === "counselor" && email) {
        try {
          // Check if a user is already logged in
          const userCheck = await checkUserEmail(email);
          
          // If user is logged in but with a different email, log them out
          if (userCheck.isLoggedIn && !userCheck.emailsMatch) {
            const result = await signOutUser();
            if (result.redirect) {
              router.push(result.redirect);
              return; // Exit early as we're redirecting
            }
          }
          
          // If everything is ok, set the email to use in the form
          setInviteEmail(email);
          
          // Also save the schoolId in localStorage for use after login
          if (schoolId && typeof window !== "undefined") {
            localStorage.setItem("addie-active-school", schoolId);
          }
        } catch (error) {
          console.error("Error handling invite parameters:", error);
        }
      }
      
      setIsLoading(false);
    }
    
    handleInviteParams();
  }, [searchParams, router]);

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <Link
        href={backUrl}
        className={cn(
          buttonVariants({ variant: "outline", size: "sm" }),
          "absolute left-4 top-4 md:left-8 md:top-8",
        )}
      >
        <>
          <Icons.chevronLeft className="mr-2 size-4" />
          Back
        </>
      </Link>
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <Icons.logo className="mx-auto size-12" />
          <h1 className="text-2xl font-semibold tracking-tight">
            Welcome back
          </h1>
        </div>
        {!isLoading && (
          <UserAuthForm baseUrl={baseUrl} defaultEmail={inviteEmail || undefined} />
        )}
      </div>
    </div>
  );
}
