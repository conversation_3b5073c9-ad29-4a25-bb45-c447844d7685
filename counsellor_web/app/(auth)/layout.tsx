import { redirect } from "next/navigation";

import log from "@/common/logger";
import { getCurrentUser } from "@/common/session";

interface AuthLayoutProps {
  children: React.ReactNode;
}

export default async function AuthLayout({ children }: AuthLayoutProps) {
  const user = await getCurrentUser();

  if (user) {
    log.debug("Auth layout: User already authenticated, redirecting to home", { 
      id: user.id, 
      email: user.email, 
      role: user.role 
    });
    redirect("/");
  }

  return <div className="min-h-screen">{children}</div>;
}
