"use client";

import React, { createContext, useCallback, useContext, useState } from "react";
import {
  addCounselorNote,
  deleteCounselorNote,
  editCounselorNote,
  getCounselorNotes,
} from "@/services/counselorNotes";
import { toast } from "sonner";

interface Note {
  id: string;
  student_id: string;
  counselor_id: string;
  topic: string;
  content: string;
  created_at: string;
}
// Context Type
interface NotesContextType {
  notes: Note[];
  fetchNotes: () => Promise<void>;
  handleAddNote: (content: string) => Promise<void>;
  isFetching: boolean;
  checkSavedNotes: (messages: any[]) => any[];
  handleDeleteNote: (noteId: string) => Promise<void>;
  handleEditNote: (noteId: string, content: string) => Promise<void>;
}

// Create Context
const NotesContext = createContext<NotesContextType | undefined>(undefined);

// Provider Component
export function NotesProvider({
  children,
  studentId,
  counselorId,
}: {
  children: React.ReactNode;
  studentId: string;
  counselorId: string;
}) {
  const [notes, setNotes] = useState<Note[]>([]);
  const [isFetching, setIsFetching] = useState(false);
  // Fetch Notes
  const fetchNotes = useCallback(async () => {
    setIsFetching(true);
    try {
      const fetchedNotes = await getCounselorNotes(studentId);
      setNotes(fetchedNotes);
      setIsFetching(false);
    } catch (error) {
      toast.error("Failed to fetch notes");
      console.error("Error fetching notes:", error);
    } finally {
      setIsFetching(false);
    }
  }, [studentId]);

  // Add Note
  const handleAddNote = async (content: string) => {
    if (!content.trim()) return;
    if (notes.some((note) => note.content.trim() === content.trim())) {
      toast.error("Note already exists");
      // console.log("Exact same note is already been saved, skip");
      setTimeout(() => {}, 3000);
      return;
    }
    const loadingId = toast.loading("Adding note");
    try {
      const newNote = await addCounselorNote(studentId, counselorId, content);
      if (newNote) {
        setNotes((prevNotes) => [newNote, ...prevNotes]); // Update state instantly
        toast.success("Note added successfully", { id: loadingId });
      }
    } catch (error) {
      console.error("Error adding note:", error);
      toast.error("Failed to add note", { id: loadingId });
    }
  };

  const checkSavedNotes = (messages: any[]): any[] => {
    return messages
      .filter((message) =>
        notes.some((note) => note.content.trim() === message.trim()),
      )
      .map((message) => message); // Return saved messages
  };

  // handle delete a note
  const handleDeleteNote = async (noteId: string) => {
    try {
      await deleteCounselorNote(noteId, studentId);
      setNotes((prevNotes) => prevNotes.filter((note) => note.id !== noteId));
      toast.success("Note is deleted");
    } catch (e) {
      console.error("failed to delete a note");
      toast.error("Deleting note failed..");
    }
  };

  const handleEditNote = async (noteId: string, content: string) => {
    try {
      const editedMessage = await editCounselorNote(noteId, content, studentId);
      setNotes((prevNotes) =>
        prevNotes.map((note) =>
          note.id === noteId ? { ...note, content } : note,
        ),
      );

      // A second way to update note list -- just fetch it, but for us i think useState should be good
      // setTimeout(await fetchNotes, 1000);
      toast.success("Note is edited");
    } catch (e) {
      console.error("Failed to edit note");
      toast.error("Failed to edit note");
    }
  };

  return (
    <NotesContext.Provider
      value={{
        notes,
        fetchNotes,
        handleAddNote,
        isFetching,
        checkSavedNotes,
        handleDeleteNote,
        handleEditNote,
      }}
    >
      {children}
    </NotesContext.Provider>
  );
}

// Hook for using context
export function useNotes() {
  const context = useContext(NotesContext);
  if (!context) {
    throw new Error("useNotes must be used within a NotesProvider");
  }
  return context;
}
