"use client";

import React, { createContext, useContext, useEffect, useState } from "react";

import log from "@/common/logger";

interface UIContextProps {
  isChatOpen: boolean;
  toggleChat: () => void;
  isSidebarCollapsed: boolean;
  toggleSidebar: () => void;
  setIsChatOpen: (value: ((prevState: boolean) => boolean) | boolean) => void;
  setIsSidebarCollapsed: (
    value: ((prevState: boolean) => boolean) | boolean,
  ) => void;
}

const UIContext = createContext<UIContextProps | undefined>(undefined);

export function UIProvider({ children }: { children: React.ReactNode }) {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  const toggleChat = () => {
    setIsChatOpen((prev) => {
      const newChatState = !prev;
      if (newChatState) {
        setIsSidebarCollapsed(true); // Ensure sidebar collapses when chat opens
      }
      return newChatState;
    });
  };

  const toggleSidebar = () => {
    setIsSidebarCollapsed((prev) => !prev);
  };

  // Force sidebar collapse when chat opens -- commented out for now
  // useEffect(() => {
  //   if (isChatOpen) setIsSidebarCollapsed(true);
  // }, [isChatOpen, isSidebarCollapsed]);

  return (
    <UIContext.Provider
      value={{
        isChatOpen,
        toggleChat,
        setIsChatOpen,
        isSidebarCollapsed,
        toggleSidebar,
        setIsSidebarCollapsed,
      }}
    >
      {children}
    </UIContext.Provider>
  );
}

export function useUI() {
  const context = useContext(UIContext);
  if (context === undefined) {
    throw new Error("useUI must be used within a UIProvider");
  }
  return context;
}
