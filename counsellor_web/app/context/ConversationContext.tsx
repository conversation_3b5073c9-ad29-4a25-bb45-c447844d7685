"use client";

import React, { createContext, useContext, useState } from "react";

import log from "@/common/logger";

interface ConversationContextProps {
  // For displaying conversation details in the UI
  activeConversation: any | null;
  setActiveConversation: (conversation: any | null) => void;
  
  // For sending to the AI chat
  chatConversation: any | null;
  setChatConversation: (conversation: any | null) => void;
  
  // For storing conversation data
  conversationsData: Record<string, any>;
  setConversationData: (workflowId: string, data: any) => void;
}

const ConversationContext = createContext<ConversationContextProps | undefined>(undefined);

export function ConversationProvider({ children }: { children: React.ReactNode }) {
  // For displaying conversation details in the UI
  const [activeConversation, setActiveConversation] = useState<any | null>(null);
  
  // For sending to the AI chat
  const [chatConversation, setChatConversation] = useState<any | null>(null);
  
  // For storing conversation data
  const [conversationsData, setConversationsData] = useState<Record<string, any>>({});

  const setConversationData = (workflowId: string, data: any) => {
    setConversationsData((prev) => ({ ...prev, [workflowId]: data }));
  };

  return (
    <ConversationContext.Provider
      value={{
        activeConversation,
        setActiveConversation,
        chatConversation,
        setChatConversation,
        conversationsData,
        setConversationData,
      }}
    >
      {children}
    </ConversationContext.Provider>
  );
}

export function useConversation() {
  const context = useContext(ConversationContext);
  if (context === undefined) {
    throw new Error("useConversation must be used within a ConversationProvider");
  }
  return context;
}
