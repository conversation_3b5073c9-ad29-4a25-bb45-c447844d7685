"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { UserRole } from "@prisma/client";
import { getAllSchools, getCounselorSchool } from "@/actions/schools";
import { useUser } from "@/app/context/UserContext";
import log from "@/common/logger";

// LocalStorage key for active school
export const ACTIVE_SCHOOL_KEY = "addie-active-school";

interface School {
  id: string;
  name: string;
}

interface SchoolContextProps {
  schools: School[];
  activeSchool: string | null;
  schoolName: string;
  setActiveSchool: (schoolId: string) => void;
}

const SchoolContext = createContext<SchoolContextProps | undefined>(undefined);

export function SchoolProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const { user } = useUser();
  const [schoolName, setSchoolName] = useState("Loading...");
  const [schools, setSchools] = useState<School[]>([]);
  const [activeSchool, setActiveSchool] = useState<string | null>(null);

  // Load saved active school from localStorage on initial render
  useEffect(() => {
    if (typeof window !== "undefined") {
      const savedSchool = localStorage.getItem(ACTIVE_SCHOOL_KEY);
      if (savedSchool) {
        setActiveSchool(savedSchool);
      }
    }
  }, []);

  // Fetch school information when user loads or active school changes for admin
  useEffect(() => {
    async function fetchSchoolData() {
      if (!user || !user.id) return;

      try {
        // First check for active school selection from localStorage
        if (activeSchool) {
          // Fetch the school details to get the name
          const result = await getAllSchools();
          if (!result.error && result.schools) {
            const selectedSchool = result.schools.find(s => s.id === activeSchool);
            if (selectedSchool) {
              setSchoolName(selectedSchool.name);
              setSchools(result.schools);
              return;
            }
          }
        }

        log.debug("## no active school found ##");

        // If no active school or not found, check for counselor's assigned school
        const result = await getCounselorSchool();

        log.debug(result)

        if (!result.error && result.school) {
          setSchoolName(result.school.name);
          
          // Only update localStorage and refresh if the school has changed
          if (activeSchool !== result.school.id) {
            localStorage.setItem(ACTIVE_SCHOOL_KEY, result.school.id);
            setActiveSchool(result.school.id);
            
            log.debug("## school set ##", result.school.name);
            
            // Reload the page to reflect the school change
            router.refresh();
          } else {
            log.debug("## school unchanged ##", result.school.name);
          }

          // For admin users, also load all schools for the dropdown
          if (user.role === UserRole.ADMIN) {
            const schoolsResult = await getAllSchools();
            if (!schoolsResult.error) {
              setSchools(schoolsResult.schools || []);
            }
          }
        } else {
          setSchoolName("No School Selected");

          // For admin users, still load all schools for the dropdown
          if (user.role === UserRole.ADMIN) {
            const schoolsResult = await getAllSchools();
            if (!schoolsResult.error) {
              setSchools(schoolsResult.schools || []);
            }
          }
        }
      } catch (error) {
        console.error("Error fetching school data:", error);
        setSchoolName("No School Selected");
      }
    }

    fetchSchoolData();
  }, [user, activeSchool, router]);

  // Handle school selection change
  const handleSchoolChange = (schoolId: string) => {
    // Only update if the school has changed
    if (activeSchool !== schoolId) {
      setActiveSchool(schoolId);
      localStorage.setItem(ACTIVE_SCHOOL_KEY, schoolId);

      // Find the selected school name
      const selectedSchool = schools.find(s => s.id === schoolId);
      if (selectedSchool) {
        setSchoolName(selectedSchool.name);
      }

      // Reload the page to reflect the school change
      router.refresh();
    }
  };

  return (
    <SchoolContext.Provider
      value={{
        schools,
        activeSchool,
        schoolName,
        setActiveSchool: handleSchoolChange,
      }}
    >
      {children}
    </SchoolContext.Provider>
  );
}

export function useSchool() {
  const context = useContext(SchoolContext);
  if (!context) {
    throw new Error("useSchool must be used within a SchoolProvider");
  }
  return context;
}
