"use client";

import { User } from "@/common/types";
import { createContext, useContext, ReactNode } from "react";

interface UserContextType {
  user: User | null | undefined;
}

const UserContext = createContext<UserContextType | undefined>({
  user: null,
});

export function UserProvider({ children, user }: { children: ReactNode; user?: User }) {
  return (
    <UserContext.Provider value={{ user }}>
      {children}
    </UserContext.Provider>
  );
}

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};
