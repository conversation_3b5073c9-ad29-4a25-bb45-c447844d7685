import { auth } from "@/auth";
import { AppRouteHandlerFn } from "next-auth/lib/types";

import { prisma } from "@/common/db";
import { getUserByEmail } from "@/common/user";

export const DELETE: any = auth(async (req) => {
  if (!req.auth) {
    return new Response("Not authenticated", { status: 401 });
  }

  const currentUser = req.auth.user;
  if (!currentUser) {
    return new Response("Invalid user", { status: 401 });
  }

  try {
    await prisma.user.delete({
      where: {
        id: currentUser.id,
      },
    });
  } catch (error) {
    return new Response("Internal server error", { status: 500 });
  }

  return new Response("User deleted successfully!", { status: 200 });
});

export const GET: any = auth(async (req) => {
  // get email from query param
  const email = req.nextUrl.searchParams.get("email");
  if (email) {
    const user = await getUserByEmail(email);

    return new Response(JSON.stringify(user), { status: 200 });
  } else {
    return new Response("No email provided", { status: 400 });
  }
});
