import { NextResponse } from "next/server";

import { prisma } from "@/common/db";
import { getCurrentUser } from "@/common/session";

export async function POST() {
  try {
    const user = await prisma.user.findUnique({
      where: { email: "<EMAIL>" },
    });
    console.log("user", user);
    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 401 });
    }

    const existingCounselor = await prisma.counselor.findUnique({
      where: { user_id: user.id },
    });

    if (existingCounselor) {
      return NextResponse.json({
        message: "Counselor already exists",
        counselorId: existingCounselor.id,
      });
    }

    const newCounselor = await prisma.counselor.create({
      data: {
        user_id: user.id,
      },
    });

    console.log("new counsellor ==>", newCounselor);
    return NextResponse.json({
      message: "Counselor created",
      counselorId: newCounselor.id,
    });
  } catch (error) {
    console.error("Error creating counselor:", error);
    return NextResponse.json(
      { error: "Failed to create counselor" },
      { status: 500 },
    );
  }
}
