import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { AppRouteHandlerFn } from "next-auth/lib/types";

import { prisma } from "@/common/db";
import log from "@/common/logger";
import { getCurrentUser } from "@/common/session";
import {queueExperiment} from "@/actions/queue";

interface StudentAgentConfigCreate {
  role: string;
  prompt: {
    content: string;
  };
}

export const POST: any = auth(async (req) => {
  const user = await getCurrentUser();

  if (!user) {
    return new Response("Not authenticated", { status: 401 });
  }

  const data: StudentAgentConfigCreate = await req.json();
  const config = await prisma.studentAgentConfig.create({
    data: {
      role: data.role,
      prompt: {
        create: {
          content: data.prompt.content,
        },
      },
    },
    include: { prompt: true },
  });

  await queueExperiment(config.id);

  return NextResponse.json(config);
});
