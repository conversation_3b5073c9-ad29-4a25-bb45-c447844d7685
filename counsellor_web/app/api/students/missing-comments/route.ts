import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET() {
  try {
    const students = await prisma.student.findMany({
      include: {
        users: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        teacher_comments: true,
        gen_student_conversation_summaries: true,
        ocean_scores: true,
        student_majors: {
          include: {
            matches: true,
            ocean_score: true,
          },
        },
      },
    });

    const missingComments = students
      .filter((student) => {
        const email = student.users[0]?.email || "";
        return (
          email.endsWith("@lakesideschool.org") &&
          student.teacher_comments.length === 0 &&
          student.gen_student_conversation_summaries.length === 0
        );
      })
      .map((student) => ({
        studentId: student.id,
        firstName: student.users[0]?.first_name || "Unknown",
        lastName: student.users[0]?.last_name || "Unknown",
        email: student.users[0]?.email || "No Email",
      }));

    return NextResponse.json(missingComments);
  } catch (error) {
    console.error("Error fetching lakeside students missing comments:", error);
    return NextResponse.json(
      { error: "Failed to fetch lakeside students missing comments" },
      { status: 500 },
    );
  }
}
