import { NextResponse } from "next/server";

import { prisma } from "@/common/db";

export async function GET(
  request: Request,
  context: { params: Promise<{ id: string }> },
) {
  try {
    const resolvedParams = await context.params;
    const comments = await prisma.teacherComment.findMany({
      where: {
        student_id: resolvedParams.id,
      },
      orderBy: {
        created_at: "desc",
      },
    });

    return NextResponse.json(comments);
  } catch (error) {
    console.error("Failed to fetch teacher comments:", error);
    return NextResponse.json([]);
  }
}
