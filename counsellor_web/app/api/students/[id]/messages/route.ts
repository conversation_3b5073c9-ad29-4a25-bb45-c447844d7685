import { NextResponse } from "next/server";

import { prisma } from "@/common/db";

export async function GET(
  request: Request,
  context: { params: Promise<{ id: string }> },
) {
  try {
    const resolvedParams = await context.params;
    const studentAgent = await prisma.studentAgent.findFirst({
      where: {
        student_id: resolvedParams.id,
      },
    });

    if (!studentAgent?.messages_id) {
      return NextResponse.json([]);
    }

    const messages = await prisma.messages.findMany({
      where: {
        session_id: studentAgent.messages_id,
      },
      orderBy: {
        created_at: "asc",
      },
    });

    // Transform messages to match the ChatMessage interface
    const formattedMessages = messages.map((msg) => {
      const parsedMessage = msg.message as any;
      return {
        id: msg.id.toString(),
        content: parsedMessage.content,
        timestamp: parsedMessage.timestamp,
        isAddie: parsedMessage.role === "assistant",
      };
    });

    return NextResponse.json(formattedMessages);
  } catch (error) {
    console.error("Failed to fetch messages:", error);
    return NextResponse.json([]);
  }
}
