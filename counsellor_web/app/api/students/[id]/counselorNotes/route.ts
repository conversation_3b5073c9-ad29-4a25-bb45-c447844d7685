import { NextResponse } from "next/server";

import { prisma } from "@/common/db";

export async function GET(
  request: Request,
  context: { params: Promise<{ id: string }> },
) {
  try {
    const resolvedParams = await context.params;
    const counselorNotes = await prisma.counselorNote.findMany({
      where: {
        student_id: resolvedParams.id,
      },
      orderBy: {
        created_at: "desc",
      },
    });
    // console.log("Counselor Notes ===>", counselorNotes);
    return NextResponse.json(counselorNotes);
  } catch (error) {
    console.error("Failed to fetch counselor notes:", error);
    return NextResponse.json([]);
  }
}

export async function POST(
  req: Request,
  context: { params: Promise<{ id: string }> },
) {
  try {
    const resolvedParams = await context.params;
    const studentId = resolvedParams.id;
    if (!studentId) {
      return NextResponse.json(
        { error: "Missing student ID" },
        { status: 400 },
      );
    }
    const { counselor_id, content, topic = "General" } = await req.json();
    if (!counselor_id || !content) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 },
      );
    }

    const newNote = await prisma.counselorNote.create({
      data: { student_id: studentId, counselor_id, topic, content },
    });

    return NextResponse.json(newNote);
  } catch (error) {
    console.error("Error creating note:", error);
    return NextResponse.json(
      { error: "Failed to create note" },
      { status: 500 },
    );
  }
}

export async function DELETE(req: Request) {
  const { noteId } = await req.json();
  if (!noteId) {
    return NextResponse.json({ error: "Note Id is required" }, { status: 400 });
  }
  try {
    const deletedNote = await prisma.counselorNote.delete({
      where: { id: noteId },
    });
    if (!deletedNote) {
      return NextResponse.json("Cannot find a note with this noteId in db");
    }
    // console.log("DeletedNote ===>", deletedNote);
    return NextResponse.json({
      message: "Note deleted successfully",
      deletedNote,
    });
  } catch (e) {
    console.error("Deleting note failed", e);
    return NextResponse.json(
      { error: "Deleting note failed" },
      { status: 500 },
    );
  }
}

export async function PUT(req: Request) {
  const { noteId, content } = await req.json();
  if (!noteId) {
    return NextResponse.json({ error: "Note Id is required" }, { status: 400 });
  }

  if (!content) {
    return NextResponse.json(
      { error: "Need a new content to update the note" },
      { status: 400 },
    );
  }
  try {
    const updatedNoteContent = await prisma.counselorNote.update({
      where: {
        id: noteId,
      },
      data: {
        content,
      },
    });

    // console.log("Updated counsellor note ===>", updatedNoteContent);
    return NextResponse.json({
      message: "Note update succesfully",
      updatedNoteContent,
    });
  } catch (e) {
    console.error("Updating note failed", e);
    return NextResponse.json(
      { error: "Updating note failed" },
      { status: 500 },
    );
  }
}
