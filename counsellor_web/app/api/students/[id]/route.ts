import { NextResponse } from "next/server";

import { prisma } from "@/common/db";

export async function GET(
  request: Request,
  context: { params: Promise<{ id: string }> },
) {
  try {
    const { params } = context;
    const resolvedParams = await params;

    const student = await prisma.student.findUnique({
      where: { id: resolvedParams.id },
      include: {
        users: true,
        workflow_steps: true,
        academic_achievements: true,
        counselors: {
          include: {
            user: true,
          },
        },
        ocean_scores: true,
        student_majors: {
          include: {
            matches: true,
            ocean_score: true,
          },
        },
      },
    });

    if (!student) {
      return NextResponse.json({ error: "Student not found" }, { status: 404 });
    }

    return NextResponse.json(student);
  } catch (error) {
    console.error("Failed to fetch student:", error);
    return NextResponse.json(
      { error: "Failed to fetch student" },
      { status: 500 },
    );
  }
}
