import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { prisma } from "@/common/db";

export async function GET() {
  try {
    // Use the auth() function from Next-Auth to get the session
    const session = await auth();
    
    if (!session || !session.user || !session.user.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Get the user from the database with their full profile
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: {
        id: true,
        email: true,
        first_name: true,
        last_name: true,
        isProfileComplete: true,
        phone_number: true,
        gender: true,
        pronouns: true,
        updated_at: true, // Use updated_at instead of last_active
        role: true,
      }
    });
    
    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    return NextResponse.json(user);
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return NextResponse.json(
      { error: "Failed to fetch user profile" },
      { status: 500 }
    );
  }
}
