import { NextRequest, NextResponse } from "next/server";
import { NextApiRequest, NextApiResponse } from "next/types";
import { POST as authPost, GET } from "@/auth";

import { ALLOWED_DOMAINS } from "@/common/constants";

// Handle OPTIONS requests for CORS preflight
async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}

async function POST(req: NextRequest) {
  const domain = ["getaddie.com", "futuresight.ventures"];
  // check user email domain and deny authenticate if domain is not valid

  return await authPost(req);
}

export { GET, POST, OPTIONS };
