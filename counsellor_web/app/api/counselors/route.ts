import { prisma } from "@/common/db";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const counselors = await prisma.user.findMany({
      where: {
        role: "COUNSELOR"
      },
      include: {
        Counselor: true
      }
    });

    return NextResponse.json(counselors);
  } catch (error) {
    console.error("Failed to fetch counselors:", error);
    return NextResponse.json([]);
  }
}
