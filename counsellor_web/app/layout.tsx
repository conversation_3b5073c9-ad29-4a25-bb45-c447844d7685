import "@/styles/globals.css";

import { cookies } from "next/headers";
import { setTzOffset } from "@/actions/queue";
import { fontGeist, fontHeading, fontSans, fontUrban } from "@/assets/fonts";
import { SessionProvider } from "next-auth/react";
import { CookiesProvider } from "next-client-cookies/server";
import { ThemeProvider } from "next-themes";

import log from "@/common/logger";
import { getCurrentUser } from "@/common/session";
import { cn, constructMetadata } from "@/common/utils";
import { Toaster } from "@/components/ui/sonner";
import { Analytics } from "@/components/analytics";
import ModalProvider from "@/components/modals/providers";
import RequireProfileCompletion from "@/components/RequireProfileCompletion";
import { TailwindIndicator } from "@/components/tailwind-indicator";
import { UserpilotProvider } from "@/components/userpilot";

import { UserProvider } from "./context/UserContext";

interface RootLayoutProps {
  children: React.ReactNode;
}

export const metadata = constructMetadata();

export default async function RootLayout({ children }: RootLayoutProps) {
  const user = await getCurrentUser();

  return (
    <html lang="en" suppressHydrationWarning>
      <head />
      <body
        className={cn(
          "min-h-screen bg-background font-sans antialiased",
          fontSans.variable,
          fontUrban.variable,
          fontHeading.variable,
          fontGeist.variable,
        )}
      >
        <CookiesProvider>
          <SessionProvider>
            <UserProvider user={user}>
              <ThemeProvider
                attribute="class"
                defaultTheme="light"
                forcedTheme="light"
                enableSystem={false}
                disableTransitionOnChange
              >
                <ModalProvider>
                  <RequireProfileCompletion>
                    {children}
                  </RequireProfileCompletion>
                </ModalProvider>
                <Analytics />
                <Toaster richColors closeButton position="bottom-right" />
                <TailwindIndicator />
                <UserpilotProvider />
              </ThemeProvider>
            </UserProvider>
          </SessionProvider>
        </CookiesProvider>
      </body>
    </html>
  );
}
